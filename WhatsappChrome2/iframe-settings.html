<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configurações do IFrame</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f9fafb;
            color: #1f2937;
            min-width: 350px;
        }

        .container {
            padding: 10px;
        }

        .header {
            margin-bottom: 24px;
            text-align: center;
        }

        h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #4f46e5;
        }

        .subtitle {
            font-size: 0.875rem;
            color: #6b7280;
        }

        /* Card styles */
        .card {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            background-color: #f9fafb;
            padding: 12px 16px;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 500;
            color: #4f46e5;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-body {
            padding: 16px;
        }

        /* Step styles */
        .step {
            display: flex;
            margin-bottom: 20px;
            align-items: flex-start;
        }

        .step-number {
            background-color: #4f46e5;
            color: white;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .step-heading {
            font-weight: 600;
            font-size: 1.125rem;
            color: #4f46e5;
            margin-left: 8px;
        }

        .step-content {
            flex-grow: 1;
        }

        .step-title {
            font-weight: 500;
            margin-bottom: 8px;
            color: #374151;
        }

        .step-description {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 16px;
        }

        .step-result {
            margin: 16px 0 24px 40px;
            border-left: 2px solid #e5e7eb;
            padding-left: 16px;
        }

        .step-result-title {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 8px;
            font-weight: 500;
        }

        /* Form styles */
        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }

        .input-group {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-group i {
            position: absolute;
            left: 12px;
            color: #9ca3af;
        }

        input[type="url"] {
            width: 100%;
            padding: 10px 12px 10px 36px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            color: #1f2937;
            transition: all 0.2s;
        }

        input[type="url"]:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        input[type="url"]:disabled {
            background-color: #f3f4f6;
            cursor: not-allowed;
        }

        .help-text {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 4px;
        }

        .actions {
            display: flex;
            gap: 12px;
            margin-top: 24px;
        }

        /* Button styles */
        .btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 10px 16px;
            border: none;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: #4f46e5;
            color: white;
        }

        .btn-primary:hover {
            background-color: #4338ca;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: #f3f4f6;
            color: #4b5563;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background-color: #e5e7eb;
            transform: translateY(-1px);
        }

        .btn-outline {
            background-color: transparent;
            color: #4f46e5;
            border: 1px solid #4f46e5;
        }

        .btn-outline:hover {
            background-color: #f5f3ff;
            transform: translateY(-1px);
        }

        .btn-search {
            background-color: #0ea5e9;
            color: white;
        }

        .btn-search:hover {
            background-color: #0284c7;
            transform: translateY(-1px);
        }

        /* Voltar button style - Completamente redesenhado */
        .btn-back {
            background-color: white;
            color: #4b5563;
            border: none;
            border-radius: 12px;
            display: none; /* Hidden by default */
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
            font-weight: 500;
            padding: 14px 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .btn-back .back-content {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 12px;
            position: relative;
            z-index: 1;
        }

        .btn-back .icon-wrapper {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #f5f3ff;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            transition: all 0.2s ease;
        }

        .btn-back i {
            color: #6366f1;
            font-size: 1rem;
            transition: transform 0.2s ease;
        }

        .btn-back .text-wrapper {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: center;
        }

        .btn-back .back-label {
            font-size: 0.8rem;
            opacity: 0.75;
            margin-bottom: 2px;
        }

        .btn-back .back-destination {
            font-weight: 600;
            color: #1f2937;
            font-size: 0.95rem;
        }

        .btn-back:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            background-color: #fafafa;
        }

        .btn-back:hover .icon-wrapper {
            background-color: #ede9fe;
        }

        .btn-back:hover i {
            transform: translateX(-3px);
        }

        .btn-back:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        /* Current URL styles */
        .current-url-value {
            font-size: 0.875rem;
            color: #1f2937;
            word-break: break-all;
            padding: 8px 12px;
            background-color: #f9fafb;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        /* Notification styles */
        .notification {
            display: none;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 16px;
            font-size: 0.875rem;
            align-items: center;
            gap: 8px;
        }

        .notification.success {
            background-color: #ecfdf5;
            color: #047857;
            border: 1px solid #a7f3d0;
        }

        .notification.error {
            background-color: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        /* Tabs list styles */
        .search-container {
            position: relative;
            margin-bottom: 16px;
        }

        .search-input {
            width: 100%;
            padding: 10px 12px 10px 36px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
        }

        .tabs-list {
            max-height: 260px;
            overflow-y: auto;
            margin-bottom: 12px;
            border-radius: 8px;
        }

        /* Estilo visual de botão para cada item (Opção 2) */
        .tab-item {
            background-color: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .tab-content {
            flex: 1;
        }

        .tab-item:hover {
            background-color: #f5f5ff;
            border-color: #4f46e5;
            box-shadow: 0 2px 4px rgba(79, 70, 229, 0.1);
        }

        .tab-item.selected {
            background-color: #dbeafe;
            border-left: 3px solid #3b82f6;
        }

        /* Botão de seleção explícito (Opção 1) */
        .btn-select {
            background-color: #4f46e5;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
        }

        .btn-select:hover {
            background-color: #4338ca;
        }

        .btn-select.selected {
            background-color: #10b981;
        }

        .btn-select.selected:hover {
            background-color: #059669;
        }

        /* Instrução explícita no topo da lista (Opção 4) */
        .instruction-box {
            display: flex;
            align-items: center;
            gap: 8px;
            background-color: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 6px;
            padding: 10px 12px;
            margin-bottom: 12px;
            font-size: 0.875rem;
            color: #0369a1;
        }

        .instruction-box i {
            color: #0ea5e9;
            font-size: 1rem;
        }

        /* Destacar o primeiro item (Opção 8) */
        .tab-item.highlighted {
            border-left: 3px solid #4f46e5;
            background-color: #f5f3ff;
            position: relative;
        }

        .highlight-text {
            position: absolute;
            top: -8px;
            right: 10px;
            background-color: #4f46e5;
            color: white;
            font-size: 0.7rem;
            padding: 2px 8px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* Texto de status abaixo da lista (Opção 15) */
        .selection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px;
            background-color: #f9fafb;
            border-radius: 6px;
            margin-top: 12px;
            font-size: 0.875rem;
            color: #6b7280;
            border: 1px dashed #d1d5db;
        }

        .selection-status.selected {
            background-color: #f0fdf4;
            border: 1px solid #86efac;
            color: #166534;
        }

        .selection-status i {
            font-size: 1rem;
        }

        .tab-title {
            font-weight: 500;
            font-size: 0.875rem;
            margin-bottom: 4px;
            color: #1f2937;
        }

        .tab-index {
            display: inline-block;
            min-width: 20px;
            color: #4f46e5;
            font-weight: 600;
        }

        .tab-url {
            font-size: 0.75rem;
            color: #6b7280;
            word-break: break-all;
            max-width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .tab-url-tooltip {
            display: none;
            position: absolute;
            background: #374151;
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            max-width: 300px;
            word-break: break-all;
            z-index: 100;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .tab-url:hover .tab-url-tooltip {
            display: block;
        }

        #noTabsMessage {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6b7280;
            font-size: 0.875rem;
            background-color: #f9fafb;
            padding: 12px;
            border-radius: 6px;
            border: 1px dashed #d1d5db;
        }

        /* Badge styles */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-left: 8px;
        }

        .badge-count {
            background-color: #e0f2fe;
            color: #0284c7;
        }

        .badge-info {
            background-color: #e0f2fe;
            color: #0284c7;
        }

        /* Manual entry trigger */
        .manual-entry-trigger {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            color: #4f46e5;
            margin-top: 8px;
            cursor: pointer;
            padding: 6px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .manual-entry-trigger:hover {
            background-color: #f5f3ff;
            text-decoration: underline;
        }

        .manual-entry-trigger i {
            margin-right: 6px;
        }

        /* Estilos para o iframe inativo/ativo */
        .iframe-inactive {
            opacity: 0.7;
            filter: grayscale(30%);
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb !important;
        }

        .iframe-active {
            opacity: 1;
            filter: none;
            transition: all 0.3s ease;
            border: 2px solid #4f46e5 !important;
            box-shadow: 0 0 8px rgba(79, 70, 229, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Configurações do IFrame</h1>
            <p class="subtitle">Configure a URL que será exibida no painel lateral</p>
        </div>

        <div id="notification" class="notification"></div>

        <!-- Botão de voltar redesenhado -->
        <button id="backButton" class="btn-back" style="width: 100%; display: none;">
            <div class="back-content">
                <div class="icon-wrapper">
                    <i class="fas fa-arrow-left"></i>
                </div>
                <div class="text-wrapper">
                    <span class="back-label">Voltar para</span>
                    <span class="back-destination">página atual</span>
                </div>
            </div>
        </button>

        <div class="card">
            <div class="card-header">
                <i class="fas fa-cog"></i>
                <span>Configurar URL do Painel</span>
            </div>
            <div class="card-body">
                <div class="step">
                    <div class="step-content">
                        <div class="step-header">
                            <div class="step-number">1</div>
                            <div class="step-heading">Selecionar página</div>
                        </div>
                        <div class="step-title">Selecione uma página <span id="tabCountBadge" class="badge badge-count" style="display:none">0</span></div>
                        <div class="step-description">Escolha entre suas páginas abertas do Promokit ou insira manualmente</div>

                        <button type="button" id="findTabsBtn" class="btn btn-search" style="width: 100%; margin-bottom: 16px;">
                            <i class="fas fa-sync-alt"></i>
                            Buscar páginas do Promokit
                        </button>

                        <div id="tabsContainer" style="display: none;">
                            <div class="search-container">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="tabSearchInput" class="search-input" placeholder="Filtrar páginas...">
                            </div>

                            <div id="tabsList" class="tabs-list"></div>

                            <div id="noTabsMessage" style="display: none;">
                                <i class="fas fa-exclamation-circle"></i>
                                Nenhuma página do promokit.com.br encontrada.
                            </div>

                            <div class="manual-entry-trigger" id="manualEntryTrigger">
                                <i class="fas fa-edit"></i>
                                Inserir URL manualmente
                            </div>
                        </div>

                        <form id="iframeSettingsForm" style="width: 100%; margin-top: 20px; display: none;">
                            <div class="form-group">
                                <div class="input-group">
                                    <i class="fas fa-link"></i>
                                    <input
                                        type="url"
                                        id="iframeUrl"
                                        name="iframeUrl"
                                        placeholder="https://exemplo.promokit.com.br"
                                        required
                                    >
                                </div>
                                <p class="help-text" id="urlHelpText">Insira a URL completa incluindo https://</p>
                            </div>

                            <div class="actions">
                                <button type="button" id="cancelBtn" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                    Cancelar
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Salvar
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="iframe-settings.js"></script>
</body>
</html>
