/* Estilos para a extensão WhatsApp Suporte */

/* Estilo para mensagens não lidas */
.wp-support-unread {
  position: relative;
  border-left: 3px solid #4a6bff !important;
  padding-left: 8px;
  background-color: rgba(74, 107, 255, 0.05) !important;
}

/* Painel de suporte */
.whatsapp-support-panel {
  position: fixed;
  top: 60px;
  right: 20px;
  width: 320px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
  transition: all 0.3s ease;
}

/* Cabeçalho do painel */
.support-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #4a6bff, #7e5bef);
  color: white;
}

.support-panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.support-panel-toggle {
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

.support-panel-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Conteúdo do painel */
.support-panel-content {
  padding: 16px;
  max-height: 500px;
  overflow-y: auto;
}

/* Informações do cliente */
.client-card {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  margin-bottom: 16px;
}

.client-name {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.client-number {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.client-status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 12px;
}

.client-status.registered {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.client-status.new {
  background-color: #e3f2fd;
  color: #1565c0;
}

.client-details {
  font-size: 13px;
  line-height: 1.5;
}

.client-detail {
  margin-bottom: 4px;
}

/* Botões de ação */
.support-panel-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.support-action-button {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f0f2f5;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.support-action-button:hover {
  background: #e0e2e5;
}

.support-action-icon {
  margin-right: 8px;
  font-weight: bold;
}

/* Botão de configuração do iframe */
.whatsapp-support-iframe-settings-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.whatsapp-support-iframe-settings-btn:hover {
  background: #f5f5f5;
  transform: scale(1.05);
}

.whatsapp-support-iframe-settings-btn svg {
  width: 16px;
  height: 16px;
  fill: #666;
}

/* Histórico de atendimento */
.support-history-item {
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
}

.support-history-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 10px;
}

.support-history-status.active {
  background-color: #4caf50;
}

.support-history-status.waiting {
  background-color: #ff9800;
}

.support-history-status.finished {
  background-color: #9e9e9e;
}

.support-history-details {
  flex: 1;
}

.support-history-title {
  font-weight: 500;
  font-size: 14px;
}

.support-history-date {
  font-size: 12px;
  color: #666;
}

/* Customização da barra de rolagem */
.support-panel-content::-webkit-scrollbar {
  width: 6px;
}

.support-panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.support-panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.support-panel-content::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Indicadores de notificação */
.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #f44336;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Animações */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 107, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(74, 107, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 107, 255, 0);
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Responsividade */
@media (max-width: 768px) {
  .whatsapp-support-panel {
    width: 280px;
    right: 10px;
    top: 50px;
  }
}

/* Tema escuro (quando o WhatsApp estiver em modo escuro) */
.dark .whatsapp-support-panel {
  background: #222e35;
  color: #e9edef;
}

.dark .client-card {
  background: #111b21;
  border-color: #374045;
}

.dark .client-number {
  color: #8696a0;
}

.dark .support-action-button {
  background: #2a3942;
  color: #e9edef;
}

.dark .support-action-button:hover {
  background: #374248;
}

.dark .support-panel-content::-webkit-scrollbar-track {
  background: #111b21;
}

.dark .support-panel-content::-webkit-scrollbar-thumb {
  background: #374045;
}

.dark .support-panel-content::-webkit-scrollbar-thumb:hover {
  background: #4a5c64;
}
