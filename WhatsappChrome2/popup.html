<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WhatsApp Suporte</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Arial', sans-serif;
      background-color: #f0f2f5;
      color: #333;
      width: 320px;
    }
    
    .container {
      padding: 16px;
    }
    
    .header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #e0e0e0;
    }
    
    .logo {
      width: 40px;
      height: 40px;
      margin-right: 12px;
    }
    
    .title {
      font-size: 18px;
      font-weight: bold;
      color: #128C7E;
    }
    
    .status {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .status-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
    }
    
    .status-active {
      background-color: #25D366;
    }
    
    .status-inactive {
      background-color: #FF5252;
    }
    
    .status-message {
      font-size: 14px;
    }
    
    .stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      background-color: white;
      border-radius: 8px;
      padding: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .stat-item {
      text-align: center;
    }
    
    .stat-count {
      font-size: 20px;
      font-weight: bold;
      color: #128C7E;
    }
    
    .stat-label {
      font-size: 12px;
      color: #666;
    }
    
    .actions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      margin-bottom: 16px;
    }
    
    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 10px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .btn-primary {
      background-color: #128C7E;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #0E7369;
    }
    
    .btn-secondary {
      background-color: #E8F5E9;
      color: #128C7E;
    }
    
    .btn-secondary:hover {
      background-color: #C8E6C9;
    }
    
    .btn-full {
      grid-column: span 2;
    }
    
    .footer {
      font-size: 12px;
      color: #666;
      text-align: center;
      margin-top: 16px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="assets/logo.png" alt="WhatsApp Suporte" class="logo">
      <div class="title">WhatsApp Suporte</div>
    </div>
    
    <div class="status">
      <div id="status-indicator" class="status-indicator status-inactive"></div>
      <div id="status-message" class="status-message">Aguardando conexão...</div>
    </div>
    
    <div class="stats">
      <div class="stat-item">
        <div id="messages-count" class="stat-count">0</div>
        <div class="stat-label">Mensagens</div>
      </div>
      <div class="stat-item">
        <div id="clients-count" class="stat-count">0</div>
        <div class="stat-label">Clientes</div>
      </div>
      <div class="stat-item">
        <div id="pending-count" class="stat-count">0</div>
        <div class="stat-label">Pendentes</div>
      </div>
    </div>
    
    <div class="actions">
      <button id="open-whatsapp" class="btn btn-primary">
        <i class="fab fa-whatsapp"></i>
        Abrir WhatsApp
      </button>
      <button id="settings-button" class="btn btn-secondary">
        <i class="fas fa-cog"></i>
        Configurações
      </button>
      <button id="find-promokit" class="btn btn-secondary btn-full">
        <i class="fas fa-search"></i>
        Buscar Abas PromoKit
      </button>
    </div>
    
    <div class="footer">
      <p>WhatsApp Suporte v1.0.4 - MeuCardapio</p>
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
