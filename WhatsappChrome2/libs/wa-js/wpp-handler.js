// Script para executar no mesmo contexto do wpp-wapi.js
// Este script assina eventos de mensagens e exibe alertas quando novas mensagens chegam

(function() {
    'use strict';
    
    console.log('[WPP-HANDLER] Script iniciado');

    // Função para obter o status do WhatsApp de forma segura
    function obtenhaStatusWhatsapp() {
        const status = {
            carregou: true,  // Sempre responde que carregou para iniciar a integração
            pronto: false,
            conectado: false,
            numero: ''
        };

        try {
            // Verificar se o WPP está disponível
            if (window.WPP) {
                // Verificar se o WPP.conn está disponível
                if (window.WPP.conn) {
                    // Verificar se está conectado
                    if (typeof window.WPP.conn.isConnected === 'boolean') {
                        status.pronto = true;
                        status.conectado = window.WPP.conn.isConnected;
                    } else if (typeof window.WPP.conn.connected === 'boolean') {
                        status.pronto = true;
                        status.conectado = window.WPP.conn.connected;
                    }

                    // Obter o número do WhatsApp
                    if (window.WPP.conn.wid) {
                        status.numero = window.WPP.conn.wid;
                    } else if (window.WPP.conn.me) {
                        status.numero = window.WPP.conn.me;
                    }
                }
            }

            // Verificar se o WAPI está disponível como fallback
            if ((!status.pronto || !status.numero) && window.WAPI) {
                // Verificar se está conectado
                if (typeof window.WAPI.isConnected === 'function') {
                    try {
                        status.pronto = true;
                        status.conectado = window.WAPI.isConnected();
                    } catch (e) {
                        console.error('[WPP-HANDLER] Erro ao verificar conexão via WAPI:', e);
                    }
                }

                // Obter o número do WhatsApp
                if (!status.numero && typeof window.WAPI.getMe === 'function') {
                    try {
                        const me = window.WAPI.getMe();
                        if (me && me.user) {
                            status.numero = me.user;
                        } else if (me && me.wid) {
                            status.numero = me.wid;
                        } else if (me) {
                            status.numero = me;
                        }
                    } catch (e) {
                        console.error('[WPP-HANDLER] Erro ao obter número via WAPI:', e);
                    }
                }
            }

            // Verificar se o Store está disponível como último recurso
            if ((!status.pronto || !status.numero) && window.Store) {
                // Verificar se o Store.Conn está disponível
                if (window.Store.Conn) {
                    // Verificar se está conectado
                    if (typeof window.Store.Conn.connected === 'boolean') {
                        status.pronto = true;
                        status.conectado = window.Store.Conn.connected;
                    }

                    // Obter o número do WhatsApp
                    if (!status.numero) {
                        const user = window.Store.Conn.wid || window.Store.Conn.me;
                        if (user) {
                            if (user.user) {
                                status.numero = user.user;
                            } else if (user._serialized) {
                                status.numero = user._serialized;
                            } else {
                                status.numero = user;
                            }
                        }
                    }
                }
            }
        } catch (e) {
            console.error('[WPP-HANDLER] Erro ao obter status do WhatsApp:', e);
        }

        return status;
    }

    // Função para enviar envieParaPromokitmensagem para o PromoKit
    function envieParaPromokit(tipo, dados) {
        try {
            // Envia a mensagem para o window principal (para o content.js)
            window.postMessage({
                tipo: tipo,
                ...dados
            }, '*');
            console.log(`[WPP-HANDLER] Mensagem ${tipo} enviada via window.postMessage`);

            // Também tenta enviar para o iframe se existir
            let frame = document.getElementById('whatsapp-support-iframe');
            if (frame && frame.contentWindow) {
                frame.contentWindow.postMessage({
                    tipo: tipo,
                    ...dados
                }, '*');
                console.log(`[WPP-HANDLER] Mensagem ${tipo} também enviada para o iframe`);
            }
            
            return true;
        } catch (e) {
            console.error(`[WPP-HANDLER] Erro ao enviar mensagem ${tipo}`, e);
            return false;
        }
    }

    // Função para inicializar os listeners de eventos
    function inicializarEventListeners() {

        // Assina o evento de novas mensagens usando o WPP
        if (window.WPP && window.WPP.on) {
            // Assina o evento de mensagens recebidas
            window.WPP.on('chat.new_message', function(mensagem) {
                console.debug('[WPP-HANDLER] Nova mensagem recebida:', mensagem);

                // Exibe um alerta com informações da mensagem
                //console.log('[WPP-HANDLER] Nova mensagem recebida: ' + (mensagem.body || 'Mensagem sem texto'));

                // Envia um evento para o iframe com a mensagem
                /*
                window.parent.postMessage({
                    tipo: "NOVA_MENSAGEM",
                    text: {
                        tipo: 'MENSAGEM_RECEBIDA',
                        mensagem: mensagem
                    }
                }, "*");
                 */
            });

            // Assina o evento de mudança de chat ativo
            window.WPP.on('chat.active_chat', async function(chat) {
                if (chat && chat.id) {
                    // Extrair telefone do chat removendo @c.us ou @g.us
                    const chatId = chat.id._serialized || chat.id;
                    const telefone = chatId.replace('@c.us', '').replace('@g.us', '');
                    const nome = chat.name || chat.formattedTitle || 'Sem nome';
                    
                    console.log('[WPP-HANDLER] Chat ativo mudou para:', telefone, nome);
                    
                    // Capturar últimas mensagens da conversa
                    let mensagensFormatadas = [];
                    try {
                        // Tentar diferentes métodos de captura
                        let mensagens = null;
                        
                        // Método 1: getMessages
                        if (window.WPP.chat.getMessages) {
                            mensagens = await window.WPP.chat.getMessages(chatId, {count: 10});
                        }
                        // Método 2: list (alternativo)
                        else if (window.WPP.chat.list) {
                            const chatObj = window.WPP.chat.list().find(c => c.id === chatId);
                            if (chatObj && chatObj.msgs) {
                                mensagens = chatObj.msgs.models.slice(-10);
                            }
                        }
                        
                        if (mensagens && mensagens.length > 0) {
                            // Obter o ID do usuário logado no WhatsApp
                            let myId = null;
                            try {
                                if (window.WAPI && window.WAPI.getMe) {
                                    const me = window.WAPI.getMe();
                                    myId = me.wid || me.id || me._serialized || me.user;
                                    console.log('[WPP-HANDLER] Meu WhatsApp ID via WAPI:', myId);
                                }
                            } catch (e) {
                                console.error('[WPP-HANDLER] Erro ao obter ID do usuário:', e);
                            }
                            
                            mensagensFormatadas = mensagens
                                .map(m => {
                                    // Determinar remetente comparando m.from com myId
                                    let remetente = 'Lead';
                                    
                                    // Verificar se a mensagem foi enviada por mim
                                    if (myId && m.from) {
                                        if (m.from === myId || 
                                            (typeof m.from === 'string' && typeof myId === 'string' && m.from.includes(myId)) ||
                                            (m.from._serialized && m.from._serialized === myId) ||
                                            (m.from.user && myId && m.from.user === myId)) {
                                            remetente = 'Eu';
                                        }
                                    }
                                    
                                    // Fallback: verificar outras propriedades que possam indicar mensagem enviada
                                    if (remetente === 'Lead' && (m.isSentByMe || m.isFromMe || m.fromMe === true)) {
                                        remetente = 'Eu';
                                    }
                                    
                                    const texto = m.body || m.text || '';
                                    
                                    return {
                                        texto: texto,
                                        remetente: remetente,
                                        horario: new Date((m.t || m.timestamp) * 1000).toLocaleTimeString('pt-BR'),
                                        tipo: m.type || 'text'
                                    };
                                })
                                .filter(msg => {
                                    // Filtrar mensagens sem texto
                                    const textoLimpo = msg.texto.trim();
                                    if (!textoLimpo || textoLimpo === '[Mensagem sem texto]') {
                                        console.log('[WPP-HANDLER] Ignorando mensagem sem texto');
                                        return false;
                                    }
                                    return true;
                                });
                        }
                    } catch (erro) {
                        console.error('[WPP-HANDLER] Erro ao capturar mensagens:', erro);
                    }


                    let url = '/admin/contatos/iframe/' + encodeURIComponent('+' + telefone) + '?nome=' + encodeURIComponent(nome);

                    // Enviar evento para o PromoKit com informações do contato
                    envieParaPromokit('SELECIONOU_CONTATO', {
                        telefone: telefone,
                        nome: nome,
                        chat: WAPI._serializeRawObj(chat),
                        url: url,
                        extensao: 'vendas'
                    });
                    
                    // Enviar contexto da conversa com as mensagens (com múltiplas tentativas)
                    let tentativas = 0;
                    const maxTentativas = 5;
                    
                    const enviarContexto = () => {
                        tentativas++;
                        
                        const sucesso = envieParaPromokit('CONTEXTO_CONVERSA', {
                            telefone: telefone,
                            nome: nome,
                            mensagens: mensagensFormatadas,
                            tentativa: tentativas
                        });
                        
                        if (!sucesso && tentativas < maxTentativas) {
                            setTimeout(enviarContexto, 1000);
                        }
                    };
                    
                    // Primeira tentativa após 2 segundos
                    setTimeout(enviarContexto, 2000);
                    
                } else {
                    console.log('[WPP-HANDLER] Chat desativado (nenhum chat selecionado)');
                }
            });
        }

        // Assina eventos de mensagem do iframe
        window.addEventListener("message", async function(event) {
            try {
                // Verifica se é solicitação de mensagens atuais
                if (event.data && event.data.tipo === "SOLICITAR_MENSAGENS_ATUAIS") {
                    console.log('[WPP-HANDLER] Solicitação de mensagens atuais recebida, requestId:', event.data.requestId);
                    console.log('[WPP-HANDLER] Evento completo:', event.data);
                    console.log('[WPP-HANDLER] Origin:', event.origin);
                    
                    try {
                        // Obter o chat ativo
                        let chatId = null;
                        let mensagensFormatadas = [];
                        
                        // Tentar obter o chat ativo
                        if (window.WPP && window.WPP.chat && window.WPP.chat.getActiveChat) {
                            const activeChat = await window.WPP.chat.getActiveChat();
                            if (activeChat && activeChat.id) {
                                chatId = activeChat.id._serialized || activeChat.id;
                                
                                // Capturar mensagens usando o mesmo código do chat.active_chat
                                let mensagens = null;
                                
                                // Método 1: getMessages
                                if (window.WPP.chat.getMessages) {
                                    mensagens = await window.WPP.chat.getMessages(chatId, {count: 20});
                                }
                                // Método 2: list (alternativo)
                                else if (window.WPP.chat.list) {
                                    const chatObj = window.WPP.chat.list().find(c => c.id === chatId);
                                    if (chatObj && chatObj.msgs) {
                                        mensagens = chatObj.msgs.models.slice(-20);
                                    }
                                }
                                
                                if (mensagens && mensagens.length > 0) {
                                    // Obter o ID do usuário logado
                                    let myId = null;
                                    try {
                                        if (window.WAPI && window.WAPI.getMe) {
                                            const me = window.WAPI.getMe();
                                            myId = me.wid || me.id || me._serialized || me.user;
                                        }
                                    } catch (e) {
                                        console.error('[WPP-HANDLER] Erro ao obter ID do usuário:', e);
                                    }
                                    
                                    mensagensFormatadas = mensagens
                                        .map(m => {
                                            // Determinar remetente
                                            let remetente = 'Lead';
                                            
                                            if (m.fromMe === true || m.isSentByMe === true) {
                                                remetente = 'Eu';
                                            } else if (myId && m.from) {
                                                if (m.from === myId || 
                                                    (typeof m.from === 'string' && typeof myId === 'string' && m.from.includes(myId)) ||
                                                    (m.from._serialized && m.from._serialized === myId) ||
                                                    (m.from.user && myId && m.from.user === myId)) {
                                                    remetente = 'Eu';
                                                }
                                            }
                                            
                                            const texto = m.body || m.text || '';
                                            
                                            return {
                                                texto: texto,
                                                remetente: remetente,
                                                horario: m.t ? new Date(m.t * 1000).toLocaleTimeString('pt-BR') : new Date().toLocaleTimeString('pt-BR'),
                                                tipo: remetente === 'Eu' ? 'saida' : 'entrada',
                                                fromMe: remetente === 'Eu',
                                                isOutgoing: remetente === 'Eu'
                                            };
                                        })
                                        .filter(msg => {
                                            // Filtrar mensagens sem texto
                                            const textoLimpo = msg.texto.trim();
                                            if (!textoLimpo || textoLimpo === '[Mensagem sem texto]') {
                                                console.log('[WPP-HANDLER] Ignorando mensagem sem texto');
                                                return false;
                                            }
                                            return true;
                                        });
                                }
                            }
                        }
                        
                        // Enviar resposta com as mensagens capturadas
                        console.log('[WPP-HANDLER] Enviando mensagens formatadas:', mensagensFormatadas);
                        const enviado = envieParaPromokit('MENSAGENS_ATUALIZADAS', {
                            requestId: event.data.requestId,
                            mensagens: mensagensFormatadas,
                            sucesso: true
                        });
                        
                        console.log('[WPP-HANDLER] Mensagens enviadas:', mensagensFormatadas.length, 'Enviado:', enviado);
                        
                    } catch (erro) {
                        console.error('[WPP-HANDLER] Erro ao capturar mensagens atuais:', erro);
                        envieParaPromokit('MENSAGENS_ATUALIZADAS', {
                            requestId: event.data.requestId,
                            mensagens: [],
                            sucesso: false,
                            erro: erro.message
                        });
                    }
                }
                
                // Log de todos os eventos recebidos para debug
                if (event.data && event.data.tipo) {
                    console.log('[WPP-HANDLER] Evento recebido:', event.data.tipo, event.data);
                }
                
                // Verifica se é um evento válido
                if (event.data && event.data.tipo === "NOVA_MENSAGEM") {
                    // Exibe um alerta para qualquer evento NOVA_MENSAGEM

                    if (event.data.text) {
                        console.log('[WPP-HANDLER] Texto da mensagem:', event.data.text);
                        console.log('[WPP-HANDLER] Tipo de mensagem:', event.data.text.tipo);

                    // Verifica se é uma mensagem que contém telefone e texto para enviar
                    if (event.data.text.telefone && event.data.text.mensagem) {
                        const telefone = event.data.text.telefone;
                        const mensagem = event.data.text.mensagem;
                        const tipoMensagem = event.data.text.tipo || 'DESCONHECIDO';
                        const msg = event.data.text; // Objeto de mensagem completo

                        console.log('[WPP-HANDLER] Enviando mensagem tipo ' + tipoMensagem + ' para:', telefone);

                        // Usar WhatsappApi para enviar a mensagem
                        (async function() {
                            var apiWhatsapp = new WhatsappApi();
                            let respostaEnvio = null;

                            try {
                                // Enviar a mensagem usando WhatsappApi
                                respostaEnvio = await apiWhatsapp.envieMensagem(msg.telefone, msg.mensagem, 2000, msg.dadosImagem, msg);

                                console.log('[WPP-HANDLER] Mensagem enviada com sucesso via WhatsappApi');
                                respostaEnvio.idMensagem = msg.id;
                                respostaEnvio.msg = msg;
                            } catch (erro) {
                                // Erro no envio de mensagem
                                console.error('[WPP-HANDLER] Erro ao enviar mensagem via WhatsappApi:', erro);
                                respostaEnvio = {
                                    sucesso: false,
                                    status: 'ERRO',
                                    mensagem: 'Erro ao enviar mensagem: ' + erro.message,
                                    idMensagem: msg.id,
                                    msg: msg
                                };
                            } finally {
                                // Notificar sobre o resultado do envio
                                try {
                                    // Enviar mensagem para o iframe com o resultado do envio usando a função envieParaPromokit
                                    const enviado = envieParaPromokit('ENVIOU_MENSAGEM', {
                                        respostaEnvio: respostaEnvio
                                    });

                                    if (enviado) {
                                        console.log('[WPP-HANDLER] Notificação de envio enviada com sucesso');
                                    } else {
                                        console.error('[WPP-HANDLER] Não foi possível enviar notificação de envio');
                                    }
                                } catch (erroNotificacao) {
                                    console.error('[WPP-HANDLER] Erro ao notificar framePromokit:', erroNotificacao);
                                }
                            }
                        })();
                    }
                }
                }
            } catch (erro) {
                console.error('[WPP-HANDLER] Erro ao processar evento:', erro);
                console.log('Erro ao processar evento: ' + erro.message);
            }
        });

        console.log('[WPP-HANDLER] Event listener para mensagens do iframe registrado com sucesso!');
    }

    // Adiciona um listener global para eventos de mensagem do iframe
    // Este listener é adicionado imediatamente, antes mesmo de verificar se o WPP está disponível
    window.addEventListener("message", function(event) {
        try {
            // Verifica se é um evento de verificação de carregamento
            if (event.data && event.data.tipo === "NOVA_MENSAGEM" &&
                event.data.text && event.data.text.tipo === 'CARREGOU_WHATSAPP') {

                console.log('[WPP-HANDLER] Recebido evento CARREGOU_WHATSAPP');

                // Responde imediatamente, mesmo que o WPP ainda não esteja totalmente carregado
                const statusWhatsapp = obtenhaStatusWhatsapp();

                // Exibe um alerta para debug
                console.log('Respondendo CARREGOU_WHATSAPP com carregou=' + statusWhatsapp.carregou);

                // Envia resposta para o iframe do PromoKit
                envieParaPromokit("RESP_CARREGOU_WHATSAPP", {
                    carregou: statusWhatsapp.carregou,
                    numero: statusWhatsapp.numero || 'numero-nao-disponivel',
                    waId: statusWhatsapp.numero || 'numero-nao-disponivel'
                });
            }
        } catch (erro) {
            console.error('[WPP-HANDLER] Erro ao processar evento global:', erro);
        }
    });

    // Função para verificar se o WPP ou WAPI está disponível e inicializar
    function verificarEInicializar() {
        if (window.WPP || window.WAPI) {
            console.log('[WPP-HANDLER] WPP ou WAPI disponível, inicializando...');
            inicializarEventListeners();
        } else {
            console.log('[WPP-HANDLER] WPP e WAPI não disponíveis, aguardando...');
            setTimeout(verificarEInicializar, 2000);
        }
    }

    // Inicia o processo de verificação e inicialização
    verificarEInicializar();

    // Exibe mensagem de inicialização
    console.log('[WPP-HANDLER] Script wpp-handler.js carregado e aguardando inicialização...');
})();
