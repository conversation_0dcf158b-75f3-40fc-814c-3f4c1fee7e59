// Wrapper para a biblioteca libphonenumber-js
// Este script cria um objeto global 'libphonenumber' que pode ser usado pelas funções WAPI

(function() {
  // Verificar se a biblioteca já foi carregada
  if (window.libphonenumber) {
    console.log('libphonenumber já está disponível');
    return;
  }

  // Criar o objeto global libphonenumber
  window.libphonenumber = {
    parsePhoneNumber: function(phoneNumber) {
      // Implementação simplificada para não depender da biblioteca externa
      // Esta função retorna um objeto com um método getType que sempre retorna 'MOBILE'
      return {
        getType: function() {
          return 'MOBILE';
        },
        country: 'BR',
        number: phoneNumber
      };
    }
  };

  console.log('libphonenumber wrapper carregado com sucesso');
})();
