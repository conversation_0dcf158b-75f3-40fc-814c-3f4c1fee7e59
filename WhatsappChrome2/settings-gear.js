// Script para adicionar botão de engrenagem nas páginas PromoKit
// Permite voltar às configurações do iframe-settings

(function() {
    'use strict';

    // Verificar se já foi injetado para evitar duplicação
    if (document.getElementById('whatsapp-settings-gear')) {
        return;
    }

    // Verificar se estamos numa página PromoKit
    if (!window.location.hostname.endsWith('promokit.com.br')) {
        return;
    }

    // Criar o botão de engrenagem
    const settingsGear = document.createElement('button');
    settingsGear.id = 'whatsapp-settings-gear';
    settingsGear.innerHTML = '<i class="fas fa-cog"></i>';
    
    // Estilos do botão
    settingsGear.style.cssText = `
        position: fixed;
        top: 15px;
        right: 15px;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #128C7E, #25D366);
        border: none;
        border-radius: 50%;
        color: white;
        font-size: 18px;
        cursor: pointer;
        z-index: 999999;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0.9;
    `;

    // Efeitos hover
    settingsGear.onmouseenter = function() {
        this.style.transform = 'scale(1.1) rotate(90deg)';
        this.style.boxShadow = '0 6px 20px rgba(37, 211, 102, 0.4)';
        this.style.opacity = '1';
    };

    settingsGear.onmouseleave = function() {
        this.style.transform = 'scale(1) rotate(0deg)';
        this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        this.style.opacity = '0.9';
    };

    // Ação do clique
    settingsGear.onclick = function() {
        console.log('Botão de engrenagem clicado');
        
        // Verificar se estamos em um side panel (contexto da extensão)
        if (window.chrome && window.chrome.runtime) {
            try {
                // Tentar usar a API do side panel via background script
                chrome.runtime.sendMessage({
                    type: 'REDIRECT_SIDEPANEL',
                    url: chrome.runtime.getURL('iframe-settings.html')
                }, function(response) {
                    if (chrome.runtime.lastError) {
                        console.error('Erro no runtime:', chrome.runtime.lastError);
                        // Fallback: redirecionamento direto
                        window.location.href = chrome.runtime.getURL('iframe-settings.html');
                    } else if (response && response.status === 'redirected') {
                        console.log('Voltou para configurações via background');
                    } else {
                        console.log('Tentando redirecionamento direto...');
                        window.location.href = chrome.runtime.getURL('iframe-settings.html');
                    }
                });
            } catch (error) {
                console.error('Erro ao enviar mensagem:', error);
                // Fallback final
                window.location.href = chrome.runtime.getURL('iframe-settings.html');
            }
        } else {
            console.log('Chrome runtime não disponível, tentando redirecionamento direto');
            // Se não temos acesso ao runtime, tentar redirecionamento direto
            window.location.href = '../iframe-settings.html';
        }
    };

    // Adicionar tooltip
    settingsGear.title = 'Configurações do WhatsApp Suporte';

    // Adicionar o botão ao DOM
    document.body.appendChild(settingsGear);

    // Função para verificar se FontAwesome está carregado
    function checkFontAwesome() {
        const testElement = document.createElement('i');
        testElement.className = 'fas fa-cog';
        testElement.style.cssText = 'position: absolute; left: -9999px;';
        document.body.appendChild(testElement);
        
        const computedStyle = window.getComputedStyle(testElement, ':before');
        const fontFamily = computedStyle.getPropertyValue('font-family');
        
        document.body.removeChild(testElement);
        
        // Se FontAwesome não estiver carregado, usar texto
        if (!fontFamily.includes('Font Awesome')) {
            settingsGear.innerHTML = '⚙️';
            settingsGear.style.fontSize = '24px';
        }
    }

    // Verificar FontAwesome após um pequeno delay
    setTimeout(checkFontAwesome, 1000);

    console.log('WhatsApp Suporte: Botão de configurações adicionado');
})();