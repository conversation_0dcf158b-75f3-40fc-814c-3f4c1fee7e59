{"folders": [{"path": "."}], "settings": {"git.ignoreLimitWarning": true, "sqltools.connections": [{"askForPassword": false, "database": "promokit", "driver": "MySQL", "mysqlOptions": {"authProtocol": "default"}, "name": "Promokit", "password": "root", "port": 3307, "previewLimit": 50, "server": "localhost", "username": "root"}], "files.exclude": {"**/.history": true}, "tabnine.disable_file_regex": ["**/.history/*"], "editor.hover.sticky": false, "path-intellisense.mappings": {}, "path-intellisense.extensionOnImport": true}}