(this["webpackJsonprd-station-crm-whatsapp-web-extension"] = this["webpackJsonprd-station-crm-whatsapp-web-extension"] || []).push([[0], {
    121: function (e, t, a) {
    }, 123: function (e, t, a) {
    }, 124: function (e, t, a) {
    }, 125: function (e, t, a) {
    }, 126: function (e, t, a) {
    }, 127: function (e, t, a) {
    }, 128: function (e, t, a) {
    }, 129: function (e, t, a) {
    }, 130: function (e, t, a) {
    }, 131: function (e, t, a) {
    }, 132: function (e, t, a) {
    }, 133: function (e, t, a) {
    }, 134: function (e, t, a) {
    }, 136: function (e, t, a) {
    }, 194: function (e, t, a) {
    }, 195: function (e, t, a) {
    }, 196: function (e, t, a) {
    }, 197: function (e, t, a) {
    }, 198: function (e, t, a) {
    }, 199: function (e, t, a) {
    }, 200: function (e, t, a) {
    }, 201: function (e, t, a) {
    }, 202: function (e, t, a) {
    }, 203: function (e, t, a) {
    }, 204: function (e, t, a) {
        "use strict";
        a.r(t);
        var n = a(0), r = a.n(n), o = a(36), c = a(8), s = (a(103), null), l = null, i = null, u = null, d = {}, h = {
                init: function () {
                    return new Promise((function (e) {
                        chrome.storage.local.get(["auth", "user", "contacts", "agreementsStatus"], (function (t) {
                            return s = t.auth ? t.auth : null, l = t.user ? t.user : null, i = t.agreementsStatus ? t.agreementsStatus : null, d = t.contacts ? t.contacts : {}, e()
                        }))
                    }))
                }, saveAuthenticationInfo: function (e) {
                    return s = e, chrome.storage.local.set({auth: e}, (function () {
                        console.log("Storage: authentication data was saved")
                    })), s
                }, getAuthenticationInfo: function () {
                    return s
                }, saveUserInfo: function (e) {
                    return l = e, chrome.storage.local.set({user: e}, (function () {
                        console.log("Storage: user data was saved")
                    })), l
                }, getUserInfo: function () {
                    return l
                }, saveAgreementsStatus: function (e) {
                    return i = e, chrome.storage.local.set({agreementsStatus: !0}, (function () {
                        console.log("Storage: agreements status was saved")
                    })), i
                }, getAgreementsStatus: function () {
                    return i
                }, setActiveInstance: function (e) {
                    u = e
                }, getActiveInstance: function () {
                    return u
                }, cleanStorage: function () {
                    s = null, l = null, u = null, chrome.storage.local.set({
                        auth: null, user: null, function: function () {
                            console.log("Storage: Data was cleaned")
                        }
                    })
                }, getRelatedContact: function (e) {
                    return d[l.id] && d[l.id][u.id] && d[l.id][u.id][e] ? d[l.id][u.id][e] : null
                }, removeRelatedContact: function (e) {
                    d[l.id] && d[l.id][u.id] && d[l.id][u.id][e] && (delete d[l.id][u.id][e], chrome.storage.local.set({contacts: d}, (function () {
                        console.log("Storage: contacts data was saved")
                    })))
                }, saveRelatedContact: function (e, t) {
                    d[l.id] || (d[l.id] = {}), d[l.id][u.id] || (d[l.id][u.id] = {}), d[l.id][u.id][e] = t, chrome.storage.local.set({contacts: d}, (function () {
                        console.log("Storage: contacts data was saved")
                    }))
                }
            }, m = {
                executeScript: function (e) {
                    return new Promise((function (t) {
                        chrome.tabs.getCurrent((function (a) {
                            chrome.tabs.executeScript(a.id, {code: e}, (function (e) {
                                t(e[0])
                            }))
                        }))
                    }))
                }, getCurrentChatObject: function () {
                    var e = this;
                    return new Promise((function (t, a) {
                        e.executeScript("retrieveChatObject()").then((function (e) {
                            !e || e && e.id.indexOf("@g.") >= 0 ? a() : t(e)
                        }))
                    }))
                }, getCurrentChatId: function () {
                    var e = this;
                    return new Promise((function (t) {
                        e.getCurrentChatObject().then((function (e) {
                            t(e.id)
                        }), (function () {
                            t(null)
                        }))
                    }))
                }, getCurrentChatPhoneNumber: function () {
                    var e = this;
                    return new Promise((function (t) {
                        e.getCurrentChatId().then((function (e) {
                            if (e) {
                                var a = "+" + e.split("@")[0];
                                t(a)
                            } else t(null)
                        }), (function () {
                            t(null)
                        }))
                    }))
                }, getCurrentChatName: function () {
                    var e = this;
                    return new Promise((function (t) {
                        e.getCurrentChatObject().then((function (e) {
                            t(e.name)
                        }), (function () {
                            t(null)
                        }))
                    }))
                }, getCurrentChatMessages: function () {
                    var e = this;
                    return new Promise((function (t) {
                        e.getCurrentChatObject().then((function (e) {
                            t(e.msgs)
                        }), (function () {
                            t(null)
                        }))
                    }))
                }, writeInChat: function (e) {
                    var t = this, a = function (e) {
                        var t = document.querySelector("#main footer [contenteditable~=true]");
                        setTimeout((function () {
                            t.innerHTML = e, t.dispatchEvent(new Event("input", {bubbles: !0}))
                        }), 1)
                    };
                    return new Promise((function (n) {
                        t.executeScript("(".concat(a, ")('").concat(e.replace("'", '"'), "')")).then((function () {
                            n()
                        }))
                    }))
                }
            }, E = a(19), p = Object(E.b)(), C = a(33), S = a(88),
            A = {SUCCESS: "ALERT_SUCCESS", ERROR: "ALERT_ERROR", CLEAR: "ALERT_CLEAR"}, v = {
                LOGIN_REQUEST: "USER_LOGIN_REQUEST",
                LOGIN_SUCCESS: "USER_LOGIN_SUCCESS",
                LOGIN_FAILURE: "USER_LOGIN_FAILURE",
                REFRESH_TOKEN_REQUEST: "USER_REFRESH_TOKEN_REQUEST",
                REFRESH_TOKEN_SUCCESS: "USER_REFRESH_TOKEN_SUCCESS",
                REFRESH_TOKEN_FAILURE: "USER_REFRESH_TOKEN_FAILURE",
                LOGOUT_REQUEST: "USER_LOGOUT_REQUEST",
                LOGOUT_SUCCESS: "USER_LOGOUT_SUCCESS",
                LOGOUT_FAILURE: "USER_LOGOUT_FAILURE",
                GET_INSTANCES_REQUEST: "USER_GET_INSTANCES_REQUEST",
                GET_INSTANCES_SUCCESS: "USER_GET_INSTANCES_SUCCESS",
                GET_INSTANCES_FAILURE: "USER_GET_INSTANCES_FAILURE",
                SET_INSTANCE_REQUEST: "USER_SET_INSTANCE_REQUEST",
                SET_INSTANCE_SUCCESS: "USER_SET_INSTANCE_SUCCESS",
                SET_INSTANCE_FAILURE: "USER_SET_INSTANCE_FAILURE"
            }, f = {
                CREATE_CONTACT_REQUEST: "CONTACT_CREATE_CONTACT_REQUEST",
                CREATE_CONTACT_SUCCESS: "CONTACT_CREATE_CONTACT_SUCCESS",
                CREATE_CONTACT_FAILURE: "CONTACT_CREATE_CONTACT_FAILURE",
                SEARCH_CONTACTS_REQUEST: "CONTACT_SEARCH_CONTACTS_REQUEST",
                SEARCH_CONTACTS_SUCCESS: "CONTACT_SEARCH_CONTACTS_SUCCESS",
                SEARCH_CONTACTS_FAILURE: "CONTACT_SEARCH_CONTACTS_FAILURE",
                SEARCH_CONTACT_BY_PHONE_NUMBER_REQUEST: "CONTACT_SEARCH_CONTACT_BY_PHONE_NUMBER_REQUEST",
                SEARCH_CONTACT_BY_PHONE_NUMBER_SUCCESS: "CONTACT_SEARCH_CONTACT_BY_PHONE_NUMBER_SUCCESS",
                SEARCH_CONTACT_BY_PHONE_NUMBER_FAILURE: "CONTACT_SEARCH_CONTACT_BY_PHONE_NUMBER_FAILURE",
                ADD_PHONE_NUMBER_TO_CONTACT_REQUEST: "CONTACT_ADD_PHONE_NUMBER_TO_CONTACT_REQUEST",
                ADD_PHONE_NUMBER_TO_CONTACT_SUCCESS: "CONTACT_ADD_PHONE_NUMBER_TO_CONTACT_SUCCESS",
                ADD_PHONE_NUMBER_TO_CONTACT_FAILURE: "CONTACT_ADD_PHONE_NUMBER_TO_CONTACT_FAILURE",
                GET_CONTACT_REQUEST: "CONTACT_GET_CONTACT_REQUEST",
                GET_CONTACT_SUCCESS: "CONTACT_GET_CONTACT_SUCCESS",
                GET_CONTACT_FAILURE: "CONTACT_GET_CONTACT_FAILURE",
                CONNECT_CONTACT_SUCCESS: "CONTACT_CONNECT_CONTACT_SUCCESS",
                CONNECT_CONTACT_FAILURE: "CONTACT_CONNECT_CONTACT_FAILURE"
            }, g = {
                CREATE_ORGANIZATION_REQUEST: "ORGANIZATION_CREATE_ORGANIZATION_REQUEST",
                CREATE_ORGANIZATION_SUCCESS: "ORGANIZATION_CREATE_ORGANIZATION_SUCCESS",
                CREATE_ORGANIZATION_FAILURE: "ORGANIZATION_CREATE_ORGANIZATION_FAILURE",
                SEARCH_ORGANIZATIONS_REQUEST: "ORGANIZATION_SEARCH_ORGANIZATIONS_REQUEST",
                SEARCH_ORGANIZATIONS_SUCCESS: "ORGANIZATION_SEARCH_ORGANIZATIONS_SUCCESS",
                SEARCH_ORGANIZATIONS_FAILURE: "ORGANIZATION_SEARCH_ORGANIZATIONS_FAILURE"
            }, b = {
                CREATE_DEAL_REQUEST: "DEAL_CREATE_DEAL_REQUEST",
                CREATE_DEAL_SUCCESS: "DEAL_CREATE_DEAL_SUCCESS",
                CREATE_DEAL_FAILURE: "DEAL_CREATE_DEAL_FAILURE",
                LIST_DEALS_REQUEST: "DEAL_LIST_DEALS_REQUEST",
                LIST_DEALS_SUCCESS: "DEAL_LIST_DEALS_SUCCESS",
                LIST_DEALS_FAILURE: "DEAL_LIST_DEALS_FAILURE",
                GET_DEAL_REQUEST: "DEAL_GET_DEAL_REQUEST",
                GET_DEAL_SUCCESS: "DEAL_GET_DEAL_SUCCESS",
                GET_DEAL_FAILURE: "DEAL_GET_DEAL_FAILURE",
                UPDATE_DEAL_REQUEST: "DEAL_UPDATE_DEAL_REQUEST",
                UPDATE_DEAL_SUCCESS: "DEAL_UPDATE_DEAL_SUCCESS",
                UPDATE_DEAL_FAILURE: "DEAL_UPDATE_DEAL_FAILURE",
                GET_DEAL_STAGES_REQUEST: "DEAL_GET_DEAL_STAGES_REQUEST",
                GET_DEAL_STAGES_SUCCESS: "DEAL_GET_DEAL_STAGES_SUCCESS",
                GET_DEAL_STAGES_FAILURE: "DEAL_GET_DEAL_STAGES_FAILURE",
                GET_DEAL_LOST_REASONS_REQUEST: "DEAL_GET_DEAL_LOST_REASONS_REQUEST",
                GET_DEAL_LOST_REASONS_SUCCESS: "DEAL_GET_DEAL_LOST_REASONS_SUCCESS",
                GET_DEAL_LOST_REASONS_FAILURE: "DEAL_GET_DEAL_LOST_REASONS_FAILURE",
                GET_DEAL_PRODUCTS_REQUEST: "DEAL_GET_PRODUCTS_REQUEST",
                GET_DEAL_PRODUCTS_SUCCESS: "DEAL_GET_PRODUCTS_SUCCESS",
                GET_DEAL_PRODUCTS_FAILURE: "DEAL_GET_PRODUCTS_FAILURE",
                CREATE_DEAL_PRODUCT_REQUEST: "DEAL_CREATE_PRODUCT_REQUEST",
                CREATE_DEAL_PRODUCT_SUCCESS: "DEAL_CREATE_PRODUCT_SUCCESS",
                CREATE_DEAL_PRODUCT_FAILURE: "DEAL_CREATE_PRODUCT_FAILURE",
                UPDATE_DEAL_PRODUCT_REQUEST: "DEAL_UPDATE_PRODUCT_REQUEST",
                UPDATE_DEAL_PRODUCT_SUCCESS: "DEAL_UPDATE_PRODUCT_SUCCESS",
                UPDATE_DEAL_PRODUCT_FAILURE: "DEAL_UPDATE_PRODUCT_FAILURE",
                GET_DEAL_TASKS_REQUEST: "DEAL_GET_TASKS_REQUEST",
                GET_DEAL_TASKS_SUCCESS: "DEAL_GET_TASKS_SUCCESS",
                GET_DEAL_TASKS_FAILURE: "DEAL_GET_TASKS_FAILURE",
                CREATE_DEAL_TASK_REQUEST: "DEAL_CREATE_TASK_REQUEST",
                CREATE_DEAL_TASK_SUCCESS: "DEAL_CREATE_TASK_SUCCESS",
                CREATE_DEAL_TASK_FAILURE: "DEAL_CREATE_TASK_FAILURE",
                UPDATE_DEAL_TASK_REQUEST: "DEAL_UPDATE_TASK_REQUEST",
                UPDATE_DEAL_TASK_SUCCESS: "DEAL_UPDATE_TASK_SUCCESS",
                UPDATE_DEAL_TASK_FAILURE: "DEAL_UPDATE_TASK_FAILURE",
                CREATE_DEAL_NOTE_REQUEST: "DEAL_CREATE_NOTE_REQUEST",
                CREATE_DEAL_NOTE_SUCCESS: "DEAL_CREATE_NOTE_SUCCESS",
                CREATE_DEAL_NOTE_FAILURE: "DEAL_CREATE_NOTE_FAILURE"
            }, _ = {
                LIST_PRODUCTS_REQUEST: "DEAL_LIST_PRODUCTS_REQUEST",
                LIST_PRODUCTS_SUCCESS: "DEAL_LIST_PRODUCTS_SUCCESS",
                LIST_PRODUCTS_FAILURE: "DEAL_LIST_PRODUCTS_FAILURE"
            };
        var T = Object(C.c)({
            user: function () {
                var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                switch ((arguments.length > 1 ? arguments[1] : void 0).type) {
                    case v.LOGIN_REQUEST:
                        return {loggingIn: !0};
                    case v.LOGIN_SUCCESS:
                        return {};
                    case v.LOGIN_FAILURE:
                        return {logginError: !0};
                    case v.REFRESH_TOKEN_REQUEST:
                    case v.REFRESH_TOKEN_SUCCESS:
                    case v.REFRESH_TOKEN_FAILURE:
                        return {};
                    case v.LOGOUT_REQUEST:
                        return {loggingOut: !0};
                    case v.LOGOUT_SUCCESS:
                    case v.LOGOUT_FAILURE:
                        return {};
                    default:
                        return e
                }
            }, userInstances: function () {
                var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                    t = arguments.length > 1 ? arguments[1] : void 0;
                switch (t.type) {
                    case v.GET_INSTANCES_REQUEST:
                        return {loading: !0};
                    case v.GET_INSTANCES_SUCCESS:
                        return {instances: t.instances};
                    case v.GET_INSTANCES_FAILURE:
                        return {error: t.error};
                    case v.SET_INSTANCE_REQUEST:
                        return {loading: !0};
                    case v.SET_INSTANCE_SUCCESS:
                        return {user: t.user};
                    case v.SET_INSTANCE_FAILURE:
                        return {error: t.error};
                    default:
                        return e
                }
            }, contact: function () {
                var e, t, a, n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                    r = arguments.length > 1 ? arguments[1] : void 0;
                switch (r.type) {
                    case f.CREATE_CONTACT_REQUEST:
                        return {saving: !0};
                    case f.CREATE_CONTACT_SUCCESS:
                        return {contact: r.contact};
                    case f.CREATE_CONTACT_FAILURE:
                        return {};
                    case f.SEARCH_CONTACTS_REQUEST:
                        return {searching: !0};
                    case f.SEARCH_CONTACTS_SUCCESS:
                        return {searching: !1, contacts: r.contacts};
                    case f.SEARCH_CONTACTS_FAILURE:
                        return {searching: !1};
                    case f.SEARCH_CONTACT_BY_PHONE_NUMBER_REQUEST:
                        return {searchingPhoneNumber: !0};
                    case f.SEARCH_CONTACT_BY_PHONE_NUMBER_SUCCESS:
                        return {searchingPhoneNumber: !1, contact: r.contact, contactNotFound: null === r.contact};
                    case f.SEARCH_CONTACT_BY_PHONE_NUMBER_FAILURE:
                        return {searchingPhoneNumber: !1};
                    case f.ADD_PHONE_NUMBER_TO_CONTACT_REQUEST:
                        return {saving: !0};
                    case f.ADD_PHONE_NUMBER_TO_CONTACT_SUCCESS:
                        return {contact: r.contact};
                    case f.ADD_PHONE_NUMBER_TO_CONTACT_FAILURE:
                        return {};
                    case f.GET_CONTACT_REQUEST:
                        return {getting: !0};
                    case f.GET_CONTACT_SUCCESS:
                        return {getting: !1, contact: r.contact};
                    case f.GET_CONTACT_FAILURE:
                        return {
                            getting: !1,
                            contactNotFound: null !== (e = 404 === (null === (t = r.error) || void 0 === t ? void 0 : null === (a = t.response) || void 0 === a ? void 0 : a.status)) && void 0 !== e && e
                        };
                    case f.CONNECT_CONTACT_SUCCESS:
                    case f.CONNECT_CONTACT_FAILURE:
                        return {};
                    default:
                        return n
                }
            }, organization: function () {
                var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                    t = arguments.length > 1 ? arguments[1] : void 0;
                switch (t.type) {
                    case g.CREATE_ORGANIZATION_REQUEST:
                        return {saving: !0};
                    case g.CREATE_ORGANIZATION_SUCCESS:
                        return {organization: t.organization};
                    case g.CREATE_ORGANIZATION_FAILURE:
                        return {};
                    case g.SEARCH_ORGANIZATIONS_REQUEST:
                        return {searching: !0};
                    case g.SEARCH_ORGANIZATIONS_SUCCESS:
                        return {organizations: t.organizations, searching: !1};
                    case g.SEARCH_ORGANIZATIONS_FAILURE:
                        return {searching: !1};
                    default:
                        return e
                }
            }, deal: function () {
                var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                    t = arguments.length > 1 ? arguments[1] : void 0;
                switch (t.type) {
                    case b.CREATE_DEAL_REQUEST:
                        return {saving: !0};
                    case b.CREATE_DEAL_SUCCESS:
                        return {deal: t.deal};
                    case b.CREATE_DEAL_FAILURE:
                        return {};
                    case b.LIST_DEALS_REQUEST:
                        return {listing: !0};
                    case b.LIST_DEALS_SUCCESS:
                        return {listing: !1, deals: t.deals};
                    case b.LIST_DEALS_FAILURE:
                        return {listing: !1};
                    case b.GET_DEAL_REQUEST:
                        return {};
                    case b.GET_DEAL_SUCCESS:
                        return {deal: t.deal};
                    case b.GET_DEAL_FAILURE:
                        return {};
                    case b.UPDATE_DEAL_REQUEST:
                        return {saving: !0};
                    case b.UPDATE_DEAL_SUCCESS:
                        return {deal: t.deal};
                    case b.UPDATE_DEAL_FAILURE:
                        return {};
                    case b.GET_DEAL_STAGES_REQUEST:
                        return {listingStages: !0};
                    case b.GET_DEAL_STAGES_SUCCESS:
                        return {listingStages: !1, dealStages: t.dealStages};
                    case b.GET_DEAL_STAGES_FAILURE:
                        return {listingStages: !1};
                    case b.GET_DEAL_LOST_REASONS_REQUEST:
                        return {listingLostReasons: !0};
                    case b.GET_DEAL_LOST_REASONS_SUCCESS:
                        return {listingLostReasons: !1, dealLostReasons: t.dealLostReasons};
                    case b.GET_DEAL_LOST_REASONS_FAILURE:
                        return {listingLostReasons: !1};
                    case b.GET_DEAL_PRODUCTS_REQUEST:
                        return {listingProducts: !0};
                    case b.GET_DEAL_PRODUCTS_SUCCESS:
                        return {listingProducts: !1, dealProducts: t.dealProducts};
                    case b.GET_DEAL_PRODUCTS_FAILURE:
                        return {listingProducts: !1};
                    case b.CREATE_DEAL_PRODUCT_REQUEST:
                        return {saving: !0};
                    case b.CREATE_DEAL_PRODUCT_SUCCESS:
                        return {dealProduct: t.dealProduct};
                    case b.CREATE_DEAL_PRODUCT_FAILURE:
                        return {};
                    case b.UPDATE_DEAL_PRODUCT_REQUEST:
                        return {saving: !0};
                    case b.UPDATE_DEAL_PRODUCT_SUCCESS:
                        return {dealProduct: t.dealProduct};
                    case b.UPDATE_DEAL_PRODUCT_FAILURE:
                        return {};
                    case b.GET_DEAL_TASKS_REQUEST:
                        return {listingTasks: !0};
                    case b.GET_DEAL_TASKS_SUCCESS:
                        return {listingTasks: !1, dealTasks: t.dealTasks};
                    case b.GET_DEAL_TASKS_FAILURE:
                        return {listingTasks: !1};
                    case b.CREATE_DEAL_TASK_REQUEST:
                        return {saving: !0};
                    case b.CREATE_DEAL_TASK_SUCCESS:
                        return {dealTask: t.dealTask};
                    case b.CREATE_DEAL_TASK_FAILURE:
                        return {};
                    case b.UPDATE_DEAL_TASK_REQUEST:
                        return {saving: !0};
                    case b.UPDATE_DEAL_TASK_SUCCESS:
                        return {dealTask: t.dealTask};
                    case b.UPDATE_DEAL_TASK_FAILURE:
                        return {};
                    case b.CREATE_DEAL_NOTE_REQUEST:
                        return {savingNote: !0};
                    case b.CREATE_DEAL_NOTE_SUCCESS:
                        return {dealNote: t.dealNote};
                    case b.CREATE_DEAL_NOTE_FAILURE:
                        return {};
                    default:
                        return e
                }
            }, product: function () {
                var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                    t = arguments.length > 1 ? arguments[1] : void 0;
                switch (t.type) {
                    case _.LIST_PRODUCTS_REQUEST:
                        return {listing: !0};
                    case _.LIST_PRODUCTS_SUCCESS:
                        return {listing: !1, products: t.products};
                    case _.LIST_PRODUCTS_FAILURE:
                        return {listing: !1};
                    default:
                        return e
                }
            }, alert: function () {
                var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                    t = arguments.length > 1 ? arguments[1] : void 0;
                switch (t.type) {
                    case A.SUCCESS:
                        return {type: "alert-success", message: t.message};
                    case A.ERROR:
                        return {type: "alert-danger", message: t.message};
                    case A.CLEAR:
                        return {};
                    default:
                        return e
                }
            }
        }), D = Object(C.d)(T, Object(C.a)(S.a));

        function N() {
            var e = h.getAuthenticationInfo();
            return e && e.access_token ? {Authorization: "Bearer " + e.access_token} : {}
        }

        function O(e) {
            return "R$" + parseFloat(e).toFixed(2)
        }

        var R = a(5), U = a(6), k = a(9), L = a(7), y = a(3), I = a(10), w = a(68), j = a(31), P = a(93), F = a(15),
            G = a.n(F), Q = a(39), M = a.n(Q), H = {
                success: function (e) {
                    return {type: A.SUCCESS, message: e}
                }, error: function (e) {
                    return {type: A.ERROR, message: e}
                }, clear: function () {
                    return {type: A.CLEAR}
                }
            };
        var B = a(11), z = a.n(B), K = "https://plugcrm.net", x = "".concat(K, "/oauth/token"),
            W = "".concat(K, "/api/v3"), Y = ("".concat(W, "/auth/select_instance"), "password"), Z = "refresh_token",
            J = ["81", "6f1", "5db", "482", "34d", "46", "1024", "44", "37e", "9b7", "1d9", "00c", "d6", "8c", "4c", "a2b", "9f", "77", "e79", "6f5", "b39", "a46", "f9", "74", "415"],
            q = J[14] + J[17] + J[7] + J[5] + J[0] + J[15] + J[1] + J[10] + J[8] + J[18] + J[24] + J[3] + J[23] + J[16] + J[11] + J[2] + J[19] + J[4] + J[17] + J[13] + J[21] + J[20] + J[12] + J[9] + J[22],
            V = ["0f8", "c27", "6d", "54", "1b", "fc", "16a", "8e", "8c1", "a26", "1ac", "b7", "42b", "e6", "cd6", "ed0", "253", "math", "a3b", "98", "f4", "c76", "c0", "45", "c31", "67"],
            X = V[10] + V[15] + V[12] + V[0] + V[1] + V[19] + V[23] + V[21] + V[13] + V[3] + V[6] + V[14] + V[5] + V[22] + V[4] + V[11] + V[16] + V[2] + V[25] + V[20] + V[24] + V[7] + V[18] + V[9] + V[8] + "2",
            $ = [{name: "Liga\xe7\xe3o", id: "call"}, {name: "Email", id: "email"}, {
                name: "Almo\xe7o",
                id: "lunch"
            }, {name: "Reuni\xe3o", id: "meeting"}, {name: "Tarefa", id: "task"}, {name: "Visita", id: "visit"}], ee = {
                login: function (e, t) {
                    var a = {"Content-Type": "application/x-www-form-urlencoded"},
                        n = {grant_type: Y, client_id: q, client_secret: X, username: e, password: t};
                    return new Promise((function (e, t) {
                        z.a.post("".concat(x), n, a).then((function (t) {
                            return h.saveAuthenticationInfo(t.data), e(t.data)
                        })).catch((function (e) {
                            var a, n,
                                r = null === e || void 0 === e ? void 0 : null === (a = e.response) || void 0 === a ? void 0 : null === (n = a.data) || void 0 === n ? void 0 : n.error_description;
                            return t(null !== r && void 0 !== r ? r : e)
                        }))
                    }))
                }, refreshToken: function () {
                    var e = {"Content-Type": "application/x-www-form-urlencoded"},
                        t = {grant_type: Z, refresh_token: h.getAuthenticationInfo().refresh_token};
                    return new Promise((function (a, n) {
                        z.a.post("".concat(x), t, e).then((function (e) {
                            return h.saveAuthenticationInfo(e.data), a(e.data)
                        })).catch((function (e) {
                            var t, a,
                                r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (a = t.data) || void 0 === a ? void 0 : a.error_description;
                            return n(null !== r && void 0 !== r ? r : e)
                        }))
                    }))
                }, logout: function () {
                    return new Promise((function (e, t) {
                        z.a.get("".concat(W, "/auth/logout"), {headers: N()}).then((function (t) {
                            return h.cleanStorage(), e(t.data)
                        })).catch((function (e) {
                            var a, n,
                                r = null === e || void 0 === e ? void 0 : null === (a = e.response) || void 0 === a ? void 0 : null === (n = a.data) || void 0 === n ? void 0 : n.error_description;
                            return t(null !== r && void 0 !== r ? r : e)
                        }))
                    }))
                }, getInstances: function () {
                    return new Promise((function (e, t) {
                        z.a.get("".concat(W, "/auth/select_instance"), {headers: N()}).then((function (t) {
                            return e(t.data)
                        })).catch((function (e) {
                            var a, n,
                                r = null === e || void 0 === e ? void 0 : null === (a = e.response) || void 0 === a ? void 0 : null === (n = a.data) || void 0 === n ? void 0 : n.error;
                            return t(null !== r && void 0 !== r ? r : e)
                        }))
                    }))
                }, selectInstance: function (e) {
                    var t = {id: e.id};
                    return new Promise((function (a, n) {
                        z.a.post("".concat(W, "/auth/select_instance"), t, {headers: N()}).then((function (t) {
                            z.a.get("".concat(W, "/users/profile"), {headers: N()}).then((function (t) {
                                return h.saveUserInfo(t.data.user), h.setActiveInstance(e), a(t.data.user)
                            })).catch((function (e) {
                                var t, a,
                                    r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (a = t.data) || void 0 === a ? void 0 : a.error_description;
                                return n(null !== r && void 0 !== r ? r : e)
                            }))
                        })).catch((function (e) {
                            var t, a,
                                r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (a = t.data) || void 0 === a ? void 0 : a.error_description;
                            return n(null !== r && void 0 !== r ? r : e)
                        }))
                    }))
                }
            };
        var te = a(44), ae = {
            create: function (e, t, a) {
                var n = {contact: {name: e, emails: [], phones: [{phone: t, type: "work"}], organization_id: a}};
                return new Promise((function (e, t) {
                    z.a.post("".concat(W, "/contacts"), n, {headers: N()}).then((function (t) {
                        return e(t.data)
                    })).catch((function (e) {
                        var a, n,
                            r = null === e || void 0 === e ? void 0 : null === (a = e.response) || void 0 === a ? void 0 : null === (n = a.data) || void 0 === n ? void 0 : n.error_description;
                        return t(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, search: function (e) {
                var t = {name: e};
                return new Promise((function (e, a) {
                    z.a.post("".concat(W, "/contacts/list"), t, {headers: N()}).then((function (t) {
                        return e(t.data)
                    })).catch((function (e) {
                        var t, n,
                            r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (n = t.data) || void 0 === n ? void 0 : n.error_description;
                        return a(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, searchByPhoneNumber: function (e) {
                var t = {
                    filters: [{
                        nested: {
                            path: "phones",
                            query: {
                                bool: {
                                    should: {regexp: {"phones.phone": ".*".concat(e, ".*")}},
                                    minimum_should_match: 1
                                }
                            }
                        }
                    }]
                };
                return new Promise((function (e, a) {
                    z.a.post("".concat(W, "/contacts/list"), t, {headers: N()}).then((function (t) {
                        return e(t.data)
                    })).catch((function (e) {
                        var t, n,
                            r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (n = t.data) || void 0 === n ? void 0 : n.error_description;
                        return a(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, addPhoneNumberToContact: function (e, t) {
                var a = e.phones, n = e.emails;
                a = a.length > 0 ? a : [{}], n = n.length > 0 ? n : [{}], a.push({phone: t, type: "work"});
                var r = Object(te.a)({}, e, {phones: a, emails: n});
                return new Promise((function (t, a) {
                    z.a.put("".concat(W, "/contacts/").concat(e._id), r, {headers: N()}).then((function (e) {
                        return t(e.data)
                    })).catch((function (e) {
                        var t, n,
                            r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (n = t.data) || void 0 === n ? void 0 : n.error_description;
                        return a(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, get: function (e) {
                return new Promise((function (t, a) {
                    z.a.get("".concat(W, "/contacts/").concat(e), {headers: N()}).then((function (e) {
                        return t(e.data)
                    })).catch((function (e) {
                        var t, n,
                            r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (n = t.data) || void 0 === n ? void 0 : n.error_description;
                        return a(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }
        };
        var ne = {
            create: function (e) {
                var t = {organization: {name: e}};
                return new Promise((function (e, a) {
                    z.a.post("".concat(W, "/organizations"), t, {headers: N()}).then((function (t) {
                        return e(t.data)
                    })).catch((function (e) {
                        var t, n,
                            r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (n = t.data) || void 0 === n ? void 0 : n.error_description;
                        return a(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, search: function (e) {
                var t = {name: e};
                return new Promise((function (e, a) {
                    z.a.post("".concat(W, "/organizations/list"), t, {headers: N()}).then((function (t) {
                        return e(t.data)
                    })).catch((function (e) {
                        var t, n,
                            r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (n = t.data) || void 0 === n ? void 0 : n.error_description;
                        return a(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }
        };
        var re = {
            create: function (e, t, a) {
                var n = {name: e, deal: {name: e}, organization: {_id: a}, set_contacts: [{_id: t}]};
                return new Promise((function (e, t) {
                    z.a.post("".concat(W, "/deals"), n, {headers: N()}).then((function (t) {
                        return e(t.data)
                    })).catch((function (e) {
                        var a, n,
                            r = null === e || void 0 === e ? void 0 : null === (a = e.response) || void 0 === a ? void 0 : null === (n = a.data) || void 0 === n ? void 0 : n.error_description;
                        return t(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, list: function (e) {
                return new Promise((function (t, a) {
                    z.a.get("".concat(W, "/deals?organization_id=").concat(e), {headers: N()}).then((function (e) {
                        return t(e.data)
                    })).catch((function (e) {
                        var t, n,
                            r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (n = t.data) || void 0 === n ? void 0 : n.error_description;
                        return a(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, get: function (e) {
                return new Promise((function (t, a) {
                    z.a.get("".concat(W, "/deals/").concat(e), {headers: N()}).then((function (e) {
                        return t(e.data)
                    })).catch((function (e) {
                        var t, n,
                            r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (n = t.data) || void 0 === n ? void 0 : n.error_description;
                        return a(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, update: function (e) {
                return new Promise((function (t, a) {
                    z.a.put("".concat(W, "/deals/").concat(e._id), e, {headers: N()}).then((function (e) {
                        return t(e.data)
                    })).catch((function (e) {
                        var t, n,
                            r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (n = t.data) || void 0 === n ? void 0 : n.error_description;
                        return a(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, getDealStages: function () {
                return new Promise((function (e, t) {
                    z.a.get("".concat(W, "/deal_stages/all"), {headers: N()}).then((function (t) {
                        return e(t.data)
                    })).catch((function (e) {
                        var a, n,
                            r = null === e || void 0 === e ? void 0 : null === (a = e.response) || void 0 === a ? void 0 : null === (n = a.data) || void 0 === n ? void 0 : n.error_description;
                        return t(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, getDealLostReasons: function () {
                return new Promise((function (e, t) {
                    z.a.get("".concat(W, "/deal_lost_reasons"), {headers: N()}).then((function (t) {
                        return e(t.data)
                    })).catch((function (e) {
                        var a, n,
                            r = null === e || void 0 === e ? void 0 : null === (a = e.response) || void 0 === a ? void 0 : null === (n = a.data) || void 0 === n ? void 0 : n.error_description;
                        return t(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, getDealProducts: function (e) {
                return new Promise((function (t, a) {
                    z.a.get("".concat(W, "/deals/").concat(e, "/deal_products"), {headers: N()}).then((function (e) {
                        return t(e.data)
                    })).catch((function (e) {
                        var t, n,
                            r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (n = t.data) || void 0 === n ? void 0 : n.error_description;
                        return a(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, createDealProduct: function (e, t) {
                var a = {deal_product: t};
                return new Promise((function (t, n) {
                    z.a.post("".concat(W, "/deals/").concat(e, "/deal_products"), a, {headers: N()}).then((function (e) {
                        return t(e.data)
                    })).catch((function (e) {
                        var t, a,
                            r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (a = t.data) || void 0 === a ? void 0 : a.error_description;
                        return n(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, updateDealProduct: function (e, t) {
                var a = {deal_product: t};
                return new Promise((function (n, r) {
                    z.a.put("".concat(W, "/deals/").concat(e, "/deal_products/").concat(t._id), a, {headers: N()}).then((function (e) {
                        return n(e.data)
                    })).catch((function (e) {
                        var t, a,
                            n = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (a = t.data) || void 0 === a ? void 0 : a.error_description;
                        return r(null !== n && void 0 !== n ? n : e)
                    }))
                }))
            }, getDealTasks: function (e) {
                return new Promise((function (t, a) {
                    z.a.get("".concat(W, "/deals/").concat(e, "/tasks"), {headers: N()}).then((function (e) {
                        return t(e.data)
                    })).catch((function (e) {
                        var t, n,
                            r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (n = t.data) || void 0 === n ? void 0 : n.error_description;
                        return a(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, createDealTask: function (e) {
                var t = {task: e};
                return new Promise((function (e, a) {
                    z.a.post("".concat(W, "/tasks"), t, {headers: N()}).then((function (t) {
                        return e(t.data)
                    })).catch((function (e) {
                        var t, n,
                            r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (n = t.data) || void 0 === n ? void 0 : n.error_description;
                        return a(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, updateDealTask: function (e) {
                var t = e;
                return new Promise((function (a, n) {
                    z.a.put("".concat(W, "/tasks/").concat(e._id), t, {headers: N()}).then((function (e) {
                        return a(e.data)
                    })).catch((function (e) {
                        var t, a,
                            r = null === e || void 0 === e ? void 0 : null === (t = e.response) || void 0 === t ? void 0 : null === (a = t.data) || void 0 === a ? void 0 : a.error_description;
                        return n(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }, createDealNote: function (e, t) {
                var a = {deal_id: e, type: "note", text: t};
                return new Promise((function (e, t) {
                    z.a.post("".concat(W, "/activities"), a, {headers: N()}).then((function (t) {
                        return e(t.data)
                    })).catch((function (e) {
                        var a, n,
                            r = null === e || void 0 === e ? void 0 : null === (a = e.response) || void 0 === a ? void 0 : null === (n = a.data) || void 0 === n ? void 0 : n.error_description;
                        return t(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }
        };
        var oe = {
            list: function () {
                return new Promise((function (e, t) {
                    z.a.get("".concat(W, "/products"), {headers: N()}).then((function (t) {
                        return e(t.data)
                    })).catch((function (e) {
                        var a, n,
                            r = null === e || void 0 === e ? void 0 : null === (a = e.response) || void 0 === a ? void 0 : null === (n = a.data) || void 0 === n ? void 0 : n.error_description;
                        return t(null !== r && void 0 !== r ? r : e)
                    }))
                }))
            }
        };
        var ce = {
            login: function (e, t) {
                return function (a) {
                    a({type: v.LOGIN_REQUEST}), ee.login(e, t).then((function (e) {
                        a(function (e) {
                            return {type: v.LOGIN_SUCCESS, auth: e}
                        }(e)), p.push("/select-instance")
                    }), (function (e) {
                        a(function (e) {
                            return {type: v.LOGIN_FAILURE, error: e}
                        }(e))
                    }))
                }
            }, refreshToken: function () {
                return function (e) {
                    e({type: v.LOGIN_REQUEST}), ee.refreshToken().then((function (t) {
                        e(function (e) {
                            return {type: v.LOGIN_SUCCESS, auth: e}
                        }(t))
                    }), (function (t) {
                        e(function (e) {
                            return {type: v.LOGIN_FAILURE, error: e}
                        }(t)), p.push("/login")
                    }))
                }
            }, logout: function () {
                return function (e) {
                    h.getAuthenticationInfo() && (e({type: v.LOGOUT_REQUEST}), ee.logout().then((function () {
                        e({type: v.LOGOUT_SUCCESS}), p.push("/login")
                    }), (function (t) {
                        e(function (e) {
                            return {type: v.LOGOUT_FAILURE, error: e}
                        }(t)), e(H.error(t + ""))
                    })))
                }
            }, getInstances: function () {
                return function (e) {
                    e({type: v.GET_INSTANCES_REQUEST}), ee.getInstances().then((function (t) {
                        return e(function (e) {
                            return {type: v.GET_INSTANCES_SUCCESS, instances: e}
                        }(t))
                    }), (function (t) {
                        return e(function (e) {
                            return {type: v.GET_INSTANCES_FAILURE, error: e}
                        }(t))
                    }))
                }
            }, selectInstance: function (e) {
                return function (t) {
                    t(function (e) {
                        return {type: v.SET_INSTANCE_REQUEST, instance: e}
                    }({instance: e})), ee.selectInstance(e).then((function (e) {
                        t(function (e) {
                            return {type: v.SET_INSTANCE_SUCCESS, user: e}
                        }(e)), p.push("/home")
                    }), (function (e) {
                        t(function (e) {
                            return {type: v.SET_INSTANCE_FAILURE, error: e}
                        }(e)), t(H.error(e + ""))
                    }))
                }
            }
        };
        var se = {
            create: function (e, t, a) {
                return function (n) {
                    n(function (e, t, a) {
                        return {type: f.CREATE_CONTACT_REQUEST, name: e, phoneNumber: t, organizationId: a}
                    }({name: e, phoneNumber: t, organizationId: a})), ae.create(e, t, a).then((function (e) {
                        n(function (e) {
                            return {type: f.CREATE_CONTACT_SUCCESS, contact: e}
                        }(e))
                    }), (function (e) {
                        n(function (e) {
                            return {type: f.CREATE_CONTACT_FAILURE, error: e}
                        }(e)), n(H.error(e + ""))
                    }))
                }
            }, search: function (e) {
                return function (t) {
                    t(function (e) {
                        return {type: f.SEARCH_CONTACTS_REQUEST, name: e}
                    }({name: e})), ae.search(e).then((function (e) {
                        t(function (e) {
                            return {type: f.SEARCH_CONTACTS_SUCCESS, contacts: e}
                        }(e.contacts))
                    }), (function (e) {
                        t(function (e) {
                            return {type: f.SEARCH_CONTACTS_FAILURE, error: e}
                        }(e)), t(H.error(e + ""))
                    }))
                }
            }, searchByPhoneNumber: function (e) {
                return function (t) {
                    t(function (e) {
                        return {type: f.SEARCH_CONTACT_BY_PHONE_NUMBER_REQUEST, phoneNumber: e}
                    }({phoneNumber: e})), ae.searchByPhoneNumber(e).then((function (e) {
                        var a;
                        t((a = e.contacts.length > 0 ? e.contacts[0] : null, {
                            type: f.SEARCH_CONTACT_BY_PHONE_NUMBER_SUCCESS,
                            contact: a
                        }))
                    }), (function (e) {
                        t(function (e) {
                            return {type: f.SEARCH_CONTACT_BY_PHONE_NUMBER_FAILURE, error: e}
                        }(e)), t(H.error(e + ""))
                    }))
                }
            }, addPhoneNumberToContact: function (e, t) {
                return function (a) {
                    a(function (e, t) {
                        return {type: f.ADD_PHONE_NUMBER_TO_CONTACT_REQUEST, contact: e, phoneNumber: t}
                    }({contact: e, phoneNumber: t})), ae.addPhoneNumberToContact(e, t).then((function (e) {
                        a(function (e) {
                            return {type: f.ADD_PHONE_NUMBER_TO_CONTACT_SUCCESS, contact: e}
                        }(e))
                    }), (function (e) {
                        a(function (e) {
                            return {type: f.ADD_PHONE_NUMBER_TO_CONTACT_FAILURE, error: e}
                        }(e)), a(H.error(e + ""))
                    }))
                }
            }, get: function (e, t) {
                return function (a) {
                    a(function (e) {
                        return {type: f.GET_CONTACT_REQUEST, id: e}
                    }({id: e})), ae.get(e).then((function (e) {
                        a(function (e) {
                            return {type: f.GET_CONTACT_SUCCESS, contact: e}
                        }(e))
                    }), (function (e) {
                        a(function (e) {
                            return {type: f.GET_CONTACT_FAILURE, error: e}
                        }(e)), e.response && 404 === e.response.status ? (h.removeRelatedContact(t), a(H.error("O contato relacionado n\xe3o foi encontrado no RD Station CRM."))) : a(H.error(e + ""))
                    }))
                }
            }, connect: function (e, t) {
                return function (a) {
                    h.saveRelatedContact(e, t), a(function (e) {
                        return {type: f.CONNECT_CONTACT_SUCCESS, contact: e}
                    }(t))
                }
            }
        };
        var le = {
            create: function (e) {
                return function (t) {
                    t(function (e) {
                        return {type: g.CREATE_ORGANIZATION_REQUEST, name: e}
                    }({name: e})), ne.create(e).then((function (e) {
                        t(function (e) {
                            return {type: g.CREATE_ORGANIZATION_SUCCESS, organization: e}
                        }(e))
                    }), (function (e) {
                        t(function (e) {
                            return {type: g.CREATE_ORGANIZATION_FAILURE, error: e}
                        }(e));
                        var a = e + "";
                        e && e.response && e.response.data && e.response.data.errors && e.response.data.errors.name && (a = e.response.data.errors.name[0]), t(H.error(a))
                    }))
                }
            }, search: function (e) {
                return function (t) {
                    t(function (e) {
                        return {type: g.SEARCH_ORGANIZATIONS_REQUEST, name: e}
                    }({name: e})), ne.search(e).then((function (e) {
                        t(function (e) {
                            return {type: g.SEARCH_ORGANIZATIONS_SUCCESS, organizations: e}
                        }(e.organizations))
                    }), (function (e) {
                        t(function (e) {
                            return {type: g.SEARCH_ORGANIZATIONS_FAILURE, error: e}
                        }(e)), t(H.error(e + ""))
                    }))
                }
            }
        };
        var ie = {
            create: function (e, t, a) {
                return function (n) {
                    n(function (e, t, a) {
                        return {type: b.CREATE_DEAL_REQUEST, name: e, contactId: t, organizationId: a}
                    }({name: e, contactId: t, organizationId: a})), re.create(e, t, a).then((function (e) {
                        n(function (e) {
                            return {type: b.CREATE_DEAL_SUCCESS, deal: e}
                        }(e))
                    }), (function (e) {
                        n(function (e) {
                            return {type: b.CREATE_DEAL_FAILURE, error: e}
                        }(e)), n(H.error(e + ""))
                    }))
                }
            }, list: function (e) {
                return function (t) {
                    t(function (e) {
                        return {type: b.LIST_DEALS_REQUEST, organizationId: e}
                    }({organizationId: e})), re.list(e).then((function (e) {
                        t(function (e) {
                            return {type: b.LIST_DEALS_SUCCESS, deals: e}
                        }(e.deals))
                    }), (function (e) {
                        t(function (e) {
                            return {type: b.LIST_DEALS_FAILURE, error: e}
                        }(e)), t(H.error(e + ""))
                    }))
                }
            }, get: function (e) {
                return function (t) {
                    t(function (e) {
                        return {type: b.GET_DEAL_REQUEST, id: e}
                    }({id: e})), re.get(e).then((function (e) {
                        t(function (e) {
                            return {type: b.GET_DEAL_SUCCESS, deal: e}
                        }(e))
                    }), (function (e) {
                        t(function (e) {
                            return {type: b.GET_DEAL_FAILURE, error: e}
                        }(e)), t(H.error(e + ""))
                    }))
                }
            }, update: function (e) {
                return function (t) {
                    t(function (e) {
                        return {type: b.UPDATE_DEAL_REQUEST, deal: e}
                    }({deal: e})), re.update(e).then((function (e) {
                        t(function (e) {
                            return {type: b.UPDATE_DEAL_SUCCESS, deal: e}
                        }(e))
                    }), (function (e) {
                        t(function (e) {
                            return {type: b.UPDATE_DEAL_FAILURE, error: e}
                        }(e)), t(H.error(e + ""))
                    }))
                }
            }, getDealStages: function () {
                return function (e) {
                    e({type: b.GET_DEAL_STAGES_REQUEST}), re.getDealStages().then((function (t) {
                        e(function (e) {
                            return {type: b.GET_DEAL_STAGES_SUCCESS, dealStages: e}
                        }(t))
                    }), (function (t) {
                        e(function (e) {
                            return {type: b.GET_DEAL_STAGES_FAILURE, error: e}
                        }(t)), e(H.error(t + ""))
                    }))
                }
            }, getDealLostReasons: function () {
                return function (e) {
                    e({type: b.GET_DEAL_LOST_REASONS_REQUEST}), re.getDealLostReasons().then((function (t) {
                        e(function (e) {
                            return {type: b.GET_DEAL_LOST_REASONS_SUCCESS, dealLostReasons: e}
                        }(t.deal_lost_reasons))
                    }), (function (t) {
                        e(function (e) {
                            return {type: b.GET_DEAL_LOST_REASONS_FAILURE, error: e}
                        }(t)), e(H.error(t + ""))
                    }))
                }
            }, getDealProducts: function (e) {
                return function (t) {
                    t(function (e) {
                        return {type: b.GET_DEAL_PRODUCTS_REQUEST, dealId: e}
                    }({dealId: e})), re.getDealProducts(e).then((function (e) {
                        t(function (e) {
                            return {type: b.GET_DEAL_PRODUCTS_SUCCESS, dealProducts: e}
                        }(e))
                    }), (function (e) {
                        t(function (e) {
                            return {type: b.GET_DEAL_PRODUCTS_FAILURE, error: e}
                        }(e)), t(H.error(e + ""))
                    }))
                }
            }, createDealProduct: function (e, t) {
                return function (a) {
                    a(function (e, t) {
                        return {type: b.CREATE_DEAL_PRODUCT_REQUEST, dealId: e, dealProduct: t}
                    }({dealId: e, dealProduct: t})), re.createDealProduct(e, t).then((function (e) {
                        a(function (e) {
                            return {type: b.CREATE_DEAL_PRODUCT_SUCCESS, dealProduct: e}
                        }(e))
                    }), (function (e) {
                        a(function (e) {
                            return {type: b.CREATE_DEAL_PRODUCT_FAILURE, error: e}
                        }(e)), a(H.error(e + ""))
                    }))
                }
            }, updateDealProduct: function (e, t) {
                return function (a) {
                    a(function (e, t) {
                        return {type: b.UPDATE_DEAL_PRODUCT_REQUEST, dealId: e, dealProduct: t}
                    }({dealId: e, dealProduct: t})), re.updateDealProduct(e, t).then((function (e) {
                        a(function (e) {
                            return {type: b.UPDATE_DEAL_PRODUCT_SUCCESS, dealProduct: e}
                        }(e))
                    }), (function (e) {
                        a(function (e) {
                            return {type: b.UPDATE_DEAL_PRODUCT_FAILURE, error: e}
                        }(e)), a(H.error(e + ""))
                    }))
                }
            }, getDealTasks: function (e) {
                return function (t) {
                    t(function (e) {
                        return {type: b.GET_DEAL_TASKS_REQUEST, dealId: e}
                    }({dealId: e})), re.getDealTasks(e).then((function (e) {
                        t(function (e) {
                            return {type: b.GET_DEAL_TASKS_SUCCESS, dealTasks: e}
                        }(e.tasks))
                    }), (function (e) {
                        t(function (e) {
                            return {type: b.GET_DEAL_TASKS_FAILURE, error: e}
                        }(e)), t(H.error(e + ""))
                    }))
                }
            }, createDealTask: function (e) {
                return function (t) {
                    t(function (e) {
                        return {type: b.CREATE_DEAL_TASK_REQUEST, dealTask: e}
                    }({dealTask: e})), re.createDealTask(e).then((function (e) {
                        t(function (e) {
                            return {type: b.CREATE_DEAL_TASK_SUCCESS, dealTask: e}
                        }(e))
                    }), (function (e) {
                        t(function (e) {
                            return {type: b.CREATE_DEAL_TASK_FAILURE, error: e}
                        }(e)), t(H.error(e + ""))
                    }))
                }
            }, updateDealTask: function (e) {
                return function (t) {
                    t(function (e) {
                        return {type: b.UPDATE_DEAL_TASK_REQUEST, dealTask: e}
                    }({dealTask: e})), re.updateDealTask(e).then((function (e) {
                        t(function (e) {
                            return {type: b.UPDATE_DEAL_TASK_SUCCESS, dealTask: e}
                        }(e))
                    }), (function (e) {
                        t(function (e) {
                            return {type: b.UPDATE_DEAL_TASK_FAILURE, error: e}
                        }(e)), t(H.error(e + ""))
                    }))
                }
            }, createDealNote: function (e, t) {
                return function (a) {
                    a(function (e, t) {
                        return {type: b.CREATE_DEAL_NOTE_REQUEST, dealId: e, note: t}
                    }({dealId: e, note: t})), re.createDealNote(e, t).then((function (e) {
                        a(function (e) {
                            return {type: b.CREATE_DEAL_NOTE_SUCCESS, dealNote: e}
                        }(e))
                    }), (function (e) {
                        a(function (e) {
                            return {type: b.CREATE_DEAL_NOTE_FAILURE, error: e}
                        }(e)), a(H.error(e + ""))
                    }))
                }
            }
        };
        var ue = {
            list: function () {
                return function (e) {
                    e({type: _.LIST_PRODUCTS_REQUEST}), oe.list().then((function (t) {
                        e(function (e) {
                            return {type: _.LIST_PRODUCTS_SUCCESS, products: e}
                        }(t.products))
                    }), (function (t) {
                        e(function (e) {
                            return {type: _.LIST_PRODUCTS_FAILURE, error: e}
                        }(t)), e(H.error(t + ""))
                    }))
                }
            }
        };
        var de = a(89), he = a(90), me = a.n(he), Ee = (a(82), a(121), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    user: h.getUserInfo(),
                    instance: h.getActiveInstance()
                }, a.logout = a.logout.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "logout", value: function () {
                    this.props.dispatch(ce.logout())
                }
            }, {
                key: "render", value: function () {
                    var e = this, t = this.state.instance && this.state.instance.path;
                    return r.a.createElement("div", {className: "logged-area-header" + (t ? "" : " instance-not-selected")}, r.a.createElement("div", {className: "logo-container"}, r.a.createElement("img", {
                        className: "logo",
                        src: me.a,
                        alt: "RD Station CRM"
                    })), t && r.a.createElement("div", {className: "user-info-container"}, r.a.createElement("p", {className: "user-name"}, "Ol\xe1, ", this.state.user && this.state.user.name.split(" ")[0]), r.a.createElement("p", {className: "user-instance"}, t)), r.a.createElement("div", {
                        className: "logout-container",
                        onClick: function () {
                            return e.logout()
                        }
                    }, r.a.createElement("i", {className: "fa fa-power-off"})))
                }
            }]), t
        }(r.a.Component));
        var pe = Object(c.b)((function () {
            return {}
        }))(Ee), Ce = function (e) {
            var t = e.component, a = Object(de.a)(e, ["component"]);
            return r.a.createElement(j.b, Object.assign({}, a, {
                render: function (e) {
                    var a = h.getAuthenticationInfo();
                    return a && a.access_token ? r.a.createElement("div", null, r.a.createElement(pe, null), r.a.createElement(t, e)) : r.a.createElement(j.a, {
                        to: {
                            pathname: "/login",
                            state: {from: e.location}
                        }
                    })
                }
            }))
        }, Se = function (e) {
            function t() {
                return Object(R.a)(this, t), Object(k.a)(this, Object(L.a)(t).apply(this, arguments))
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "render", value: function () {
                    return r.a.createElement("img", {
                        alt: "Carregando...",
                        className: "loading",
                        src: "data:image/gif;base64,R0lGODlhEAAQAPIAAP///wAAAMLCwkJCQgAAAGJiYoKCgpKSkiH/C05FVFNDQVBFMi4wAwEAAAAh/hpDcmVhdGVkIHdpdGggYWpheGxvYWQuaW5mbwAh+QQJCgAAACwAAAAAEAAQAAADMwi63P4wyklrE2MIOggZnAdOmGYJRbExwroUmcG2LmDEwnHQLVsYOd2mBzkYDAdKa+dIAAAh+QQJCgAAACwAAAAAEAAQAAADNAi63P5OjCEgG4QMu7DmikRxQlFUYDEZIGBMRVsaqHwctXXf7WEYB4Ag1xjihkMZsiUkKhIAIfkECQoAAAAsAAAAABAAEAAAAzYIujIjK8pByJDMlFYvBoVjHA70GU7xSUJhmKtwHPAKzLO9HMaoKwJZ7Rf8AYPDDzKpZBqfvwQAIfkECQoAAAAsAAAAABAAEAAAAzMIumIlK8oyhpHsnFZfhYumCYUhDAQxRIdhHBGqRoKw0R8DYlJd8z0fMDgsGo/IpHI5TAAAIfkECQoAAAAsAAAAABAAEAAAAzIIunInK0rnZBTwGPNMgQwmdsNgXGJUlIWEuR5oWUIpz8pAEAMe6TwfwyYsGo/IpFKSAAAh+QQJCgAAACwAAAAAEAAQAAADMwi6IMKQORfjdOe82p4wGccc4CEuQradylesojEMBgsUc2G7sDX3lQGBMLAJibufbSlKAAAh+QQJCgAAACwAAAAAEAAQAAADMgi63P7wCRHZnFVdmgHu2nFwlWCI3WGc3TSWhUFGxTAUkGCbtgENBMJAEJsxgMLWzpEAACH5BAkKAAAALAAAAAAQABAAAAMyCLrc/jDKSatlQtScKdceCAjDII7HcQ4EMTCpyrCuUBjCYRgHVtqlAiB1YhiCnlsRkAAAOwAAAAAAAAAAAA=="
                    })
                }
            }]), t
        }(r.a.Component), Ae = (a(123), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {modalOpened: !0}, a.handleOK = a.handleOK.bind(Object(y.a)(a)), a.handleCancel = a.handleCancel.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "handleOK", value: function () {
                    this.setState({modalOpened: !1}), (0, this.props.okHandler)()
                }
            }, {
                key: "handleCancel", value: function () {
                    this.setState({modalOpened: !1}), (0, this.props.cancelHandler)()
                }
            }, {
                key: "render", value: function () {
                    var e = this.props, t = e.title, a = e.message, n = e.okText, o = e.cancelText,
                        c = this.state.modalOpened;
                    return r.a.createElement(G.a, {
                        visible: c,
                        dialogClassName: "confirm-modal modal-dialog-centered"
                    }, r.a.createElement("div", {className: "modal-header"}, r.a.createElement("h5", {className: "modal-title"}, t), r.a.createElement("button", {
                        type: "button",
                        className: "close",
                        onClick: this.handleCancel
                    }, r.a.createElement("span", {"aria-hidden": "true"}, "\xd7"))), r.a.createElement("div", {className: "modal-body"}, a), r.a.createElement("div", {className: "modal-footer"}, r.a.createElement("button", {
                        className: "btn btn-block",
                        onClick: this.handleOK
                    }, n), r.a.createElement("button", {
                        className: "btn btn-cancel btn-block mt-2",
                        onClick: this.handleCancel
                    }, o)))
                }
            }]), t
        }(r.a.Component));
        var ve = Object(c.b)((function () {
            return {}
        }))(Ae), fe = a(16), ge = a(46), be = a.n(ge), _e = (a(124), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    useTermAgreed: !0,
                    dataUsageAgreed: !1
                }, a.goToNextPage = a.goToNextPage.bind(Object(y.a)(a)), a.onCheckboxClick = a.onCheckboxClick.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "componentDidMount", value: function () {
                }
            }, {
                key: "onCheckboxClick", value: function (e) {
                    var t = e.target, a = t.name, n = t.checked;
                    this.setState(Object(fe.a)({}, a, n))
                }
            }, {
                key: "goToNextPage", value: function () {
                    var e = this.state, t = e.useTermAgreed, a = e.dataUsageAgreed;
                    if (t && a) {
                        h.saveAgreementsStatus(!0);
                        var n = h.getAuthenticationInfo();
                        n && n.access_token ? p.push("/select-instance") : p.push("/login")
                    }
                }
            }, {
                key: "render", value: function () {
                    var e = this, t = this.state, a = t.useTermAgreed, n = t.dataUsageAgreed;
                    return r.a.createElement("div", {className: "agreements"}, r.a.createElement("div", {className: "content-header"}, r.a.createElement("img", {
                        className: "logo",
                        src: be.a,
                        alt: "whatStation"
                    })), r.a.createElement("div", {className: "content-body"}, r.a.createElement("div", {className: "intro"}, r.a.createElement("h2", null, "Ol\xe1"), "Para acessar a whatStation leia e aceite os termos de uso.")), r.a.createElement("div", {className: "section"}, r.a.createElement("div", {className: "form-check"}, r.a.createElement("input", {
                        id: "useTermAgreed",
                        type: "checkbox",
                        className: "form-check-input",
                        value: this.state.useTermAgreed,
                        name: "useTermAgreed",
                        onClick: this.onCheckboxClick
                    }), r.a.createElement("label", {htmlFor: "useTermAgreed"}, "Estou de acordo com os ", r.a.createElement("a", {
                        href: "http://app.whatstation.com.br/termos-de-uso",
                        target: "_blank",
                        rel: "noopener noreferrer"
                    }, "termos"), " de uso do produto")), r.a.createElement("div", {className: "form-check"}, r.a.createElement("input", {
                        id: "dataUsageAgreed",
                        type: "checkbox",
                        className: "form-check-input",
                        value: this.state.dataUsageAgreed,
                        name: "dataUsageAgreed",
                        onClick: this.onCheckboxClick
                    }), r.a.createElement("label", {htmlFor: "dataUsageAgreed"}, "Os dados salvos no CRM tiveram consentimento do contato")), r.a.createElement("div", {className: "form-group"}, r.a.createElement("button", {
                        className: "btn btn-block",
                        disabled: !a || !n,
                        onClick: function () {
                            return e.goToNextPage()
                        }
                    }, "Come\xe7ar"))))
                }
            }]), t
        }(r.a.Component));
        var Te = Object(c.b)((function (e) {
            return {}
        }))(_e), De = (a(125), function (e) {
            function t() {
                return Object(R.a)(this, t), Object(k.a)(this, Object(L.a)(t).apply(this, arguments))
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "componentDidMount", value: function () {
                    var e = h.getAuthenticationInfo(), t = e && e.access_token;
                    t && this.props.dispatch(ce.refreshToken()), h.getAgreementsStatus() ? t ? p.push("/select-instance") : p.push("/login") : p.push("/agreements")
                }
            }, {
                key: "render", value: function () {
                    return r.a.createElement("div", {className: "splash-screen"}, r.a.createElement(Se, null))
                }
            }]), t
        }(r.a.Component));
        var Ne = Object(c.b)((function (e) {
            return {}
        }))(De), Oe = (a(126), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {user: h.getUserInfo()}, a.selectInstance = a.selectInstance.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "componentDidMount", value: function () {
                    this.props.dispatch(ce.getInstances())
                }
            }, {
                key: "selectInstance", value: function (e) {
                    this.props.dispatch(ce.selectInstance(e))
                }
            }, {
                key: "render", value: function () {
                    var e = this, t = this.props.userInstances;
                    return r.a.createElement("div", {className: "select-instance"}, t.loading && r.a.createElement(Se, null), t.error && r.a.createElement("div", null, r.a.createElement("div", {className: "intro"}, r.a.createElement("h2", {className: "mb-2"}, "Ol\xe1, "), r.a.createElement("p", null, r.a.createElement("b", null, "Ocorreu um erro ao recuperar os dados da sua conta no RD Station CRM.")), r.a.createElement("p", null, "Se o problema persistir, verifique a ", r.a.createElement("a", {
                        href: "https://plugcrm.net/app#/accounts",
                        target: "_blank",
                        rel: "noopener noreferrer"
                    }, "situa\xe7\xe3o do seu plano"), " ou para mais informa\xe7\xf5es, ", r.a.createElement("a", {
                        href: "https://crmsupport.rdstation.com.br/hc/pt-br",
                        target: "_blank",
                        rel: "noopener noreferrer"
                    }, "fale com o suporte"), "!"))), t.instances && Array.isArray(t.instances) && 0 === t.instances.length && r.a.createElement("div", null, r.a.createElement("div", {className: "intro"}, r.a.createElement("h2", {className: "mb-2"}, "Ol\xe1, "), r.a.createElement("p", null, r.a.createElement("b", null, "O seu login n\xe3o est\xe1 associado com nenhuma conta ativa no RD Station CRM.")), r.a.createElement("p", null, "Voc\xea pode ", r.a.createElement("a", {
                        href: "https://accounts.rdstation.com.br/subscriptions/crm/new",
                        target: "_blank",
                        rel: "noopener noreferrer"
                    }, "criar uma nova conta FREE no RD Station CRM"), " ou para mais informa\xe7\xf5es, ", r.a.createElement("a", {
                        href: "https://crmsupport.rdstation.com.br/hc/pt-br",
                        target: "_blank",
                        rel: "noopener noreferrer"
                    }, "fale com o suporte"), "!"))), t.instances && t.instances.length > 1 && r.a.createElement("div", null, r.a.createElement("div", {className: "intro"}, r.a.createElement("h2", null, "Ol\xe1, ", this.state.user && this.state.user.name.split(" ")[0]), "Selecione uma inst\xe2ncia"), r.a.createElement("ul", null, t.instances.map((function (t, a) {
                        return r.a.createElement("li", {key: t.id}, r.a.createElement("button", {
                            className: "btn btn-block btn-inverse",
                            onClick: function () {
                                return e.selectInstance(t)
                            }
                        }, t.path))
                    })))), t.instances && !Array.isArray(t.instances) && this.selectInstance(t.instances))
                }
            }]), t
        }(r.a.Component));
        var Re = Object(c.b)((function (e) {
            return {userInstances: e.userInstances}
        }))(Oe), Ue = function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    name: "",
                    organization: null,
                    submitted: !1
                }, a.handleKeyDown = a.handleKeyDown.bind(Object(y.a)(a)), a.handleChange = a.handleChange.bind(Object(y.a)(a)), a.handleSubmit = a.handleSubmit.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "handleKeyDown", value: function (e) {
                    "Enter" !== e.key || this.props.saving || this.handleSubmit()
                }
            }, {
                key: "handleChange", value: function (e) {
                    var t = e.target, a = t.name, n = t.value;
                    this.setState(Object(fe.a)({}, a, n))
                }
            }, {
                key: "handleSubmit", value: function () {
                    this.setState({submitted: !0});
                    var e = this.state.name, t = this.props.dispatch;
                    e && t(le.create(e))
                }
            }, {
                key: "componentDidUpdate", value: function (e, t, a) {
                    e.organization !== this.props.organization && this.props.organization && this.props.selectOrganization(this.props.organization)
                }
            }, {
                key: "render", value: function () {
                    var e = this.props.saving, t = this.state, a = t.name, n = t.submitted;
                    return r.a.createElement("div", null, r.a.createElement("div", {className: "input-group" + (n && !a ? " has-error" : "")}, r.a.createElement("input", {
                        type: "text",
                        className: "form-control",
                        name: "name",
                        value: a,
                        onChange: this.handleChange,
                        onKeyDown: this.handleKeyDown,
                        autoComplete: "off"
                    })), r.a.createElement("div", {className: "input-group mt-3"}, r.a.createElement("button", {
                        disabled: !a,
                        className: "btn btn-primary btn-block",
                        onClick: this.handleSubmit
                    }, "Salvar")), e && r.a.createElement(Se, null))
                }
            }]), t
        }(r.a.Component);
        var ke = Object(c.b)((function (e) {
            var t = e.organization;
            return {saving: t.saving, organization: t.organization}
        }))(Ue), Le = (a(127), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    name: "",
                    organizations: [],
                    organization: null,
                    submitted: !1,
                    newOrganization: !1,
                    modalOpened: !1
                }, a.handleKeyDown = a.handleKeyDown.bind(Object(y.a)(a)), a.handleChange = a.handleChange.bind(Object(y.a)(a)), a.handleSearch = a.handleSearch.bind(Object(y.a)(a)), a.selectOrganization = a.selectOrganization.bind(Object(y.a)(a)), a.handleOrganizationModal = a.handleOrganizationModal.bind(Object(y.a)(a)), a.handleNewOrganization = a.handleNewOrganization.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "handleKeyDown", value: function (e) {
                    "Enter" !== e.key || this.props.searching || this.handleSearch()
                }
            }, {
                key: "handleChange", value: function (e) {
                    var t = e.target, a = t.name, n = t.value;
                    this.setState(Object(fe.a)({}, a, n))
                }
            }, {
                key: "handleSearch", value: function () {
                    this.setState({organization: null, submitted: !0});
                    var e = this.state.name, t = this.props.dispatch;
                    e && t(le.search(e))
                }
            }, {
                key: "selectOrganization", value: function (e) {
                    this.setState({
                        organization: e,
                        name: e.name,
                        newOrganization: !1,
                        organizations: [],
                        modalOpened: !1
                    }), this.props.selectOrganization(e)
                }
            }, {
                key: "handleOrganizationModal", value: function () {
                    this.setState({modalOpened: !this.state.modalOpened})
                }
            }, {
                key: "handleNewOrganization", value: function () {
                    this.setState({
                        newOrganization: !this.state.newOrganization,
                        organizations: [],
                        organization: null,
                        submitted: !1
                    })
                }
            }, {
                key: "componentDidUpdate", value: function (e, t, a) {
                    e.organizations !== this.props.organizations && this.setState({organizations: this.props.organizations})
                }
            }, {
                key: "render", value: function () {
                    var e = this, t = this.props.searching, a = this.state, n = a.newOrganization, o = a.organization,
                        c = a.name, s = a.submitted, l = a.organizations, i = a.modalOpened;
                    return r.a.createElement("div", {className: "select-organization"}, r.a.createElement(G.a, {
                        visible: i,
                        dialogClassName: "modal-dialog-centered"
                    }, r.a.createElement("div", {className: "modal-header"}, r.a.createElement("h5", {className: "modal-title"}, n ? "Adicionar Empresa/Cliente" : "Buscar Empresa/Cliente"), r.a.createElement("button", {
                        type: "button",
                        className: "close",
                        onClick: this.handleOrganizationModal
                    }, r.a.createElement("span", {"aria-hidden": "true"}, "\xd7"))), r.a.createElement("div", {className: "modal-body"}, !n && r.a.createElement("div", {className: "existing-organization"}, r.a.createElement("div", {className: "input-group has-append" + (s && !c ? " has-error" : "")}, r.a.createElement("input", {
                        type: "text",
                        className: "form-control",
                        name: "name",
                        value: c,
                        placeholder: "Buscar Empresa/Cliente",
                        autoComplete: "off",
                        onChange: this.handleChange,
                        onKeyDown: this.handleKeyDown
                    }), r.a.createElement("div", {className: "input-group-append"}, r.a.createElement("button", {
                        className: "btn btn-input",
                        onClick: this.handleSearch
                    }, r.a.createElement("i", {className: "fa fa-search"})))), t && r.a.createElement(Se, null), !o && l && l.length > 0 && r.a.createElement("ul", {className: "organization-list"}, l.map((function (t) {
                        return r.a.createElement("li", {
                            key: t.id, onClick: function () {
                                return e.selectOrganization(t)
                            }
                        }, t.name)
                    }))), s && !t && !o && l && 0 === l.length && r.a.createElement("p", {className: "mt-2 text-center"}, "Nenhum resultado para a busca")), n && r.a.createElement("div", {className: "new-organization"}, r.a.createElement(ke, {selectOrganization: this.selectOrganization}))), r.a.createElement("div", {className: "modal-footer"}, !n && r.a.createElement("div", {className: "w-100"}, "N\xe3o encontrou?", r.a.createElement("button", {
                        className: "btn btn-inverse btn-block mt-2",
                        onClick: this.handleNewOrganization
                    }, "Adicione uma Empresa/Cliente")), n && r.a.createElement("div", {className: "w-100"}, "Deseja utilizar uma existente?", r.a.createElement("button", {
                        className: "btn btn-inverse btn-block mt-2",
                        onClick: this.handleNewOrganization
                    }, "Busque uma Empresa/Cliente")))), r.a.createElement("div", {className: "input-group has-append"}, r.a.createElement("input", {
                        type: "text",
                        className: "form-control",
                        value: o && o.name,
                        placeholder: "Buscar Empresa/Cliente",
                        onClick: this.handleOrganizationModal
                    }), r.a.createElement("div", {className: "input-group-append"}, r.a.createElement("button", {
                        className: "btn btn-input",
                        onClick: this.handleOrganizationModal
                    }, r.a.createElement("i", {className: "fa fa-chevron-down"})))))
                }
            }]), t
        }(r.a.Component));
        var ye = Object(c.b)((function (e) {
            var t = e.organization;
            return {searching: t.searching, organizations: t.organizations}
        }))(Le), Ie = (a(128), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    submitted: !1,
                    name: "",
                    organization: null
                }, a.handleKeyDown = a.handleKeyDown.bind(Object(y.a)(a)), a.handleChange = a.handleChange.bind(Object(y.a)(a)), a.handleSubmit = a.handleSubmit.bind(Object(y.a)(a)), a.selectOrganization = a.selectOrganization.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "handleKeyDown", value: function (e) {
                    "Enter" !== e.key || this.props.saving || this.handleSubmit()
                }
            }, {
                key: "handleChange", value: function (e) {
                    var t = e.target, a = t.name, n = t.value;
                    this.setState(Object(fe.a)({}, a, n))
                }
            }, {
                key: "handleSubmit", value: function () {
                    this.setState({submitted: !0});
                    var e = this.state, t = e.name, a = e.organization, n = this.props.dispatch;
                    t && a && n(se.create(t, this.props.phoneNumber, a._id))
                }
            }, {
                key: "selectOrganization", value: function (e) {
                    this.setState({organization: e})
                }
            }, {
                key: "componentDidUpdate", value: function (e, t, a) {
                    e.contact !== this.props.contact && this.props.contact && this.props.selectContact(this.props.contact)
                }
            }, {
                key: "render", value: function () {
                    var e = this.props.saving, t = this.state, a = t.name, n = t.organization, o = t.submitted;
                    return r.a.createElement("div", {className: "new-contact section"}, r.a.createElement("h2", null, "Adicionar"),
                        r.a.createElement("p", {className: "info"}, "Adicione ao RD Station CRM"),
                        r.a.createElement("div", {className: "form-group" + (o && !n ? " has-error" : "")},
                            r.a.createElement(ye, {selectOrganization: this.selectOrganization})), n &&
                        r.a.createElement("div", {className: "input-group mb-3" + (o && !a ? " has-error" : "")}, r.a.createElement("input", {
                        type: "text",
                        className: "form-control",
                        name: "name",
                        value: a,
                        autoComplete: "off",
                        placeholder: "Nome do contato",
                        onChange: this.handleChange,
                        onKeyDown: this.handleKeyDown
                    })), !e && r.a.createElement("button", {
                        className: "btn btn-block",
                        disabled: !n || !a,
                        onClick: this.handleSubmit
                    }, "Salvar"), e && r.a.createElement(Se, null))
                }
            }]), t
        }(r.a.Component));
        var we = Object(c.b)((function (e) {
            var t = e.contact;
            return {saving: t.saving, contact: t.contact}
        }))(Ie), je = (a(129), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    name: "",
                    contacts: [],
                    contact: null,
                    submitted: !1,
                    modalOpened: !1
                }, a.handleKeyDown = a.handleKeyDown.bind(Object(y.a)(a)), a.handleChange = a.handleChange.bind(Object(y.a)(a)), a.handleSearch = a.handleSearch.bind(Object(y.a)(a)), a.handleContactModal = a.handleContactModal.bind(Object(y.a)(a)), a.selectContact = a.selectContact.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "handleKeyDown", value: function (e) {
                    "Enter" !== e.key || this.props.searching || this.handleSearch()
                }
            }, {
                key: "handleChange", value: function (e) {
                    var t = e.target, a = t.name, n = t.value;
                    this.setState(Object(fe.a)({}, a, n))
                }
            }, {
                key: "handleSearch", value: function () {
                    this.setState({submitted: !0, contact: null});
                    var e = this.state.name, t = this.props.dispatch;
                    e && t(se.search(e))
                }
            }, {
                key: "handleContactModal", value: function () {
                    this.setState({modalOpened: !this.state.modalOpened})
                }
            }, {
                key: "selectContact", value: function (e) {
                    this.setState({name: e.name, contacts: []});
                    var t = this.props, a = t.dispatch, n = t.phoneNumber;
                    a(se.addPhoneNumberToContact(e, n))
                }
            }, {
                key: "componentDidUpdate", value: function (e, t, a) {
                    e.contacts !== this.props.contacts && this.setState({contacts: this.props.contacts}), e.contact !== this.props.contact && this.props.contact && this.props.selectContact(this.props.contact)
                }
            }, {
                key: "render", value: function () {
                    var e = this, t = this.props.searching, a = this.state, n = a.contact, o = a.name, c = a.submitted,
                        s = a.contacts, l = a.modalOpened;
                    return r.a.createElement("div", {className: "contact section"}, r.a.createElement(G.a, {
                        visible: l,
                        dialogClassName: "modal-dialog-centered"
                    }, r.a.createElement("div", {className: "modal-header"}, r.a.createElement("h5", {className: "modal-title"}, "Buscar Contato"), r.a.createElement("button", {
                        type: "button",
                        className: "close",
                        onClick: this.handleContactModal
                    }, r.a.createElement("span", {"aria-hidden": "true"}, "\xd7"))), r.a.createElement("div", {className: "modal-body"}, r.a.createElement("div", {className: "input-group has-append" + (c && !o ? " has-error" : "")}, r.a.createElement("input", {
                        type: "text",
                        className: "form-control",
                        name: "name",
                        value: o,
                        placeholder: "Buscar Contato",
                        autoComplete: "off",
                        onChange: this.handleChange,
                        onKeyDown: this.handleKeyDown
                    }), r.a.createElement("div", {className: "input-group-append"}, r.a.createElement("button", {
                        className: "btn btn-input",
                        onClick: this.handleSearch
                    }, r.a.createElement("i", {className: "fa fa-search"})))), t && r.a.createElement(Se, null), !n && s && s.length > 0 && r.a.createElement("ul", {className: "contact-list"}, s.map((function (t) {
                        return r.a.createElement("li", {
                            key: t.id, onClick: function () {
                                return e.selectContact(t)
                            }
                        }, t.name, " (", t.organization.name, ")")
                    }))), c && !t && !n && s && 0 === s.length && r.a.createElement("p", {className: "mt-2 text-center"}, "Nenhum resultado para a busca")), r.a.createElement("div", {className: "modal-footer"}, r.a.createElement("button", {
                        className: "btn btn-cancel btn-block mt-2",
                        onClick: this.handleContactModal
                    }, "Cancelar"))), r.a.createElement("h2", null, "Relacionar"), r.a.createElement("p", {className: "info"}, "Relacione a um contato existente no RD Station CRM"), r.a.createElement("div", {className: "input-group has-append"}, r.a.createElement("input", {
                        type: "text",
                        className: "form-control",
                        value: n && n.name,
                        placeholder: "Buscar Contato",
                        onClick: this.handleContactModal
                    }), r.a.createElement("div", {className: "input-group-append"}, r.a.createElement("button", {
                        className: "btn btn-input",
                        onClick: this.handleContactModal
                    }, r.a.createElement("i", {className: "fa fa-chevron-down"})))))
                }
            }]), t
        }(r.a.Component));
        var Pe = Object(c.b)((function (e) {
            var t = e.contact;
            return {searching: t.searching, contact: t.contact, contacts: t.contacts}
        }))(je), Fe = (a(130), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).selectContact = a.selectContact.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "selectContact", value: function (e) {
                    this.props.selectContact(e)
                }
            }, {
                key: "render", value: function () {
                    var e = this.props.phoneNumber;
                    return r.a.createElement("div", {className: "new-phone-number"}, r.a.createElement("div", {className: "new-phone-number-info"}, r.a.createElement("i", {className: "fa fa-user-plus"}), r.a.createElement("span", {class: "phone-number"}, e), r.a.createElement("span", {class: "label"}, "Novo")), r.a.createElement("div", {className: "new-phone-number-actions"}, r.a.createElement(we, {
                        selectContact: this.selectContact,
                        phoneNumber: e
                    }), r.a.createElement(Pe, {selectContact: this.selectContact, phoneNumber: e})))
                }
            }]), t
        }(r.a.Component));
        var Ge = Object(c.b)((function (e) {
            return {}
        }))(Fe), Qe = (a(131), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    name: "",
                    submitted: !1,
                    modalOpened: !0
                }, a.handleKeyDown = a.handleKeyDown.bind(Object(y.a)(a)), a.handleChange = a.handleChange.bind(Object(y.a)(a)), a.handleSubmit = a.handleSubmit.bind(Object(y.a)(a)), a.handleCloseModal = a.handleCloseModal.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "handleKeyDown", value: function (e) {
                    "Enter" !== e.key || this.props.saving || this.handleSubmit()
                }
            }, {
                key: "handleChange", value: function (e) {
                    var t = e.target, a = t.name, n = t.value;
                    this.setState(Object(fe.a)({}, a, n))
                }
            }, {
                key: "handleSubmit", value: function () {
                    this.setState({submitted: !0});
                    var e = this.state.name, t = this.props.dispatch;
                    e && t(ie.create(e, this.props.contact._id, this.props.contact.organization_id))
                }
            }, {
                key: "handleCloseModal", value: function () {
                    this.setState({modalOpened: !1}), this.props.cancel()
                }
            }, {
                key: "componentDidUpdate", value: function (e, t, a) {
                    e.deal !== this.props.deal && this.props.deal && this.props.newDealCreated(this.props.deal)
                }
            }, {
                key: "render", value: function () {
                    var e = this.props.saving, t = this.state, a = t.name, n = t.submitted, o = t.modalOpened;
                    return r.a.createElement("div", {className: "new-deal"}, r.a.createElement(G.a, {
                        visible: o,
                        dialogClassName: "modal-dialog-centered"
                    }, r.a.createElement("div", {className: "modal-header"}, r.a.createElement("h5", {className: "modal-title"}, "Nova Oportunidade"), r.a.createElement("button", {
                        type: "button",
                        className: "close",
                        onClick: this.handleCloseModal
                    }, r.a.createElement("span", {"aria-hidden": "true"}, "\xd7"))), r.a.createElement("div", {className: "modal-body"}, r.a.createElement("div", {className: "input-group" + (n && !a ? " has-error" : "")}, r.a.createElement("input", {
                        type: "text",
                        className: "form-control",
                        name: "name",
                        value: a,
                        placeholder: "Nome da Oportunidade",
                        autoComplete: "off",
                        onChange: this.handleChange,
                        onKeyDown: this.handleKeyDown
                    }))), r.a.createElement("div", {className: "modal-footer"}, e && r.a.createElement(Se, null), !e && r.a.createElement("button", {
                        className: "btn btn-block",
                        disabled: !a,
                        onClick: this.handleSubmit
                    }, "Salvar"), !e && r.a.createElement("button", {
                        className: "btn btn-cancel btn-block mt-2",
                        onClick: this.handleCloseModal
                    }, "Cancelar"))))
                }
            }]), t
        }(r.a.Component));
        var Me = Object(c.b)((function (e) {
            var t = e.deal;
            return {saving: t.saving, deal: t.deal}
        }))(Qe), He = (a(132), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    currentDealStageId: e.currentDeal.deal_stage._id,
                    currentDealStage: e.currentDeal.deal_stage,
                    currentDeal: e.currentDeal,
                    showSuccessMessage: !1,
                    showChangeConfirmation: !1,
                    showChangeConfirmationStage: null
                }, a.handleCurrentDealStageChange = a.handleCurrentDealStageChange.bind(Object(y.a)(a)), a.handleCancelConfirmation = a.handleCancelConfirmation.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "componentDidMount", value: function () {
                    this.props.dispatch(ie.getDealStages())
                }
            }, {
                key: "componentDidUpdate", value: function (e, t, a) {
                    e.dealStages !== this.props.dealStages && this.props.dealStages && this.setState({dealStages: this.props.dealStages}), e.currentDeal !== this.props.currentDeal && this.setState({
                        currentDealStageId: this.props.currentDeal.deal_stage._id,
                        currentDealStage: this.props.currentDeal.deal_stage,
                        currentDeal: this.props.currentDeal,
                        showSuccessMessage: !1
                    })
                }
            }, {
                key: "setCurrentDealStage", value: function (e) {
                    var t = this.state.currentDeal, a = this.props.dispatch;
                    t.deal_stage = e, t.deal_stage_id = e._id, a(ie.update(t)), this.setState({
                        currentDealStage: e,
                        currentDeal: t,
                        currentDealStageId: e._id,
                        showChangeConfirmation: !1,
                        showChangeConfirmationStage: null,
                        showSuccessMessage: !0
                    })
                }
            }, {
                key: "handleCurrentDealStageChange", value: function (e) {
                    var t = this.state.dealStages, a = e.target.value;
                    if (a) {
                        var n = t.find((function (e) {
                            return e._id === a
                        }));
                        this.setState({
                            showChangeConfirmationStage: n,
                            showChangeConfirmation: !0,
                            showSuccessMessage: !1
                        })
                    }
                }
            }, {
                key: "handleCancelConfirmation", value: function () {
                    this.setState({showChangeConfirmationStage: null, showChangeConfirmation: !1})
                }
            }, {
                key: "render", value: function () {
                    var e = this, t = this.props.saving, a = this.state, n = a.currentDealStageId, o = a.dealStages,
                        c = a.currentDeal, s = a.showSuccessMessage, l = a.showChangeConfirmation,
                        i = a.showChangeConfirmationStage, u = l ? r.a.createElement(ve, {
                            title: "Alterar Etapa",
                            message: 'Deseja mesmo alterar a etapa da oportunidade para "'.concat(i.name, '"?'),
                            okText: "Sim",
                            okHandler: function () {
                                e.setCurrentDealStage(i)
                            },
                            cancelText: "Cancelar",
                            cancelHandler: this.handleCancelConfirmation
                        }) : null;
                    return r.a.createElement("div", null, o && r.a.createElement("div", {className: "deal-stage section"}, r.a.createElement("h2", null, "Etapa do Funil"), r.a.createElement("select", {
                        className: "custom-select",
                        disabled: null !== c.win,
                        value: n,
                        onChange: this.handleCurrentDealStageChange
                    }, o.map((function (e) {
                        return r.a.createElement("option", {key: e._id, value: e._id}, e.name)
                    }))), !t && s && r.a.createElement("p", {className: "success-message"}, "Etapa alterada com sucesso"), l && u, null !== c.win && r.a.createElement("p", {className: "deal-done-warning" + (c.win ? " deal-done-warning-won" : "")}, c.win && "Oportunidade marcada como vendida", !1 === c.win && "Oportunidade marcada como perdida"), t && r.a.createElement(Se, null)))
                }
            }]), t
        }(r.a.Component));
        var Be = Object(c.b)((function (e) {
                var t = e.deal;
                return {saving: t.saving, dealStages: t.dealStages}
            }))(He), ze = (a(133), [{id: "percent", name: "%"}, {id: "value", name: "R$"}]),
            Ke = [{id: "spare", name: "\xdanico"}, {id: "monthly", name: "Recorrente"}], xe = function (e) {
                function t(e) {
                    var a;
                    Object(R.a)(this, t), a = Object(k.a)(this, Object(L.a)(t).call(this, e));
                    var n = {
                        _id: null,
                        product_id: null,
                        amount: 1,
                        price: 0,
                        recurrence: Ke[0].id,
                        discount_type: ze[0].id,
                        discount: 0
                    };
                    return a.props.currentDealProduct && (n = a.props.currentDealProduct), a.state = {
                        dealProduct: n,
                        showDiscount: !1,
                        submitted: !1,
                        modalOpened: !0
                    }, a.handleShowDiscountChange = a.handleShowDiscountChange.bind(Object(y.a)(a)), a.handleKeyDown = a.handleKeyDown.bind(Object(y.a)(a)), a.handleChange = a.handleChange.bind(Object(y.a)(a)), a.handleSubmit = a.handleSubmit.bind(Object(y.a)(a)), a.handleCloseModal = a.handleCloseModal.bind(Object(y.a)(a)), a
                }

                return Object(I.a)(t, e), Object(U.a)(t, [{
                    key: "componentDidMount", value: function () {
                        this.props.dispatch(ue.list())
                    }
                }, {
                    key: "componentDidUpdate", value: function (e, t, a) {
                        e.dealProduct !== this.props.dealProduct && this.props.dealProduct && this.props.dealProductSaved()
                    }
                }, {
                    key: "handleShowDiscountChange", value: function (e) {
                        var t = e.target.checked;
                        if (this.setState({showDiscount: t}), !t) {
                            var a = this.state.dealProduct;
                            a.discount = 0, a.discount_type = ze[0].id, this.setState({dealProduct: a})
                        }
                    }
                }, {
                    key: "handleKeyDown", value: function (e) {
                        "Enter" !== e.key || this.props.saving || this.handleSubmit()
                    }
                }, {
                    key: "handleChange", value: function (e) {
                        var t = e.target, a = t.name, n = t.value, r = this.state.dealProduct;
                        if (r[a] = n, "product_id" === a) {
                            var o = this.props.products.find((function (e) {
                                return e._id === n
                            }));
                            o.base_price > 0 && (r.price = o.base_price)
                        }
                        this.setState({dealProduct: r})
                    }
                }, {
                    key: "handleSubmit", value: function () {
                        var e = this.state, t = e.dealProduct, a = e.showDiscount;
                        if (this.setState({submitted: !0}), t.product_id && t.amount && t.price && t.recurrence && t.discount_type) {
                            if (a && (!t.discount_type || !t.discount)) return;
                            if (t.amount < 0) return t.amount = 0, void this.setState({dealProduct: t});
                            if (t.price < 0) return t.price = 0, void this.setState({dealProduct: t});
                            if (t.discount < 0) return t.discount = 0, void this.setState({dealProduct: t});
                            var n = this.props, r = n.currentDeal, o = n.dispatch;
                            t._id ? o(ie.updateDealProduct(r._id, t)) : o(ie.createDealProduct(r._id, t))
                        }
                    }
                }, {
                    key: "handleCloseModal", value: function () {
                        this.setState({modalOpened: !1}), this.props.cancel()
                    }
                }, {
                    key: "render", value: function () {
                        var e = this.props, t = e.saving, a = e.listing, n = e.products, o = e.title, c = this.state,
                            s = c.dealProduct, l = c.showDiscount, i = c.submitted, u = c.modalOpened;
                        return r.a.createElement("div", {className: "deal-product-form"}, r.a.createElement(G.a, {
                            visible: u,
                            dialogClassName: "modal-dialog-centered"
                        }, r.a.createElement("div", {className: "modal-header"}, r.a.createElement("h5", {className: "modal-title"}, o), r.a.createElement("button", {
                            type: "button",
                            className: "close",
                            onClick: this.handleCloseModal
                        }, r.a.createElement("span", {"aria-hidden": "true"}, "\xd7"))), r.a.createElement("div", {className: "modal-body"}, r.a.createElement("div", {className: "form-group" + (i && !s.product_id ? " has-error" : "")}, r.a.createElement("label", {htmlFor: "product_id"}, "Produto"), a && r.a.createElement("div", null, r.a.createElement(Se, null)), !a && n && n.length > 0 && r.a.createElement("select", {
                            name: "product_id",
                            className: "custom-select mt-0",
                            value: s.product_id,
                            onChange: this.handleChange
                        }, r.a.createElement("option", null, "Selecione"), n.map((function (e) {
                            return r.a.createElement("option", {key: e._id, value: e._id}, e.name)
                        }))), !a && n && 0 === n.length && r.a.createElement("p", null, r.a.createElement("b", null, "N\xe3o existem Produtos cadastrados. Acesse o ", r.a.createElement("a", {
                            href: "https://plugcrm.net/",
                            target: "_blank",
                            rel: "noopener noreferrer"
                        }, "RD Station CRM"), " e crie algum produto para poder prosseguir."))), r.a.createElement("div", {className: "form-group" + (i && !s.amount ? " has-error" : "")}, r.a.createElement("label", {htmlFor: "amount"}, "Quantidade"), r.a.createElement("input", {
                            type: "number",
                            min: "0",
                            className: "form-control",
                            autoComplete: "off",
                            name: "amount",
                            value: s.amount,
                            onChange: this.handleChange,
                            onKeyDown: this.handleKeyDown
                        })), r.a.createElement("div", {className: "form-group" + (i && !s.price ? " has-error" : "")}, r.a.createElement("label", {htmlFor: "price"}, "Pre\xe7o"), r.a.createElement("div", {className: "input-group has-prepend input-grou-price"}, r.a.createElement("div", {className: "input-group-prepend"}, r.a.createElement("span", {className: "input-group-text"}, "R$")), r.a.createElement("input", {
                            className: "form-control",
                            autoComplete: "off",
                            name: "price",
                            value: s.price,
                            separator: ",",
                            onChange: this.handleChange,
                            onKeyDown: this.handleKeyDown
                        }))), r.a.createElement("div", {className: "form-group" + (i && !s.recurrence ? " has-error" : "")}, r.a.createElement("label", {htmlFor: "recurrence"}, "Recorr\xeancia"), r.a.createElement("select", {
                            name: "recurrence",
                            className: "custom-select",
                            value: s.recurrence,
                            onChange: this.handleChange
                        }, Ke.map((function (e) {
                            return r.a.createElement("option", {key: e.id, value: e.id}, e.name)
                        })))), r.a.createElement("div", {className: "form-group"}, r.a.createElement("label", {htmlFor: "showDiscount"}, "Desconto"), r.a.createElement("div", {className: "form-check"}, r.a.createElement("input", {
                            id: "showDiscount",
                            type: "checkbox",
                            className: "form-check-input show-discount-label",
                            value: l,
                            name: "showDiscount",
                            onClick: this.handleShowDiscountChange
                        }), r.a.createElement("label", {htmlFor: "showDiscount"}, "Adicionar Desconto"))), l && r.a.createElement("div", null, r.a.createElement("div", {className: "form-group" + (i && l && !s.discount_type ? " has-error" : "")}, r.a.createElement("label", {htmlFor: "discount_type"}, "Tipo"), r.a.createElement("select", {
                            name: "discount_type",
                            className: "custom-select",
                            value: s.discount_type,
                            onChange: this.handleChange
                        }, ze.map((function (e) {
                            return r.a.createElement("option", {key: e.id, value: e.id}, e.name)
                        })))), r.a.createElement("div", {className: "form-group" + (i && l && !s.discount ? " has-error" : "")}, r.a.createElement("label", {htmlFor: "discount"}, "Valor do desconto"), r.a.createElement("input", {
                            type: "number",
                            autoComplete: "off",
                            min: "0",
                            className: "form-control",
                            name: "discount",
                            value: s.discount,
                            onChange: this.handleChange,
                            onKeyDown: this.handleKeyDown
                        })))), r.a.createElement("div", {className: "modal-footer"}, !t && n && n.length > 0 && r.a.createElement("button", {
                            className: "btn btn-block",
                            onClick: this.handleSubmit
                        }, "Salvar"), !t && r.a.createElement("button", {
                            className: "btn btn-cancel btn-block",
                            onClick: this.handleCloseModal
                        }, "Cancelar"), t && r.a.createElement(Se, null))))
                    }
                }]), t
            }(r.a.Component);
        var We = Object(c.b)((function (e) {
            var t = e.product, a = t.listing, n = t.products, r = e.deal;
            return {listing: a, saving: r.saving, products: n, dealProduct: r.dealProduct}
        }))(xe), Ye = function (e) {
            function t() {
                return Object(R.a)(this, t), Object(k.a)(this, Object(L.a)(t).apply(this, arguments))
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "render", value: function () {
                    var e = this.props, t = e.currentDeal, a = e.dealProductSaved, n = e.cancel;
                    return r.a.createElement(We, {
                        title: "Adicionar novo Produto e Servi\xe7o",
                        currentDeal: t,
                        dealProductSaved: a,
                        cancel: n
                    })
                }
            }]), t
        }(r.a.Component);
        var Ze = Object(c.b)((function (e) {
        }))(Ye), Je = function (e) {
            function t() {
                return Object(R.a)(this, t), Object(k.a)(this, Object(L.a)(t).apply(this, arguments))
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "render", value: function () {
                    var e = this.props, t = e.currentDeal, a = e.currentDealProduct, n = e.dealProductSaved,
                        o = e.cancel;
                    return r.a.createElement(We, {
                        title: "Editar Produto e Servi\xe7o",
                        currentDeal: t,
                        currentDealProduct: a,
                        dealProductSaved: n,
                        cancel: o
                    })
                }
            }]), t
        }(r.a.Component);
        var qe = Object(c.b)((function (e) {
        }))(Je), Ve = (a(134), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    currentDeal: e.currentDeal,
                    newDealProduct: !1,
                    editingDealProduct: null
                }, a.dealProductSaved = a.dealProductSaved.bind(Object(y.a)(a)), a.cancelDealProductEdit = a.cancelDealProductEdit.bind(Object(y.a)(a)), a.handleNewDealProduct = a.handleNewDealProduct.bind(Object(y.a)(a)), a.handleEditDealProduct = a.handleEditDealProduct.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "componentDidMount", value: function () {
                    this.props.dispatch(ie.getDealProducts(this.state.currentDeal._id))
                }
            }, {
                key: "componentDidUpdate", value: function (e, t, a) {
                    e.dealProducts !== this.props.dealProducts && this.props.dealProducts && this.setState({dealProducts: this.props.dealProducts}), e.currentDeal !== this.props.currentDeal && (this.setState({
                        currentDeal: this.props.currentDeal,
                        newDealProduct: !1,
                        editingDealProduct: null,
                        dealProducts: null
                    }), this.props.dispatch(ie.getDealProducts(this.props.currentDeal._id)))
                }
            }, {
                key: "dealProductSaved", value: function () {
                    var e = this;
                    this.setState({newDealProduct: !1, editingDealProduct: null}), setTimeout((function () {
                        e.props.dispatch(ie.getDealProducts(e.props.currentDeal._id))
                    }), 500)
                }
            }, {
                key: "cancelDealProductEdit", value: function () {
                    this.setState({editingDealProduct: null})
                }
            }, {
                key: "handleNewDealProduct", value: function () {
                    this.setState({newDealProduct: !this.state.newDealProduct, editingDealProduct: null})
                }
            }, {
                key: "handleEditDealProduct", value: function (e) {
                    this.setState({newDealProduct: !1, editingDealProduct: e})
                }
            }, {
                key: "render", value: function () {
                    var e = this, t = this.state, a = t.dealProducts, n = t.newDealProduct, o = t.currentDeal,
                        c = t.editingDealProduct, s = this.props.listingProducts, l = r.a.createElement(Ze, {
                            currentDeal: o,
                            dealProductSaved: this.dealProductSaved,
                            cancel: this.handleNewDealProduct
                        }), i = r.a.createElement(qe, {
                            currentDeal: o,
                            currentDealProduct: c,
                            dealProductSaved: this.dealProductSaved,
                            cancel: this.cancelDealProductEdit
                        });
                    return r.a.createElement("div", null, (null === o.win || a && a.length > 0) && r.a.createElement("div", {className: "deal-products section"}, r.a.createElement("h2", null, "Produtos e Servi\xe7os"), n && !c && l, c && !n && i, s && r.a.createElement(Se, null), a && a.length > 0 && r.a.createElement("div", {className: "products mt-2"}, a.map((function (t) {
                        return r.a.createElement("div", {className: "product"}, r.a.createElement("p", {className: "product-name"}, r.a.createElement("span", {className: "product-amount"}, t.amount), t.product_name), r.a.createElement("div", {className: "product-body"}, r.a.createElement("div", {className: "row"}, r.a.createElement("div", {className: "col"}, "Valor"), r.a.createElement("div", {className: "col text-right"}, O(t.price))), t.discount > 0 && r.a.createElement("div", {className: "row"}, r.a.createElement("div", {className: "col"}, "Desconto"), r.a.createElement("div", {className: "col text-right"}, t.discount)), r.a.createElement("div", {className: "row"}, r.a.createElement("div", {className: "col"}, "Total"), r.a.createElement("div", {className: "col text-right"}, r.a.createElement("b", null, O(t.total)))), null === o.win && r.a.createElement("button", {
                            className: "btn btn-block btn-edit",
                            onClick: function () {
                                e.handleEditDealProduct(t)
                            }
                        }, "Editar")))
                    }))), null === o.win && r.a.createElement("button", {
                        className: "btn btn-inverse btn-block mt-2",
                        onClick: this.handleNewDealProduct
                    }, "Novo Produto e Servi\xe7o")))
                }
            }]), t
        }(r.a.Component));
        var Xe = Object(c.b)((function (e) {
            var t = e.deal;
            return {listingProducts: t.listingProducts, dealProducts: t.dealProducts}
        }))(Ve), $e = (a(135), a(136), function (e) {
            function t(e) {
                var a;
                Object(R.a)(this, t), a = Object(k.a)(this, Object(L.a)(t).call(this, e));
                var n = {date: new Date, hour: new Date, subject: "", type: $[0].id, deal_id: a.props.currentDeal._id};
                return a.props.currentDealTask && ((n = Object(te.a)({}, a.props.currentDealTask)).date = new Date(n.date), n.hour = new Date(n.date.getFullYear(), n.date.getMonth(), n.date.getDate(), n.hour.split(":")[0], n.hour.split(":")[1], 0)), a.state = {
                    dealTask: n,
                    submitted: !1,
                    modalOpened: !0
                }, a.handleKeyDown = a.handleKeyDown.bind(Object(y.a)(a)), a.handleChange = a.handleChange.bind(Object(y.a)(a)), a.handleDateChange = a.handleDateChange.bind(Object(y.a)(a)), a.handleTimeChange = a.handleTimeChange.bind(Object(y.a)(a)), a.handleSubmit = a.handleSubmit.bind(Object(y.a)(a)), a.handleDone = a.handleDone.bind(Object(y.a)(a)), a.handleCancel = a.handleCancel.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "componentDidUpdate", value: function (e, t, a) {
                    e.dealTask !== this.props.dealTask && this.props.dealTask && this.props.dealTaskSaved()
                }
            }, {
                key: "handleKeyDown", value: function (e) {
                    "Enter" !== e.key || this.props.saving || this.handleSubmit()
                }
            }, {
                key: "handleChange", value: function (e) {
                    var t = e.target, a = t.name, n = t.value, r = this.state.dealTask;
                    r[a] = n, this.setState({dealTask: r})
                }
            }, {
                key: "handleDateChange", value: function (e) {
                    var t = this.state.dealTask;
                    t.date = e, this.setState({dealTask: t})
                }
            }, {
                key: "handleTimeChange", value: function (e) {
                    var t = this.state.dealTask;
                    t.hour = e, this.setState({dealTask: t})
                }
            }, {
                key: "handleDone", value: function () {
                    var e = this, t = this.state.dealTask;
                    t.done = !0, this.setState({dealTask: t}, (function () {
                        e.handleSubmit()
                    }))
                }
            }, {
                key: "handleSubmit", value: function () {
                    var e = this.state.dealTask;
                    if (this.setState({submitted: !0}), e.date && e.hour && e.subject && e.type) {
                        var t = Object(te.a)({}, e);
                        t.hour = t.hour.getHours() + ":" + t.hour.getMinutes();
                        var a = this.props.dispatch;
                        t._id ? a(ie.updateDealTask(t)) : a(ie.createDealTask(t))
                    }
                }
            }, {
                key: "handleCancel", value: function () {
                    this.setState({modalOpened: !1}), this.props.cancel()
                }
            }, {
                key: "render", value: function () {
                    var e = this, t = this.props, a = t.saving, n = t.title, o = this.state, c = o.dealTask,
                        s = o.submitted, l = o.modalOpened;
                    return r.a.createElement("div", {className: "deal-task-form"}, r.a.createElement(G.a, {
                        visible: l,
                        dialogClassName: "modal-dialog-centered"
                    }, r.a.createElement("div", {className: "modal-header"}, r.a.createElement("h5", {className: "modal-title"}, n), r.a.createElement("button", {
                        type: "button",
                        className: "close",
                        onClick: this.handleCancel
                    }, r.a.createElement("span", {"aria-hidden": "true"}, "\xd7"))), r.a.createElement("div", {className: "modal-body"}, r.a.createElement("div", {className: "form-group" + (s && !c.subject ? " has-error" : "")}, r.a.createElement("label", {htmlFor: "subject"}, "Descri\xe7\xe3o"), r.a.createElement("input", {
                        type: "text",
                        className: "form-control",
                        autoComplete: "off",
                        name: "subject",
                        value: c.subject,
                        onChange: this.handleChange,
                        onKeyDown: this.handleKeyDown
                    })), r.a.createElement("div", {className: "form-group" + (s && !c.type ? " has-error" : "")}, r.a.createElement("label", {htmlFor: "type"}, "Tipo"), r.a.createElement("select", {
                        name: "type",
                        className: "custom-select mt-0",
                        value: c.type,
                        onChange: this.handleChange
                    }, $.map((function (e) {
                        return r.a.createElement("option", {key: e.id, value: e.id}, e.name)
                    })))), r.a.createElement("div", {className: "form-group" + (s && !c.date ? " has-error" : "")}, r.a.createElement("label", {htmlFor: "date"}, "Data"), r.a.createElement("div", {class: "has-input-icon"}, r.a.createElement(M.a, {
                        id: "date",
                        selected: c.date,
                        onChange: function (t) {
                            return e.handleDateChange(t)
                        },
                        name: "date",
                        dateFormat: "dd/MM/yyyy",
                        className: "form-control",
                        autoComplete: "off",
                        onKeyDown: this.handleKeyDown
                    }), r.a.createElement("label", {
                        htmlFor: "date",
                        className: "input-icon"
                    }, r.a.createElement("i", {className: "fa fa-calendar"})))), r.a.createElement("div", {className: "form-group" + (s && !c.hour ? " has-error" : "")}, r.a.createElement("label", {htmlFor: "hour"}, "Hora"), r.a.createElement("div", {class: "has-input-icon"}, r.a.createElement(M.a, {
                        id: "hour",
                        selected: c.hour,
                        onChange: function (t) {
                            return e.handleTimeChange(t)
                        },
                        showTimeSelect: !0,
                        showTimeSelectOnly: !0,
                        name: "hour",
                        timeIntervals: 15,
                        timeFormat: "HH:mm",
                        dateFormat: "HH:mm",
                        timeCaption: "Hora",
                        className: "form-control",
                        autoComplete: "off",
                        onKeyDown: this.handleKeyDown
                    }), r.a.createElement("label", {
                        htmlFor: "hour",
                        className: "input-icon"
                    }, r.a.createElement("i", {className: "fa fa-clock-o"}))))), r.a.createElement("div", {className: "modal-footer"}, !a && r.a.createElement("button", {
                        className: "btn btn-block",
                        onClick: this.handleSubmit
                    }, "Salvar"), !a && c._id && !c.done && r.a.createElement("button", {
                        className: "btn btn-block btn-inverse",
                        onClick: this.handleDone
                    }, "Concluir tarefa"), !a && r.a.createElement("button", {
                        className: "btn btn-block btn-cancel",
                        onClick: this.handleCancel
                    }, "Cancelar"), a && r.a.createElement(Se, null))))
                }
            }]), t
        }(r.a.Component));
        var et = Object(c.b)((function (e) {
            var t = e.deal;
            return {saving: t.saving, dealTask: t.dealTask}
        }))($e), tt = function (e) {
            function t() {
                return Object(R.a)(this, t), Object(k.a)(this, Object(L.a)(t).apply(this, arguments))
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "render", value: function () {
                    var e = this.props, t = e.currentDeal, a = e.dealTaskSaved, n = e.cancel;
                    return r.a.createElement(et, {
                        title: "Adicionar nova Tarefa",
                        currentDeal: t,
                        dealTaskSaved: a,
                        cancel: n
                    })
                }
            }]), t
        }(r.a.Component);
        var at = Object(c.b)((function (e) {
        }))(tt), nt = function (e) {
            function t() {
                return Object(R.a)(this, t), Object(k.a)(this, Object(L.a)(t).apply(this, arguments))
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "render", value: function () {
                    var e = this.props, t = e.currentDeal, a = e.currentDealTask, n = e.dealTaskSaved, o = e.cancel;
                    return r.a.createElement(et, {
                        title: "Editar Tarefa",
                        currentDeal: t,
                        currentDealTask: a,
                        dealTaskSaved: n,
                        cancel: o
                    })
                }
            }]), t
        }(r.a.Component);
        var rt = Object(c.b)((function (e) {
        }))(nt), ot = (a(194), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    currentDeal: e.currentDeal,
                    newDealTask: !1,
                    editingDealTask: null,
                    itemsToShow: 3
                }, a.dealTaskSaved = a.dealTaskSaved.bind(Object(y.a)(a)), a.cancelDealTaskEdit = a.cancelDealTaskEdit.bind(Object(y.a)(a)), a.handleNewDealTask = a.handleNewDealTask.bind(Object(y.a)(a)), a.handleEditDealTask = a.handleEditDealTask.bind(Object(y.a)(a)), a.handleShowMore = a.handleShowMore.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "componentDidMount", value: function () {
                    this.props.dispatch(ie.getDealTasks(this.state.currentDeal._id))
                }
            }, {
                key: "componentDidUpdate", value: function (e, t, a) {
                    if (e.dealTasks !== this.props.dealTasks && this.props.dealTasks) {
                        var n = this.props.dealTasks;
                        n = n.sort((function (e, t) {
                            return e.done === t.done ? new Date(e.date).getTime() - new Date(t.date).getTime() : e.done ? 1 : -1
                        })), this.setState({dealTasks: n})
                    }
                    e.currentDeal !== this.props.currentDeal && (this.setState({
                        currentDeal: this.props.currentDeal,
                        newDealTask: !1,
                        editingDealTask: null,
                        dealTasks: null,
                        itemsToShow: 3
                    }), this.props.dispatch(ie.getDealTasks(this.props.currentDeal._id)))
                }
            }, {
                key: "dealTaskSaved", value: function () {
                    var e = this;
                    this.setState({newDealTask: !1, editingDealTask: null}), setTimeout((function () {
                        e.props.dispatch(ie.getDealTasks(e.props.currentDeal._id))
                    }), 500)
                }
            }, {
                key: "cancelDealTaskEdit", value: function () {
                    this.setState({editingDealTask: null})
                }
            }, {
                key: "handleNewDealTask", value: function () {
                    this.setState({newDealTask: !this.state.newDealTask, editingDealTask: null})
                }
            }, {
                key: "handleEditDealTask", value: function (e) {
                    this.setState({newDealTask: !1, editingDealTask: e})
                }
            }, {
                key: "handleShowMore", value: function () {
                    this.setState({itemsToShow: this.state.itemsToShow + 3})
                }
            }, {
                key: "render", value: function () {
                    var e = this, t = this.state, a = t.dealTasks, n = t.newDealTask, o = t.currentDeal,
                        c = t.editingDealTask, s = t.itemsToShow, l = this.props.listingTasks, i = a && a.length > s,
                        u = r.a.createElement(at, {
                            currentDeal: o,
                            dealTaskSaved: this.dealTaskSaved,
                            cancel: this.handleNewDealTask
                        }), d = r.a.createElement(rt, {
                            currentDeal: o,
                            currentDealTask: c,
                            dealTaskSaved: this.dealTaskSaved,
                            cancel: this.cancelDealTaskEdit
                        });
                    return r.a.createElement("div", {className: "deal-tasks section"}, r.a.createElement("h2", null, "Tarefas"), n && !c && u, c && !n && d, l && r.a.createElement(Se, null), a && a.length > 0 && r.a.createElement("div", {className: "tasks mt-2"}, a.slice(0, s).map((function (t) {
                        return r.a.createElement("div", {className: "task"}, r.a.createElement("p", {className: "task-name"}, t.subject), r.a.createElement("div", {className: "task-body"}, r.a.createElement("div", {className: "row"}, r.a.createElement("div", {className: "col"}, "Data"), r.a.createElement("div", {className: "col text-right"}, ((a = new Date(t.date)).getDate() < 10 ? "0" : "") + a.getDate() + "/" + (a.getMonth() + 1 < 10 ? "0" : "") + (a.getMonth() + 1) + "/" + a.getFullYear())), r.a.createElement("div", {className: "row"}, r.a.createElement("div", {className: "col"}, "Hora"), r.a.createElement("div", {className: "col text-right"}, t.hour)), r.a.createElement("div", {className: "row"}, r.a.createElement("div", {className: "col"}, "Tipo"), r.a.createElement("div", {className: "col text-right"}, $.find((function (e) {
                            return e.id === t.type
                        })).name)), r.a.createElement("div", {className: "row"}, r.a.createElement("div", {className: "col"}, "Finalizada"), r.a.createElement("div", {className: "col text-right"}, t.done ? "Sim" : "N\xe3o")), r.a.createElement("button", {
                            className: "btn btn-block btn-edit",
                            onClick: function () {
                                e.handleEditDealTask(t)
                            }
                        }, "Editar")));
                        var a
                    }))), i && r.a.createElement("button", {
                        className: "btn btn-cancel btn-block mt-2",
                        onClick: this.handleShowMore
                    }, "Mostrar mais"), r.a.createElement("button", {
                        className: "btn btn-inverse btn-block mt-2",
                        onClick: this.handleNewDealTask
                    }, "Nova Tarefa"))
                }
            }]), t
        }(r.a.Component));
        var ct = Object(c.b)((function (e) {
            var t = e.deal;
            return {listingTasks: t.listingTasks, dealTasks: t.dealTasks}
        }))(ot), st = (a(195), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    note: "",
                    submitted: !1,
                    modalOpened: !0
                }, a.handleChange = a.handleChange.bind(Object(y.a)(a)), a.handleSubmit = a.handleSubmit.bind(Object(y.a)(a)), a.handleCloseModal = a.handleCloseModal.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "handleChange", value: function (e) {
                    var t = e.target, a = t.name, n = t.value;
                    this.setState(Object(fe.a)({}, a, n))
                }
            }, {
                key: "handleSubmit", value: function () {
                    this.setState({submitted: !0});
                    var e = this.state.note;
                    if (e) {
                        var t = this.props, a = t.dispatch, n = t.currentDeal;
                        a(ie.createDealNote(n._id, e))
                    }
                }
            }, {
                key: "handleCloseModal", value: function () {
                    this.setState({modalOpened: !1}), this.props.cancel()
                }
            }, {
                key: "componentDidUpdate", value: function (e, t, a) {
                    e.dealNote !== this.props.dealNote && this.props.dealNote && (this.setState({
                        creatingNote: !1,
                        messages: []
                    }), this.props.saved())
                }
            }, {
                key: "render", value: function () {
                    var e = this.props.savingNote, t = this.state, a = t.note, n = t.submitted, o = t.modalOpened;
                    return r.a.createElement(G.a, {
                        visible: o,
                        dialogClassName: "new-note modal-dialog-centered"
                    }, r.a.createElement("div", {className: "modal-header"}, r.a.createElement("h5", {className: "modal-title"}, "Adicionar Anota\xe7\xe3o"), r.a.createElement("button", {
                        type: "button",
                        className: "close",
                        onClick: this.handleCloseModal
                    }, r.a.createElement("span", {"aria-hidden": "true"}, "\xd7"))), r.a.createElement("div", {className: "modal-body"}, r.a.createElement("div", {className: "input-group" + (n && !a ? " has-error" : "")}, r.a.createElement("textarea", {
                        className: "form-control",
                        name: "note",
                        value: a,
                        placeholder: "Anota\xe7\xe3o...",
                        onChange: this.handleChange
                    }))), r.a.createElement("div", {className: "modal-footer"}, e && r.a.createElement(Se, null), !e && r.a.createElement("button", {
                        className: "btn btn-block",
                        disabled: !a,
                        onClick: this.handleSubmit
                    }, "Salvar"), !e && r.a.createElement("button", {
                        className: "btn btn-cancel btn-block mt-2",
                        onClick: this.handleCloseModal
                    }, "Cancelar")))
                }
            }]), t
        }(r.a.Component));
        var lt = Object(c.b)((function (e) {
            var t = e.deal;
            return {savingNote: t.savingNote, dealNote: t.dealNote}
        }))(st), it = (a(196), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    currentDeal: e.currentDeal,
                    modalOpened: !0,
                    messages: [],
                    userName: "",
                    contactName: "",
                    contactAgreement: !1,
                    error: !1
                }, a.handleSelectAll = a.handleSelectAll.bind(Object(y.a)(a)), a.handleDeselectAll = a.handleDeselectAll.bind(Object(y.a)(a)), a.handleCancel = a.handleCancel.bind(Object(y.a)(a)), a.handleChange = a.handleChange.bind(Object(y.a)(a)), a.handleContactAgreementChange = a.handleContactAgreementChange.bind(Object(y.a)(a)), a.handleSave = a.handleSave.bind(Object(y.a)(a)), a.scrollChatMessagesToBottom = a.scrollChatMessagesToBottom.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "componentDidMount", value: function () {
                    var e = this;
                    this.setState({
                        userName: h.getUserInfo().name,
                        contactName: this.props.currentContact.name
                    }), m.getCurrentChatMessages().then((function (t) {
                        e.setState({messages: t}, (function () {
                            e.scrollChatMessagesToBottom()
                        }))
                    }))
                }
            }, {
                key: "componentDidUpdate", value: function (e, t, a) {
                    e.dealNote !== this.props.dealNote && this.props.dealNote && (this.setState({
                        modalOpened: !1,
                        messages: [],
                        error: !1
                    }), this.props.saved())
                }
            }, {
                key: "handleSelectAll", value: function () {
                    for (var e = 0; e < this.state.messages.length; e++) {
                        var t = this.state.messages[e];
                        ("chat" === t.type || "image" === t.type && t.caption) && (t.checked = !0)
                    }
                    this.setState({messages: this.state.messages})
                }
            }, {
                key: "handleDeselectAll", value: function () {
                    for (var e = 0; e < this.state.messages.length; e++) {
                        this.state.messages[e].checked = !1
                    }
                    this.setState({messages: this.state.messages})
                }
            }, {
                key: "handleCancel", value: function () {
                    this.setState({modalOpened: !1, messages: []}), this.props.cancel()
                }
            }, {
                key: "handleChange", value: function (e) {
                    var t = e.target, a = t.name, n = t.checked;
                    this.state.messages.find((function (e) {
                        return e.id.id === a
                    })).checked = n, this.setState({messages: this.state.messages})
                }
            }, {
                key: "handleContactAgreementChange", value: function (e) {
                    var t = e.target.checked;
                    this.setState({contactAgreement: t})
                }
            }, {
                key: "handleSave", value: function () {
                    this.setState({error: !1});
                    for (var e, t = "", a = 0; a < this.state.messages.length; a++) if (this.state.messages[a].checked) {
                        var n = this.state.messages[a],
                            r = ((e = new Date(1e3 * n.t)).getDate() < 10 ? "0" : "") + e.getDate() + "/" + (e.getMonth() + 1 < 10 ? "0" : "") + (e.getMonth() + 1) + "/" + e.getFullYear() + " " + (e.getHours() < 10 ? "0" : "") + e.getHours() + ":" + (e.getMinutes() < 10 ? "0" : "") + e.getMinutes() + ":" + (e.getSeconds() < 10 ? "0" : "") + e.getSeconds();
                        t = "".concat(t, "[").concat(r, "] ").concat(n.id.fromMe ? this.state.userName : this.state.contactName, ": ").concat(n.caption ? n.caption : n.body, "\n")
                    }
                    if ("" !== t) {
                        this.setState({error: !1});
                        var o = this.props, c = o.dispatch, s = o.currentDeal;
                        c(ie.createDealNote(s._id, t))
                    } else this.setState({error: !0})
                }
            }, {
                key: "scrollChatMessagesToBottom", value: function () {
                    setTimeout((function () {
                        var e = document.getElementById("chatMessages");
                        e.scrollTop = e.scrollHeight
                    }), 50)
                }
            }, {
                key: "render", value: function () {
                    var e = this, t = this.props.savingNote, a = this.state, n = a.modalOpened, o = a.messages,
                        c = a.contactAgreement, s = a.error;
                    return r.a.createElement(G.a, {
                        visible: n,
                        dialogClassName: "new-note-from-chat modal-dialog-centered"
                    }, r.a.createElement("div", {className: "modal-header"}, r.a.createElement("h5", {className: "modal-title"}, "Criar Anota\xe7\xe3o"), r.a.createElement("button", {
                        type: "button",
                        className: "close",
                        onClick: this.handleCancel
                    }, r.a.createElement("span", {"aria-hidden": "true"}, "\xd7"))), r.a.createElement("div", {className: "modal-body"}, r.a.createElement("div", {className: "chat-selection"}, r.a.createElement("div", {className: "chat-selection-controls"}, r.a.createElement("button", {
                        className: "btn btn-inverse",
                        onClick: this.handleSelectAll
                    }, "Selecionar tudo"), r.a.createElement("button", {
                        className: "btn btn-inverse",
                        onClick: this.handleDeselectAll
                    }, "Apagar sele\xe7\xe3o")), r.a.createElement("div", {
                        className: "chat",
                        id: "chatMessages"
                    }, 0 === o.length && r.a.createElement(Se, null), o.length && o.map((function (t) {
                        return r.a.createElement("div", null, ("chat" === t.type || "image" === t.type && t.caption) && r.a.createElement("div", {
                            key: t.id.id,
                            className: "message " + (t.id.fromMe ? "message-from-me" : "")
                        }, r.a.createElement("input", {
                            id: "checkbox_messsge_".concat(t.id.id),
                            type: "checkbox",
                            name: t.id.id,
                            checked: t.checked,
                            onChange: e.handleChange
                        }), r.a.createElement("label", {htmlFor: "checkbox_messsge_".concat(t.id.id)}, t.caption ? t.caption : t.body)))
                    }))), s && r.a.createElement("div", {className: "help-block"}, "Selecione pelo menos uma mensagem para salvar como anota\xe7\xe3o"), r.a.createElement("div", {className: "form-check"}, r.a.createElement("input", {
                        id: "contactAgreement",
                        type: "checkbox",
                        className: "form-check-input show-discount-label",
                        value: c,
                        name: "contactAgreement",
                        onClick: this.handleContactAgreementChange
                    }), r.a.createElement("label", {htmlFor: "contactAgreement"}, "O contato deu consentimento sobre a informa\xe7\xe3o salva")))), r.a.createElement("div", {className: "modal-footer pt-0"}, !t && r.a.createElement("button", {
                        className: "btn btn-block",
                        disabled: !c,
                        onClick: this.handleSave
                    }, "Salvar como Anota\xe7\xe3o"), !t && r.a.createElement("button", {
                        className: "btn btn-block btn-cancel",
                        onClick: this.handleCancel
                    }, "Cancelar"), t && r.a.createElement(Se, null)))
                }
            }]), t
        }(r.a.Component));
        var ut = Object(c.b)((function (e) {
            var t = e.deal;
            return {savingNote: t.savingNote, dealNote: t.dealNote}
        }))(it), dt = (a(197), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    currentDeal: e.currentDeal,
                    currentContact: e.currentContact,
                    creatingNoteFromChat: !1,
                    creatingNote: !1
                }, a.handleSaved = a.handleSaved.bind(Object(y.a)(a)), a.handleCanceled = a.handleCanceled.bind(Object(y.a)(a)), a.handleNewDealNote = a.handleNewDealNote.bind(Object(y.a)(a)), a.handleNewDealNoteFromChat = a.handleNewDealNoteFromChat.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "componentDidUpdate", value: function (e, t, a) {
                    e.currentDeal !== this.props.currentDeal && this.setState({currentDeal: this.props.currentDeal})
                }
            }, {
                key: "handleNewDealNote", value: function () {
                    this.setState({creatingNoteFromChat: !1, creatingNote: !this.state.creatingNote, saved: !1})
                }
            }, {
                key: "handleNewDealNoteFromChat", value: function () {
                    this.setState({creatingNoteFromChat: !this.state.creatingNoteFromChat, creatingNote: !1, saved: !1})
                }
            }, {
                key: "handleSaved", value: function () {
                    this.setState({creatingNoteFromChat: !1, creatingNote: !1, saved: !0})
                }
            }, {
                key: "handleCanceled", value: function () {
                    this.setState({creatingNoteFromChat: !1, creatingNote: !1, saved: !1})
                }
            }, {
                key: "render", value: function () {
                    var e = this.state, t = e.currentDeal, a = e.currentContact, n = e.creatingNoteFromChat,
                        o = e.creatingNote, c = e.saved, s = r.a.createElement(ut, {
                            currentDeal: t,
                            currentContact: a,
                            saved: this.handleSaved,
                            cancel: this.handleCanceled
                        }), l = r.a.createElement(lt, {
                            currentDeal: t,
                            saved: this.handleSaved,
                            cancel: this.handleCanceled
                        });
                    return r.a.createElement("div", {className: "new-note section"}, r.a.createElement("h2", null, "Criar Anota\xe7\xe3o"), r.a.createElement("p", {class: "info"}, "Adicione uma anota\xe7\xe3o ou selecione mensagens para salvar como anota\xe7\xe3o da Oportunidade"), n && s, o && l, r.a.createElement("button", {
                        className: "btn btn-inverse btn-block",
                        onClick: this.handleNewDealNote
                    }, "Adicionar Anota\xe7\xe3o"), r.a.createElement("button", {
                        className: "btn btn-inverse btn-block mt-2",
                        onClick: this.handleNewDealNoteFromChat
                    }, "Selecionar Mensagens"), c && r.a.createElement("p", {class: "success-message"}, "Anota\xe7\xe3o criada com sucesso!"))
                }
            }]), t
        }(r.a.Component));
        var ht = Object(c.b)((function () {
            return {}
        }))(dt), mt = (a(198), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    currentDealLostReasonId: null,
                    currentDeal: e.currentDeal,
                    dealLostReasons: null,
                    currentDealDone: !1,
                    showDealLostReasons: !1,
                    showWonConfirmation: !1,
                    showLostConfirmation: !1
                }, a.handleConfirmation = a.handleConfirmation.bind(Object(y.a)(a)), a.handleCancelConfirmation = a.handleCancelConfirmation.bind(Object(y.a)(a)), a.handleCurrentDealWon = a.handleCurrentDealWon.bind(Object(y.a)(a)), a.handleShowDealLostReasons = a.handleShowDealLostReasons.bind(Object(y.a)(a)), a.handleCurrentDealLost = a.handleCurrentDealLost.bind(Object(y.a)(a)), a.handleCurrentDealLostReasonChange = a.handleCurrentDealLostReasonChange.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "componentDidMount", value: function () {
                    this.props.dispatch(ie.getDealLostReasons())
                }
            }, {
                key: "componentDidUpdate", value: function (e, t, a) {
                    e.dealLostReasons !== this.props.dealLostReasons && this.props.dealLostReasons && this.setState({dealLostReasons: this.props.dealLostReasons}), e.deal !== this.props.deal && this.props.deal && this.setState({deal: this.props.deal}), e.currentDeal !== this.props.currentDeal && this.setState({
                        currentDealLostReasonId: null,
                        currentDeal: this.props.currentDeal,
                        currentDealDone: !1,
                        showLostConfirmation: !1,
                        showWonConfirmation: !1,
                        showDealLostReasons: !1
                    })
                }
            }, {
                key: "handleCurrentDealWon", value: function () {
                    var e = this.state.currentDeal, t = this.props, a = t.dispatch, n = t.dealWonChanged;
                    e.win = !0, this.setState({
                        currentDealDone: !0,
                        currentDealLostReasonId: null,
                        showLostConfirmation: !1,
                        showWonConfirmation: !1,
                        showDealLostReasons: !1
                    }), a(ie.update(e)), n(!0)
                }
            }, {
                key: "handleCurrentDealLost", value: function () {
                    var e = this.state, t = e.currentDeal, a = e.dealLostReasons, n = e.dealLostReasonId,
                        r = this.props, o = r.dispatch, c = r.dealWonChanged, s = a.find((function (e) {
                            return e._id === n
                        }));
                    t.win = !1, t.deal_lost_reason = s, t.deal_lost_reason_id = n, this.setState({
                        currentDealDone: !0,
                        currentDealLostReasonId: null,
                        showLostConfirmation: !1,
                        showWonConfirmation: !1,
                        showDealLostReasons: !1
                    }), o(ie.update(t)), c(!1)
                }
            }, {
                key: "handleShowDealLostReasons", value: function () {
                    this.setState({showDealLostReasons: !this.state.showDealLostReasons})
                }
            }, {
                key: "handleConfirmation", value: function (e) {
                    e ? this.setState({
                        showLostConfirmation: !1,
                        showWonConfirmation: !0
                    }) : this.setState({showLostConfirmation: !0, showWonConfirmation: !1})
                }
            }, {
                key: "handleCancelConfirmation", value: function () {
                    this.setState({
                        showLostConfirmation: !1,
                        showWonConfirmation: !1,
                        showDealLostReasons: !1,
                        currentDealLostReasonId: null
                    })
                }
            }, {
                key: "handleCurrentDealLostReasonChange", value: function (e) {
                    var t = this, a = e.target.value;
                    a && this.setState({dealLostReasonId: a}, (function () {
                        t.handleConfirmation(!1)
                    }))
                }
            }, {
                key: "render", value: function () {
                    var e = this, t = this.props.saving, a = this.state, n = a.currentDeal, o = a.showDealLostReasons,
                        c = a.currentDealLostReasonId, s = a.dealLostReasons, l = a.currentDealDone,
                        i = a.showLostConfirmation, u = a.showWonConfirmation;
                    return r.a.createElement("div", null, null === n.win && r.a.createElement("div", {className: "deal-won-lost section"}, r.a.createElement("h2", null, "Venda Vendida ou Perdida"), !l && !o && !t && r.a.createElement("button", {
                        className: "mt-2 btn btn-block",
                        onClick: function () {
                            e.handleConfirmation(!0)
                        }
                    }, "Marcar como Vendida"), !l && !o && !t && r.a.createElement("button", {
                        className: "btn btn-block btn-inverse",
                        onClick: this.handleShowDealLostReasons
                    }, "Marcar como Perdida"), o && !t && s && r.a.createElement("div", null, r.a.createElement("select", {
                        className: "custom-select mt-2",
                        value: c,
                        onChange: this.handleCurrentDealLostReasonChange
                    }, r.a.createElement("option", null, "Selecione o Motivo"), s.map((function (e) {
                        return r.a.createElement("option", {key: e._id, value: e._id}, e.name)
                    }))), r.a.createElement("button", {
                        className: "btn btn-cancel btn-block mt-2",
                        onClick: this.handleShowDealLostReasons
                    }, "Cancelar")), u && r.a.createElement(ve, {
                        title: "Marcar como Vendida",
                        message: "Deseja mesmo marcar a oportunidade como vendida?",
                        okText: "Sim",
                        okHandler: this.handleCurrentDealWon,
                        cancelText: "Cancelar",
                        cancelHandler: this.handleCancelConfirmation
                    }), i && r.a.createElement(ve, {
                        title: "Marcar como Perdida",
                        message: "Deseja mesmo marcar a oportunidade como perdida?",
                        okText: "Sim",
                        okHandler: this.handleCurrentDealLost,
                        cancelText: "Cancelar",
                        cancelHandler: this.handleCancelConfirmation
                    }), t && r.a.createElement(Se, null)))
                }
            }]), t
        }(r.a.Component));
        var Et = Object(c.b)((function (e) {
            var t = e.deal;
            return {saving: t.saving, deal: t.deal, dealLostReasons: t.dealLostReasons}
        }))(mt), pt = (a(199), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    waitingForPersistence: !1,
                    currentDealId: null,
                    currentDeal: null,
                    newDeal: !1
                }, a.setCurrentDeal = a.setCurrentDeal.bind(Object(y.a)(a)), a.newDealCreated = a.newDealCreated.bind(Object(y.a)(a)), a.handleNewDeal = a.handleNewDeal.bind(Object(y.a)(a)), a.handleCurrentDealChange = a.handleCurrentDealChange.bind(Object(y.a)(a)), a.handleCurrentDealWonChange = a.handleCurrentDealWonChange.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "componentDidMount", value: function () {
                    this.props.dispatch(ie.list(this.props.contact.organization_id))
                }
            }, {
                key: "componentDidUpdate", value: function (e, t, a) {
                    var n = this;
                    if (e.deals !== this.props.deals && this.props.deals) {
                        if (this.setState({
                            deals: this.props.deals,
                            waitingForPersistence: !1
                        }), 1 === this.props.deals.length) this.setCurrentDeal(this.props.deals[0]); else {
                            for (var r = null, o = 0; o < this.props.deals.length; o++) {
                                var c = this.props.deals[o];
                                if (null === c.win) {
                                    if (null !== r) {
                                        r = null;
                                        break
                                    }
                                    r = c
                                }
                            }
                            r && this.setCurrentDeal(r)
                        }
                        if (this.state.currentDealId && !this.state.currentDeal) {
                            var s = this.props.deals.find((function (e) {
                                return e._id === n.state.currentDealId
                            }));
                            this.setCurrentDeal(s)
                        }
                    }
                }
            }, {
                key: "newDealCreated", value: function (e) {
                    var t = this;
                    setTimeout((function () {
                        t.props.dispatch(ie.list(t.props.contact.organization_id))
                    }), 800), this.setState({
                        deals: null,
                        newDeal: !1,
                        currentDeal: null,
                        currentDealId: e._id,
                        waitingForPersistence: !0
                    })
                }
            }, {
                key: "setCurrentDeal", value: function (e) {
                    this.setState({newDeal: !1, currentDeal: e, currentDealId: e._id})
                }
            }, {
                key: "handleCurrentDealChange", value: function (e) {
                    var t = e.target.value;
                    if (t) {
                        var a = this.state.deals.find((function (e) {
                            return e._id === t
                        }));
                        this.setCurrentDeal(a)
                    }
                }
            }, {
                key: "handleNewDeal", value: function () {
                    this.setState({newDeal: !this.state.newDeal})
                }
            }, {
                key: "handleCurrentDealWonChange", value: function (e) {
                    var t = this;
                    this.state.deals.find((function (e) {
                        return e._id === t.state.currentDealId
                    })).win = e, this.setState({deals: this.state.deals})
                }
            }, {
                key: "render", value: function () {
                    var e = this.state, t = e.newDeal, a = e.currentDeal, n = e.currentDealId, o = e.deals,
                        c = e.waitingForPersistence, s = this.props, l = s.listing, i = s.contact,
                        u = r.a.createElement(Me, {
                            contact: i,
                            newDealCreated: this.newDealCreated,
                            cancel: this.handleNewDeal
                        }), d = r.a.createElement(Be, {currentDeal: a}), h = r.a.createElement(Xe, {currentDeal: a}),
                        m = r.a.createElement(ct, {currentDeal: a}),
                        E = r.a.createElement(ht, {currentContact: i, currentDeal: a}),
                        p = r.a.createElement(Et, {currentDeal: a, dealWonChanged: this.handleCurrentDealWonChange});
                    return r.a.createElement("div", {className: "deal"}, (c || l) && r.a.createElement(Se, null), !c && !l && r.a.createElement("div", {className: "select-deal section"}, r.a.createElement("h2", null, "Oportunidade"), o && o.length > 0 && r.a.createElement("div", null, r.a.createElement("select", {
                        className: "custom-select",
                        value: n,
                        onChange: this.handleCurrentDealChange
                    }, r.a.createElement("option", null, "Selecione"), o.map((function (e) {
                        return r.a.createElement("option", {
                            key: e._id,
                            value: e._id
                        }, e.name, " (", null === e.win ? "Em andamento" : e.win ? "Vendida" : "Perdida", ")")
                    }))), a && r.a.createElement("a", {
                        className: "btn btn-block btn-open-deal",
                        href: K + "/app#/deals/".concat(a._id),
                        target: "_blank",
                        rel: "noopener noreferrer"
                    }, "Abrir no RD Station CRM")), !c && r.a.createElement("button", {
                        className: "btn btn-inverse btn-block mt-2",
                        onClick: this.handleNewDeal
                    }, "Nova Oportunidade")), t && u, a && r.a.createElement("div", null, d, E, h, m, p))
                }
            }]), t
        }(r.a.Component));
        var Ct = Object(c.b)((function (e) {
            return {deals: e.deal.deals}
        }))(pt), St = (a(200), function (e) {
            function t(e) {
                var a;
                Object(R.a)(this, t);
                var n = (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).props.contact,
                    r = h.getUserInfo().name.split(" ")[0];
                return a.state = {
                    currentMessageTemplateId: 0,
                    currentMessageTemplate: null,
                    templateMessages: [{
                        id: "1",
                        name: "Primeiro contato",
                        value: "Ol\xe1 ".concat(n.name, ", tudo bem? Sou ").concat(r, " e vi que se cadastrou para falar com nossa empresa.")
                    }, {
                        id: "2",
                        name: "Tentativa de contato",
                        value: "Oi ".concat(n.name, ", tentei te ligar hoje mas acredito que n\xe3o estava dispon\xedvel. Quando puder falar, me avise por aqui?")
                    }, {
                        id: "3",
                        name: "Conferir e-mail",
                        value: "Te enviei um e-mail. Pode conferir se recebeu?"
                    }, {
                        id: "4",
                        name: "D\xfavida de proposta",
                        value: "Ficou com alguma d\xfavida quanto a proposta que enviei?"
                    }, {
                        id: "5",
                        name: "Acompanhamento de proposta",
                        value: "Oi ".concat(n.name, ", teve alguma resposta quanto a proposta que te apresentei?")
                    }, {
                        id: "6",
                        name: "Lembrete de reuni\xe3o",
                        value: "Apenas para relembrar, marcamos uma reuni\xe3o para logo mais. Ela est\xe1 confirmada?"
                    }, {
                        id: "7",
                        name: "Agradecimento",
                        value: "Fico muito feliz com sua aten\xe7\xe3o. Se puder ajudar com mais alguma coisa, pode nos procurar. Atenciosamente."
                    }]
                }, a.handleMessageTemplateChange = a.handleMessageTemplateChange.bind(Object(y.a)(a)), a.handleUseIt = a.handleUseIt.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "handleMessageTemplateChange", value: function (e) {
                    var t = e.target.value;
                    t || this.setState({currentMessageTemplateId: 0, currentMessageTemplate: null});
                    var a = this.state.templateMessages.find((function (e) {
                        return e.id === t
                    }));
                    this.setState({currentMessageTemplateId: t, currentMessageTemplate: a})
                }
            }, {
                key: "handleUseIt", value: function () {
                    var e = this.state.currentMessageTemplate;
                    m.writeInChat(e.value), this.setState({currentMessageTemplateId: 0, currentMessageTemplate: null})
                }
            }, {
                key: "render", value: function () {
                    var e = this.state, t = e.currentMessageTemplateId, a = e.templateMessages,
                        n = e.currentMessageTemplate;
                    return r.a.createElement("div", null, a && r.a.createElement("div", {className: "message-template section"}, r.a.createElement("h2", null, "Mensagens prontas"), r.a.createElement("select", {
                        className: "custom-select",
                        value: t,
                        onChange: this.handleMessageTemplateChange
                    }, r.a.createElement("option", {value: 0}, "Selecione"), a.map((function (e) {
                        return r.a.createElement("option", {key: e.id, value: e.id}, e.name)
                    }))), n && r.a.createElement("div", {className: "current-message"}, r.a.createElement("p", null, n.value), r.a.createElement("button", {
                        className: "btn btn-block",
                        onClick: this.handleUseIt
                    }, "Usar no Chat"))))
                }
            }]), t
        }(r.a.Component));
        var At = Object(c.b)((function (e) {
            return {}
        }))(St), vt = (a(201), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {
                    currentWhatsappWebId: null,
                    currentWhatsappWebNumber: null,
                    relatedContact: null,
                    isNewContact: !1
                }, a.whatsAppWebChangeListener = a.whatsAppWebChangeListener.bind(Object(y.a)(a)), a.setRelatedContact = a.setRelatedContact.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "componentDidMount", value: function () {
                    this.whatsAppWebChangeListener()
                }
            }, {
                key: "whatsAppWebChangeListener", value: function () {
                    var e = this;
                    m.getCurrentChatId().then((function (t) {
                        e.state.currentWhatsappWebId !== t ? e.setState({currentWhatsappWebId: t}, (function () {
                            setTimeout((function () {
                                e.whatsAppWebChangeListener()
                            }), 500)
                        })) : setTimeout((function () {
                            e.whatsAppWebChangeListener()
                        }), 500)
                    }))
                }
            }, {
                key: "setRelatedContact", value: function (e) {
                    (0, this.props.dispatch)(se.connect(this.state.currentWhatsappWebId, {
                        id: e._id,
                        name: e.name
                    })), this.setState({isNewContact: !1, relatedContact: e})
                }
            }, {
                key: "componentDidUpdate", value: function (e, t, a) {
                    var n = this;
                    t.currentWhatsappWebId !== this.state.currentWhatsappWebId && (this.state.currentWhatsappWebId ? m.getCurrentChatPhoneNumber().then((function (e) {
                        var t = h.getRelatedContact(n.state.currentWhatsappWebId);
                        n.setState({currentWhatsappWebNumber: e, isNewContact: !1, relatedContact: null}, (function () {
                            var a = n.props.dispatch;
                            if (t) a(se.get(t.id, n.state.currentWhatsappWebId)); else {
                                var r = e.replace("+55", "");
                                a(se.searchByPhoneNumber(r))
                            }
                        }))
                    })) : this.setState({
                        isNewContact: !1,
                        currentWhatsappWebNumber: null,
                        relatedContact: null
                    })), e.contact !== this.props.contact && this.props.contact && this.setRelatedContact(this.props.contact), e.contactNotFound !== this.props.contactNotFound && this.props.contactNotFound && this.setState({
                        isNewContact: !0,
                        relatedContact: null
                    })
                }
            }, {
                key: "render", value: function () {
                    var e = this.state, t = e.isNewContact, a = e.currentWhatsappWebNumber, n = e.relatedContact,
                        o = this.props, c = o.searchingPhoneNumber, s = o.getting,
                        l = r.a.createElement(Ge, {phoneNumber: a, selectContact: this.setRelatedContact}),
                        i = r.a.createElement("div", {className: "section"}, r.a.createElement("h2", null, "Sugest\xe3o"), r.a.createElement("p", {className: "info mb-0"}, "Deixe sua sugest\xe3o para o WhatStation ", r.a.createElement("a", {
                            href: "https://pt.surveymonkey.com/r/SJKPLVB",
                            target: "_blank",
                            rel: "noopener noreferrer"
                        }, "clicando aqui")));
                    return r.a.createElement("div", {className: "home"}, !a && r.a.createElement("div", {className: "select-contact"}, r.a.createElement("p", {className: "person-icon"}, r.a.createElement("svg", {
                        xmlns: "http://www.w3.org/2000/svg",
                        viewBox: "0 0 212 212",
                        width: "212",
                        height: "212"
                    }, r.a.createElement("path", {
                        fill: "#DFE5E7",
                        d: "M106.251.5C164.653.5 212 47.846 212 106.25S164.653 212 106.25 212C47.846 212 .5 164.654.5 106.25S47.846.5 106.251.5z"
                    }), r.a.createElement("g", {fill: "#FFF"}, r.a.createElement("path", {d: "M173.561 171.615a62.767 62.767 0 0 0-2.065-2.955 67.7 67.7 0 0 0-2.608-3.299 70.112 70.112 0 0 0-3.184-3.527 71.097 71.097 0 0 0-5.924-5.47 72.458 72.458 0 0 0-10.204-7.026 75.2 75.2 0 0 0-5.98-3.055c-.062-.028-.118-.059-.18-.087-9.792-4.44-22.106-7.529-37.416-7.529s-27.624 3.089-37.416 7.529c-.338.153-.653.318-.985.474a75.37 75.37 0 0 0-6.229 3.298 72.589 72.589 0 0 0-9.15 6.395 71.243 71.243 0 0 0-5.924 5.47 70.064 70.064 0 0 0-3.184 3.527 67.142 67.142 0 0 0-2.609 3.299 63.292 63.292 0 0 0-2.065 2.955 56.33 56.33 0 0 0-1.447 2.324c-.033.056-.073.119-.104.174a47.92 47.92 0 0 0-1.07 1.926c-.559 1.068-.818 1.678-.818 1.678v.398c18.285 17.927 43.322 28.985 70.945 28.985 27.678 0 52.761-11.103 71.055-29.095v-.289s-.619-1.45-1.992-3.778a58.346 58.346 0 0 0-1.446-2.322zM106.002 125.5c2.645 0 5.212-.253 7.68-.737a38.272 38.272 0 0 0 3.624-.896 37.124 37.124 0 0 0 5.12-1.958 36.307 36.307 0 0 0 6.15-3.67 35.923 35.923 0 0 0 9.489-10.48 36.558 36.558 0 0 0 2.422-4.84 37.051 37.051 0 0 0 1.716-5.25c.299-1.208.542-2.443.725-3.701.275-1.887.417-3.827.417-5.811s-.142-3.925-.417-5.811a38.734 38.734 0 0 0-1.215-5.494 36.68 36.68 0 0 0-3.648-8.298 35.923 35.923 0 0 0-9.489-10.48 36.347 36.347 0 0 0-6.15-3.67 37.124 37.124 0 0 0-5.12-1.958 37.67 37.67 0 0 0-3.624-.896 39.875 39.875 0 0 0-7.68-.737c-21.162 0-37.345 16.183-37.345 37.345 0 21.159 16.183 37.342 37.345 37.342z"})))), r.a.createElement("p", null, "Selecione um contato para come\xe7ar"), r.a.createElement("p", {className: "finger"}, "\ud83d\udc48")), s || c ? r.a.createElement("div", {className: "mt-5"}, r.a.createElement(Se, null)) : r.a.createElement("div", null, t && l, t && i, !t && n && r.a.createElement("div", {className: "mb-3"}, r.a.createElement("div", {className: "contact-info"}, r.a.createElement("p", {className: "contact-name"}, n.name), r.a.createElement("p", {className: "contact-organization"}, n.organization.name)), r.a.createElement("div", {className: "home-content"}, r.a.createElement(At, {contact: n}), r.a.createElement(Ct, {contact: n}), i))))
                }
            }]), t
        }(r.a.Component));
        var ft = Object(c.b)((function (e) {
            var t = e.contact, a = t.contact, n = t.contactNotFound;
            return {getting: t.getting, contact: a, contactNotFound: n, searchingPhoneNumber: t.searchingPhoneNumber}
        }))(vt), gt = (a(202), function (e) {
            function t(e) {
                var a;
                return Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).props.dispatch(ce.logout()), a.state = {
                    username: "",
                    password: "",
                    submitted: !1
                }, a.handleKeyDown = a.handleKeyDown.bind(Object(y.a)(a)), a.handleChange = a.handleChange.bind(Object(y.a)(a)), a.handleSubmit = a.handleSubmit.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "handleChange", value: function (e) {
                    var t = e.target, a = t.name, n = t.value;
                    this.setState(Object(fe.a)({}, a, n))
                }
            }, {
                key: "handleSubmit", value: function (e) {
                    e.preventDefault(), this.setState({submitted: !0});
                    var t = this.state, a = t.username, n = t.password, r = this.props.dispatch;
                    a && n && r(ce.login(a, n))
                }
            }, {
                key: "handleKeyDown", value: function (e) {
                    "Enter" !== e.key || this.props.loggingIn || this.handleSubmit(e)
                }
            }, {
                key: "handleGetInstances", value: function (e) {
                    (0, this.props.dispatch)(ce.getInstances())
                }
            }, {
                key: "render", value: function () {
                    var e = this.props, t = e.loggingIn, a = e.logginError, n = this.state, o = n.username,
                        c = n.password, s = n.submitted;
                    return r.a.createElement("div", {className: "login"}, r.a.createElement("div", {className: "content-header"}, r.a.createElement("img", {
                        className: "logo",
                        src: be.a,
                        alt: "whatStation"
                    })), r.a.createElement("div", {className: "content-body"}, r.a.createElement("div", {className: "intro"}, r.a.createElement("h2", null, "Entrar")), r.a.createElement("div", {className: "login-info"}, "Preencha seu login e senha do RD Station CRM"), r.a.createElement("div", {className: "input-group has-prepend" + (s && !o || a ? " has-error" : "")}, r.a.createElement("div", {className: "input-group-prepend"}, r.a.createElement("span", {className: "input-group-text"}, r.a.createElement("i", {className: "fa fa-envelope"}))), r.a.createElement("input", {
                        type: "email",
                        autoComplete: "off",
                        autoCorrect: !1,
                        className: "form-control",
                        name: "username",
                        value: o,
                        onChange: this.handleChange,
                        onKeyDown: this.handleKeyDown,
                        placeholder: "Email"
                    })), r.a.createElement("div", {className: "input-group has-prepend" + (s && !c || a ? " has-error" : "")}, r.a.createElement("div", {className: "input-group-prepend"}, r.a.createElement("span", {className: "input-group-text"}, r.a.createElement("i", {className: "fa fa-lock"}))), r.a.createElement("input", {
                        type: "password",
                        autoComplete: "off",
                        autoCorrect: !1,
                        className: "form-control",
                        name: "password",
                        value: c,
                        onChange: this.handleChange,
                        onKeyDown: this.handleKeyDown,
                        placeholder: "Senha"
                    })), r.a.createElement("div", {className: "input-group"}, t && r.a.createElement(Se, null), !t && r.a.createElement("button", {
                        className: "btn btn-block",
                        disabled: !o || !c,
                        onClick: this.handleSubmit
                    }, "Entrar")), a && r.a.createElement("div", {className: "error"}, "Login e/ou senha errados. Entre com seu acesso do RD Station CRM."), r.a.createElement("div", {className: "signup"}, "O whatStation funciona integrado a uma ferramenta de CRM. No momento, apenas o RD Station CRM est\xe1 habilitado. ", r.a.createElement("br", null), r.a.createElement("b", null, r.a.createElement("a", {
                        href: "https://plugcrm.net/signup?utm_medium=rdstation-site&utm_source=whatstation",
                        target: "_blank",
                        rel: "noopener noreferrer"
                    }, "Crie sua conta, \xe9 gr\xe1tis.")))))
                }
            }]), t
        }(r.a.Component));
        var bt = Object(c.b)((function (e) {
            var t = e.user;
            return {loggingIn: t.loggingIn, logginError: t.logginError}
        }))(gt), _t = (a(203), function (e) {
            function t(e) {
                var a;
                Object(R.a)(this, t), (a = Object(k.a)(this, Object(L.a)(t).call(this, e))).state = {isInitializing: !0}, Object(Q.registerLocale)("pt-BR", P.a), Object(Q.setDefaultLocale)("pt-BR");
                var n = a.props.dispatch;
                return p.listen((function (e, t) {
                    n(H.clear())
                })), a.closeAlert = a.closeAlert.bind(Object(y.a)(a)), a
            }

            return Object(I.a)(t, e), Object(U.a)(t, [{
                key: "componentDidMount", value: function () {
                    var e = this;
                    h.init().then((function () {
                        e.setState({isInitializing: !1})
                    }))
                }
            }, {
                key: "componentDidUpdate", value: function (e, t, a) {
                    e.alert !== this.props.alert && this.props.alert && this.setState({newAlert: !0})
                }
            }, {
                key: "closeAlert", value: function () {
                    this.setState({newAlert: !1})
                }
            }, {
                key: "render", value: function () {
                    if (this.state.isInitializing) return null;
                    var e = this.props.alert, t = this.state.newAlert;
                    return r.a.createElement("div", {className: "app"}, r.a.createElement(G.a, {
                        className: "modal-alert",
                        dialogClassName: "modal-dialog-centered",
                        visible: t && e.message
                    }, r.a.createElement("div", {className: "modal-header"}, r.a.createElement("h5", {className: "modal-title"}, "Erro"), r.a.createElement("button", {
                        type: "button",
                        className: "close",
                        onClick: this.closeAlert
                    }, r.a.createElement("span", {"aria-hidden": "true"}, "\xd7"))), r.a.createElement("div", {className: "modal-body pb-3"}, e.message), r.a.createElement("div", {className: "modal-footer"}, r.a.createElement("button", {
                        type: "button",
                        className: "btn btn-inverse btn-block",
                        onClick: this.closeAlert
                    }, "OK"))), r.a.createElement(w.a, {history: p}, r.a.createElement(j.b, {
                        exact: !0,
                        path: "/",
                        component: Ne
                    }), r.a.createElement(j.b, {
                        exact: !0,
                        path: "/agreements",
                        component: Te
                    }), r.a.createElement(j.b, {
                        exact: !0,
                        path: "/login",
                        component: bt
                    }), r.a.createElement(Ce, {
                        exact: !0,
                        path: "/home",
                        component: ft
                    }), r.a.createElement(Ce, {exact: !0, path: "/select-instance", component: Re})))
                }
            }]), t
        }(r.a.Component));
        var Tt = Object(c.b)((function (e) {
            return {alert: e.alert}
        }))(_t);
        Object(o.render)(r.a.createElement(c.a, {store: D}, r.a.createElement(Tt, null)), document.getElementById("plugin"))
    }, 46: function (e, t, a) {
        e.exports = a.p + "static/media/logo_full_name.ff6a38fd.png"
    }, 90: function (e, t) {
        e.exports = "data:image/png;base64,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"
    }, 94: function (e, t, a) {
        e.exports = a(204)
    }
}, [[94, 1, 2]]]);
//# sourceMappingURL=main.6cbd7351.chunk.js.map