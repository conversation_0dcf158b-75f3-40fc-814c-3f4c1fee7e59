import 'package:flutter/material.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

class SemInternetPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Sem Conexão com Internet"),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              Icons.network_check,
              color: Colors.grey,
              size: 80.0,
              semanticLabel: 'Text to announce in accessibility modes',
            ),
            Text(
              "Não foi possível carregar o app. Sem Conexão com internet! Clique em atualizar para tentar novamente",
              textAlign: TextAlign.center,
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
            ),
            ElevatedButton(
              onPressed: () async {
                var status = await InternetConnectionChecker().connectionStatus;

                if( status == InternetConnectionStatus.connected ) {
                  Navigator.pop(context);
                }
              },
              child: Text('Atualizar'),
            ),
          ]
        ),
      )
    );
  }
}