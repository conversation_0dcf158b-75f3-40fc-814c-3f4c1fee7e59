var gulp = require('gulp');
const { src, dest } = require('gulp');
var replace = require('gulp-replace');
var rename = require("gulp-rename");
var clean = require('gulp-clean');
const fs = require('fs');
const { series } = require('gulp');
var download = require("gulp-download-stream");
const { exec } = require('child_process');
var log = require('fancy-log');

var argv = require('yargs').argv;

console.log(__dirname);

const nomeEmpresa = argv.empresa;
const dirDest = './' + nomeEmpresa + "/";
const dir = './../meucardapio_app/';
const nomeDoApp = argv.app;
const logo = argv.logo;

function defaultTask(cb) {
  // place code for your default task here
  cb();
}

function limparDiretorioApp() {
  return src(dirDest, {read: false, allowEmpty: true})
  .pipe(clean({force: true}));
}

function build(cb) {
  console.log('argv: ', argv);

  var caminhoPlist = dir + "../certificados/" + nomeEmpresa + "/GoogleService-Info.plist";
  var listaDeChaves = [dir + '**/*', dir + "../certificados/" + nomeEmpresa + ".jks"];

  if( argv.ios === 'true' ) {
    console.log('tirou');
    listaDeChaves.push(caminhoPlist);
  }

  src(listaDeChaves, {dot: true}).
  pipe(replace('ai.meucardapio.fibo', 'ai.meucardapio.' + nomeEmpresa)).
  pipe(replace('__NomeDoAplicativo__', nomeDoApp)).
  pipe(replace("fibo.meucardapio.ai", nomeEmpresa + ".meucardapio.ai")).
  pipe(replace("fibo.promokit.com.br", nomeEmpresa + ".promokit.com.br")).
  pipe(replace("chave.jks", nomeEmpresa + ".jks")).
  
  pipe(dest(dirDest)).on("end", async() => {
    fs.renameSync(dirDest + "android/app/src/main/kotlin/ai/meucardapio/fibo",
    dirDest + "android/app/src/main/kotlin/ai/meucardapio/" + nomeEmpresa);  

    fs.unlinkSync(dirDest + "assets/icon/icon.png");

    await download({
      file: "icon.png",
      url: logo
    })
      .pipe(dest(dirDest + "assets/icon/")).on("end", () => {
      var comando = `convert assets/icon/icon.png -alpha off assets/icon/icon.png`;

            exec(comando, {
              cwd: dirDest
            }, function(error, stdout, stderr) {
              if (error) {
                  log(error.message);
                  log(error.stack);
                  log('Error code: '+error.code);
                    log('Signal received: '+error.signal);
              }

              const pubGet = exec('flutter pub get', {
                cwd: dirDest
              }, () => {
                const ls = exec('flutter pub run flutter_launcher_icons:main', {
                  cwd: dirDest
                }, function (error , stdout, stderr) {
                  if (error) {
                    console.log(error.stack);
                    console.log('Error code: '+error.code);
                    console.log('Signal received: '+error.signal);
                  }
                  console.log('Child Process STDOUT: '+stdout);
                  console.log('Child Process STDERR: '+stderr);
                });
              });
            });
      });
  });

  console.log('build');
  cb();
}

exports.build = series(limparDiretorioApp, build);
exports.default = defaultTask;

