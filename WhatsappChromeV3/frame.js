var botao = document.getElementById("botao");

if( localStorage.getItem('urlServidor') ) {
    var url = localStorage.getItem('urlServidor');

    var div_dominio = document.getElementById('div_dominio');

    div_dominio.style.display = 'none';

    let iFrame = document.getElementById('frame');

    if( iFrame != null ) {
        iFrame.style.display = 'block';

        iFrame.src = url;
    }

    carregouIframe(url);
}

function carregouIframe(url) {
    window.parent.postMessage({tipo: 'URL_SERVIDOR', url: url}, '*');
}

function obtenhaURL(dominio) {
    if( dominio === 'localhost' ) {
        return 'https://localhost:8443';
    }

    return 'https://' + dominio + '.promokit.com.br';
}

botao.onclick = () => {
    var txt = document.getElementById("dominio");

    var url = obtenhaURL(txt.value) + '/admin/index';
    localStorage.setItem('urlServidor', url);

    let iFrame = document.getElementById('frame');

    if( iFrame != null ) {
        iFrame.style.display = 'block';

        iFrame.src = url;

        carregouIframe(url);
    }

    var div_dominio = document.getElementById('div_dominio');

    div_dominio.style.display = 'none';
}

window.onmessage = (e) => {
    console.log(e.data);

    if( e.data.tipo === 'NOVA_MENSAGEM' ) {
        console.log('nova mensagem');
        window.parent.postMessage(e.data, '*');
    } else { //enviar para o promokit
        let iFrame = document.getElementById('frame');
        iFrame.contentWindow.postMessage(e.data, '*');
    }
}