._10V4p._1jxtm {
  border-right: solid 1px #ececec;
}

html[dir] .app-wrapper-web ._1Jzz1 {
  box-shadow: none !important;
}

#app .two {
  width: 100% !important;
}

#app .three {
  width: 100% !important;
}

.app._1Jzz1 {
  width: 100%;
}

.app {
  width: 100% !important;
}

body {
  box-shadow: 0 1px 1px 0 rgba(0,0,0,.06), 0 2px 5px 0 rgba(0,0,0,.2) !important;
  height: calc(100% - 19px) !important;
}

#frame_promokit {
  position: fixed;
  top: 0;
  right: 0;
  display: block;
  height: 100%;
}

@media (min-width: 1441px) {
  #frame_promokit {
    height: calc(100% - 19px) !important;
    min-height: 500px;
  }
}
