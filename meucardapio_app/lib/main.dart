import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:http/http.dart' as http;
import 'seminternet.dart';
import 'package:geolocator/geolocator.dart';
import 'package:app_tracking_transparency/app_tracking_transparency.dart';

String url_inicial = '';
String urlServidor = 'https://fibo.promokit.com.br';

/*
class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host,
          int port) => true;
  }
}
}
 */

MyApp? myApp;
final navigatorKey = GlobalKey<NavigatorState>();

AndroidNotificationChannel? channel;
FlutterLocalNotificationsPlugin? flutterLocalNotificationsPlugin;

bool isFlutterLocalNotificationsInitialized = false;

void showFlutterNotification(RemoteMessage message) {
  RemoteNotification? notification = message.notification;
  AndroidNotification? android = message.notification?.android;
  if (notification != null && android != null) {
    flutterLocalNotificationsPlugin!.show(
      notification.hashCode,
      notification.title,
      notification.body,
      NotificationDetails(
        android: AndroidNotificationDetails(
          channel!.id,
          channel!.name,
          channelDescription: channel!.description,
          // TODO add a proper drawable resource to android, for now using
          //      one that already exists in example app.
          icon: 'launch_background',
        ),
      ),
    );
  }
}

Future<void> setupFlutterNotifications() async {
  if ( isFlutterLocalNotificationsInitialized) {
    return;
  }
  channel = const AndroidNotificationChannel(
      'high_importance_channel', // id
      'High Importance Notifications', // title
      description:
      'Usado para notificações importantes', // description
      importance: Importance.high
  );

  flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  /// Create an Android Notification Channel.
  ///
  /// We use this channel in the `AndroidManifest.xml` file to override the
  /// default FCM channel to enable heads up notifications.
  await flutterLocalNotificationsPlugin!
      .resolvePlatformSpecificImplementation<
      AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel!);

  /// Update the iOS foreground notification presentation options to allow
  /// heads up notifications.
  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: false,
    sound: true,
  );
  isFlutterLocalNotificationsInitialized = true;
}

void main() async {
  //HttpOverrides.global = new MyHttpOverrides();

  url_inicial = 'https://fibo.meucardapio.ai';

  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Tente inicializar o Firebase com opções padrão
    await Firebase.initializeApp(
      options: FirebaseOptions(
        apiKey: 'AIzaSyDummyApiKey123456789',
        appId: '1:123456789:android:123456789',
        messagingSenderId: '123456789',
        projectId: 'dummy-project-id',
      ),
    );

    await setupFlutterNotifications();

    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    firebaseCloudMessaging_Listeners();
  } catch (e) {
    print("Erro ao inicializar Firebase: $e");
    // Continue sem Firebase
  }

  _requestTrackingAuthorization();
  myApp = MyApp();
  runApp(myApp!);
}

Future<void> _requestTrackingAuthorization() async {
  // Verifica o status atual da permissão

  final status = await AppTrackingTransparency.trackingAuthorizationStatus;
  if (status == TrackingStatus.notDetermined) {
    // Solicita permissão
    final status = await AppTrackingTransparency.requestTrackingAuthorization();
    // Lide com o status da permissão conforme necessário
    print("Status de autorização de rastreamento: $status");
  }
}

void firebaseCloudMessaging_Listeners() async {
  FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  /*
  /// Create an Android Notification Channel.
  ///
  /// We use this channel in the `AndroidManifest.xml` file to override the
  /// default FCM channel to enable heads up notifications.
  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
      AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);
   */
  /// Update the iOS foreground notification presentation options to allow
  /// heads up notifications.
  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );

  NotificationSettings settings = await _firebaseMessaging.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );
}

showAlertDialog(BuildContext context, String mensagem) {
  /*
  if( context == null ) {
    return;
  }

  // set up the button
  Widget okButton = FlatButton(
    child: Text("OK"),
    onPressed: () { },
  );

  // set up the AlertDialog
  AlertDialog alert = AlertDialog(
    title: Text(""),
    content: SelectableText(mensagem),
    actions: [
      okButton,
    ],
  );

  // show the dialog
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return alert;
    },
  );
   */
  print('Alert Message: ' + mensagem);
}

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    await Firebase.initializeApp(
      options: FirebaseOptions(
        apiKey: 'AIzaSyDummyApiKey123456789',
        appId: '1:123456789:android:123456789',
        messagingSenderId: '123456789',
        projectId: 'dummy-project-id',
      ),
    );
    // If you're going to use other Firebase services in the background, such as Firestore,
    // make sure you call `initializeApp` before using other Firebase services.
    String url = message.data["LINK"];

    if( url != null ) {
      url_inicial = url;
    }

    print("Alert Message: Handling a background message: ${message.messageId}: " + url_inicial);
  } catch (e) {
    print("Erro ao inicializar Firebase no background: $e");
  }
}

class MyApp extends StatelessWidget {
  MyApp() {

  }
  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    //if (Platform.isAndroid) WebView.platform = SurfaceAndroidWebView();

    /*
    FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

    _firebaseMessaging.getToken().then((token) {
      showAlertDialog(context, "token: $token");
    });
*/

    const MaterialColor white = const MaterialColor(
        0xFFFFFFFF,
        const <int, Color>{
          50: const Color(0xFFFFFFFF),
          100: const Color(0xFFFFFFFF),
          200: const Color(0xFFFFFFFF),
          300: const Color(0xFFFFFFFF),
          400: const Color(0xFFFFFFFF),
          500: const Color(0xFFFFFFFF),
          600: const Color(0xFFFFFFFF),
          700: const Color(0xFFFFFFFF),
          800: const Color(0xFFFFFFFF),
          900: const Color(0xFFFFFFFF),
        });


    SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          //color set to transperent or set your own color
          //set brightness for icons, like dark background light icons
        )
    );
    return MaterialApp(
      title: 'Flutter Demo',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        // This is the theme of your application.
        //
        // Try running your application with "flutter run". You'll see the
        // application has a blue toolbar. Then, without quitting the app, try
        // changing the primarySwatch below to Colors.green and then invoke
        // "hot reload" (press "r" in the console where you ran "flutter run",
        // or simply save your changes to "hot reload" in a Flutter IDE).
        // Notice that the counter didn't reset back to zero; the application
        // is not restarted.
        primarySwatch: white,
        // This makes the visual density adapt to the platform that you run
        // the app on. For desktop platforms, the controls will be smaller and
        // closer together (more dense) than on mobile platforms.
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: MyHomePage(title: 'Flutter Demo Home Page'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  MyHomePage({Key? key, required this.title}) : super(key: key);

  // This widget is the home page of your application. It is stafiteful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  final String title;

  @override
  _MyHomePageState createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _counter = 0;
  final GlobalKey webViewKey = GlobalKey();
  InAppWebViewController? webViewController;

  FirebaseMessaging? _firebaseMessaging;

  StreamSubscription<InternetConnectionStatus>? listener;

  InAppWebView? webview;

  bool carregando = true;

  Future<Position> obtenhaLocalizacao() async {
    bool serviceEnabled;
    LocationPermission permission;

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        this.notifiqueWebApp('negouGPS');
        // Permissions are denied, next time you could try
        // requesting permissions again (this is also where
        // Android's shouldShowRequestPermissionRationale
        // returned true. According to Android guidelines
        // your App should show an explanatory UI now.
        return Future.error('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      // Permissions are denied forever, handle appropriately.
      this.notifiqueWebApp('negouGPSParaSempre');
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    // When we reach here, permissions are granted and we can
    // continue accessing the position of the device.
    return await Geolocator.getCurrentPosition();
  }

  @override
  void initState() {
    super.initState();

    //obtenhaLocalizacao();

    final InAppReview inAppReview = InAppReview.instance;

    inAppReview.isAvailable().then((disponivel) {
      if( disponivel ) {
        inAppReview.requestReview();
      }
    });

    // Get any messages which caused the application to open from
    // a terminated state.
    FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? initialMessage) {
      if( initialMessage != null ) {
        url_inicial = initialMessage.data['LINK'];

        webViewController!.loadUrl(urlRequest: URLRequest(url: WebUri(url_inicial)));
      }
    });

    FirebaseMessaging.onMessage.listen(showFlutterNotification);

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
      await Firebase.initializeApp();
      // If you're going to use other Firebase services in the background, such as Firestore,
      // make sure you call `initializeApp` before using other Firebase services.
      String url = message.data["LINK"];

      url_inicial = url;

      print("Alert Message: Apertou na notificação");
    });
  }
  /*
  @override
  void initState() {
    super.initState();




  }
*/

  @override
  void dispose() {
    listener!.cancel();
    super.dispose();
  }

  checkInternet() async {
    // Simple check to see if we have internet
    print("The statement 'this machine is connected to the Internet' is: ");
    print(await InternetConnectionChecker().hasConnection);
    // returns a bool

    // We can also get an enum value instead of a bool
    print("Current status: ${await InternetConnectionChecker().connectionStatus}");
    // prints either DataConnectionStatus.connected
    // or DataConnectionStatus.disconnected

    // This returns the last results from the last call
    // to either hasConnection or connectionStatus
    //print("Last results: ${InternetConnectionChecker().lastTryResults}");

    // actively listen for status updates
    // this will cause DataConnectionChecker to check periodically
    // with the interval specified in DataConnectionChecker().checkInterval
    // until listener.cancel() is called
    listener = InternetConnectionChecker().onStatusChange.listen((status) {
      switch (status) {
        case InternetConnectionStatus.connected:
          print('Data connection is available.');
          break;
        case InternetConnectionStatus.disconnected:
          print('You are disconnected from the internet.');
          break;
      }
    });

    return await InternetConnectionChecker().connectionStatus;
  }

  _MyHomePageState() {
  }

  Widget crie() {
    return carregando ?  Center(
        child: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          color: Colors.white,
          child: Center(
              child: Container(
                child: CircularProgressIndicator(
                  strokeWidth: 14,
                ),
                height: 105,
                width: 105,
              )

          ),
        )
    )
        : Center();
  }

  notifiqueWebApp(String nomeEvento) {
    this.webViewController!.evaluateJavascript(source: """
    const event = new CustomEvent("$nomeEvento", {
      detail: {foo: 1, bar: false}
    });
    window.dispatchEvent(event);
  """);
  }

  salveToken(String token, Map dadosUsuario) async {
    var url = urlServidor + '/api/insiraToken';

    var dados = <String, dynamic>{
      'token': token
    };

    if( dadosUsuario != null ) {
      dados['contato'] = {
        'id': dadosUsuario['id']
      };
    }

    var response = await http.post(Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode(dados)
    );
    print('Url: ${url}');
    print('Response status: ${response.statusCode}');
    print('Response body: ${response.body}');
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    //showAlertDialog(context, "criando webview: " + url_inicial);

    InAppWebViewGroupOptions options = InAppWebViewGroupOptions(
        crossPlatform: InAppWebViewOptions(
          useShouldOverrideUrlLoading: true,
          mediaPlaybackRequiresUserGesture: false,
        ),
        android: AndroidInAppWebViewOptions(
          useHybridComposition: true,
        ),
        ios: IOSInAppWebViewOptions(
          allowsInlineMediaPlayback: true,
        ));

    webview = InAppWebView(
      key: webViewKey,
      initialUrlRequest:
      URLRequest(url: WebUri(url_inicial)),
      initialOptions: options,
      onWebViewCreated: (controller) {
        webViewController = controller;

        FirebaseMessaging.instance.getToken().then((value) {
          FirebaseMessaging.instance.subscribeToTopic("fibo.meucardapio.ai").then( (valor) => {
            print("valor: ")
          });
        });

        // register a JavaScript handler with name "myHandlerName"
        controller.addJavaScriptHandler(handlerName: 'ativarGPS', callback: (args) async {
          // print arguments coming from the JavaScript side!
          var posicao = await obtenhaLocalizacao();

          this.notifiqueWebApp('ativouGPS');

          return posicao;
        });
        controller.addJavaScriptHandler(handlerName: 'logou', callback: (List<dynamic> args) async {
          setState(() {
            carregando = false;
            var objMensagem = jsonDecode(args[0].message);

            var tipoDeMensagem = objMensagem['tipo'];
            var usuario = null;

            if( tipoDeMensagem == 'usuario' ) {
              usuario = objMensagem['usuario'];
            }

            FirebaseMessaging.instance.getToken().then((value) {
              salveToken(value!, usuario);
            });
          });
        });
      },
      onLoadStart: (controller, url) {
        setState(() {
          carregando = false;
        });
      },
      androidOnGeolocationPermissionsShowPrompt:
          (InAppWebViewController controller, String origin) async {
        return GeolocationPermissionShowPromptResponse(
            origin: origin,
            allow: true,
            retain: true
        );
      },
      androidOnPermissionRequest: (controller, origin, resources) async {
        return PermissionRequestResponse(
            resources: resources,
            action: PermissionRequestResponseAction.GRANT);
      },
      shouldOverrideUrlLoading: (controller, navigationAction) async {
        var uri = navigationAction.request.url;

        if (![ "http", "https", "file", "chrome",
          "data", "javascript", "about"].contains(uri!.scheme)) {
          if (await canLaunch(uri.toString())) {
            // Launch the App
            await _launchURL(uri.toString());

            // and cancel the request
            return NavigationActionPolicy.CANCEL;
          }
        }

        return NavigationActionPolicy.ALLOW;
      },
      onLoadStop: (controller, url) async {

      },
      onLoadError: (controller, url, code, message) {
        //pullToRefreshController.endRefreshing();
      },
      onProgressChanged: (controller, progress) {
      },
      onUpdateVisitedHistory: (controller, url, androidIsReload) {

      },
      onConsoleMessage: (controller, consoleMessage) {
        print(consoleMessage);
      },
    );

    return WillPopScope(
      child: Scaffold(
        appBar: AppBar(
          toolbarHeight: 0,
          elevation: 0,
        ),
        body: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            child: Stack(
              children: [
                webview!,
                crie()
              ],
            )
        ),
      ),
      onWillPop: () async {
        return _onWillPop(context);
      },
    );
  }

  _launchURL(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  Future<bool> _onWillPop(BuildContext context) async {
    //TODO: back do android
    if (await webViewController!.canGoBack()) {
      webViewController!.goBack();
      return Future.value(false);
    } else {
      return Future.value(true);
    }
  }
}
