{"version": 3, "file": "polyfills.170025868d7e0763.js", "mappings": "8GAiQsCA,MAiNtC,MA2GMC,GAAc,SAAUC,KAAiBC,GAC3C,GAAIF,GAAYG,UAAW,CAEvB,MAAMC,EAAcJ,GAAYG,UAAUF,EAAcC,GACxDD,EAAeG,EAAY,GAC3BF,EAAcE,EAAY,EAC9B,CACA,IAAIC,EAAUC,GAAWL,EAAa,GAAIA,EAAaM,IAAI,IAC3D,QAASC,EAAI,EAAGA,EAAIP,EAAaQ,OAAQD,IACrCH,GAAWH,EAAYM,EAAI,GAAKF,GAAWL,EAAaO,GAAIP,EAAaM,IAAIC,IAEjF,OAAOH,CACX,EAeA,SAASC,GAAWI,EAAaC,GAC7B,MAfiB,MAeVA,EAAeC,OAAO,GACzBF,EAAYG,UA5WpB,SAASC,GAAeC,EAAQR,GAC5B,QAASS,EAAc,EAAGC,EAAW,EAAGD,EAAcD,EAAON,OAAQO,IAAeC,IAChF,GAAsB,OAAlBV,EAAIU,GACJA,YArNW,MAuNNF,EAAOC,GACZ,OAAOA,EAGf,MAAM,IAAIjB,MAAM,6CAA6CQ,MACjE,CAkW8BO,CAAeJ,EAAaC,GAAkB,GACpED,CACR,CA1IkC,YAAcQ,WAAe,KAAeA,mBAClEC,OAAW,KAAeA,eAAmBC,OAAW,KAAeA,eACvEC,KAAS,YAAsBC,kBAAsB,KACzDD,gBAAgBC,mBAAqBD,KAHX,GCjclCE,UAAqBC,0BCJrB,SAAYL,GACR,MAAMM,EAAcN,EAAOM,YAC3B,SAASC,EAAKC,IACVF,GAAeA,EAAYC,MAAWD,EAAYC,KAAQC,GAC9D,CACA,SAASC,EAAmBD,GAAME,GAC9BJ,GAAeA,EAAYK,SAAcL,EAAYK,QAAWH,GAAME,EAC1E,CACAH,EAAK,QAIL,MAAMK,EAAeZ,EAAOa,sBAA2B,kBACvD,SAASC,EAAWN,IAChB,OAAOI,EAAeJ,EAC1B,CACA,MAAMO,GAAmE,IAAlDf,EAAOc,EAAW,4BACzC,GAAId,EAAOgB,KAAS,CAUhB,GAAID,GAAuD,mBAA9Bf,EAAOgB,KAAQF,WACxC,MAAM,IAAIlC,MAAM,wBAGhB,OAAOoB,EAAOgB,IAEtB,CAAC,IACKA,EAAI,MAAV,MAAMA,GACFC,YAAYC,EAAQC,GAChBC,KAAKC,QAAUH,EACfE,KAAKE,MAAQH,EAAWA,EAASX,MAAQ,UAAY,SACrDY,KAAKG,YAAcJ,GAAYA,EAASK,YAAc,CAAC,EACvDJ,KAAKK,cACD,IAAIC,EAAcN,KAAMA,KAAKC,SAAWD,KAAKC,QAAQI,cAAeN,EAC5E,CACAQ,2BACI,GAAI3B,EAAO4B,UAAeC,GAAQC,iBAC9B,MAAM,IAAIlD,MAAM,gSAMxB,CACWmD,kBACP,IAAIC,EAAOhB,GAAKiB,QAChB,KAAOD,EAAKd,QACRc,EAAOA,EAAKd,OAEhB,OAAOc,CACX,CACWC,qBACP,OAAOC,GAAkBF,IAC7B,CACWG,yBACP,OAAOC,EACX,CAEAT,oBAAoBnB,EAAM6B,EAAIC,GAAkB,GAC5C,GAAIT,GAAQU,eAAe/B,IAIvB,IAAK8B,GAAmBvB,EACpB,MAAMnC,MAAM,yBAA2B4B,QAAI,IAGzCR,EAAO,kBAAoBQ,GAAO,CACxC,MAAMgC,GAAW,QAAUhC,EAC3BD,EAAKiC,IACLX,GAAQrB,GAAQ6B,EAAGrC,EAAQgB,GAAMyB,IACjChC,EAAmB+B,GAAUA,GACjC,CACJ,CACItB,aACA,OAAOE,KAAKC,OAChB,CACIb,WACA,OAAOY,KAAKE,KAChB,CACAoB,IAAIC,GACA,MAAMX,EAAOZ,KAAKwB,YAAYD,GAC9B,GAAIX,EACA,OAAOA,EAAKT,YAAYoB,EAChC,CACAC,YAAYD,GACR,IAAIV,EAAUb,KACd,KAAOa,GAAS,CACZ,GAAIA,EAAQV,YAAYgB,eAAeI,GACnC,OAAOV,EAEXA,EAAUA,EAAQZ,OACtB,CACA,OAAO,IACX,CACAwB,KAAK1B,GACD,IAAKA,EACD,MAAM,IAAIvC,MAAM,sBACpB,OAAOwC,KAAKK,cAAcoB,KAAKzB,KAAMD,EACzC,CACA2B,KAAKC,EAAUC,GACX,GAAwB,mBAAbD,EACP,MAAM,IAAInE,MAAM,2BAA6BmE,GAEjD,MAAME,EAAY7B,KAAKK,cAAcyB,UAAU9B,KAAM2B,EAAUC,GACzDhB,GAAOZ,KACb,OAAO,WACH,OAAOY,GAAKmB,WAAWF,EAAW7B,KAAMgC,UAAWJ,EACvD,CACJ,CACAK,IAAIN,EAAUO,EAAWC,EAAWP,IAChCd,GAAoB,CAAEhB,OAAQgB,GAAmBF,KAAMZ,MACvD,IACI,OAAOA,KAAKK,cAAc+B,OAAOpC,KAAM2B,EAAUO,EAAWC,EAAWP,GAI3E,CAHA,QAEId,GAAoBA,GAAkBhB,MAC1C,CACJ,CACAiC,WAAWJ,EAAUO,EAAY,KAAMC,EAAWP,IAC9Cd,GAAoB,CAAEhB,OAAQgB,GAAmBF,KAAMZ,MACvD,IACI,IACI,OAAOA,KAAKK,cAAc+B,OAAOpC,KAAM2B,EAAUO,EAAWC,EAAWP,GAM3E,CALA,MACOS,GACH,GAAIrC,KAAKK,cAAciC,YAAYtC,KAAMqC,GACrC,MAAMA,CAEd,CAIJ,CAHA,QAEIvB,GAAoBA,GAAkBhB,MAC1C,CACJ,CACAyC,QAAQC,EAAMN,EAAWC,GACrB,GAAIK,EAAK5B,MAAQZ,KACb,MAAM,IAAIxC,MAAM,+DACXgF,EAAK5B,MAAQ6B,IAASrD,KAAO,gBAAkBY,KAAKZ,KAAO,KAKpE,GAAIoD,EAAKE,QAAUC,IAAiBH,EAAKI,OAASC,IAAaL,EAAKI,OAASE,IACzE,OAEJ,MAAMC,GAAeP,EAAKE,OAASM,EACnCD,IAAgBP,EAAKS,cAAcD,EAASE,IAC5CV,EAAKW,WACL,MAAMC,EAAepC,GACrBA,GAAewB,EACf1B,GAAoB,CAAEhB,OAAQgB,GAAmBF,KAAMZ,MACvD,IACQwC,EAAKI,MAAQE,IAAaN,EAAKa,OAASb,EAAKa,KAAKC,aAClDd,EAAKe,cAAWC,GAEpB,IACI,OAAOxD,KAAKK,cAAcoD,WAAWzD,KAAMwC,EAAMN,EAAWC,EAMhE,CALA,MACOE,GACH,GAAIrC,KAAKK,cAAciC,YAAYtC,KAAMqC,GACrC,MAAMA,CAEd,CAkBJ,CAjBA,QAIQG,EAAKE,QAAUC,GAAgBH,EAAKE,QAAUgB,IAC1ClB,EAAKI,MAAQC,IAAcL,EAAKa,MAAQb,EAAKa,KAAKC,WAClDP,IAAgBP,EAAKS,cAAcC,GAAWF,IAG9CR,EAAKW,SAAW,EAChBnD,KAAK2D,iBAAiBnB,GAAM,GAC5BO,IACIP,EAAKS,cAAcN,EAAcK,EAASL,KAGtD7B,GAAoBA,GAAkBhB,OACtCkB,GAAeoC,CACnB,CACJ,CACAQ,aAAapB,GACT,GAAIA,EAAK5B,MAAQ4B,EAAK5B,OAASZ,KAAM,CAGjC,IAAI6D,EAAU7D,KACd,KAAO6D,GAAS,CACZ,GAAIA,IAAYrB,EAAK5B,KACjB,MAAMpD,MAAM,8BAA8BwC,KAAKZ,kDAAkDoD,EAAK5B,KAAKxB,QAE/GyE,EAAUA,EAAQ/D,MACtB,CACJ,CACA0C,EAAKS,cAAca,GAAYnB,GAC/B,MAAMoB,EAAgB,GACtBvB,EAAKwB,eAAiBD,EACtBvB,EAAKyB,MAAQjE,KACb,IACIwC,EAAOxC,KAAKK,cAAcuD,aAAa5D,KAAMwC,EASjD,CARA,MACO0B,GAGH1B,QAAKS,cAAcS,EAASI,GAAYnB,GAExC3C,KAAKK,cAAciC,YAAYtC,KAAMkE,GAC/BA,CACV,CACA,OAAI1B,EAAKwB,iBAAmBD,GAExB/D,KAAK2D,iBAAiBnB,EAAM,GAE5BA,EAAKE,OAASoB,IACdtB,EAAKS,cAAcC,GAAWY,IAE3BtB,CACX,CACA2B,kBAAkBvC,EAAQD,EAAU0B,EAAMe,IACtC,OAAOpE,KAAK4D,aAAa,IAAIS,EAASC,EAAW1C,EAAQD,EAAU0B,EAAMe,QAAgBZ,GAC7F,CACAe,kBAAkB3C,EAAQD,EAAU0B,EAAMe,GAAgBI,GACtD,OAAOxE,KAAK4D,aAAa,IAAIS,EAASvB,GAAWlB,EAAQD,EAAU0B,EAAMe,GAAgBI,GAC7F,CACAC,kBAAkB7C,EAAQD,EAAU0B,EAAMe,GAAgBI,GACtD,OAAOxE,KAAK4D,aAAa,IAAIS,EAASxB,GAAWjB,EAAQD,EAAU0B,EAAMe,GAAgBI,GAC7F,CACAE,WAAWlC,GACP,GAAIA,EAAK5B,MAAQZ,KACb,MAAM,IAAIxC,MAAM,qEACXgF,EAAK5B,MAAQ6B,IAASrD,KAAO,gBAAkBY,KAAKZ,KAAO,KACpEoD,EAAKS,cAAc0B,GAAWzB,GAAWF,GACzC,IACIhD,KAAKK,cAAcqE,WAAW1E,KAAMwC,EAOxC,CANA,MACO0B,GAEH1B,QAAKS,cAAcS,EAASiB,IAC5B3E,KAAKK,cAAciC,YAAYtC,KAAMkE,GAC/BA,CACV,CACA,YAAKP,iBAAiBnB,GAAM,GAC5BA,EAAKS,cAAcN,EAAcgC,IACjCnC,EAAKW,SAAW,EACTX,CACX,CACAmB,iBAAiBnB,EAAMoC,GACnB,MAAMb,EAAgBvB,EAAKwB,gBACd,GAATY,IACApC,EAAKwB,eAAiB,MAE1B,QAAS/F,GAAI,EAAGA,GAAI8F,EAAc7F,OAAQD,KACtC8F,EAAc9F,IAAG0F,iBAAiBnB,EAAKI,KAAMgC,EAErD,EAGJhF,UAAKF,WAAaA,EAvOZE,EAAI,KAwOV,MAAMiF,EAAc,CAChBzF,KAAM,GACN0F,UAAWA,CAACC,GAAUC,EAAGC,EAAQC,IAAiBH,GAASI,QAAQF,EAAQC,GAC3EE,eAAgBA,CAACL,GAAUC,EAAGC,EAAQzC,IAASuC,GAASnB,aAAaqB,EAAQzC,GAC7E6C,aAAcA,CAACN,GAAUC,EAAGC,EAAQzC,EAAMN,EAAWC,KAAc4C,GAAStB,WAAWwB,EAAQzC,EAAMN,EAAWC,IAChHmD,aAAcA,CAACP,GAAUC,EAAGC,EAAQzC,IAASuC,GAASL,WAAWO,EAAQzC,IAE7E,MAAMlC,EACFT,YAAYe,EAAM2E,EAAgBxF,GAC9BC,KAAKwF,YAAc,CAAElB,UAAa,EAAGxB,UAAa,EAAGD,UAAa,GAClE7C,KAAKY,KAAOA,EACZZ,KAAKyF,gBAAkBF,EACvBvF,KAAK0F,QAAU3F,IAAaA,GAAYA,EAAS4F,OAAS5F,EAAWwF,EAAeG,SACpF1F,KAAK4F,UAAY7F,IAAaA,EAAS4F,OAASJ,EAAiBA,EAAeK,WAChF5F,KAAK6F,cACD9F,IAAaA,EAAS4F,OAAS3F,KAAKY,KAAO2E,EAAeM,eAC9D7F,KAAK8F,aACD/F,IAAaA,EAASgG,YAAchG,EAAWwF,EAAeO,cAClE9F,KAAKgG,eACDjG,IAAaA,EAASgG,YAAcR,EAAiBA,EAAeS,gBACxEhG,KAAKiG,mBACDlG,IAAaA,EAASgG,YAAc/F,KAAKY,KAAO2E,EAAeU,oBACnEjG,KAAKkG,UAAYnG,IAAaA,EAASoG,SAAWpG,EAAWwF,EAAeW,WAC5ElG,KAAKoG,YACDrG,IAAaA,EAASoG,SAAWZ,EAAiBA,EAAea,aACrEpG,KAAKqG,gBACDtG,IAAaA,EAASoG,SAAWnG,KAAKY,KAAO2E,EAAec,iBAChErG,KAAKsG,eACDvG,IAAaA,EAASwG,cAAgBxG,EAAWwF,EAAee,gBACpEtG,KAAKwG,iBACDzG,IAAaA,EAASwG,cAAgBhB,EAAiBA,EAAeiB,kBAC1ExG,KAAKyG,qBACD1G,IAAaA,EAASwG,cAAgBvG,KAAKY,KAAO2E,EAAekB,sBACrEzG,KAAK0G,gBACD3G,IAAaA,EAASqF,eAAiBrF,EAAWwF,EAAemB,iBACrE1G,KAAK2G,kBAAoB5G,IACpBA,EAASqF,eAAiBG,EAAiBA,EAAeoB,mBAC/D3G,KAAK4G,sBACD7G,IAAaA,EAASqF,eAAiBpF,KAAKY,KAAO2E,EAAeqB,uBACtE5G,KAAK6G,cACD9G,IAAaA,EAASsF,aAAetF,EAAWwF,EAAesB,eACnE7G,KAAK8G,gBACD/G,IAAaA,EAASsF,aAAeE,EAAiBA,EAAeuB,iBACzE9G,KAAK+G,oBACDhH,IAAaA,EAASsF,aAAerF,KAAKY,KAAO2E,EAAewB,qBACpE/G,KAAKgH,cACDjH,IAAaA,EAASuF,aAAevF,EAAWwF,EAAeyB,eACnEhH,KAAKiH,gBACDlH,IAAaA,EAASuF,aAAeC,EAAiBA,EAAe0B,iBACzEjH,KAAKkH,oBACDnH,IAAaA,EAASuF,aAAetF,KAAKY,KAAO2E,EAAe2B,qBACpElH,KAAKmH,WAAa,KAClBnH,KAAKoH,aAAe,KACpBpH,KAAKqH,kBAAoB,KACzBrH,KAAKsH,iBAAmB,KACxB,MAAMC,EAAkBxH,GAAYA,EAAS+E,WAEzCyC,GADkBhC,GAAkBA,EAAe4B,cAInDnH,KAAKmH,WAAaI,EAAkBxH,EAAW8E,EAC/C7E,KAAKoH,aAAe7B,EACpBvF,KAAKqH,kBAAoBrH,KACzBA,KAAKsH,iBAAmB1G,EACnBb,EAASqF,iBACVpF,KAAK0G,gBAAkB7B,EACvB7E,KAAK2G,kBAAoBpB,EACzBvF,KAAK4G,sBAAwB5G,KAAKY,MAEjCb,EAASsF,eACVrF,KAAK6G,cAAgBhC,EACrB7E,KAAK8G,gBAAkBvB,EACvBvF,KAAK+G,oBAAsB/G,KAAKY,MAE/Bb,EAASuF,eACVtF,KAAKgH,cAAgBnC,EACrB7E,KAAKiH,gBAAkB1B,EACvBvF,KAAKkH,oBAAsBlH,KAAKY,MAG5C,CACAa,KAAK+F,EAAYzH,GACb,OAAOC,KAAK0F,QAAU1F,KAAK0F,QAAQC,OAAO3F,KAAK4F,UAAW5F,KAAKY,KAAM4G,EAAYzH,GAC7E,IAAIH,EAAK4H,EAAYzH,EAC7B,CACA+B,UAAU0F,EAAY7F,EAAUC,GAC5B,OAAO5B,KAAK8F,aACR9F,KAAK8F,aAAaC,YAAY/F,KAAKgG,eAAgBhG,KAAKiG,mBAAoBuB,EAAY7F,EAAUC,GAClGD,CACR,CACAS,OAAOoF,EAAY7F,EAAUO,EAAWC,EAAWP,IAC/C,OAAO5B,KAAKkG,UAAYlG,KAAKkG,UAAUC,SAASnG,KAAKoG,YAAapG,KAAKqG,gBAAiBmB,EAAY7F,EAAUO,EAAWC,EAAWP,IAChID,EAAS8F,MAAMvF,EAAWC,EAClC,CACAG,YAAYkF,EAAYnF,GACpB,OAAOrC,KAAKsG,gBACRtG,KAAKsG,eAAeC,cAAcvG,KAAKwG,iBAAkBxG,KAAKyG,qBAAsBe,EAAYnF,EAExG,CACAuB,aAAa4D,EAAYhF,GACrB,IAAIkF,EAAalF,EACjB,GAAIxC,KAAK0G,gBACD1G,KAAKmH,YACLO,EAAW1D,eAAe2D,KAAK3H,KAAKqH,mBAGxCK,EAAa1H,KAAK0G,gBAAgBtB,eAAepF,KAAK2G,kBAAmB3G,KAAK4G,sBAAuBY,EAAYhF,GAE5GkF,IACDA,EAAalF,WAGbA,EAAKoF,WACLpF,EAAKoF,WAAWpF,OAAI,IAEfA,EAAKI,MAAQ0B,EAIlB,MAAM,IAAI9G,MAAM,+BAHhB2G,GAAkB3B,EAG2B,CAGrD,OAAOkF,CACX,CACAjE,WAAW+D,EAAYhF,EAAMN,EAAWC,GACpC,OAAOnC,KAAK6G,cAAgB7G,KAAK6G,cAAcxB,aAAarF,KAAK8G,gBAAiB9G,KAAK+G,oBAAqBS,EAAYhF,EAAMN,EAAWC,GACrIK,EAAKb,SAAS8F,MAAMvF,EAAWC,EACvC,CACAuC,WAAW8C,EAAYhF,GACnB,IAAIqF,EACJ,GAAI7H,KAAKgH,cACLa,EAAQ7H,KAAKgH,cAAc1B,aAAatF,KAAKiH,gBAAiBjH,KAAKkH,oBAAqBM,EAAYhF,OAEnG,CACD,IAAKA,EAAKe,SACN,MAAM/F,MAAM,0BAEhBqK,EAAQrF,EAAKe,SAASf,EAC1B,CACA,OAAOqF,CACX,CACA1C,QAAQqC,EAAYM,GAGhB,IACI9H,KAAKmH,YACDnH,KAAKmH,WAAWrC,UAAU9E,KAAKoH,aAAcpH,KAAKsH,iBAAkBE,EAAYM,EAIxF,CAHA,MACO5D,GACHlE,KAAKsC,YAAYkF,EAAYtD,EACjC,CACJ,CAEAP,iBAAiBf,EAAMgC,GACnB,MAAMmD,EAAS/H,KAAKwF,YACdwC,EAAOD,EAAOnF,GACdqF,GAAOF,EAAOnF,GAAQoF,EAAOpD,EACnC,GAAIqD,GAAO,EACP,MAAM,IAAIzK,MAAM,4CAER,GAARwK,GAAqB,GAARC,IAObjI,KAAKmF,QAAQnF,KAAKY,KANF,CACZ0D,UAAWyD,EAAOzD,UAAe,EACjCxB,UAAWiF,EAAOjF,UAAe,EACjCD,UAAWkF,EAAOlF,UAAe,EACjCqF,OAAQtF,GAIpB,EAEJ,MAAMyB,EACFxE,YAAY+C,EAAMhB,EAAQD,EAAUwG,EAASP,GAAYrE,GAarD,GAXAvD,KAAKiE,MAAQ,KACbjE,KAAKmD,SAAW,EAEhBnD,KAAKgE,eAAiB,KAEtBhE,KAAKoI,OAAS,eACdpI,KAAK4C,KAAOA,EACZ5C,KAAK4B,OAASA,EACd5B,KAAKqD,KAAO8E,EACZnI,KAAK4H,WAAaA,GAClB5H,KAAKuD,SAAWA,GACX5B,EACD,MAAM,IAAInE,MAAM,2BAEpBwC,KAAK2B,SAAWA,EAChB,MAAM7C,EAAOkB,KAGTA,KAAKoC,OADLQ,IAASC,IAAasF,GAAWA,EAAQE,KAC3BhE,EAASZ,WAGT,WACV,OAAOY,EAASZ,WAAW6E,KAAK1J,EAAQE,EAAMkB,KAAMgC,UACxD,CAER,CACAzB,kBAAkBiC,EAAMyC,EAAQsD,GACvB/F,IACDA,EAAOxC,MAEXwI,KACA,IACIhG,SAAKW,WACEX,EAAK5B,KAAK2B,QAAQC,EAAMyC,EAAQsD,EAO3C,CANA,QAEqC,GAA7BC,IACAC,IAEJD,IACJ,CACJ,CACI5H,WACA,OAAOZ,KAAKiE,KAChB,CACIvB,YACA,OAAO1C,KAAKoI,MAChB,CACAM,wBACI1I,KAAKiD,cAAcN,EAAcmB,GACrC,CAEAb,cAAc0F,EAASC,EAAYC,GAC/B,GAAI7I,KAAKoI,SAAWQ,GAAc5I,KAAKoI,SAAWS,EAO9C,MAAM,IAAIrL,MAAM,GAAGwC,KAAK4C,SAAS5C,KAAK4B,mCAAmC+G,wBAA8BC,KAAcC,EAAa,QAAWA,EAAa,IAAO,YAAY7I,KAAKoI,YANlLpI,KAAKoI,OAASO,EACVA,GAAWhG,IACX3C,KAAKgE,eAAiB,KAMlC,CACA8E,WACI,OAAI9I,KAAKqD,aAAerD,KAAKqD,KAAK0F,SAAa,IACpC/I,KAAKqD,KAAK0F,SAASD,WAGnBE,OAAOC,UAAUH,SAASR,KAAKtI,KAE9C,CAGAkJ,SACI,MAAO,CACHtG,KAAM5C,KAAK4C,KACXF,MAAO1C,KAAK0C,MACZd,OAAQ5B,KAAK4B,OACbhB,KAAMZ,KAAKY,KAAKxB,KAChB+D,SAAUnD,KAAKmD,SAEvB,EAOJ,MAAMgG,GAAmBzJ,EAAW,cAC9B0J,GAAgB1J,EAAW,WAC3B2J,GAAa3J,EAAW,QAC9B,IAEI4J,GAFAC,GAAkB,GAClBC,IAA4B,EAEhC,SAASC,GAAwBC,IAM7B,GALKJ,IACG1K,EAAOwK,MACPE,GAA8B1K,EAAOwK,IAAeO,QAAQ,IAGhEL,GAA6B,CAC7B,IAAIM,EAAaN,GAA4BD,IACxCO,IAGDA,EAAaN,GAA4BO,MAE7CD,EAAWtB,KAAKgB,GAA6BI,GACjD,MAEI9K,EAAOuK,IAAkBO,GAAM,EAEvC,CACA,SAASvF,GAAkB3B,IAGW,IAA9BgG,IAA8D,IAA3Be,GAAgBrL,QAEnDuL,GAAwBhB,GAE5BjG,IAAQ+G,GAAgB5B,KAAKnF,GACjC,CACA,SAASiG,IACL,IAAKe,GAA2B,CAE5B,IADAA,IAA4B,EACrBD,GAAgBrL,QAAQ,CAC3B,MAAM4L,GAAQP,GACdA,GAAkB,GAClB,QAAStL,EAAI,EAAGA,EAAI6L,GAAM5L,OAAQD,IAAK,CACnC,MAAMuE,EAAOsH,GAAM7L,GACnB,IACIuE,EAAK5B,KAAK2B,QAAQC,EAAM,KAAM,KAIlC,CAHA,MACOH,GACHhB,GAAK0I,iBAAiB1H,EAC1B,CACJ,CACJ,CACAhB,GAAK2I,qBACLR,IAA4B,CAChC,CACJ,CAMA,MAAM/G,GAAU,CAAErD,KAAM,WAClBuD,EAAe,eAAgBmB,GAAa,aAAcZ,GAAY,YAAaF,EAAU,UAAW2B,GAAY,YAAajB,EAAU,UAC3IY,EAAY,YAAaxB,GAAY,YAAaD,GAAY,YAC9DpC,GAAU,CAAC,EACXY,GAAO,CACT4I,OAAQvK,EACRwK,iBAAkBA,IAAMpJ,GACxBiJ,iBAAkBI,EAClBH,mBAAoBG,EACpBhG,kBAAmBA,GACnBiG,kBAAmBA,KAAOxK,EAAKF,EAAW,oCAC1C2K,iBAAkBA,IAAM,GACxBC,kBAAmBH,EACnBI,YAAaA,IAAMJ,EACnBK,cAAeA,IAAM,GACrBC,UAAWA,IAAMN,EACjBO,eAAgBA,IAAMP,EACtBQ,oBAAqBA,IAAMR,EAC3BS,WAAYA,KAAM,EAClBC,iBAAkBA,OAClBC,qBAAsBA,IAAMX,EAC5BY,+BAAgCA,OAChCC,aAAcA,OACdC,WAAYA,IAAM,GAClBC,WAAYA,IAAMf,EAClBgB,oBAAqBA,IAAMhB,EAC3BiB,iBAAkBA,IAAM,GACxBC,sBAAuBA,IAAMlB,EAC7BmB,kBAAmBA,IAAMnB,EACzBoB,eAAgBA,IAAMpB,EACtBV,wBAAyBA,IAE7B,IAAI3I,GAAoB,CAAEhB,OAAQ,KAAMc,KAAM,IAAIhB,EAAK,KAAM,OACzDoB,GAAe,KACfwH,GAA4B,EAChC,SAAS2B,IAAS,CAClB9K,EAAmB,OAAQ,QACpBT,EAAOgB,KAAUA,CAC5B,CAjnBA,QAinBWf,OAAW,KAAeA,eAAiBC,KAAS,KAAeA,MAAQF,QAiBtF,MAAMmM,GAAiC/B,OAAOwC,yBAExCV,GAAuB9B,OAAOyC,eAE9BC,GAAuB1C,OAAO2C,eAE9BX,GAAehC,OAAO4C,OAEtBX,GAAaY,MAAM5C,UAAU6C,MAE7BC,GAAyB,mBAEzBC,GAA4B,sBAE5BC,GAAiCrM,KAAKF,WAAWqM,IAEjDG,GAAoCtM,KAAKF,WAAWsM,IAEpDG,GAAW,OAEXC,GAAY,QAEZC,GAAqBzM,KAAKF,WAAW,IAC3C,SAASyL,GAAoBxJ,EAAUC,GACnC,OAAOhC,KAAKiB,QAAQa,KAAKC,EAAUC,EACvC,CACA,SAAS0K,GAAiC1K,EAAQD,EAAU0B,EAAMe,EAAgBI,GAC9E,OAAO5E,KAAKiB,QAAQ0D,kBAAkB3C,EAAQD,EAAU0B,EAAMe,EAAgBI,EAClF,CACA,MAAM+H,GAAa3M,KAAKF,WAClB8M,UAAwB3N,OAAW,IACnC4N,GAAiBD,GAAiB3N,YAAS2E,EAC3CkJ,GAAUF,IAAkBC,IAAkC,iBAAT3N,MAAqBA,MAAQF,OAExF,SAAS4L,GAAcjC,EAAM3G,GACzB,QAAS3D,EAAIsK,EAAKrK,OAAS,EAAGD,GAAK,EAAGA,IACX,mBAAZsK,EAAKtK,KACZsK,EAAKtK,GAAKkN,GAAoB5C,EAAKtK,GAAI2D,EAAS,IAAM3D,IAG9D,OAAOsK,CACX,CAqBA,SAASoE,GAAmBC,GACxB,OAAKA,IAGyB,IAA1BA,EAAaC,YAGoB,mBAArBD,EAAatL,YAA6BsL,EAAaE,IAAQ,IACnF,CACA,MAAMC,UAAsBhO,kBAAsB,KAAeD,gBAAgBC,kBAG3EiO,KAAY,OAAQN,YAAmBA,GAAQO,QAAY,KACvB,qBAAtC,CAAC,EAAEnE,SAASR,KAAKoE,GAAQO,SACvBC,IAAaF,KAAWD,OAAkBP,KAAkBC,GAAeU,aAI3EC,UAAeV,GAAQO,QAAY,KACC,qBAAtC,CAAC,EAAEnE,SAASR,KAAKoE,GAAQO,WAAoCF,OAC1DP,KAAkBC,GAAeU,aAClCE,GAAyB,CAAC,EAC1BC,GAAS,SAAUC,GAIrB,KADAA,EAAQA,GAASb,GAAQa,OAErB,OAEJ,IAAIC,EAAkBH,GAAuBE,EAAM3K,MAC9C4K,IACDA,EAAkBH,GAAuBE,EAAM3K,MAAQ2J,GAAW,cAAgBgB,EAAM3K,OAE5F,MAAMqC,EAASjF,MAAQuN,EAAMtI,QAAUyH,GACjCe,EAAWxI,EAAOuI,GACxB,IAAIE,EACJ,GAAIR,IAAajI,IAAWwH,IAAiC,UAAfc,EAAM3K,KAAkB,CAIlE,MAAM+K,EAAaJ,EACnBG,EAASD,GACLA,EAASnF,KAAKtI,KAAM2N,EAAW7P,QAAS6P,EAAWC,SAAUD,EAAWE,OAAQF,EAAWG,MAAOH,EAAWtL,QAClG,IAAXqL,GACAH,EAAMQ,gBAEd,MAEIL,EAASD,GAAYA,EAAShG,MAAMzH,KAAMgC,WAC5BwB,MAAVkK,IAAwBA,GACxBH,EAAMQ,iBAGd,OAAOL,CACX,EACA,SAASM,GAAcC,EAAKC,EAAMjF,GAC9B,IAAIkF,EAAOpD,GAA+BkD,EAAKC,GAU/C,IATKC,GAAQlF,GAEa8B,GAA+B9B,EAAWiF,KAE5DC,EAAO,CAAEC,YAAY,EAAMC,cAAc,KAK5CF,IAASA,EAAKE,aACf,OAEJ,MAAMC,EAAsB/B,GAAW,KAAO2B,EAAO,WACrD,GAAID,EAAI9M,eAAemN,IAAwBL,EAAIK,GAC/C,cAOGH,EAAKtB,gBACLsB,EAAKtG,MACZ,MAAM0G,EAAkBJ,EAAK7M,IACvBkN,EAAkBL,EAAKrB,IAEvB2B,EAAYP,EAAKpC,MAAM,GAC7B,IAAI0B,EAAkBH,GAAuBoB,GACxCjB,IACDA,EAAkBH,GAAuBoB,GAAalC,GAAW,cAAgBkC,IAErFN,EAAKrB,IAAM,SAAU4B,GAGjB,IAAIzJ,EAASjF,MACRiF,GAAUgJ,IAAQvB,KACnBzH,EAASyH,IAERzH,IAIwB,mBADPA,EAAOuI,IAEzBvI,EAAO0J,oBAAoBF,EAAWnB,IAI1CkB,GAAmBA,EAAgBlG,KAAKrD,EAAQ,MAChDA,EAAOuI,GAAmBkB,EACF,mBAAbA,GACPzJ,EAAO2J,iBAAiBH,EAAWnB,IAAQ,GAEnD,EAGAa,EAAK7M,IAAM,WAGP,IAAI2D,EAASjF,KAIb,IAHKiF,GAAUgJ,IAAQvB,KACnBzH,EAASyH,KAERzH,EACD,OAAO,KAEX,MAAMwI,EAAWxI,EAAOuI,GACxB,GAAIC,EACA,OAAOA,EAEN,GAAIc,EAAiB,CAOtB,IAAI1G,GAAQ0G,EAAgBjG,KAAKtI,MACjC,GAAI6H,GACAsG,SAAKrB,IAAIxE,KAAKtI,KAAM6H,IACoB,mBAA7B5C,EAAO4J,iBACd5J,EAAO6J,gBAAgBZ,GAEpBrG,EAEf,CACA,OAAO,IACX,EACAiD,GAAqBmD,EAAKC,EAAMC,GAChCF,EAAIK,IAAuB,CAC/B,CACA,SAAShE,GAAkB2D,EAAK7N,EAAY6I,GACxC,GAAI7I,EACA,QAASnC,EAAI,EAAGA,EAAImC,EAAWlC,OAAQD,IACnC+P,GAAcC,EAAK,KAAO7N,EAAWnC,GAAIgL,OAG5C,CACD,MAAM8F,EAAe,GACrB,UAAWb,KAAQD,EACS,MAApBC,EAAKpC,MAAM,EAAG,IACdiD,EAAapH,KAAKuG,GAG1B,QAASc,EAAI,EAAGA,EAAID,EAAa7Q,OAAQ8Q,IACrChB,GAAcC,EAAKc,EAAaC,GAAI/F,EAE5C,CACJ,CACA,MAAMgG,GAAsB1C,GAAW,oBAEvC,SAASrB,GAAWgE,GAChB,MAAMC,EAAgBzC,GAAQwC,GAC9B,IAAKC,EACD,OAEJzC,GAAQH,GAAW2C,IAAcC,EACjCzC,GAAQwC,GAAa,WACjB,MAAME,EAAI5E,GAAcxI,UAAWkN,GACnC,OAAQE,EAAElR,aACD,EACD8B,KAAKiP,IAAuB,IAAIE,EAChC,WACC,EACDnP,KAAKiP,IAAuB,IAAIE,EAAcC,EAAE,IAChD,WACC,EACDpP,KAAKiP,IAAuB,IAAIE,EAAcC,EAAE,GAAIA,EAAE,IACtD,WACC,EACDpP,KAAKiP,IAAuB,IAAIE,EAAcC,EAAE,GAAIA,EAAE,GAAIA,EAAE,IAC5D,WACC,EACDpP,KAAKiP,IAAuB,IAAIE,EAAcC,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,IAClE,cAEA,MAAM,IAAI5R,MAAM,sBAE5B,EAEA6N,GAAsBqB,GAAQwC,GAAYC,GAC1C,MAAME,EAAW,IAAIF,EAAc,WAAc,GACjD,IAAIjB,EACJ,IAAKA,KAAQmB,EAES,mBAAdH,GAA2C,iBAAThB,GAErC,SAAUA,GACuB,mBAAnBmB,EAASnB,GAChBxB,GAAQwC,GAAWjG,UAAUiF,GAAQ,WACjC,OAAOlO,KAAKiP,IAAqBf,GAAMzG,MAAMzH,KAAKiP,IAAsBjN,UAC5E,EAGA8I,GAAqB4B,GAAQwC,GAAWjG,UAAWiF,EAAM,CACrDpB,IAAK,SAAU7L,GACO,mBAAPA,GACPjB,KAAKiP,IAAqBf,GAAQ/C,GAAoBlK,EAAIiO,EAAY,IAAMhB,GAI5E7C,GAAsBrL,KAAKiP,IAAqBf,GAAOjN,IAGvDjB,KAAKiP,IAAqBf,GAAQjN,CAE1C,EACAK,IAAK,WACD,OAAOtB,KAAKiP,IAAqBf,EACrC,GAGZ,CAzBC,CAyBCA,GAEN,IAAKA,KAAQiB,EACI,cAATjB,GAAwBiB,EAAchO,eAAe+M,KACrDxB,GAAQwC,GAAWhB,GAAQiB,EAAcjB,GAGrD,CACA,SAAS3D,GAAYtF,EAAQ7F,EAAMkQ,GAC/B,IAAIC,EAAQtK,EACZ,KAAOsK,IAAUA,EAAMpO,eAAe/B,IAClCmQ,EAAQ7D,GAAqB6D,IAE5BA,GAAStK,EAAO7F,KAEjBmQ,EAAQtK,GAEZ,MAAMuK,EAAejD,GAAWnN,GAChC,IAAI2F,EAAW,KACf,GAAIwK,MAAYxK,EAAWwK,EAAMC,MAAmBD,EAAMpO,eAAeqO,MACrEzK,EAAWwK,EAAMC,GAAgBD,EAAMnQ,GAInCuN,GADS4C,GAASxE,GAA+BwE,EAAOnQ,KAC9B,CAC1B,MAAMqQ,EAAgBH,EAAQvK,EAAUyK,EAAcpQ,GACtDmQ,EAAMnQ,GAAQ,WACV,OAAOqQ,EAAczP,KAAMgC,UAC/B,EACAqJ,GAAsBkE,EAAMnQ,GAAO2F,EACvC,CAEJ,OAAOA,CACX,CAEA,SAAS2F,GAAeuD,EAAKyB,EAAUC,GACnC,IAAIC,EAAY,KAChB,SAAShM,EAAapB,GAClB,MAAMa,EAAOb,EAAKa,KAClBA,SAAKkF,KAAKlF,EAAKwM,OAAS,WACpBrN,EAAKJ,OAAOqF,MAAMzH,KAAMgC,UAC5B,EACA4N,EAAUnI,MAAMpE,EAAK4B,OAAQ5B,EAAKkF,MAC3B/F,CACX,CACAoN,EAAYrF,GAAY0D,EAAKyB,EAAW3K,GAAa,SAAUjG,EAAMyJ,GACjE,MAAMuH,EAAOH,EAAY7Q,EAAMyJ,GAC/B,OAAIuH,EAAKD,OAAS,GAAiC,mBAArBtH,EAAKuH,EAAKD,OAC7BvD,GAAiCwD,EAAK1Q,KAAMmJ,EAAKuH,EAAKD,OAAQC,EAAMlM,GAIpEmB,EAAS0C,MAAM3I,EAAMyJ,EAEpC,EACJ,CACA,SAAS8C,GAAsB0E,EAASC,GACpCD,EAAQxD,GAAW,qBAAuByD,CAC9C,CACA,IAAIC,IAAqB,EACrBC,IAAW,EAYf,SAAStF,KACL,GAAIqF,GACA,OAAOC,GAEXD,IAAqB,EACrB,IACI,MAAME,EAAK1D,GAAe2D,UAAUC,YACR,IAAxBF,EAAGG,QAAQ,WAA8C,IAA3BH,EAAGG,QAAQ,cAA8C,IAAxBH,EAAGG,QAAQ,YAC1EJ,IAAW,EAInB,CAFA,MAEA,CACA,OAAOA,EACX,CASAtQ,KAAK2Q,aAAa,mBAAoB,CAAC3R,EAAQgB,EAAM4Q,KACjD,MAAMzF,EAAiC/B,OAAOwC,yBACxCV,EAAuB9B,OAAOyC,eAQ9B/L,EAAa8Q,EAAIvG,OACjBwG,EAAyB,GACzBC,GAAkH,IAAtE9R,EAAOc,EAAW,gDAC9D0J,EAAgB1J,EAAW,WAC3B2J,EAAa3J,EAAW,QAE9B8Q,EAAIzG,iBAAoB4G,IACpB,GAAIH,EAAIpG,oBAAqB,CACzB,MAAMwG,EAAYD,GAAKA,EAAEC,UACrBA,EACAC,QAAQxO,MAAM,+BAAgCuO,aAAqBpT,MAAQoT,EAAU9S,QAAU8S,EAAW,UAAWD,EAAE/P,KAAKxB,KAAM,UAAWuR,EAAEnO,MAAQmO,EAAEnO,KAAKZ,OAAQ,WAAYgP,EAAWA,aAAqBpT,MAAQoT,EAAUE,WAAQtN,GAG5OqN,QAAQxO,MAAMsO,EAEtB,GAEJH,EAAIxG,mBAAqB,KACrB,KAAOyG,EAAuBvS,QAAQ,CAClC,MAAM6S,EAAuBN,EAAuBO,QACpD,IACID,EAAqBnQ,KAAKmB,WAAW,KACjC,MAAIgP,EAAqBE,cACfF,EAAqBH,UAEzBG,GAKd,CAHA,MACO1O,GACH6O,GAAyB7O,EAC7B,CACJ,GAEJ,MAAM8O,GAA6CzR,EAAW,oCAC9D,SAASwR,GAAyBP,GAC9BH,EAAIzG,iBAAiB4G,GACrB,IACI,MAAMS,EAAUxR,EAAKuR,IACE,mBAAZC,GACPA,EAAQ9I,KAAKtI,KAAM2Q,EAI3B,CAFA,MAEA,CACJ,CACA,SAASU,GAAWxJ,GAChB,OAAOA,GAASA,EAAMgC,IAC1B,CACA,SAASyH,GAAkBzJ,GACvB,OAAOA,CACX,CACA,SAAS0J,GAAiBX,GACtB,OAAOlQ,EAAiB8Q,OAAOZ,EACnC,CACA,MAAMa,GAAc/R,EAAW,SACzBgS,GAAchS,EAAW,SACzBiS,EAAgBjS,EAAW,WAC3BkS,GAA2BlS,EAAW,sBACtCmS,EAA2BnS,EAAW,sBAEtCoS,GAAa,KACbC,GAAW,EACXC,IAAW,EAEjB,SAASC,EAAaC,EAASxP,GAC3B,OAAQyP,IACJ,IACIC,GAAeF,EAASxP,EAAOyP,EAInC,CAHA,MACOjO,GACHkO,GAAeF,GAAS,EAAOhO,EACnC,EAGR,CACA,MAAMmO,GAAO,WACT,IAAIC,GAAY,EAChB,OAAO,SAAiBC,GACpB,OAAO,WACCD,IAGJA,GAAY,EACZC,EAAgB9K,MAAM,KAAMzF,WAChC,CACJ,CACJ,EAEMwQ,GAA4B9S,EAAW,oBAE7C,SAAS0S,GAAeF,EAASxP,EAAOmF,GACpC,MAAM4K,EAAcJ,KACpB,GAAIH,IAAYrK,EACZ,MAAM,IAAI6K,UANC,gCAQf,GAAIR,EAAQT,MAAiBK,GAAY,CAErC,IAAIjI,EAAO,KACX,KACyB,iBAAVhC,GAAuC,mBAAVA,KACpCgC,EAAOhC,GAASA,EAAMgC,KAQ9B,CANA,MACO3F,GACHuO,SAAY,KACRL,GAAeF,GAAS,EAAOhO,EAAG,EADtCuO,GAGOP,CACX,CAEA,GAAIxP,IAAUsP,IAAYnK,aAAiBnH,GACvCmH,EAAM1G,eAAesQ,KAAgB5J,EAAM1G,eAAeuQ,KAC1D7J,EAAM4J,MAAiBK,GACvBa,GAAqB9K,GACrBuK,GAAeF,EAASrK,EAAM4J,IAAc5J,EAAM6J,UAAY,GAEzDhP,IAAUsP,IAA4B,mBAATnI,EAClC,IACIA,EAAKvB,KAAKT,EAAO4K,EAAYR,EAAaC,EAASxP,IAAS+P,EAAYR,EAAaC,GAAS,IAMlG,CALA,MACOhO,GACHuO,EAAY,KACRL,GAAeF,GAAS,EAAOhO,EAAG,EADtCuO,EAGJ,KAEC,CACDP,EAAQT,IAAe/O,EACvB,MAAMoH,EAAQoI,EAAQR,IAatB,GAZAQ,EAAQR,IAAe7J,EACnBqK,EAAQP,KAAmBA,GAEvBjP,IAAUqP,IAGVG,EAAQT,IAAeS,EAAQL,GAC/BK,EAAQR,IAAeQ,EAAQN,KAKnClP,IAAUsP,IAAYnK,aAAiBrK,MAAO,CAE9C,MAAMoV,EAAQhT,EAAKmB,aAAenB,EAAKmB,YAAYsC,MAC/CzD,EAAKmB,YAAYsC,KAAKwP,kBACtBD,GAEA9H,EAAqBjD,EAAO2K,GAA2B,CAAEnE,cAAc,EAAMD,YAAY,EAAOvB,UAAU,EAAMhF,MAAO+K,GAE/H,CACA,QAAS3U,EAAI,EAAGA,EAAI6L,EAAM5L,QACtB4U,GAAwBZ,EAASpI,EAAM7L,KAAM6L,EAAM7L,KAAM6L,EAAM7L,KAAM6L,EAAM7L,MAE/E,GAAoB,GAAhB6L,EAAM5L,QAAewE,GAASsP,GAAU,CACxCE,EAAQT,IA3FE,EA4FV,IAAIV,EAAuBlJ,EAC3B,IAII,MAAM,IAAIrK,MAAM,0BAvKpC,SAASuV,EAAuB9E,GAC5B,OAAIA,GAAOA,EAAInF,WAAaE,OAAOC,UAAUH,UACvBmF,EAAIpO,aAAeoO,EAAIpO,YAAYT,MACrB,IAAM,KAAO4T,KAAKC,UAAUhF,GAEzDA,EAAMA,EAAInF,WAAaE,OAAOC,UAAUH,SAASR,KAAK2F,EACjE,CAiKgE8E,CAAuBlL,IAC9DA,GAASA,EAAMiJ,MAAQ,KAAOjJ,EAAMiJ,MAAQ,IAIrD,CAHA,MACO5M,IACH6M,EAAuB7M,EAC3B,CACIwM,IAGAK,EAAqBE,eAAgB,GAEzCF,EAAqBH,UAAY/I,EACjCkJ,EAAqBmB,QAAUA,EAC/BnB,EAAqBnQ,KAAOhB,EAAKiB,QACjCkQ,EAAqBvO,KAAO5C,EAAKmB,YACjC0P,EAAuB9I,KAAKoJ,GAC5BP,EAAIrM,mBACR,CACJ,CACJ,CAEA,OAAO+N,CACX,CACA,MAAMgB,GAA4BxT,EAAW,2BAC7C,SAASiT,GAAqBT,GAC1B,GA1HsB,IA0HlBA,EAAQT,IAAoC,CAM5C,IACI,MAAML,EAAUxR,EAAKsT,IACjB9B,GAA8B,mBAAZA,GAClBA,EAAQ9I,KAAKtI,KAAM,CAAE4Q,UAAWsB,EAAQR,IAAcQ,QAASA,GAIvE,CAFA,MAEA,CACAA,EAAQT,IAAeO,GACvB,QAAS/T,EAAI,EAAGA,EAAIwS,EAAuBvS,OAAQD,IAC3CiU,IAAYzB,EAAuBxS,GAAGiU,SACtCzB,EAAuB0C,OAAOlV,EAAG,EAG7C,CACJ,CACA,SAAS6U,GAAwBZ,EAAStR,EAAMwS,EAAcC,EAAaC,GACvEX,GAAqBT,GACrB,MAAMqB,EAAerB,EAAQT,IACvB1M,EAAWwO,EACW,mBAAhBF,EAA8BA,EAAc/B,GAC7B,mBAAfgC,EAA6BA,EACjC/B,GACR3Q,EAAKuD,kBA3JM,eA2JoB,KAC3B,IACI,MAAMqP,GAAqBtB,EAAQR,IAC7B+B,KAAqBL,GAAgBzB,IAAkByB,EAAazB,GACtE8B,KAEAL,EAAaxB,IAA4B4B,GACzCJ,EAAavB,GAA4B0B,GAG7C,MAAM1L,GAAQjH,EAAKqB,IAAI8C,OAAUvB,EAAWiQ,IAAoB1O,IAAawM,IAAoBxM,IAAauM,GAC1G,GACA,CAACkC,KACLpB,GAAegB,GAAc,EAAMvL,GAKvC,CAJA,MACOxF,IAEH+P,GAAegB,GAAc,EAAO/Q,GACxC,GACD+Q,EACP,CACA,MACMjJ,GAAO,WAAc,EACrBuJ,EAAiB9U,EAAO8U,eAC9B,MAAMhT,EACFH,kBACI,MAL6B,+CAMjC,CACAA,eAAesH,GACX,OAAOuK,GAAe,IAAIpS,KAAK,MAAO+R,EAAUlK,EACpD,CACAtH,cAAc8B,GACV,OAAO+P,GAAe,IAAIpS,KAAK,MAAOgS,GAAU3P,EACpD,CACA9B,WAAWoT,GACP,IAAKA,GAA6C,mBAA5BA,EAAOC,OAAOC,UAChC,OAAOrT,QAAQgR,OAAO,IAAIkC,EAAe,GAAI,+BAEjD,MAAMI,EAAW,GACjB,IAAIlP,EAAQ,EACZ,IACI,QAASuN,KAAKwB,EACV/O,IACAkP,EAASnM,KAAKjH,EAAiBiJ,QAAQwI,GAK/C,CAHA,MAEI,OAAO3R,QAAQgR,OAAO,IAAIkC,EAAe,GAAI,8BACjD,CACA,GAAc,IAAV9O,EACA,OAAOpE,QAAQgR,OAAO,IAAIkC,EAAe,GAAI,+BAEjD,IAAIK,GAAW,EACf,MAAMC,EAAS,GACf,OAAO,IAAItT,EAAiB,CAACiJ,EAAS6H,MAClC,QAASvT,GAAI,EAAGA,GAAI6V,EAAS5V,OAAQD,KACjC6V,EAAS7V,IAAG4L,KAAKsI,KACT4B,IAGJA,GAAW,EACXpK,EAAQwI,IAAC,EACVjO,KACC8P,EAAOrM,KAAKzD,IACZU,IACc,IAAVA,IACAmP,GAAW,EACXvC,GAAO,IAAIkC,EAAeM,EAAQ,+BAA6B,EAEtE,EAGb,CAEAzT,YAAYoT,GACR,IAAIhK,EACA6H,EACAU,EAAU,IAAIlS,KAAK,CAACiU,GAAKC,MACzBvK,EAAUsK,GACVzC,EAAS0C,KAEb,SAASC,EAAUtM,IACf8B,EAAQ9B,GACZ,CACA,SAASuM,EAAS/R,IACdmP,EAAOnP,GACX,CACA,QAASwF,MAAS8L,EACTtC,GAAWxJ,MACZA,GAAQ7H,KAAK2J,QAAQ9B,KAEzBA,GAAMgC,KAAKsK,EAAWC,GAE1B,OAAOlC,CACX,CACA3R,WAAWoT,GACP,OAAOjT,EAAiB2T,gBAAgBV,EAC5C,CACApT,kBAAkBoT,GAEd,OADU3T,MAAQA,KAAKiJ,qBAAqBvI,EAAmBV,KAAOU,GAC7D2T,gBAAgBV,EAAQ,CAC7BW,aAAezM,KAAa0M,OAAQ,YAAa1M,UACjD2M,cAAgBtQ,KAAWqQ,OAAQ,WAAYE,OAAQvQ,KAE/D,CACA3D,uBAAuBoT,EAAQhS,GAC3B,IAAIgI,EACA6H,EACAU,EAAU,IAAIlS,KAAK,CAACiU,GAAKC,MACzBvK,EAAUsK,GACVzC,EAAS0C,KAGTQ,EAAkB,EAClBC,GAAa,EACjB,MAAMC,GAAiB,GACvB,QAAS/M,MAAS8L,EAAQ,CACjBtC,GAAWxJ,MACZA,GAAQ7H,KAAK2J,QAAQ9B,KAEzB,MAAMgN,GAAgBF,GACtB,IACI9M,GAAMgC,KAAMhC,KACR+M,GAAeC,IAAiBlT,EAAWA,EAAS2S,aAAazM,IAASA,GAC1E6M,IACwB,IAApBA,GACA/K,EAAQiL,GAAc,EAE1B1Q,KACKvC,GAIDiT,GAAeC,IAAiBlT,EAAS6S,cAActQ,IACvDwQ,IACwB,IAApBA,GACA/K,EAAQiL,KANZpD,EAAOtN,GAAG,EAatB,CAHA,MACO4Q,IACHtD,EAAOsD,GACX,CACAJ,IACAC,IACJ,CAEAD,UAAmB,EACK,IAApBA,GACA/K,EAAQiL,IAEL1C,CACX,CACArS,YAAYkV,GACR,MAAM7C,EAAUlS,KAChB,KAAMkS,aAAmBxR,GACrB,MAAM,IAAIlD,MAAM,kCAEpB0U,EAAQT,IAAeK,GACvBI,EAAQR,IAAe,GACvB,IACI,MAAMe,EAAcJ,KACpB0C,GACIA,EAAStC,EAAYR,EAAaC,EAASH,IAAYU,EAAYR,EAAaC,EAASF,KAIjG,CAHA,MACO3P,GACH+P,GAAeF,GAAS,EAAO7P,EACnC,CACJ,CACY2S,IAAPpB,OAAOoB,eACR,MAAO,SACX,CACYC,IAAPrB,OAAOqB,WACR,OAAOvU,CACX,CACAmJ,KAAKwJ,EAAaC,GACd,IAAI4B,EASJ,IAAIC,EAAgC,QAA3BD,EAAKlV,KAAKH,mBAAgC,IAAPqV,OAAgB,EAASA,EAAGtB,OAAOqB,WAC1EE,GAAkB,mBAANA,KACbA,EAAInV,KAAKH,aAAea,GAE5B,MAAM0S,EAAe,IAAI+B,EAAEhL,IACrBvJ,EAAOhB,EAAKiB,QAClB,OAAIb,KAAKyR,KAAgBK,GACrB9R,KAAK0R,IAAa/J,KAAK/G,EAAMwS,EAAcC,EAAaC,GAGxDR,GAAwB9S,KAAMY,EAAMwS,EAAcC,EAAaC,GAE5DF,CACX,CACAgC,MAAM9B,GACF,OAAOtT,KAAK6J,KAAK,KAAMyJ,EAC3B,CACA+B,QAAQC,GACJ,IAAIJ,EAEJ,IAAIC,EAAgC,QAA3BD,EAAKlV,KAAKH,mBAAgC,IAAPqV,OAAgB,EAASA,EAAGtB,OAAOqB,WAC1EE,GAAkB,mBAANA,KACbA,EAAIzU,GAER,MAAM0S,EAAe,IAAI+B,EAAEhL,IAC3BiJ,EAAazB,GAAiBA,EAC9B,MAAM/Q,EAAOhB,EAAKiB,QAClB,OAAIb,KAAKyR,KAAgBK,GACrB9R,KAAK0R,IAAa/J,KAAK/G,EAAMwS,EAAckC,EAAWA,GAGtDxC,GAAwB9S,KAAMY,EAAMwS,EAAckC,EAAWA,GAE1DlC,CACX,EAIJ1S,EAAiBiJ,QAAajJ,EAAiBiJ,QAC/CjJ,EAAiB8Q,OAAY9Q,EAAiB8Q,OAC9C9Q,EAAiB6U,KAAU7U,EAAiB6U,KAC5C7U,EAAiB8U,IAAS9U,EAAiB8U,IAC3C,MAAMC,EAAgB7W,EAAOwK,GAAiBxK,EAAO4B,QACrD5B,EAAO4B,QAAaE,EACpB,MAAMgV,EAAoBhW,EAAW,eACrC,SAAS+K,GAAUkL,GACf,MAAMpG,EAAQoG,EAAK1M,UACbiF,EAAOnD,EAA+BwE,EAAO,QACnD,GAAIrB,KAA2B,IAAlBA,EAAKrB,WAAuBqB,EAAKG,cAG1C,OAEJ,MAAMuH,EAAerG,EAAM1F,KAE3B0F,EAAMlG,GAAcuM,EACpBD,EAAK1M,UAAUY,KAAO,SAAUsK,EAAWC,GAIvC,OAHgB,IAAI1T,EAAiB,CAACiJ,GAAS6H,MAC3CoE,EAAatN,KAAKtI,KAAM2J,GAAS6H,GAAM,GAE5B3H,KAAKsK,EAAWC,EACnC,EACAuB,EAAKD,IAAqB,CAC9B,CAeA,OAdAlF,EAAI/F,UAAYA,GAcZgL,IACAhL,GAAUgL,GACVlL,GAAY3L,EAAQ,QAASmG,GAfjC,SAAS8Q,EAAQ5U,GACb,OAAO,SAAUnC,EAAMyJ,GACnB,IAAIuN,EAAgB7U,EAAGwG,MAAM3I,EAAMyJ,GACnC,GAAIuN,aAAyBpV,EACzB,OAAOoV,EAEX,IAAIC,EAAOD,EAAcjW,YACzB,OAAKkW,EAAKL,IACNjL,GAAUsL,GAEPD,CACX,CACJ,CAG6CD,CAAQ9Q,KAGrDvE,QAAQZ,EAAKF,WAAW,0BAA4B+Q,EAC7C/P,IAYXd,KAAK2Q,aAAa,WAAa3R,IAE3B,MAAMoX,EAA2BC,SAAShN,UAAUH,SAC9CoN,EAA2B3J,GAAW,oBACtC4J,EAAiB5J,GAAW,WAC5B6J,EAAe7J,GAAW,SAC1B8J,EAAsB,WACxB,GAAoB,mBAATrW,KAAqB,CAC5B,MAAMsW,EAAmBtW,KAAKkW,GAC9B,GAAII,EACA,MAAgC,mBAArBA,EACAN,EAAyB1N,KAAKgO,GAG9BtN,OAAOC,UAAUH,SAASR,KAAKgO,GAG9C,GAAItW,OAASQ,QAAS,CAClB,MAAM+V,EAAgB3X,EAAOuX,GAC7B,GAAII,EACA,OAAOP,EAAyB1N,KAAKiO,EAE7C,CACA,GAAIvW,OAASxC,MAAO,CAChB,MAAMgZ,EAAc5X,EAAOwX,GAC3B,GAAII,EACA,OAAOR,EAAyB1N,KAAKkO,EAE7C,CACJ,CACA,OAAOR,EAAyB1N,KAAKtI,KACzC,EACAqW,EAAoBH,GAA4BF,EAChDC,SAAShN,UAAUH,SAAWuN,EAE9B,MAAMI,EAAyBzN,OAAOC,UAAUH,SAEhDE,OAAOC,UAAUH,SAAW,WACxB,MAAuB,mBAAZtI,SAA0BR,gBAAgBQ,QAFxB,mBAKtBiW,EAAuBnO,KAAKtI,KACvC,IAUJ,IAAI0W,IAAmB,EACvB,UAAW7X,OAAW,IAClB,IACI,MAAMsJ,EAAUa,OAAOyC,eAAe,CAAC,EAAG,UAAW,CACjDnK,IAAK,WACDoV,IAAmB,CACvB,IAKJ7X,OAAO+P,iBAAiB,OAAQzG,EAASA,GACzCtJ,OAAO8P,oBAAoB,OAAQxG,EAASA,EAIhD,CAHA,MAEIuO,IAAmB,CACvB,CAGJ,MAAMC,GAAiC,CACnCtO,MAAM,GAEJuO,GAAuB,CAAC,EACxBC,GAAgB,CAAC,EACjBC,GAAyB,IAAIC,OAAO,IAAM1K,GAAqB,uBAC/D2K,GAA+BzK,GAAW,sBAChD,SAAS0K,GAAkBxI,EAAWyI,GAClC,MAAMC,GAAkBD,EAAoBA,EAAkBzI,GAAaA,GAAarC,GAClFgL,GAAiBF,EAAoBA,EAAkBzI,GAAaA,GAAatC,GACjFlC,EAASoC,GAAqB8K,EAC9BE,EAAgBhL,GAAqB+K,EAC3CR,GAAqBnI,GAAa,CAAC,EACnCmI,GAAqBnI,GAAWrC,IAAanC,EAC7C2M,GAAqBnI,GAAWtC,IAAYkL,CAChD,CACA,SAAShN,GAAiBqC,EAAS8D,EAAK8G,EAAMC,GAC1C,MAAMC,EAAsBD,GAAgBA,EAAaE,KAAQ1L,GAC3D2L,EAAyBH,GAAgBA,EAAaI,IAAO3L,GAC7D4L,EAA4BL,GAAgBA,EAAaM,WAAc,iBACvEC,EAAuCP,GAAgBA,EAAaQ,OAAU,qBAC9EC,EAA6BzL,GAAWiL,GACxCS,EAA4B,IAAMT,EAAqB,IAGvD/T,GAAa,SAAUjB,GAAMyC,EAAQsI,IAGvC,GAAI/K,GAAK0V,UACL,OAEJ,MAAMnT,EAAWvC,GAAKb,SAUtB,IAAIU,GAToB,iBAAb0C,GAAyBA,EAASoT,cAEzC3V,GAAKb,SAAY4L,GAAUxI,EAASoT,YAAY5K,GAChD/K,GAAK8T,iBAAmBvR,GAO5B,IACIvC,GAAKJ,OAAOI,GAAMyC,EAAQ,CAACsI,IAI/B,CAHA,MACOrJ,GACH7B,GAAQ6B,CACZ,CACA,MAAMiE,GAAU3F,GAAK2F,QAQrB,OAPIA,IAA8B,iBAAZA,IAAwBA,GAAQkK,MAKlDpN,EAAOyS,GAAuBpP,KAAKrD,EAAQsI,GAAM3K,KADhCJ,GAAK8T,iBAAmB9T,GAAK8T,iBAAmB9T,GAAKb,SACLwG,IAE9D9F,EACX,EACA,SAAS+V,GAAeC,GAAS9K,EAAO+K,IAIpC,KADA/K,EAAQA,GAASb,EAAQa,OAErB,OAIJ,MAAMtI,EAASoT,IAAW9K,EAAMtI,QAAUyH,EACpC6L,GAAQtT,EAAO2R,GAAqBrJ,EAAM3K,MAAM0V,GAAYnM,GAAWC,KAC7E,GAAImM,GAAO,CACP,MAAMvE,GAAS,GAGf,GAAqB,IAAjBuE,GAAMra,OAAc,CACpB,MAAMgG,EAAMT,GAAW8U,GAAM,GAAItT,EAAQsI,GACzCrJ,GAAO8P,GAAOrM,KAAKzD,EACvB,KACK,CAID,MAAMsU,EAAYD,GAAMzM,QACxB,QAAS7N,GAAI,EAAGA,GAAIua,EAAUta,UACtBqP,IAAiD,IAAxCA,EAAMyJ,KADe/Y,KAAK,CAIvC,MAAMiG,EAAMT,GAAW+U,EAAUva,IAAIgH,EAAQsI,GAC7CrJ,GAAO8P,GAAOrM,KAAKzD,EACvB,CACJ,CAGA,GAAsB,IAAlB8P,GAAO9V,OACP,MAAM8V,GAAO,GAGb,QAAS/V,EAAI,EAAGA,EAAI+V,GAAO9V,OAAQD,IAAK,CACpC,MAAMiG,GAAM8P,GAAO/V,GACnBuS,EAAI/G,wBAAwB,KACxB,MAAMvF,IAEd,CAER,CACJ,CAEA,MAAMuU,GAA0B,SAAUlL,IACtC,OAAO6K,GAAepY,KAAMuN,IAAO,EACvC,EAEMmL,GAAiC,SAAUnL,IAC7C,OAAO6K,GAAepY,KAAMuN,IAAO,EACvC,EACA,SAASoL,GAAwB1K,GAAKsJ,GAClC,IAAKtJ,GACD,OAAO,EAEX,IAAI2K,IAAoB,EACpBrB,QAAsC/T,IAAtB+T,EAAalP,OAC7BuQ,GAAoBrB,EAAalP,MAErC,MAAMwQ,EAAkBtB,GAAgBA,EAAauB,GACrD,IAAInZ,IAAiB,EACjB4X,QAAwC/T,IAAxB+T,EAAawB,SAC7BpZ,GAAiB4X,EAAawB,QAElC,IAAIC,IAAe,EACfzB,QAAoC/T,IAApB+T,EAAa0B,KAC7BD,GAAezB,EAAa0B,IAEhC,IAAI1J,EAAQtB,GACZ,KAAOsB,IAAUA,EAAMpO,eAAeqW,IAClCjI,EAAQ7D,GAAqB6D,GASjC,IAPKA,GAAStB,GAAIuJ,KAEdjI,EAAQtB,KAEPsB,GAGDA,EAAMyI,GACN,OAAO,EAEX,MAAMd,GAAoBK,GAAgBA,EAAaL,kBAGjDgC,EAAW,CAAC,EACZC,EAAyB5J,EAAMyI,GAA8BzI,EAAMiI,GACnE4B,GAA4B7J,EAAMhD,GAAWmL,IAC/CnI,EAAMmI,GACJ2B,GAAkB9J,EAAMhD,GAAWqL,IACrCrI,EAAMqI,GACJ0B,GAA2B/J,EAAMhD,GAAWuL,IAC9CvI,EAAMuI,GACV,IAAIyB,GASJ,SAASC,GAA0BrR,EAASsR,GACxC,OAAK/C,IAAuC,iBAAZvO,GAAwBA,IAI3CA,EAAQuR,QAEhBhD,IAAqB+C,EAGH,kBAAZtR,EACA,CAAEuR,QAASvR,EAASsR,SAAS,GAEnCtR,EAGkB,iBAAZA,IAA4C,IAApBA,EAAQsR,QAChCzQ,OAAO2Q,OAAO3Q,OAAO2Q,OAAO,CAAC,EAAGxR,GAAU,CAAEsR,SAAS,IAEzDtR,EALI,CAAEsR,SAAS,GANXtR,CAYf,CA5BIoP,GAAgBA,EAAaqC,UAC7BL,GAA6BhK,EAAMhD,GAAWgL,EAAaqC,UACvDrK,EAAMgI,EAAaqC,UA2B3B,MAsDMxV,EAAiBwU,GAtDM,SAAUpW,GAGnC,IAAI0W,EAASW,WAGb,OAAOV,EAAuB7Q,KAAK4Q,EAASjU,OAAQiU,EAASzK,UAAWyK,EAASQ,QAAUhB,GAAiCD,GAAyBS,EAAS/Q,QAClK,EAsCgC,SAAU3F,GACtC,OAAO2W,EAAuB7Q,KAAK4Q,EAASjU,OAAQiU,EAASzK,UAAWjM,EAAKJ,OAAQ8W,EAAS/Q,QAClG,EAQM3D,EAAeoU,GA/CM,SAAUpW,GAIjC,IAAKA,EAAK0V,UAAW,CACjB,MAAM4B,EAAmBlD,GAAqBpU,EAAKiM,WACnD,IAAIsL,EACAD,IACAC,EAAkBD,EAAiBtX,EAAKkX,QAAUvN,GAAWC,KAEjE,MAAM4N,EAAgBD,GAAmBvX,EAAKyC,OAAO8U,GACrD,GAAIC,EACA,QAAS/b,EAAI,EAAGA,EAAI+b,EAAc9b,OAAQD,IAEtC,GADqB+b,EAAc/b,KACduE,EAAM,CACvBwX,EAAc7G,OAAOlV,EAAG,GAExBuE,EAAK0V,WAAY,EACY,IAAzB8B,EAAc9b,SAGdsE,EAAKyX,YAAa,EAClBzX,EAAKyC,OAAO8U,GAAmB,MAEnC,KACJ,CAGZ,CAIA,GAAKvX,EAAKyX,WAGV,OAAOb,GAA0B9Q,KAAK9F,EAAKyC,OAAQzC,EAAKiM,UAAWjM,EAAKkX,QAAUhB,GAAiCD,GAAyBjW,EAAK2F,QACrJ,EAO8B,SAAU3F,GACpC,OAAO4W,GAA0B9Q,KAAK9F,EAAKyC,OAAQzC,EAAKiM,UAAWjM,EAAKJ,OAAQI,EAAK2F,QACzF,EAQM+R,GAAW3C,GAAgBA,EAAa4C,KAAQ5C,EAAa4C,KAL7B,SAAU3X,EAAMuC,GAClD,MAAMqV,SAAwBrV,EAC9B,MAA2B,aAAnBqV,GAAiC5X,EAAKb,WAAaoD,GACnC,WAAnBqV,GAA+B5X,EAAK8T,mBAAqBvR,CAClE,EAEMsV,EAAkBza,KAAK2M,GAAW,qBAClC+N,EAAgB5N,EAAQH,GAAW,mBACnCgO,EAAkB,SAAUC,EAAgBC,EAAWC,EAAkBC,EAAgB3B,GAAe,EAAOY,IAAU,GAC3H,OAAO,WACH,MAAM3U,GAASjF,MAAQ0M,EACvB,IAAI+B,GAAYzM,UAAU,GACtBuV,GAAgBA,EAAaqD,oBAC7BnM,GAAY8I,EAAaqD,kBAAkBnM,KAE/C,IAAI1J,GAAW/C,UAAU,GACzB,IAAK+C,GACD,OAAOyV,EAAe/S,MAAMzH,KAAMgC,WAEtC,GAAIgL,IAAwB,sBAAdyB,GAEV,OAAO+L,EAAe/S,MAAMzH,KAAMgC,WAKtC,IAAI6Y,IAAgB,EACpB,GAAwB,mBAAb9V,GAAyB,CAChC,IAAKA,GAASoT,YACV,OAAOqC,EAAe/S,MAAMzH,KAAMgC,WAEtC6Y,IAAgB,CACpB,CACA,GAAIhC,IAAoBA,EAAgB2B,EAAgBzV,GAAUE,GAAQjD,WACtE,OAEJ,MAAMyX,GAAU/C,MAAsB4D,IAAsD,IAArCA,EAAchK,QAAQ7B,IACvEtG,GAAUqR,GAA0BxX,UAAU,GAAIyX,IACxD,GAAIY,EAEA,QAASpc,GAAI,EAAGA,GAAIoc,EAAgBnc,OAAQD,KACxC,GAAIwQ,KAAc4L,EAAgBpc,IAC9B,OAAIwb,GACOe,EAAelS,KAAKrD,GAAQwJ,GAAW1J,GAAUoD,IAGjDqS,EAAe/S,MAAMzH,KAAMgC,WAKlD,MAAM0X,KAAWvR,KAAqC,kBAAZA,IAA+BA,GAAQuR,SAC3ErH,MAAOlK,IAA8B,iBAAZA,KAAuBA,GAAQkK,KACxDzR,GAAOhB,KAAKiB,QAClB,IAAIiZ,GAAmBlD,GAAqBnI,IACvCqL,KACD7C,GAAkBxI,GAAWyI,IAC7B4C,GAAmBlD,GAAqBnI,KAE5C,MAAMsL,GAAkBD,GAAiBJ,GAAUvN,GAAWC,IAC9D,IAiBIxK,GAjBAoY,GAAgB/U,GAAO8U,IACvBF,IAAa,EACjB,GAAIG,IAGA,GADAH,IAAa,EACTla,GACA,QAAS1B,GAAI,EAAGA,GAAI+b,GAAc9b,OAAQD,KACtC,GAAIic,GAAQF,GAAc/b,IAAI8G,IAE1B,YAMZiV,GAAgB/U,GAAO8U,IAAmB,GAG9C,MAAMe,GAAkB7V,GAAOpF,YAAYT,KACrC2b,GAAelE,GAAciE,IAC/BC,KACAnZ,GAASmZ,GAAatM,KAErB7M,KACDA,GAASkZ,GAAkBL,GACtBvD,GAAoBA,GAAkBzI,IAAaA,KAI5DyK,EAAS/Q,QAAUA,GACfkK,KAIA6G,EAAS/Q,QAAQkK,MAAO,GAE5B6G,EAASjU,OAASA,GAClBiU,EAASQ,QAAUA,GACnBR,EAASzK,UAAYA,GACrByK,EAASW,WAAaA,GACtB,MAAMxW,GAAOuV,GAAoBjC,QAAiCnT,EAE9DH,KACAA,GAAK6V,SAAWA,GAEpB,MAAM1W,GAAO5B,GAAK6D,kBAAkB7C,GAAQmD,GAAU1B,GAAMqX,EAAkBC,GA+B9E,OA5BAzB,EAASjU,OAAS,KAEd5B,KACAA,GAAK6V,SAAW,MAIhB7G,KACAlK,GAAQkK,MAAO,IAEZqE,IAA4C,kBAAjBlU,GAAK2F,UAGnC3F,GAAK2F,QAAUA,IAEnB3F,GAAKyC,OAASA,GACdzC,GAAKkX,QAAUA,GACflX,GAAKiM,UAAYA,GACboM,KAEArY,GAAK8T,iBAAmBvR,IAEvB6U,GAIDI,GAAcgB,QAAQxY,IAHtBwX,GAAcrS,KAAKnF,IAKnBwW,EACO/T,QADX,CAGJ,CACJ,EACAsK,SAAMiI,GAAsB+C,EAAgBpB,EAAwBlB,EAA2B7T,EAAgBI,EAAcwU,IACzHO,KACAhK,EAAM0L,gBAA0BV,EAAgBhB,GAxWlB,oBAiNJ,SAAU/W,GACpC,OAAO+W,GAA2BjR,KAAK4Q,EAASjU,OAAQiU,EAASzK,UAAWjM,EAAKJ,OAAQ8W,EAAS/Q,QACtG,EAqJsI3D,EAAcwU,IAAc,IAElKzJ,EAAMmI,GAAyB,WAC3B,MAAMzS,EAASjF,MAAQ0M,EACvB,IAAI+B,EAAYzM,UAAU,GACtBuV,GAAgBA,EAAaqD,oBAC7BnM,EAAY8I,EAAaqD,kBAAkBnM,IAE/C,MAAMtG,EAAUnG,UAAU,GACpB0X,IAAWvR,IAAqC,kBAAZA,GAA+BA,EAAQuR,SAC3E3U,EAAW/C,UAAU,GAC3B,IAAK+C,EACD,OAAOqU,GAA0B3R,MAAMzH,KAAMgC,WAEjD,GAAI6W,IACCA,EAAgBO,GAA2BrU,EAAUE,EAAQjD,WAC9D,OAEJ,MAAM8X,GAAmBlD,GAAqBnI,GAC9C,IAAIsL,GACAD,KACAC,GAAkBD,GAAiBJ,EAAUvN,GAAWC,KAE5D,MAAM4N,GAAgBD,IAAmB9U,EAAO8U,IAChD,GAAIC,GACA,QAAS/b,GAAI,EAAGA,GAAI+b,GAAc9b,OAAQD,KAAK,CAC3C,MAAMid,GAAelB,GAAc/b,IACnC,GAAIic,GAAQgB,GAAcnW,GAkBtB,OAjBAiV,GAAc7G,OAAOlV,GAAG,GAExBid,GAAahD,WAAY,EACI,IAAzB8B,GAAc9b,SAGdgd,GAAajB,YAAa,EAC1BhV,EAAO8U,IAAmB,KAID,iBAAdtL,KAEPxJ,EADyBoH,GAAqB,cAAgBoC,GACnC,MAGnCyM,GAAata,KAAK8D,WAAWwW,IACzBlC,GACO/T,OAEX,CAER,CAMJ,OAAOmU,GAA0B3R,MAAMzH,KAAMgC,UACjD,EACAuN,EAAMqI,GAA4B,WAC9B,MAAM3S,EAASjF,MAAQ0M,EACvB,IAAI+B,EAAYzM,UAAU,GACtBuV,GAAgBA,EAAaqD,oBAC7BnM,EAAY8I,EAAaqD,kBAAkBnM,IAE/C,MAAMoJ,EAAY,GACZU,EAAQ4C,GAAelW,EAAQiS,GAAoBA,GAAkBzI,GAAaA,GACxF,QAASxQ,EAAI,EAAGA,EAAIsa,EAAMra,OAAQD,IAAK,CACnC,MAAMuE,GAAO+V,EAAMta,GAEnB4Z,EAAUlQ,KADKnF,GAAK8T,iBAAmB9T,GAAK8T,iBAAmB9T,GAAKb,SAExE,CACA,OAAOkW,CACX,EACAtI,EAAMuI,GAAuC,WACzC,MAAM7S,EAASjF,MAAQ0M,EACvB,IAAI+B,EAAYzM,UAAU,GAC1B,GAAKyM,EAiBA,CACG8I,GAAgBA,EAAaqD,oBAC7BnM,EAAY8I,EAAaqD,kBAAkBnM,IAE/C,MAAMqL,EAAmBlD,GAAqBnI,GAC9C,GAAIqL,EAAkB,CAClB,MAEMvB,GAAQtT,EAFU6U,EAAiB1N,KAGnCgP,GAAenW,EAFU6U,EAAiB3N,KAGhD,GAAIoM,GAAO,CACP,MAAM8C,GAAc9C,GAAMzM,QAC1B,QAAS7N,GAAI,EAAGA,GAAIod,GAAYnd,OAAQD,KAAK,CACzC,MAAMuE,GAAO6Y,GAAYpd,IAEzB+B,KAAK0X,GAAuBpP,KAAKtI,KAAMyO,EADxBjM,GAAK8T,iBAAmB9T,GAAK8T,iBAAmB9T,GAAKb,SACRa,GAAK2F,QACrE,CACJ,CACA,GAAIiT,GAAc,CACd,MAAMC,GAAcD,GAAatP,QACjC,QAAS7N,GAAI,EAAGA,GAAIod,GAAYnd,OAAQD,KAAK,CACzC,MAAMuE,GAAO6Y,GAAYpd,IAEzB+B,KAAK0X,GAAuBpP,KAAKtI,KAAMyO,EADxBjM,GAAK8T,iBAAmB9T,GAAK8T,iBAAmB9T,GAAKb,SACRa,GAAK2F,QACrE,CACJ,CACJ,CACJ,KA5CgB,CACZ,MAAMmT,EAAOtS,OAAOsS,KAAKrW,GACzB,QAAShH,EAAI,EAAGA,EAAIqd,EAAKpd,OAAQD,IAAK,CAClC,MACMsd,GAAQzE,GAAuB0E,KADxBF,EAAKrd,IAElB,IAAIwd,GAAUF,IAASA,GAAM,GAKzBE,IAAuB,mBAAZA,IACXzb,KAAK8X,GAAqCxP,KAAKtI,KAAMyb,GAE7D,CAEAzb,KAAK8X,GAAqCxP,KAAKtI,KAAM,iBACzD,CA6BA,GAAIgZ,GACA,OAAOhZ,IAEf,EAEAqL,GAAsBkE,EAAMiI,GAAqB2B,GACjD9N,GAAsBkE,EAAMmI,GAAwB0B,IAChDE,IACAjO,GAAsBkE,EAAMuI,GAAsCwB,IAElED,IACAhO,GAAsBkE,EAAMqI,GAA2ByB,KAEpD,CACX,CACA,IAAIqC,GAAU,GACd,QAASzd,GAAI,EAAGA,GAAIqZ,EAAKpZ,OAAQD,KAC7Byd,GAAQzd,IAAK0a,GAAwBrB,EAAKrZ,IAAIsZ,GAElD,OAAOmE,EACX,CACA,SAASP,GAAelW,EAAQwJ,GAC5B,IAAKA,EAAW,CACZ,MAAMkN,EAAa,GACnB,QAASzN,KAAQjJ,EAAQ,CACrB,MAAMsW,EAAQzE,GAAuB0E,KAAKtN,GAC1C,IAAIuN,EAAUF,GAASA,EAAM,GAC7B,GAAIE,KAAahN,GAAagN,IAAYhN,GAAY,CAClD,MAAM8J,EAAQtT,EAAOiJ,GACrB,GAAIqK,EACA,QAASta,EAAI,EAAGA,EAAIsa,EAAMra,OAAQD,IAC9B0d,EAAWhU,KAAK4Q,EAAMta,GAGlC,CACJ,CACA,OAAO0d,CACX,CACA,IAAI5B,EAAkBnD,GAAqBnI,GACtCsL,IACD9C,GAAkBxI,GAClBsL,EAAkBnD,GAAqBnI,IAE3C,MAAMmN,EAAoB3W,EAAO8U,EAAgB3N,KAC3CyP,EAAmB5W,EAAO8U,EAAgB5N,KAChD,OAAKyP,EAIMC,EAAmBD,EAAkBE,OAAOD,GAC/CD,EAAkB9P,QAJf+P,EAAmBA,EAAiB/P,QAAU,EAM7D,CACA,SAASnB,GAAoB/L,EAAQ4R,GACjC,MAAMuL,EAAQnd,EAAOmd,MACjBA,GAASA,EAAM9S,WACfuH,EAAIjG,YAAYwR,EAAM9S,UAAW,2BAA6BlE,GAAa,SAAUjG,EAAMyJ,GACvFzJ,EAAKkY,KAAgC,EAIrCjS,GAAYA,EAAS0C,MAAM3I,EAAMyJ,EACrC,EAER,CASA,SAASgD,GAAeiF,EAAKvL,EAAQ+W,EAAYC,EAAQC,GACrD,MAAMjS,EAASrK,KAAKF,WAAWuc,GAC/B,GAAIhX,EAAOgF,GACP,OAEJ,MAAMkS,EAAiBlX,EAAOgF,GAAUhF,EAAOgX,GAC/ChX,EAAOgX,GAAU,SAAU7c,EAAMgd,EAAMjU,GACnC,OAAIiU,GAAQA,EAAKnT,WACbiT,EAAUG,QAAQ,SAAU1a,GACxB,MAAMC,GAAS,GAAGoa,KAAcC,MAAata,EACvCsH,GAAYmT,EAAKnT,UASvB,IACI,GAAIA,GAAU9H,eAAeQ,GAAW,CACpC,MAAM2a,GAAa9L,EAAIzF,+BAA+B9B,GAAWtH,GAC7D2a,IAAcA,GAAWzU,OACzByU,GAAWzU,MAAQ2I,EAAIrF,oBAAoBmR,GAAWzU,MAAOjG,IAC7D4O,EAAIlF,kBAAkB8Q,EAAKnT,UAAWtH,EAAU2a,KAE3CrT,GAAUtH,KACfsH,GAAUtH,GAAY6O,EAAIrF,oBAAoBlC,GAAUtH,GAAWC,IAE3E,MACSqH,GAAUtH,KACfsH,GAAUtH,GAAY6O,EAAIrF,oBAAoBlC,GAAUtH,GAAWC,IAKvE,CAHJ,MAGI,CAER,GAEGua,EAAe7T,KAAKrD,EAAQ7F,EAAMgd,EAAMjU,EACnD,EACAqI,EAAInF,sBAAsBpG,EAAOgX,GAASE,EAC9C,CASA,SAAS/Q,GAAiBnG,EAAQ8J,EAAcwN,GAC5C,IAAKA,GAAgD,IAA5BA,EAAiBre,OACtC,OAAO6Q,EAEX,MAAMyN,EAAMD,EAAiBE,OAAOC,GAAMA,EAAGzX,SAAWA,GACxD,IAAKuX,GAAsB,IAAfA,EAAIte,OACZ,OAAO6Q,EAEX,MAAM4N,EAAyBH,EAAI,GAAGD,iBACtC,OAAOxN,EAAa0N,OAAOG,IAA6C,IAAvCD,EAAuBrM,QAAQsM,GACpE,CACA,SAASC,GAAwB5X,EAAQ8J,EAAcwN,EAAkBtT,GAGhEhE,GAILqF,GAAkBrF,EADSmG,GAAiBnG,EAAQ8J,EAAcwN,GACpBtT,EAClD,CAKA,SAAS6T,GAAgB7X,GACrB,OAAO+D,OAAO+T,oBAAoB9X,GAC7BwX,OAAOrd,GAAQA,EAAK4d,WAAW,OAAS5d,EAAKlB,OAAS,GACtD+e,IAAI7d,GAAQA,EAAKd,UAAU,GACpC,CAyCAsB,KAAK2Q,aAAa,OAAQ,CAAC3R,EAAQgB,EAAM4Q,KAGrC,MAAM0M,EAAaJ,GAAgBle,GACnC4R,EAAIlG,kBAAoBA,GACxBkG,EAAIjG,YAAcA,GAClBiG,EAAIhG,cAAgBA,GACpBgG,EAAI9F,eAAiBA,GAOrB,MAAMyS,EAA6Bvd,EAAKF,WAAW,uBAC7C0d,EAA0Bxd,EAAKF,WAAW,oBAC5Cd,EAAOwe,KACPxe,EAAOue,GAA8Bve,EAAOwe,IAE5Cxe,EAAOue,KACPvd,EAAKud,GAA8Bvd,EAAKwd,GACpCxe,EAAOue,IAEf3M,EAAI7F,oBAAsBA,GAC1B6F,EAAInG,iBAAmBA,GACvBmG,EAAI5F,WAAaA,GACjB4F,EAAI1F,qBAAuBA,GAC3B0F,EAAIzF,+BAAiCA,GACrCyF,EAAIxF,aAAeA,GACnBwF,EAAIvF,WAAaA,GACjBuF,EAAItF,WAAaA,GACjBsF,EAAIrF,oBAAsBA,GAC1BqF,EAAIpF,iBAAmBA,GACvBoF,EAAInF,sBAAwBA,GAC5BmF,EAAIlF,kBAAoBtC,OAAOyC,eAC/B+E,EAAIjF,eAAiBA,GACrBiF,EAAI3F,iBAAmB,MACnBgM,iBACAD,wBACAsG,aACAhQ,aACAE,SACAJ,UACAb,YACAC,aACAC,sBACAN,0BACAC,8BACJ,GAUJ,MAAMqR,GAAa9Q,GAAW,YAC9B,SAAS+Q,GAAWze,EAAQ0e,EAASC,EAAYC,GAC7C,IAAI7N,EAAY,KACZ8N,EAAc,KAElBF,GAAcC,EACd,MAAME,EAAkB,CAAC,EACzB,SAAS/Z,EAAapB,GAClB,MAAMa,EAAOb,EAAKa,KAClBA,SAAKkF,KAAK,GAAK,WACX,OAAO/F,EAAKJ,OAAOqF,MAAMzH,KAAMgC,UACnC,EACAqB,EAAK0F,SAAW6G,EAAUnI,MAAM5I,EAAQwE,EAAKkF,MACtC/F,CACX,CACA,SAASob,EAAUpb,GACf,OAAOkb,EAAYpV,KAAKzJ,EAAQ2D,EAAKa,KAAK0F,SAC9C,CACA6G,EACIrF,GAAY1L,EAfhB0e,GAAWE,EAeuB1Y,GAAa,SAAUjG,EAAMyJ,IACvD,GAAuB,mBAAZA,GAAK,GAAmB,CAC/B,MAAMJ,GAAU,CACZ7E,WAA2B,aAAfma,EACZI,MAAuB,YAAfJ,GAA2C,aAAfA,EAA6BlV,GAAK,IAAM,OACxE/E,EACJ+E,KAAMA,IAEJ5G,GAAW4G,GAAK,GACtBA,GAAK,GAAK,WACN,IACI,OAAO5G,GAAS8F,MAAMzH,KAAMgC,UAsBhC,CArBA,QASUmG,GAAQ7E,aACsB,iBAArB6E,GAAQY,gBAGR4U,EAAgBxV,GAAQY,UAE1BZ,GAAQY,WAGbZ,GAAQY,SAASsU,IAAc,MAG3C,CACJ,EACA,MAAM7a,GAAO8J,GAAiCiR,EAAShV,GAAK,GAAIJ,GAASvE,EAAcga,GACvF,IAAKpb,GACD,OAAOA,GAGX,MAAMsb,GAAStb,GAAKa,KAAK0F,SAkBzB,MAjBsB,iBAAX+U,GAGPH,EAAgBG,IAAUtb,GAErBsb,KAGLA,GAAOT,IAAc7a,IAIrBsb,IAAUA,GAAOC,KAAOD,GAAOE,OAA+B,mBAAfF,GAAOC,KAC9B,mBAAjBD,GAAOE,QACdxb,GAAKub,IAAMD,GAAOC,IAAIE,KAAKH,IAC3Btb,GAAKwb,MAAQF,GAAOE,MAAMC,KAAKH,KAEb,iBAAXA,IAAuBA,GACvBA,GAEJtb,EACX,CAGI,OAAOuC,EAAS0C,MAAM5I,EAAQ0J,GAEtC,GACJmV,EACInT,GAAY1L,EAAQ2e,EAAazY,GAAa,SAAUjG,EAAMyJ,IAC1D,MAAM2V,GAAK3V,GAAK,GAChB,IAAI/F,GACc,iBAAP0b,GAEP1b,GAAOmb,EAAgBO,KAIvB1b,GAAO0b,IAAMA,GAAGb,IAEX7a,KACDA,GAAO0b,KAGX1b,IAA6B,iBAAdA,GAAKI,KACD,iBAAfJ,GAAKE,QACJF,GAAKe,UAAYf,GAAKa,KAAKC,YAAgC,IAAlBd,GAAKW,YAC7B,iBAAP+a,UACAP,EAAgBO,IAElBA,KACLA,GAAGb,IAAc,MAGrB7a,GAAK5B,KAAK8D,WAAWlC,KAKzBuC,EAAS0C,MAAM5I,EAAQ0J,GAE/B,EACR,CA4DA3I,KAAK2Q,aAAa,SAAW3R,IACzB,MAAMuf,EAAcvf,EAAOgB,KAAKF,WAAW,gBACvCye,GACAA,GAAY,GAGpBve,KAAK2Q,aAAa,iBAAkB,CAAC3R,EAAQgB,EAAM4Q,KAC/CA,EAAIjG,YAAY3L,EAAQ,iBAAkBmG,GAC/B,SAAUjG,EAAMyJ,GACnB3I,EAAKiB,QAAQsD,kBAAkB,iBAAkBoE,EAAK,GAC1D,EACH,GAEL3I,KAAK2Q,aAAa,SAAW3R,IACzB,MAAMkO,EAAM,MACNsR,EAAQ,QACdd,GAAW1e,EAAQkO,EAAKsR,EAAO,WAC/Bd,GAAW1e,EAAQkO,EAAKsR,EAAO,YAC/Bd,GAAW1e,EAAQkO,EAAKsR,EAAO,YAAW,GAE9Cxe,KAAK2Q,aAAa,wBAA0B3R,IACxC0e,GAAW1e,EAAQ,UAAW,SAAU,kBACxC0e,GAAW1e,EAAQ,aAAc,YAAa,kBAC9C0e,GAAW1e,EAAQ,gBAAiB,eAAgB,iBAAgB,GAExEgB,KAAK2Q,aAAa,WAAY,CAAC3R,EAAQgB,KACnC,MAAMye,EAAkB,CAAC,QAAS,SAAU,WAC5C,QAASpgB,EAAI,EAAGA,EAAIogB,EAAgBngB,OAAQD,IAExCsM,GAAY3L,EADCyf,EAAgBpgB,GACH,CAAC8G,EAAUkF,EAAQ7K,IAClC,SAAUkf,EAAG/V,GAChB,OAAO3I,EAAKiB,QAAQoB,IAAI8C,EAAUnG,EAAQ2J,EAAMnJ,EACpD,EAER,GAEJQ,KAAK2Q,aAAa,cAAe,CAAC3R,EAAQgB,EAAM4Q,MA/ChD,SAAS+N,GAAW3f,EAAQ4R,GACxBA,EAAI7F,oBAAoB/L,EAAQ4R,EACpC,EA8CI+N,CAAW3f,EAAQ4R,GAxEvB,SAASgO,GAAiB9R,EAAS8D,GAC/B,GAAI5Q,KAAK4Q,EAAIvG,OAAO,qBAEhB,OAEJ,MAAQiT,aAAYtG,uBAAsBzK,WAAUC,YAAWC,sBAAuBmE,EAAI3F,mBAE1F,QAAS5M,EAAI,EAAGA,EAAIif,EAAWhf,OAAQD,IAAK,CACxC,MAAMwQ,EAAYyO,EAAWjf,GAGvBgM,GAASoC,GAFQoC,EAAYrC,GAG7BiL,GAAgBhL,GAFAoC,EAAYtC,GAGlCyK,EAAqBnI,GAAa,CAAC,EACnCmI,EAAqBnI,GAAWrC,GAAanC,GAC7C2M,EAAqBnI,GAAWtC,GAAYkL,EAChD,CACA,MAAMoH,EAAe/R,EAAQgS,YACxBD,GAAiBA,EAAaxV,WAGnCuH,EAAInG,iBAAiBqC,EAAS8D,EAAK,CAACiO,GAAgBA,EAAaxV,WAErE,CAkDIuV,CAAiB5f,EAAQ4R,GAEzB,MAAMmO,EAA4B/f,EAAO+f,0BACrCA,GAA6BA,EAA0B1V,WACvDuH,EAAInG,iBAAiBzL,EAAQ4R,EAAK,CAACmO,EAA0B1V,WAAU,GAG/ErJ,KAAK2Q,aAAa,mBAAoB,CAAC3R,EAAQgB,EAAM4Q,KACjDtF,GAAW,oBACXA,GAAW,yBAAwB,GAEvCtL,KAAK2Q,aAAa,uBAAwB,CAAC3R,EAAQgB,EAAM4Q,KACrDtF,GAAW,uBAAsB,GAErCtL,KAAK2Q,aAAa,aAAc,CAAC3R,EAAQgB,EAAM4Q,KAC3CtF,GAAW,aAAY,GAE3BtL,KAAK2Q,aAAa,cAAe,CAAC3R,EAAQgB,EAAM4Q,MA9UhD,SAASoO,GAAwBpO,EAAK9D,GAIlC,GAHIM,KAAWI,IAGXxN,KAAK4Q,EAAIvG,OAAO,gBAEhB,OAEJ,MAAMsS,EAAmB7P,EAAQmS,4BAEjC,IAAIC,EAAe,GACnB,GAAI5R,GAAW,CACX,MAAMT,EAAiB5N,OACvBigB,EAAeA,EAAahD,OAAO,CAC/B,WAAY,aAAc,UAAW,cAAe,kBAAmB,mBACvE,sBAAuB,mBAAoB,oBAAqB,qBAAsB,WAE1F,MAAMiD,EAvwCd,SAASC,KACL,IACI,MAAM7O,EAAK1D,GAAe2D,UAAUC,UACpC,IAA4B,IAAxBF,EAAGG,QAAQ,WAA8C,IAA3BH,EAAGG,QAAQ,YACzC,OAAO,CAIf,CAFA,MAEA,CACA,OAAO,CACX,CA6vCsC0O,GAAS,CAAC,CAAE/Z,OAAQwH,EAAgB8P,iBAAkB,CAAC,WAAc,GAGnGM,GAAwBpQ,EAAgBqQ,GAAgBrQ,GAAiB8P,GAAmBA,EAAiBT,OAAOiD,GAA2CrT,GAAqBe,GACxL,CACAqS,EAAeA,EAAahD,OAAO,CAC/B,iBAAkB,4BAA6B,WAAY,aAAc,mBACzE,cAAe,iBAAkB,YAAa,cAElD,QAAS7d,EAAI,EAAGA,EAAI6gB,EAAa5gB,OAAQD,IAAK,CAC1C,MAAMgH,EAASyH,EAAQoS,EAAa7gB,IACpCgH,GAAUA,EAAOgE,WACb4T,GAAwB5X,EAAOgE,UAAW6T,GAAgB7X,EAAOgE,WAAYsT,EACrF,CACJ,CAgTIqC,CAAwBpO,EAAK5R,EAAM,GAEvCgB,KAAK2Q,aAAa,iBAAkB,CAAC3R,EAAQgB,EAAM4Q,MA7GnD,SAASyO,GAAoBvS,EAAS8D,GAClC,MAAQtD,YAAWE,SAAUoD,EAAI3F,oBAC3BqC,GAAcE,IAAWV,EAAQwS,gBAAuB,mBAAoBxS,GAIlF8D,EAAIjF,eAAeiF,EAAK9D,EAAQwS,eAAgB,iBAAkB,SADhD,CAAC,oBAAqB,uBAAwB,kBAAmB,4BAEvF,CAuGID,CAAoBrgB,EAAQ4R,EAAG,GAEnC5Q,KAAK2Q,aAAa,MAAO,CAAC3R,EAAQgB,MAS9B,SAASuf,EAAStgB,GACd,MAAMugB,EAAiBvgB,EAAOugB,eAC9B,IAAKA,EAED,OAEJ,MAAMC,GAA0BD,EAAenW,UAI/C,IAAIqW,GAAiBD,GAAwBpT,IACzCsT,GAAoBF,GAAwBnT,IAChD,IAAKoT,GAAgB,CACjB,MAAMX,EAA4B9f,EAAO8f,0BACzC,GAAIA,EAA2B,CAC3B,MAAMa,EAAqCb,EAA0B1V,UACrEqW,GAAiBE,EAAmCvT,IACpDsT,GAAoBC,EAAmCtT,GAC3D,CACJ,CACA,MAAMuT,GAAqB,mBACrBC,GAAY,YAClB,SAAS9b,GAAapB,GAClB,MAAMa,EAAOb,EAAKa,KACZ4B,GAAS5B,EAAK4B,OACpBA,GAAO0a,IAAiB,EACxB1a,GAAO2a,IAA8B,EAErC,MAAMnS,GAAWxI,GAAO4a,GACnBP,KACDA,GAAiBra,GAAOgH,IACxBsT,GAAoBta,GAAOiH,KAE3BuB,IACA8R,GAAkBjX,KAAKrD,GAAQwa,GAAoBhS,IAEvD,MAAMqS,GAAc7a,GAAO4a,GAAgB,KACvC,GAAI5a,GAAO8a,aAAe9a,GAAO+a,KAG7B,IAAK3c,EAAK4c,SAAWhb,GAAO0a,IAAkBnd,EAAKE,QAAUgd,GAAW,CAQpE,MAAMQ,GAAYjb,GAAOrF,EAAKF,WAAW,cACzC,GAAsB,IAAlBuF,GAAOsP,QAAgB2L,IAAaA,GAAUhiB,OAAS,EAAG,CAC1D,MAAMiiB,GAAY3d,EAAKJ,OACvBI,EAAKJ,OAAS,WAGV,MAAM8d,GAAYjb,GAAOrF,EAAKF,WAAW,cACzC,QAASzB,EAAI,EAAGA,EAAIiiB,GAAUhiB,OAAQD,IAC9BiiB,GAAUjiB,KAAOuE,GACjB0d,GAAU/M,OAAOlV,EAAG,IAGvBoF,EAAK4c,SAAWzd,EAAKE,QAAUgd,IAChCS,GAAU7X,KAAK9F,EAEvB,EACA0d,GAAUvY,KAAKnF,EACnB,MAEIA,EAAKJ,QAEb,MACUiB,EAAK4c,UAAqC,IAA1Bhb,GAAO0a,KAE7B1a,GAAO2a,IAA8B,IAIjDN,UAAehX,KAAKrD,GAAQwa,GAAoBK,IAC7B7a,GAAOmb,KAEtBnb,GAAOmb,GAAY5d,GAEvB6d,EAAW5Y,MAAMxC,GAAQ5B,EAAKkF,MAC9BtD,GAAO0a,IAAiB,EACjBnd,CACX,CACA,SAAS8d,KAAwB,CACjC,SAAS1C,EAAUpb,GACf,MAAMa,EAAOb,EAAKa,KAGlBA,SAAK4c,SAAU,EACRM,GAAY9Y,MAAMpE,EAAK4B,OAAQ5B,EAAKkF,KAC/C,CACA,MAAMiY,GAAajW,GAAY8U,GAAyB,OAAQ,IAAM,SAAUvgB,EAAMyJ,GAClFzJ,SAAK2hB,GAAuB,GAAXlY,EAAK,GACtBzJ,EAAK4hB,GAAWnY,EAAK,GACdiY,GAAW/Y,MAAM3I,EAAMyJ,EAClC,GAEMoY,GAAoBpU,GAAW,qBAC/BqU,GAAsBrU,GAAW,uBACjC8T,EAAa9V,GAAY8U,GAAyB,OAAQ,IAAM,SAAUvgB,EAAMyJ,GAOlF,IAN0C,IAAtC3I,EAAKiB,QAAQ+f,KAMb9hB,EAAK2hB,GAEL,OAAOJ,EAAW5Y,MAAM3I,EAAMyJ,GAE7B,CACD,MAAMJ,GAAU,CAAElD,OAAQnG,EAAM+hB,IAAK/hB,EAAK4hB,GAAUpd,YAAY,EAAOiF,KAAMA,EAAM0X,SAAS,GACtFzd,GAAO8J,GAhBS,sBAgB+CgU,GAAqBnY,GAASvE,GAAcga,GAC7G9e,IAA6C,IAArCA,EAAK8gB,KAAyCzX,GAAQ8X,SAC9Dzd,GAAKE,QAAUgd,IAIfld,GAAKJ,QAEb,CACJ,GACMme,GAAchW,GAAY8U,GAAyB,QAAS,IAAM,SAAUvgB,EAAMyJ,GACpF,MAAM/F,GAtHV,SAASse,GAAgB7b,GACrB,OAAOA,EAAOmb,EAClB,CAoHiBU,CAAgBhiB,GAC7B,GAAI0D,IAA4B,iBAAbA,GAAKI,KAAkB,CAKtC,GAAqB,MAAjBJ,GAAKe,UAAqBf,GAAKa,MAAQb,GAAKa,KAAK4c,QACjD,OAEJzd,GAAK5B,KAAK8D,WAAWlC,GACzB,UAC6C,IAApC5C,EAAKiB,QAAQ8f,IAElB,OAAOJ,GAAY9Y,MAAM3I,EAAMyJ,EAKvC,EACJ,CAvJA4W,CAASvgB,GACT,MAAMwhB,EAAW7T,GAAW,WACtBkU,EAAWlU,GAAW,WACtBsT,EAAetT,GAAW,eAC1BoT,EAAgBpT,GAAW,gBAC3BmU,EAAUnU,GAAW,UACrBqT,EAA6BrT,GAAW,0BAiJ9C,GAEJ3M,KAAK2Q,aAAa,cAAgB3R,IAE1BA,EAAOwR,WAAgBxR,EAAOwR,UAAa2Q,aA5hEnD,SAASC,GAAe/X,EAAWgY,GAC/B,MAAMrf,EAASqH,EAAUpJ,YAAYT,KACrC,QAASnB,EAAI,EAAGA,EAAIgjB,EAAQ/iB,OAAQD,IAAK,CACrC,MAAMmB,EAAO6hB,EAAQhjB,GACf8G,EAAWkE,EAAU7J,GAC3B,GAAI2F,EAAU,CAEV,IAAK4H,GADiB5B,GAA+B9B,EAAW7J,IAE5D,SAEJ6J,EAAU7J,IAAU2F,IAChB,MAAMgL,EAAU,WACZ,OAAOhL,EAAS0C,MAAMzH,KAAMwK,GAAcxI,UAAWJ,EAAS,IAAMxC,GACxE,EACAiM,UAAsB0E,EAAShL,GACxBgL,CACX,EANU3Q,CAMP2F,EACP,CACJ,CACJ,CA0gEQic,CAAepiB,EAAOwR,UAAa2Q,YAAa,CAAC,qBAAsB,iBAAgB,GAG/FnhB,KAAK2Q,aAAa,wBAAyB,CAAC3R,EAAQgB,KAEhD,SAASshB,EAA4BzF,GACjC,OAAO,SAAU9K,GACMwK,GAAevc,EAAQ6c,GAC/BY,QAAQxZ,IAGf,MAAMse,EAAwBviB,EAAOuiB,sBACrC,GAAIA,EAAuB,CACvB,MAAMC,EAAM,IAAID,EAAsB1F,EAAS,CAAEvJ,QAASvB,EAAEuB,QAASuC,OAAQ9D,EAAEC,YAC/E/N,EAAUT,OAAOgf,EACrB,GAER,CACJ,CACIxiB,EAAOuiB,wBACPvhB,EAAK2M,GAAW,qCACZ2U,EAA4B,sBAChCthB,EAAK2M,GAAW,4BACZ2U,EAA4B,oBAAkB,EAEzD", "names": ["Error", "$localize$1", "messageParts", "expressions", "translate", "translation", "message", "stripBlock", "raw", "i", "length", "messagePart", "rawMessagePart", "char<PERSON>t", "substring", "findEndOfBlock", "cooked", "cookedIndex", "rawIndex", "globalThis", "global", "window", "self", "WorkerGlobalScope", "ɵ_global", "ɵ$localize", "performance", "mark", "name", "performanceMeasure", "label", "measure", "symbolPrefix", "__Zone_symbol_prefix", "__symbol__", "checkDuplicate", "Zone", "constructor", "parent", "zoneSpec", "this", "_parent", "_name", "_properties", "properties", "_zoneDelegate", "_ZoneDelegate", "static", "Promise", "patches", "ZoneAwarePromise", "root", "zone", "current", "_currentZoneFrame", "currentTask", "_currentTask", "fn", "ignoreDuplicate", "hasOwnProperty", "perfName", "_api", "get", "key", "getZoneWith", "fork", "wrap", "callback", "source", "_callback", "intercept", "runGuarded", "arguments", "run", "applyThis", "applyArgs", "invoke", "error", "handleError", "runTask", "task", "NO_ZONE", "state", "notScheduled", "type", "eventTask", "macroTask", "re<PERSON><PERSON><PERSON><PERSON><PERSON>", "running", "_transitionTo", "scheduled", "runCount", "previousTask", "data", "isPeriodic", "cancelFn", "undefined", "invokeTask", "unknown", "_updateTaskCount", "scheduleTask", "newZone", "scheduling", "zoneDelegates", "_zoneDelegates", "_zone", "err", "scheduleMicroTask", "customSchedule", "ZoneTask", "microTask", "scheduleMacroTask", "customCancel", "scheduleEventTask", "cancelTask", "canceling", "count", "DELEGATE_ZS", "onHasTask", "delegate", "_", "target", "hasTaskState", "hasTask", "onScheduleTask", "onInvokeTask", "onCancelTask", "parentDelegate", "_taskCounts", "_parentDelegate", "_forkZS", "onFork", "_forkDlgt", "_forkCurrZone", "_interceptZS", "onIntercept", "_interceptDlgt", "_interceptCurrZone", "_invokeZS", "onInvoke", "_invokeDlgt", "_invokeCurrZone", "_handleErrorZS", "onHandleError", "_handleErrorDlgt", "_handleErrorCurrZone", "_scheduleTaskZS", "_scheduleTaskDlgt", "_scheduleTaskCurrZone", "_invokeTaskZS", "_invokeTaskDlgt", "_invokeTaskCurrZone", "_cancelTaskZS", "_cancelTaskDlgt", "_cancelTaskCurrZone", "_hasTaskZS", "_hasTaskDlgt", "_hasTaskDlgtOwner", "_hasTaskCurrZone", "zoneSpecHasTask", "targetZone", "apply", "returnTask", "push", "scheduleFn", "value", "isEmpty", "counts", "prev", "next", "change", "options", "_state", "useG", "call", "args", "_numberOfNestedTaskFrames", "drainMicroTaskQueue", "cancelScheduleRequest", "toState", "fromState1", "fromState2", "toString", "handleId", "Object", "prototype", "toJSON", "symbolSetTimeout", "symbolPromise", "symbolThen", "nativeMicroTaskQueuePromise", "_microTaskQueue", "_isDrainingMicrotaskQueue", "nativeScheduleMicroTask", "func", "resolve", "nativeThen", "then", "queue", "onUnhandledError", "microtaskDrainDone", "symbol", "currentZoneFrame", "noop", "showUncaughtError", "patchEventTarget", "patchOnProperties", "patchMethod", "bindArguments", "patchThen", "patchMacroTask", "patchEventPrototype", "isIEOrEdge", "getGlobalObjects", "ObjectDefineProperty", "ObjectGetOwnPropertyDescriptor", "ObjectCreate", "ArraySlice", "patchClass", "wrapWithCurrentZone", "filterProperties", "attachOriginToPatched", "_redefineProperty", "patchCallbacks", "getOwnPropertyDescriptor", "defineProperty", "ObjectGetPrototypeOf", "getPrototypeOf", "create", "Array", "slice", "ADD_EVENT_LISTENER_STR", "REMOVE_EVENT_LISTENER_STR", "ZONE_SYMBOL_ADD_EVENT_LISTENER", "ZONE_SYMBOL_REMOVE_EVENT_LISTENER", "TRUE_STR", "FALSE_STR", "ZONE_SYMBOL_PREFIX", "scheduleMacroTaskWithCurrentZone", "zoneSymbol", "isWindowExists", "internalWindow", "_global", "isPropertyWritable", "propertyDesc", "writable", "set", "isWebWorker", "isNode", "process", "<PERSON><PERSON><PERSON><PERSON>", "HTMLElement", "isMix", "zoneSymbolEventNames$1", "wrapFn", "event", "eventNameSymbol", "listener", "result", "errorEvent", "filename", "lineno", "colno", "preventDefault", "patchProperty", "obj", "prop", "desc", "enumerable", "configurable", "onPropPatchedSymbol", "originalDescGet", "originalDescSet", "eventName", "newValue", "removeEventListener", "addEventListener", "REMOVE_ATTRIBUTE", "removeAttribute", "onProperties", "j", "originalInstanceKey", "className", "OriginalClass", "a", "instance", "patchFn", "proto", "<PERSON><PERSON><PERSON>", "patchDelegate", "funcName", "metaCreator", "setNative", "cbIdx", "meta", "patched", "original", "isDetectedIEOrEdge", "ieOrEdge", "ua", "navigator", "userAgent", "indexOf", "__load_patch", "api", "_uncaughtPromiseErrors", "isDisableWrappingUncaughtPromiseRejection", "e", "rejection", "console", "stack", "uncaughtPromiseError", "shift", "throwOriginal", "handleUnhandledRejection", "UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL", "handler", "isThenable", "forwardResolution", "forwardRejection", "reject", "symbolState", "symbolValue", "symbolFinally", "symbolParentPromiseValue", "symbolParentPromiseState", "UNRESOLVED", "RESOLVED", "REJECTED", "makeResolver", "promise", "v", "resolvePromise", "once", "wasCalled", "wrappedFunction", "CURRENT_TASK_TRACE_SYMBOL", "onceWrapper", "TypeError", "clearRejectedNoCatch", "trace", "creationTrace", "scheduleResolveOrReject", "readableObjectToString", "JSON", "stringify", "REJECTION_HANDLED_HANDLER", "splice", "chainPromise", "onFulfilled", "onRejected", "promiseState", "parentPromiseValue", "isFinallyPromise", "AggregateError", "values", "Symbol", "iterator", "promises", "finished", "errors", "res", "rej", "onResolve", "onReject", "allWithCallback", "then<PERSON>allback", "status", "<PERSON><PERSON><PERSON><PERSON>", "reason", "unresolvedCount", "valueIndex", "resolvedV<PERSON>ues", "curValueIndex", "thenErr", "executor", "toStringTag", "species", "_a", "C", "catch", "finally", "onFinally", "race", "all", "NativePromise", "symbolThenPatched", "Ctor", "originalThen", "zoneify", "resultPromise", "ctor", "originalFunctionToString", "Function", "ORIGINAL_DELEGATE_SYMBOL", "PROMISE_SYMBOL", "ERROR_SYMBOL", "newFunctionToString", "originalDelegate", "nativePromise", "nativeError", "originalObjectToString", "passiveSupported", "OPTIMIZED_ZONE_EVENT_TASK_DATA", "zoneSymbolEventNames", "globalSources", "EVENT_NAME_SYMBOL_REGX", "RegExp", "IMMEDIATE_PROPAGATION_SYMBOL", "prepareEventNames", "eventNameToString", "falseEventName", "trueEventName", "symbolCapture", "apis", "patchOptions", "ADD_EVENT_LISTENER", "add", "REMOVE_EVENT_LISTENER", "rm", "LISTENERS_EVENT_LISTENER", "listeners", "REMOVE_ALL_LISTENERS_EVENT_LISTENER", "rmAll", "zoneSymbolAddEventListener", "ADD_EVENT_LISTENER_SOURCE", "isRemoved", "handleEvent", "globalCallback", "context", "isCapture", "tasks", "copyTasks", "globalZoneAwareCallback", "globalZoneAwareCaptureCallback", "patchEventTargetMethods", "useGlobalCallback", "validate<PERSON><PERSON><PERSON>", "vh", "chkDup", "<PERSON><PERSON><PERSON><PERSON>", "rt", "taskData", "nativeAddEventListener", "nativeRemoveEventListener", "nativeListeners", "nativeRemoveAllListeners", "nativePrependEventListener", "buildEventListenerOptions", "passive", "capture", "assign", "prepend", "isExisting", "symbolEventNames", "symbolEventName", "existingTasks", "allRemoved", "compare", "diff", "typeOfDelegate", "unpatchedEvents", "passiveEvents", "makeAddListener", "nativeListener", "addSource", "customScheduleFn", "customCancelFn", "transferEventName", "isHandleEvent", "constructorName", "targetSource", "unshift", "PREPEND_EVENT_LISTENER", "existingTask", "findEventTasks", "captureTasks", "removeTasks", "keys", "match", "exec", "evtName", "results", "foundTasks", "captureFalseTasks", "captureTrueTasks", "concat", "Event", "targetName", "method", "callbacks", "nativeDelegate", "opts", "for<PERSON>ach", "descriptor", "ignoreProperties", "tip", "filter", "ip", "targetIgnoreProperties", "op", "patchFilteredProperties", "getOnEventNames", "getOwnPropertyNames", "startsWith", "map", "eventNames", "SYMBOL_BLACK_LISTED_EVENTS", "SYMBOL_UNPATCHED_EVENTS", "taskSymbol", "patchTimer", "setName", "cancelName", "nameSuffix", "clearNative", "tasksByHandleId", "clearTask", "delay", "handle", "ref", "unref", "bind", "id", "legacyPatch", "clear", "blockingMethods", "s", "patchEvent", "eventTargetPatch", "EVENT_TARGET", "EventTarget", "XMLHttpRequestEventTarget", "propertyDescriptorPatch", "__Zone_ignore_on_properties", "patchTargets", "ignoreErrorProperties", "isIE", "patchCustomElements", "customElements", "patchXHR", "XMLHttpRequest", "XMLHttpRequestPrototype", "oriAddListener", "oriRemoveListener", "XMLHttpRequestEventTargetPrototype", "READY_STATE_CHANGE", "SCHEDULED", "XHR_SCHEDULED", "XHR_ERROR_BEFORE_SCHEDULED", "XHR_LISTENER", "newListener", "readyState", "DONE", "aborted", "loadTasks", "oriInvoke", "XHR_TASK", "sendNative", "placeholder<PERSON><PERSON><PERSON>", "abortNative", "openNative", "XHR_SYNC", "XHR_URL", "fetchTaskAborting", "fetchTaskScheduling", "url", "findPendingTask", "geolocation", "patchPrototype", "fnNames", "findPromiseRejectionHandler", "PromiseRejectionEvent", "evt"], "sourceRoot": "webpack:///", "sources": ["./node_modules/@angular/localize/fesm2020/localize.mjs", "./node_modules/@angular/localize/fesm2020/init.mjs", "./node_modules/zone.js/fesm2015/zone.js"], "sourcesContent": ["/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { computeMsgId } from '@angular/compiler';\nexport { computeMsgId as ɵcomputeMsgId } from '@angular/compiler';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The character used to mark the start and end of a \"block\" in a `$localize` tagged string.\n * A block can indicate metadata about the message or specify a name of a placeholder for a\n * substitution expressions.\n *\n * For example:\n *\n * ```ts\n * $localize`Hello, ${title}:title:!`;\n * $localize`:meaning|description@@id:source message text`;\n * ```\n */\nconst BLOCK_MARKER$1 = ':';\n/**\n * The marker used to separate a message's \"meaning\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:correct|Indicates that the user got the answer correct: Right!`;\n * $localize `:movement|Button label for moving to the right: Right!`;\n * ```\n */\nconst MEANING_SEPARATOR = '|';\n/**\n * The marker used to separate a message's custom \"id\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:A welcome message on the home page@@myApp-homepage-welcome: Welcome!`;\n * ```\n */\nconst ID_SEPARATOR = '@@';\n/**\n * The marker used to separate legacy message ids from the rest of a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:@@custom-id␟2df64767cd895a8fabe3e18b94b5b6b6f9e2e3f0: Welcome!`;\n * ```\n *\n * Note that this character is the \"symbol for the unit separator\" (␟) not the \"unit separator\n * character\" itself, since that has no visual representation. See https://graphemica.com/%E2%90%9F.\n *\n * Here is some background for the original \"unit separator character\":\n * https://stackoverflow.com/questions/8695118/whats-the-file-group-record-unit-separator-control-characters-and-its-usage\n */\nconst LEGACY_ID_INDICATOR = '\\u241F';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Parse a `$localize` tagged string into a structure that can be used for translation or\n * extraction.\n *\n * See `ParsedMessage` for an example.\n */\nfunction parseMessage(messageParts, expressions, location, messagePartLocations, expressionLocations = []) {\n    const substitutions = {};\n    const substitutionLocations = {};\n    const associatedMessageIds = {};\n    const metadata = parseMetadata(messageParts[0], messageParts.raw[0]);\n    const cleanedMessageParts = [metadata.text];\n    const placeholderNames = [];\n    let messageString = metadata.text;\n    for (let i = 1; i < messageParts.length; i++) {\n        const { messagePart, placeholderName = computePlaceholderName(i), associatedMessageId } = parsePlaceholder(messageParts[i], messageParts.raw[i]);\n        messageString += `{$${placeholderName}}${messagePart}`;\n        if (expressions !== undefined) {\n            substitutions[placeholderName] = expressions[i - 1];\n            substitutionLocations[placeholderName] = expressionLocations[i - 1];\n        }\n        placeholderNames.push(placeholderName);\n        if (associatedMessageId !== undefined) {\n            associatedMessageIds[placeholderName] = associatedMessageId;\n        }\n        cleanedMessageParts.push(messagePart);\n    }\n    const messageId = metadata.customId || computeMsgId(messageString, metadata.meaning || '');\n    const legacyIds = metadata.legacyIds ? metadata.legacyIds.filter(id => id !== messageId) : [];\n    return {\n        id: messageId,\n        legacyIds,\n        substitutions,\n        substitutionLocations,\n        text: messageString,\n        customId: metadata.customId,\n        meaning: metadata.meaning || '',\n        description: metadata.description || '',\n        messageParts: cleanedMessageParts,\n        messagePartLocations,\n        placeholderNames,\n        associatedMessageIds,\n        location,\n    };\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract the message metadata from the text.\n *\n * If the message part has a metadata block this function will extract the `meaning`,\n * `description`, `customId` and `legacyId` (if provided) from the block. These metadata properties\n * are serialized in the string delimited by `|`, `@@` and `␟` respectively.\n *\n * (Note that `␟` is the `LEGACY_ID_INDICATOR` - see `constants.ts`.)\n *\n * For example:\n *\n * ```ts\n * `:meaning|description@@custom-id:`\n * `:meaning|@@custom-id:`\n * `:meaning|description:`\n * `:description@@custom-id:`\n * `:meaning|:`\n * `:description:`\n * `:@@custom-id:`\n * `:meaning|description@@custom-id␟legacy-id-1␟legacy-id-2:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing any metadata that was parsed from the message part.\n */\nfunction parseMetadata(cooked, raw) {\n    const { text: messageString, block } = splitBlock(cooked, raw);\n    if (block === undefined) {\n        return { text: messageString };\n    }\n    else {\n        const [meaningDescAndId, ...legacyIds] = block.split(LEGACY_ID_INDICATOR);\n        const [meaningAndDesc, customId] = meaningDescAndId.split(ID_SEPARATOR, 2);\n        let [meaning, description] = meaningAndDesc.split(MEANING_SEPARATOR, 2);\n        if (description === undefined) {\n            description = meaning;\n            meaning = undefined;\n        }\n        if (description === '') {\n            description = undefined;\n        }\n        return { text: messageString, meaning, description, customId, legacyIds };\n    }\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract any placeholder metadata from the\n * text.\n *\n * If the message part has a metadata block this function will extract the `placeholderName` and\n * `associatedMessageId` (if provided) from the block.\n *\n * These metadata properties are serialized in the string delimited by `@@`.\n *\n * For example:\n *\n * ```ts\n * `:placeholder-name@@associated-id:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing the metadata (`placeholderName` and `associatedMessageId`) of the\n *     preceding placeholder, along with the static text that follows.\n */\nfunction parsePlaceholder(cooked, raw) {\n    const { text: messagePart, block } = splitBlock(cooked, raw);\n    if (block === undefined) {\n        return { messagePart };\n    }\n    else {\n        const [placeholderName, associatedMessageId] = block.split(ID_SEPARATOR);\n        return { messagePart, placeholderName, associatedMessageId };\n    }\n}\n/**\n * Split a message part (`cooked` + `raw`) into an optional delimited \"block\" off the front and the\n * rest of the text of the message part.\n *\n * Blocks appear at the start of message parts. They are delimited by a colon `:` character at the\n * start and end of the block.\n *\n * If the block is in the first message part then it will be metadata about the whole message:\n * meaning, description, id.  Otherwise it will be metadata about the immediately preceding\n * substitution: placeholder name.\n *\n * Since blocks are optional, it is possible that the content of a message block actually starts\n * with a block marker. In this case the marker must be escaped `\\:`.\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns An object containing the `text` of the message part and the text of the `block`, if it\n * exists.\n * @throws an error if the `block` is unterminated\n */\nfunction splitBlock(cooked, raw) {\n    if (raw.charAt(0) !== BLOCK_MARKER$1) {\n        return { text: cooked };\n    }\n    else {\n        const endOfBlock = findEndOfBlock(cooked, raw);\n        return {\n            block: cooked.substring(1, endOfBlock),\n            text: cooked.substring(endOfBlock + 1),\n        };\n    }\n}\nfunction computePlaceholderName(index) {\n    return index === 1 ? 'PH' : `PH_${index - 1}`;\n}\n/**\n * Find the end of a \"marked block\" indicated by the first non-escaped colon.\n *\n * @param cooked The cooked string (where escaped chars have been processed)\n * @param raw The raw string (where escape sequences are still in place)\n *\n * @returns the index of the end of block marker\n * @throws an error if the block is unterminated\n */\nfunction findEndOfBlock(cooked, raw) {\n    for (let cookedIndex = 1, rawIndex = 1; cookedIndex < cooked.length; cookedIndex++, rawIndex++) {\n        if (raw[rawIndex] === '\\\\') {\n            rawIndex++;\n        }\n        else if (cooked[cookedIndex] === BLOCK_MARKER$1) {\n            return cookedIndex;\n        }\n    }\n    throw new Error(`Unterminated $localize metadata block in \"${raw}\".`);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MissingTranslationError extends Error {\n    constructor(parsedMessage) {\n        super(`No translation found for ${describeMessage(parsedMessage)}.`);\n        this.parsedMessage = parsedMessage;\n        this.type = 'MissingTranslationError';\n    }\n}\nfunction isMissingTranslationError(e) {\n    return e.type === 'MissingTranslationError';\n}\n/**\n * Translate the text of the `$localize` tagged-string (i.e. `messageParts` and\n * `substitutions`) using the given `translations`.\n *\n * The tagged-string is parsed to extract its `messageId` which is used to find an appropriate\n * `ParsedTranslation`. If this doesn't match and there are legacy ids then try matching a\n * translation using those.\n *\n * If one is found then it is used to translate the message into a new set of `messageParts` and\n * `substitutions`.\n * The translation may reorder (or remove) substitutions as appropriate.\n *\n * If there is no translation with a matching message id then an error is thrown.\n * If a translation contains a placeholder that is not found in the message being translated then an\n * error is thrown.\n */\nfunction translate$1(translations, messageParts, substitutions) {\n    const message = parseMessage(messageParts, substitutions);\n    // Look up the translation using the messageId, and then the legacyId if available.\n    let translation = translations[message.id];\n    // If the messageId did not match a translation, try matching the legacy ids instead\n    if (message.legacyIds !== undefined) {\n        for (let i = 0; i < message.legacyIds.length && translation === undefined; i++) {\n            translation = translations[message.legacyIds[i]];\n        }\n    }\n    if (translation === undefined) {\n        throw new MissingTranslationError(message);\n    }\n    return [\n        translation.messageParts, translation.placeholderNames.map(placeholder => {\n            if (message.substitutions.hasOwnProperty(placeholder)) {\n                return message.substitutions[placeholder];\n            }\n            else {\n                throw new Error(`There is a placeholder name mismatch with the translation provided for the message ${describeMessage(message)}.\\n` +\n                    `The translation contains a placeholder with name ${placeholder}, which does not exist in the message.`);\n            }\n        })\n    ];\n}\n/**\n * Parse the `messageParts` and `placeholderNames` out of a target `message`.\n *\n * Used by `loadTranslations()` to convert target message strings into a structure that is more\n * appropriate for doing translation.\n *\n * @param message the message to be parsed.\n */\nfunction parseTranslation(messageString) {\n    const parts = messageString.split(/{\\$([^}]*)}/);\n    const messageParts = [parts[0]];\n    const placeholderNames = [];\n    for (let i = 1; i < parts.length - 1; i += 2) {\n        placeholderNames.push(parts[i]);\n        messageParts.push(`${parts[i + 1]}`);\n    }\n    const rawMessageParts = messageParts.map(part => part.charAt(0) === BLOCK_MARKER$1 ? '\\\\' + part : part);\n    return {\n        text: messageString,\n        messageParts: makeTemplateObject(messageParts, rawMessageParts),\n        placeholderNames,\n    };\n}\n/**\n * Create a `ParsedTranslation` from a set of `messageParts` and `placeholderNames`.\n *\n * @param messageParts The message parts to appear in the ParsedTranslation.\n * @param placeholderNames The names of the placeholders to intersperse between the `messageParts`.\n */\nfunction makeParsedTranslation(messageParts, placeholderNames = []) {\n    let messageString = messageParts[0];\n    for (let i = 0; i < placeholderNames.length; i++) {\n        messageString += `{$${placeholderNames[i]}}${messageParts[i + 1]}`;\n    }\n    return {\n        text: messageString,\n        messageParts: makeTemplateObject(messageParts, messageParts),\n        placeholderNames\n    };\n}\n/**\n * Create the specialized array that is passed to tagged-string tag functions.\n *\n * @param cooked The message parts with their escape codes processed.\n * @param raw The message parts with their escaped codes as-is.\n */\nfunction makeTemplateObject(cooked, raw) {\n    Object.defineProperty(cooked, 'raw', { value: raw });\n    return cooked;\n}\nfunction describeMessage(message) {\n    const meaningString = message.meaning && ` - \"${message.meaning}\"`;\n    const legacy = message.legacyIds && message.legacyIds.length > 0 ?\n        ` [${message.legacyIds.map(l => `\"${l}\"`).join(', ')}]` :\n        '';\n    return `\"${message.id}\"${legacy} (\"${message.text}\"${meaningString})`;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Load translations for use by `$localize`, if doing runtime translation.\n *\n * If the `$localize` tagged strings are not going to be replaced at compiled time, it is possible\n * to load a set of translations that will be applied to the `$localize` tagged strings at runtime,\n * in the browser.\n *\n * Loading a new translation will overwrite a previous translation if it has the same `MessageId`.\n *\n * Note that `$localize` messages are only processed once, when the tagged string is first\n * encountered, and does not provide dynamic language changing without refreshing the browser.\n * Loading new translations later in the application life-cycle will not change the translated text\n * of messages that have already been translated.\n *\n * The message IDs and translations are in the same format as that rendered to \"simple JSON\"\n * translation files when extracting messages. In particular, placeholders in messages are rendered\n * using the `{$PLACEHOLDER_NAME}` syntax. For example the message from the following template:\n *\n * ```html\n * <div i18n>pre<span>inner-pre<b>bold</b>inner-post</span>post</div>\n * ```\n *\n * would have the following form in the `translations` map:\n *\n * ```ts\n * {\n *   \"2932901491976224757\":\n *      \"pre{$START_TAG_SPAN}inner-pre{$START_BOLD_TEXT}bold{$CLOSE_BOLD_TEXT}inner-post{$CLOSE_TAG_SPAN}post\"\n * }\n * ```\n *\n * @param translations A map from message ID to translated message.\n *\n * These messages are processed and added to a lookup based on their `MessageId`.\n *\n * @see `clearTranslations()` for removing translations loaded using this function.\n * @see `$localize` for tagging messages as needing to be translated.\n * @publicApi\n */\nfunction loadTranslations(translations) {\n    // Ensure the translate function exists\n    if (!$localize.translate) {\n        $localize.translate = translate;\n    }\n    if (!$localize.TRANSLATIONS) {\n        $localize.TRANSLATIONS = {};\n    }\n    Object.keys(translations).forEach(key => {\n        $localize.TRANSLATIONS[key] = parseTranslation(translations[key]);\n    });\n}\n/**\n * Remove all translations for `$localize`, if doing runtime translation.\n *\n * All translations that had been loading into memory using `loadTranslations()` will be removed.\n *\n * @see `loadTranslations()` for loading translations at runtime.\n * @see `$localize` for tagging messages as needing to be translated.\n *\n * @publicApi\n */\nfunction clearTranslations() {\n    $localize.translate = undefined;\n    $localize.TRANSLATIONS = {};\n}\n/**\n * Translate the text of the given message, using the loaded translations.\n *\n * This function may reorder (or remove) substitutions as indicated in the matching translation.\n */\nfunction translate(messageParts, substitutions) {\n    try {\n        return translate$1($localize.TRANSLATIONS, messageParts, substitutions);\n    }\n    catch (e) {\n        console.warn(e.message);\n        return [messageParts, substitutions];\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Always use __globalThis if available, which is the spec-defined global variable across all\n// environments, then fallback to __global first, because in Node tests both __global and\n// __window may be defined and _global should be __global in that case. Note: Typeof/Instanceof\n// checks are considered side-effects in Terser. We explicitly mark this as side-effect free:\n// https://github.com/terser/terser/issues/250.\nconst _global = ( /* @__PURE__ */(() => (typeof globalThis !== 'undefined' && globalThis) ||\n    (typeof global !== 'undefined' && global) || (typeof window !== 'undefined' && window) ||\n    (typeof self !== 'undefined' && typeof WorkerGlobalScope !== 'undefined' &&\n        self instanceof WorkerGlobalScope && self))());\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Tag a template literal string for localization.\n *\n * For example:\n *\n * ```ts\n * $localize `some string to localize`\n * ```\n *\n * **Providing meaning, description and id**\n *\n * You can optionally specify one or more of `meaning`, `description` and `id` for a localized\n * string by pre-pending it with a colon delimited block of the form:\n *\n * ```ts\n * $localize`:meaning|description@@id:source message text`;\n *\n * $localize`:meaning|:source message text`;\n * $localize`:description:source message text`;\n * $localize`:@@id:source message text`;\n * ```\n *\n * This format is the same as that used for `i18n` markers in Angular templates. See the\n * [Angular i18n guide](guide/i18n-common-prepare#mark-text-in-component-template).\n *\n * **Naming placeholders**\n *\n * If the template literal string contains expressions, then the expressions will be automatically\n * associated with placeholder names for you.\n *\n * For example:\n *\n * ```ts\n * $localize `Hi ${name}! There are ${items.length} items.`;\n * ```\n *\n * will generate a message-source of `Hi {$PH}! There are {$PH_1} items`.\n *\n * The recommended practice is to name the placeholder associated with each expression though.\n *\n * Do this by providing the placeholder name wrapped in `:` characters directly after the\n * expression. These placeholder names are stripped out of the rendered localized string.\n *\n * For example, to name the `items.length` expression placeholder `itemCount` you write:\n *\n * ```ts\n * $localize `There are ${items.length}:itemCount: items`;\n * ```\n *\n * **Escaping colon markers**\n *\n * If you need to use a `:` character directly at the start of a tagged string that has no\n * metadata block, or directly after a substitution expression that has no name you must escape\n * the `:` by preceding it with a backslash:\n *\n * For example:\n *\n * ```ts\n * // message has a metadata block so no need to escape colon\n * $localize `:some description::this message starts with a colon (:)`;\n * // no metadata block so the colon must be escaped\n * $localize `\\:this message starts with a colon (:)`;\n * ```\n *\n * ```ts\n * // named substitution so no need to escape colon\n * $localize `${label}:label:: ${}`\n * // anonymous substitution so colon must be escaped\n * $localize `${label}\\: ${}`\n * ```\n *\n * **Processing localized strings:**\n *\n * There are three scenarios:\n *\n * * **compile-time inlining**: the `$localize` tag is transformed at compile time by a\n * transpiler, removing the tag and replacing the template literal string with a translated\n * literal string from a collection of translations provided to the transpilation tool.\n *\n * * **run-time evaluation**: the `$localize` tag is a run-time function that replaces and\n * reorders the parts (static strings and expressions) of the template literal string with strings\n * from a collection of translations loaded at run-time.\n *\n * * **pass-through evaluation**: the `$localize` tag is a run-time function that simply evaluates\n * the original template literal string without applying any translations to the parts. This\n * version is used during development or where there is no need to translate the localized\n * template literals.\n *\n * @param messageParts a collection of the static parts of the template string.\n * @param expressions a collection of the values of each placeholder in the template string.\n * @returns the translated string, with the `messageParts` and `expressions` interleaved together.\n *\n * @globalApi\n * @publicApi\n */\nconst $localize$1 = function (messageParts, ...expressions) {\n    if ($localize$1.translate) {\n        // Don't use array expansion here to avoid the compiler adding `__read()` helper unnecessarily.\n        const translation = $localize$1.translate(messageParts, expressions);\n        messageParts = translation[0];\n        expressions = translation[1];\n    }\n    let message = stripBlock(messageParts[0], messageParts.raw[0]);\n    for (let i = 1; i < messageParts.length; i++) {\n        message += expressions[i - 1] + stripBlock(messageParts[i], messageParts.raw[i]);\n    }\n    return message;\n};\nconst BLOCK_MARKER = ':';\n/**\n * Strip a delimited \"block\" from the start of the `messagePart`, if it is found.\n *\n * If a marker character (:) actually appears in the content at the start of a tagged string or\n * after a substitution expression, where a block has not been provided the character must be\n * escaped with a backslash, `\\:`. This function checks for this by looking at the `raw`\n * messagePart, which should still contain the backslash.\n *\n * @param messagePart The cooked message part to process.\n * @param rawMessagePart The raw message part to check.\n * @returns the message part with the placeholder name stripped, if found.\n * @throws an error if the block is unterminated\n */\nfunction stripBlock(messagePart, rawMessagePart) {\n    return rawMessagePart.charAt(0) === BLOCK_MARKER ?\n        messagePart.substring(findEndOfBlock(messagePart, rawMessagePart) + 1) :\n        messagePart;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport { clearTranslations, loadTranslations, $localize$1 as ɵ$localize, MissingTranslationError as ɵMissingTranslationError, _global as ɵ_global, findEndOfBlock as ɵfindEndOfBlock, isMissingTranslationError as ɵisMissingTranslationError, makeParsedTranslation as ɵmakeParsedTranslation, makeTemplateObject as ɵmakeTemplateObject, parseMessage as ɵparseMessage, parseMetadata as ɵparseMetadata, parseTranslation as ɵparseTranslation, splitBlock as ɵsplitBlock, translate$1 as ɵtranslate };\n", "/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵ_global, ɵ$localize } from '@angular/localize';\nexport { ɵ$localize as $localize } from '@angular/localize';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Attach $localize to the global context, as a side-effect of this module.\nɵ_global.$localize = ɵ$localize;\n", "'use strict';\n/**\n * @license Angular v14.2.0-next.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n((function (global) {\n    const performance = global['performance'];\n    function mark(name) {\n        performance && performance['mark'] && performance['mark'](name);\n    }\n    function performanceMeasure(name, label) {\n        performance && performance['measure'] && performance['measure'](name, label);\n    }\n    mark('Zone');\n    // Initialize before it's accessed below.\n    // __Zone_symbol_prefix global can be used to override the default zone\n    // symbol prefix with a custom one if needed.\n    const symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n    function __symbol__(name) {\n        return symbolPrefix + name;\n    }\n    const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n    if (global['Zone']) {\n        // if global['Zone'] already exists (maybe zone.js was already loaded or\n        // some other lib also registered a global object named Zone), we may need\n        // to throw an error, but sometimes user may not want this error.\n        // For example,\n        // we have two web pages, page1 includes zone.js, page2 doesn't.\n        // and the 1st time user load page1 and page2, everything work fine,\n        // but when user load page2 again, error occurs because global['Zone'] already exists.\n        // so we add a flag to let user choose whether to throw this error or not.\n        // By default, if existing Zone is from zone.js, we will not throw the error.\n        if (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function') {\n            throw new Error('Zone already loaded.');\n        }\n        else {\n            return global['Zone'];\n        }\n    }\n    class Zone {\n        constructor(parent, zoneSpec) {\n            this._parent = parent;\n            this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n            this._properties = zoneSpec && zoneSpec.properties || {};\n            this._zoneDelegate =\n                new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n        }\n        static assertZonePatched() {\n            if (global['Promise'] !== patches['ZoneAwarePromise']) {\n                throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' +\n                    'has been overwritten.\\n' +\n                    'Most likely cause is that a Promise polyfill has been loaded ' +\n                    'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' +\n                    'If you must load one, do so before loading zone.js.)');\n            }\n        }\n        static get root() {\n            let zone = Zone.current;\n            while (zone.parent) {\n                zone = zone.parent;\n            }\n            return zone;\n        }\n        static get current() {\n            return _currentZoneFrame.zone;\n        }\n        static get currentTask() {\n            return _currentTask;\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        static __load_patch(name, fn, ignoreDuplicate = false) {\n            if (patches.hasOwnProperty(name)) {\n                // `checkDuplicate` option is defined from global variable\n                // so it works for all modules.\n                // `ignoreDuplicate` can work for the specified module\n                if (!ignoreDuplicate && checkDuplicate) {\n                    throw Error('Already loaded patch: ' + name);\n                }\n            }\n            else if (!global['__Zone_disable_' + name]) {\n                const perfName = 'Zone:' + name;\n                mark(perfName);\n                patches[name] = fn(global, Zone, _api);\n                performanceMeasure(perfName, perfName);\n            }\n        }\n        get parent() {\n            return this._parent;\n        }\n        get name() {\n            return this._name;\n        }\n        get(key) {\n            const zone = this.getZoneWith(key);\n            if (zone)\n                return zone._properties[key];\n        }\n        getZoneWith(key) {\n            let current = this;\n            while (current) {\n                if (current._properties.hasOwnProperty(key)) {\n                    return current;\n                }\n                current = current._parent;\n            }\n            return null;\n        }\n        fork(zoneSpec) {\n            if (!zoneSpec)\n                throw new Error('ZoneSpec required!');\n            return this._zoneDelegate.fork(this, zoneSpec);\n        }\n        wrap(callback, source) {\n            if (typeof callback !== 'function') {\n                throw new Error('Expecting function got: ' + callback);\n            }\n            const _callback = this._zoneDelegate.intercept(this, callback, source);\n            const zone = this;\n            return function () {\n                return zone.runGuarded(_callback, this, arguments, source);\n            };\n        }\n        run(callback, applyThis, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runGuarded(callback, applyThis = null, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                try {\n                    return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runTask(task, applyThis, applyArgs) {\n            if (task.zone != this) {\n                throw new Error('A task can only be run in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n            }\n            // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n            // will run in notScheduled(canceled) state, we should not try to\n            // run such kind of task but just return\n            if (task.state === notScheduled && (task.type === eventTask || task.type === macroTask)) {\n                return;\n            }\n            const reEntryGuard = task.state != running;\n            reEntryGuard && task._transitionTo(running, scheduled);\n            task.runCount++;\n            const previousTask = _currentTask;\n            _currentTask = task;\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                if (task.type == macroTask && task.data && !task.data.isPeriodic) {\n                    task.cancelFn = undefined;\n                }\n                try {\n                    return this._zoneDelegate.invokeTask(this, task, applyThis, applyArgs);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                // if the task's state is notScheduled or unknown, then it has already been cancelled\n                // we should not reset the state to scheduled\n                if (task.state !== notScheduled && task.state !== unknown) {\n                    if (task.type == eventTask || (task.data && task.data.isPeriodic)) {\n                        reEntryGuard && task._transitionTo(scheduled, running);\n                    }\n                    else {\n                        task.runCount = 0;\n                        this._updateTaskCount(task, -1);\n                        reEntryGuard &&\n                            task._transitionTo(notScheduled, running, notScheduled);\n                    }\n                }\n                _currentZoneFrame = _currentZoneFrame.parent;\n                _currentTask = previousTask;\n            }\n        }\n        scheduleTask(task) {\n            if (task.zone && task.zone !== this) {\n                // check if the task was rescheduled, the newZone\n                // should not be the children of the original zone\n                let newZone = this;\n                while (newZone) {\n                    if (newZone === task.zone) {\n                        throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);\n                    }\n                    newZone = newZone.parent;\n                }\n            }\n            task._transitionTo(scheduling, notScheduled);\n            const zoneDelegates = [];\n            task._zoneDelegates = zoneDelegates;\n            task._zone = this;\n            try {\n                task = this._zoneDelegate.scheduleTask(this, task);\n            }\n            catch (err) {\n                // should set task's state to unknown when scheduleTask throw error\n                // because the err may from reschedule, so the fromState maybe notScheduled\n                task._transitionTo(unknown, scheduling, notScheduled);\n                // TODO: @JiaLiPassion, should we check the result from handleError?\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            if (task._zoneDelegates === zoneDelegates) {\n                // we have to check because internally the delegate can reschedule the task.\n                this._updateTaskCount(task, 1);\n            }\n            if (task.state == scheduling) {\n                task._transitionTo(scheduled, scheduling);\n            }\n            return task;\n        }\n        scheduleMicroTask(source, callback, data, customSchedule) {\n            return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n        }\n        scheduleMacroTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n        }\n        scheduleEventTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n        }\n        cancelTask(task) {\n            if (task.zone != this)\n                throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n            task._transitionTo(canceling, scheduled, running);\n            try {\n                this._zoneDelegate.cancelTask(this, task);\n            }\n            catch (err) {\n                // if error occurs when cancelTask, transit the state to unknown\n                task._transitionTo(unknown, canceling);\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            this._updateTaskCount(task, -1);\n            task._transitionTo(notScheduled, canceling);\n            task.runCount = 0;\n            return task;\n        }\n        _updateTaskCount(task, count) {\n            const zoneDelegates = task._zoneDelegates;\n            if (count == -1) {\n                task._zoneDelegates = null;\n            }\n            for (let i = 0; i < zoneDelegates.length; i++) {\n                zoneDelegates[i]._updateTaskCount(task.type, count);\n            }\n        }\n    }\n    // tslint:disable-next-line:require-internal-with-underscore\n    Zone.__symbol__ = __symbol__;\n    const DELEGATE_ZS = {\n        name: '',\n        onHasTask: (delegate, _, target, hasTaskState) => delegate.hasTask(target, hasTaskState),\n        onScheduleTask: (delegate, _, target, task) => delegate.scheduleTask(target, task),\n        onInvokeTask: (delegate, _, target, task, applyThis, applyArgs) => delegate.invokeTask(target, task, applyThis, applyArgs),\n        onCancelTask: (delegate, _, target, task) => delegate.cancelTask(target, task)\n    };\n    class _ZoneDelegate {\n        constructor(zone, parentDelegate, zoneSpec) {\n            this._taskCounts = { 'microTask': 0, 'macroTask': 0, 'eventTask': 0 };\n            this.zone = zone;\n            this._parentDelegate = parentDelegate;\n            this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n            this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n            this._forkCurrZone =\n                zoneSpec && (zoneSpec.onFork ? this.zone : parentDelegate._forkCurrZone);\n            this._interceptZS =\n                zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n            this._interceptDlgt =\n                zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n            this._interceptCurrZone =\n                zoneSpec && (zoneSpec.onIntercept ? this.zone : parentDelegate._interceptCurrZone);\n            this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n            this._invokeDlgt =\n                zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n            this._invokeCurrZone =\n                zoneSpec && (zoneSpec.onInvoke ? this.zone : parentDelegate._invokeCurrZone);\n            this._handleErrorZS =\n                zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n            this._handleErrorDlgt =\n                zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n            this._handleErrorCurrZone =\n                zoneSpec && (zoneSpec.onHandleError ? this.zone : parentDelegate._handleErrorCurrZone);\n            this._scheduleTaskZS =\n                zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n            this._scheduleTaskDlgt = zoneSpec &&\n                (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n            this._scheduleTaskCurrZone =\n                zoneSpec && (zoneSpec.onScheduleTask ? this.zone : parentDelegate._scheduleTaskCurrZone);\n            this._invokeTaskZS =\n                zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n            this._invokeTaskDlgt =\n                zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n            this._invokeTaskCurrZone =\n                zoneSpec && (zoneSpec.onInvokeTask ? this.zone : parentDelegate._invokeTaskCurrZone);\n            this._cancelTaskZS =\n                zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n            this._cancelTaskDlgt =\n                zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n            this._cancelTaskCurrZone =\n                zoneSpec && (zoneSpec.onCancelTask ? this.zone : parentDelegate._cancelTaskCurrZone);\n            this._hasTaskZS = null;\n            this._hasTaskDlgt = null;\n            this._hasTaskDlgtOwner = null;\n            this._hasTaskCurrZone = null;\n            const zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n            const parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n            if (zoneSpecHasTask || parentHasTask) {\n                // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n                // a case all task related interceptors must go through this ZD. We can't short circuit it.\n                this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n                this._hasTaskDlgt = parentDelegate;\n                this._hasTaskDlgtOwner = this;\n                this._hasTaskCurrZone = zone;\n                if (!zoneSpec.onScheduleTask) {\n                    this._scheduleTaskZS = DELEGATE_ZS;\n                    this._scheduleTaskDlgt = parentDelegate;\n                    this._scheduleTaskCurrZone = this.zone;\n                }\n                if (!zoneSpec.onInvokeTask) {\n                    this._invokeTaskZS = DELEGATE_ZS;\n                    this._invokeTaskDlgt = parentDelegate;\n                    this._invokeTaskCurrZone = this.zone;\n                }\n                if (!zoneSpec.onCancelTask) {\n                    this._cancelTaskZS = DELEGATE_ZS;\n                    this._cancelTaskDlgt = parentDelegate;\n                    this._cancelTaskCurrZone = this.zone;\n                }\n            }\n        }\n        fork(targetZone, zoneSpec) {\n            return this._forkZS ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec) :\n                new Zone(targetZone, zoneSpec);\n        }\n        intercept(targetZone, callback, source) {\n            return this._interceptZS ?\n                this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source) :\n                callback;\n        }\n        invoke(targetZone, callback, applyThis, applyArgs, source) {\n            return this._invokeZS ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source) :\n                callback.apply(applyThis, applyArgs);\n        }\n        handleError(targetZone, error) {\n            return this._handleErrorZS ?\n                this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error) :\n                true;\n        }\n        scheduleTask(targetZone, task) {\n            let returnTask = task;\n            if (this._scheduleTaskZS) {\n                if (this._hasTaskZS) {\n                    returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n                }\n                // clang-format off\n                returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n                // clang-format on\n                if (!returnTask)\n                    returnTask = task;\n            }\n            else {\n                if (task.scheduleFn) {\n                    task.scheduleFn(task);\n                }\n                else if (task.type == microTask) {\n                    scheduleMicroTask(task);\n                }\n                else {\n                    throw new Error('Task is missing scheduleFn.');\n                }\n            }\n            return returnTask;\n        }\n        invokeTask(targetZone, task, applyThis, applyArgs) {\n            return this._invokeTaskZS ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs) :\n                task.callback.apply(applyThis, applyArgs);\n        }\n        cancelTask(targetZone, task) {\n            let value;\n            if (this._cancelTaskZS) {\n                value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n            }\n            else {\n                if (!task.cancelFn) {\n                    throw Error('Task is not cancelable');\n                }\n                value = task.cancelFn(task);\n            }\n            return value;\n        }\n        hasTask(targetZone, isEmpty) {\n            // hasTask should not throw error so other ZoneDelegate\n            // can still trigger hasTask callback\n            try {\n                this._hasTaskZS &&\n                    this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n            }\n            catch (err) {\n                this.handleError(targetZone, err);\n            }\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        _updateTaskCount(type, count) {\n            const counts = this._taskCounts;\n            const prev = counts[type];\n            const next = counts[type] = prev + count;\n            if (next < 0) {\n                throw new Error('More tasks executed then were scheduled.');\n            }\n            if (prev == 0 || next == 0) {\n                const isEmpty = {\n                    microTask: counts['microTask'] > 0,\n                    macroTask: counts['macroTask'] > 0,\n                    eventTask: counts['eventTask'] > 0,\n                    change: type\n                };\n                this.hasTask(this.zone, isEmpty);\n            }\n        }\n    }\n    class ZoneTask {\n        constructor(type, source, callback, options, scheduleFn, cancelFn) {\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._zone = null;\n            this.runCount = 0;\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._zoneDelegates = null;\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._state = 'notScheduled';\n            this.type = type;\n            this.source = source;\n            this.data = options;\n            this.scheduleFn = scheduleFn;\n            this.cancelFn = cancelFn;\n            if (!callback) {\n                throw new Error('callback is not defined');\n            }\n            this.callback = callback;\n            const self = this;\n            // TODO: @JiaLiPassion options should have interface\n            if (type === eventTask && options && options.useG) {\n                this.invoke = ZoneTask.invokeTask;\n            }\n            else {\n                this.invoke = function () {\n                    return ZoneTask.invokeTask.call(global, self, this, arguments);\n                };\n            }\n        }\n        static invokeTask(task, target, args) {\n            if (!task) {\n                task = this;\n            }\n            _numberOfNestedTaskFrames++;\n            try {\n                task.runCount++;\n                return task.zone.runTask(task, target, args);\n            }\n            finally {\n                if (_numberOfNestedTaskFrames == 1) {\n                    drainMicroTaskQueue();\n                }\n                _numberOfNestedTaskFrames--;\n            }\n        }\n        get zone() {\n            return this._zone;\n        }\n        get state() {\n            return this._state;\n        }\n        cancelScheduleRequest() {\n            this._transitionTo(notScheduled, scheduling);\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        _transitionTo(toState, fromState1, fromState2) {\n            if (this._state === fromState1 || this._state === fromState2) {\n                this._state = toState;\n                if (toState == notScheduled) {\n                    this._zoneDelegates = null;\n                }\n            }\n            else {\n                throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2 ? ' or \\'' + fromState2 + '\\'' : ''}, was '${this._state}'.`);\n            }\n        }\n        toString() {\n            if (this.data && typeof this.data.handleId !== 'undefined') {\n                return this.data.handleId.toString();\n            }\n            else {\n                return Object.prototype.toString.call(this);\n            }\n        }\n        // add toJSON method to prevent cyclic error when\n        // call JSON.stringify(zoneTask)\n        toJSON() {\n            return {\n                type: this.type,\n                state: this.state,\n                source: this.source,\n                zone: this.zone.name,\n                runCount: this.runCount\n            };\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  MICROTASK QUEUE\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const symbolSetTimeout = __symbol__('setTimeout');\n    const symbolPromise = __symbol__('Promise');\n    const symbolThen = __symbol__('then');\n    let _microTaskQueue = [];\n    let _isDrainingMicrotaskQueue = false;\n    let nativeMicroTaskQueuePromise;\n    function nativeScheduleMicroTask(func) {\n        if (!nativeMicroTaskQueuePromise) {\n            if (global[symbolPromise]) {\n                nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n            }\n        }\n        if (nativeMicroTaskQueuePromise) {\n            let nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n            if (!nativeThen) {\n                // native Promise is not patchable, we need to use `then` directly\n                // issue 1078\n                nativeThen = nativeMicroTaskQueuePromise['then'];\n            }\n            nativeThen.call(nativeMicroTaskQueuePromise, func);\n        }\n        else {\n            global[symbolSetTimeout](func, 0);\n        }\n    }\n    function scheduleMicroTask(task) {\n        // if we are not running in any task, and there has not been anything scheduled\n        // we must bootstrap the initial task creation by manually scheduling the drain\n        if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n            // We are not running in Task, so we need to kickstart the microtask queue.\n            nativeScheduleMicroTask(drainMicroTaskQueue);\n        }\n        task && _microTaskQueue.push(task);\n    }\n    function drainMicroTaskQueue() {\n        if (!_isDrainingMicrotaskQueue) {\n            _isDrainingMicrotaskQueue = true;\n            while (_microTaskQueue.length) {\n                const queue = _microTaskQueue;\n                _microTaskQueue = [];\n                for (let i = 0; i < queue.length; i++) {\n                    const task = queue[i];\n                    try {\n                        task.zone.runTask(task, null, null);\n                    }\n                    catch (error) {\n                        _api.onUnhandledError(error);\n                    }\n                }\n            }\n            _api.microtaskDrainDone();\n            _isDrainingMicrotaskQueue = false;\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  BOOTSTRAP\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const NO_ZONE = { name: 'NO ZONE' };\n    const notScheduled = 'notScheduled', scheduling = 'scheduling', scheduled = 'scheduled', running = 'running', canceling = 'canceling', unknown = 'unknown';\n    const microTask = 'microTask', macroTask = 'macroTask', eventTask = 'eventTask';\n    const patches = {};\n    const _api = {\n        symbol: __symbol__,\n        currentZoneFrame: () => _currentZoneFrame,\n        onUnhandledError: noop,\n        microtaskDrainDone: noop,\n        scheduleMicroTask: scheduleMicroTask,\n        showUncaughtError: () => !Zone[__symbol__('ignoreConsoleErrorUncaughtError')],\n        patchEventTarget: () => [],\n        patchOnProperties: noop,\n        patchMethod: () => noop,\n        bindArguments: () => [],\n        patchThen: () => noop,\n        patchMacroTask: () => noop,\n        patchEventPrototype: () => noop,\n        isIEOrEdge: () => false,\n        getGlobalObjects: () => undefined,\n        ObjectDefineProperty: () => noop,\n        ObjectGetOwnPropertyDescriptor: () => undefined,\n        ObjectCreate: () => undefined,\n        ArraySlice: () => [],\n        patchClass: () => noop,\n        wrapWithCurrentZone: () => noop,\n        filterProperties: () => [],\n        attachOriginToPatched: () => noop,\n        _redefineProperty: () => noop,\n        patchCallbacks: () => noop,\n        nativeScheduleMicroTask: nativeScheduleMicroTask\n    };\n    let _currentZoneFrame = { parent: null, zone: new Zone(null, null) };\n    let _currentTask = null;\n    let _numberOfNestedTaskFrames = 0;\n    function noop() { }\n    performanceMeasure('Zone', 'Zone');\n    return global['Zone'] = Zone;\n}))(typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global);\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n/// <reference types=\"node\"/>\n// issue #989, to reduce bundle size, use short name\n/** Object.getOwnPropertyDescriptor */\nconst ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\nconst ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\nconst ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Object.create */\nconst ObjectCreate = Object.create;\n/** Array.prototype.slice */\nconst ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\nconst ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\nconst REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** zoneSymbol addEventListener */\nconst ZONE_SYMBOL_ADD_EVENT_LISTENER = Zone.__symbol__(ADD_EVENT_LISTENER_STR);\n/** zoneSymbol removeEventListener */\nconst ZONE_SYMBOL_REMOVE_EVENT_LISTENER = Zone.__symbol__(REMOVE_EVENT_LISTENER_STR);\n/** true string const */\nconst TRUE_STR = 'true';\n/** false string const */\nconst FALSE_STR = 'false';\n/** Zone symbol prefix string const. */\nconst ZONE_SYMBOL_PREFIX = Zone.__symbol__('');\nfunction wrapWithCurrentZone(callback, source) {\n    return Zone.current.wrap(callback, source);\n}\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n    return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\nconst zoneSymbol = Zone.__symbol__;\nconst isWindowExists = typeof window !== 'undefined';\nconst internalWindow = isWindowExists ? window : undefined;\nconst _global = isWindowExists && internalWindow || typeof self === 'object' && self || global;\nconst REMOVE_ATTRIBUTE = 'removeAttribute';\nfunction bindArguments(args, source) {\n    for (let i = args.length - 1; i >= 0; i--) {\n        if (typeof args[i] === 'function') {\n            args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n        }\n    }\n    return args;\n}\nfunction patchPrototype(prototype, fnNames) {\n    const source = prototype.constructor['name'];\n    for (let i = 0; i < fnNames.length; i++) {\n        const name = fnNames[i];\n        const delegate = prototype[name];\n        if (delegate) {\n            const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name);\n            if (!isPropertyWritable(prototypeDesc)) {\n                continue;\n            }\n            prototype[name] = ((delegate) => {\n                const patched = function () {\n                    return delegate.apply(this, bindArguments(arguments, source + '.' + name));\n                };\n                attachOriginToPatched(patched, delegate);\n                return patched;\n            })(delegate);\n        }\n    }\n}\nfunction isPropertyWritable(propertyDesc) {\n    if (!propertyDesc) {\n        return true;\n    }\n    if (propertyDesc.writable === false) {\n        return false;\n    }\n    return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\nconst isWebWorker = (typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope);\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isNode = (!('nw' in _global) && typeof _global.process !== 'undefined' &&\n    {}.toString.call(_global.process) === '[object process]');\nconst isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n// we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isMix = typeof _global.process !== 'undefined' &&\n    {}.toString.call(_global.process) === '[object process]' && !isWebWorker &&\n    !!(isWindowExists && internalWindow['HTMLElement']);\nconst zoneSymbolEventNames$1 = {};\nconst wrapFn = function (event) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n    if (!event) {\n        return;\n    }\n    let eventNameSymbol = zoneSymbolEventNames$1[event.type];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n    }\n    const target = this || event.target || _global;\n    const listener = target[eventNameSymbol];\n    let result;\n    if (isBrowser && target === internalWindow && event.type === 'error') {\n        // window.onerror have different signature\n        // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n        // and onerror callback will prevent default when callback return true\n        const errorEvent = event;\n        result = listener &&\n            listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n        if (result === true) {\n            event.preventDefault();\n        }\n    }\n    else {\n        result = listener && listener.apply(this, arguments);\n        if (result != undefined && !result) {\n            event.preventDefault();\n        }\n    }\n    return result;\n};\nfunction patchProperty(obj, prop, prototype) {\n    let desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n    if (!desc && prototype) {\n        // when patch window object, use prototype to check prop exist or not\n        const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n        if (prototypeDesc) {\n            desc = { enumerable: true, configurable: true };\n        }\n    }\n    // if the descriptor not exists or is not configurable\n    // just return\n    if (!desc || !desc.configurable) {\n        return;\n    }\n    const onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n    if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n        return;\n    }\n    // A property descriptor cannot have getter/setter and be writable\n    // deleting the writable and value properties avoids this error:\n    //\n    // TypeError: property descriptors must not specify a value or be writable when a\n    // getter or setter has been specified\n    delete desc.writable;\n    delete desc.value;\n    const originalDescGet = desc.get;\n    const originalDescSet = desc.set;\n    // slice(2) cuz 'onclick' -> 'click', etc\n    const eventName = prop.slice(2);\n    let eventNameSymbol = zoneSymbolEventNames$1[eventName];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n    }\n    desc.set = function (newValue) {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return;\n        }\n        const previousValue = target[eventNameSymbol];\n        if (typeof previousValue === 'function') {\n            target.removeEventListener(eventName, wrapFn);\n        }\n        // issue #978, when onload handler was added before loading zone.js\n        // we should remove it with originalDescSet\n        originalDescSet && originalDescSet.call(target, null);\n        target[eventNameSymbol] = newValue;\n        if (typeof newValue === 'function') {\n            target.addEventListener(eventName, wrapFn, false);\n        }\n    };\n    // The getter would return undefined for unassigned properties but the default value of an\n    // unassigned property is null\n    desc.get = function () {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return null;\n        }\n        const listener = target[eventNameSymbol];\n        if (listener) {\n            return listener;\n        }\n        else if (originalDescGet) {\n            // result will be null when use inline event attribute,\n            // such as <button onclick=\"func();\">OK</button>\n            // because the onclick function is internal raw uncompiled handler\n            // the onclick will be evaluated when first time event was triggered or\n            // the property is accessed, https://github.com/angular/zone.js/issues/525\n            // so we should use original native get to retrieve the handler\n            let value = originalDescGet.call(this);\n            if (value) {\n                desc.set.call(this, value);\n                if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n                    target.removeAttribute(prop);\n                }\n                return value;\n            }\n        }\n        return null;\n    };\n    ObjectDefineProperty(obj, prop, desc);\n    obj[onPropPatchedSymbol] = true;\n}\nfunction patchOnProperties(obj, properties, prototype) {\n    if (properties) {\n        for (let i = 0; i < properties.length; i++) {\n            patchProperty(obj, 'on' + properties[i], prototype);\n        }\n    }\n    else {\n        const onProperties = [];\n        for (const prop in obj) {\n            if (prop.slice(0, 2) == 'on') {\n                onProperties.push(prop);\n            }\n        }\n        for (let j = 0; j < onProperties.length; j++) {\n            patchProperty(obj, onProperties[j], prototype);\n        }\n    }\n}\nconst originalInstanceKey = zoneSymbol('originalInstance');\n// wrap some native API on `window`\nfunction patchClass(className) {\n    const OriginalClass = _global[className];\n    if (!OriginalClass)\n        return;\n    // keep original class in global\n    _global[zoneSymbol(className)] = OriginalClass;\n    _global[className] = function () {\n        const a = bindArguments(arguments, className);\n        switch (a.length) {\n            case 0:\n                this[originalInstanceKey] = new OriginalClass();\n                break;\n            case 1:\n                this[originalInstanceKey] = new OriginalClass(a[0]);\n                break;\n            case 2:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n                break;\n            case 3:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n                break;\n            case 4:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n                break;\n            default:\n                throw new Error('Arg list too long.');\n        }\n    };\n    // attach original delegate to patched function\n    attachOriginToPatched(_global[className], OriginalClass);\n    const instance = new OriginalClass(function () { });\n    let prop;\n    for (prop in instance) {\n        // https://bugs.webkit.org/show_bug.cgi?id=44721\n        if (className === 'XMLHttpRequest' && prop === 'responseBlob')\n            continue;\n        (function (prop) {\n            if (typeof instance[prop] === 'function') {\n                _global[className].prototype[prop] = function () {\n                    return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n                };\n            }\n            else {\n                ObjectDefineProperty(_global[className].prototype, prop, {\n                    set: function (fn) {\n                        if (typeof fn === 'function') {\n                            this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop);\n                            // keep callback in wrapped function so we can\n                            // use it in Function.prototype.toString to return\n                            // the native one.\n                            attachOriginToPatched(this[originalInstanceKey][prop], fn);\n                        }\n                        else {\n                            this[originalInstanceKey][prop] = fn;\n                        }\n                    },\n                    get: function () {\n                        return this[originalInstanceKey][prop];\n                    }\n                });\n            }\n        }(prop));\n    }\n    for (prop in OriginalClass) {\n        if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n            _global[className][prop] = OriginalClass[prop];\n        }\n    }\n}\nfunction patchMethod(target, name, patchFn) {\n    let proto = target;\n    while (proto && !proto.hasOwnProperty(name)) {\n        proto = ObjectGetPrototypeOf(proto);\n    }\n    if (!proto && target[name]) {\n        // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n        proto = target;\n    }\n    const delegateName = zoneSymbol(name);\n    let delegate = null;\n    if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n        delegate = proto[delegateName] = proto[name];\n        // check whether proto[name] is writable\n        // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n        const desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n        if (isPropertyWritable(desc)) {\n            const patchDelegate = patchFn(delegate, delegateName, name);\n            proto[name] = function () {\n                return patchDelegate(this, arguments);\n            };\n            attachOriginToPatched(proto[name], delegate);\n        }\n    }\n    return delegate;\n}\n// TODO: @JiaLiPassion, support cancel task later if necessary\nfunction patchMacroTask(obj, funcName, metaCreator) {\n    let setNative = null;\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[data.cbIdx] = function () {\n            task.invoke.apply(this, arguments);\n        };\n        setNative.apply(data.target, data.args);\n        return task;\n    }\n    setNative = patchMethod(obj, funcName, (delegate) => function (self, args) {\n        const meta = metaCreator(self, args);\n        if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n            return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n        }\n        else {\n            // cause an error by calling it directly.\n            return delegate.apply(self, args);\n        }\n    });\n}\nfunction attachOriginToPatched(patched, original) {\n    patched[zoneSymbol('OriginalDelegate')] = original;\n}\nlet isDetectedIEOrEdge = false;\nlet ieOrEdge = false;\nfunction isIE() {\n    try {\n        const ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1) {\n            return true;\n        }\n    }\n    catch (error) {\n    }\n    return false;\n}\nfunction isIEOrEdge() {\n    if (isDetectedIEOrEdge) {\n        return ieOrEdge;\n    }\n    isDetectedIEOrEdge = true;\n    try {\n        const ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n            ieOrEdge = true;\n        }\n    }\n    catch (error) {\n    }\n    return ieOrEdge;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nZone.__load_patch('ZoneAwarePromise', (global, Zone, api) => {\n    const ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n    const ObjectDefineProperty = Object.defineProperty;\n    function readableObjectToString(obj) {\n        if (obj && obj.toString === Object.prototype.toString) {\n            const className = obj.constructor && obj.constructor.name;\n            return (className ? className : '') + ': ' + JSON.stringify(obj);\n        }\n        return obj ? obj.toString() : Object.prototype.toString.call(obj);\n    }\n    const __symbol__ = api.symbol;\n    const _uncaughtPromiseErrors = [];\n    const isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] === true;\n    const symbolPromise = __symbol__('Promise');\n    const symbolThen = __symbol__('then');\n    const creationTrace = '__creationTrace__';\n    api.onUnhandledError = (e) => {\n        if (api.showUncaughtError()) {\n            const rejection = e && e.rejection;\n            if (rejection) {\n                console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n            }\n            else {\n                console.error(e);\n            }\n        }\n    };\n    api.microtaskDrainDone = () => {\n        while (_uncaughtPromiseErrors.length) {\n            const uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n            try {\n                uncaughtPromiseError.zone.runGuarded(() => {\n                    if (uncaughtPromiseError.throwOriginal) {\n                        throw uncaughtPromiseError.rejection;\n                    }\n                    throw uncaughtPromiseError;\n                });\n            }\n            catch (error) {\n                handleUnhandledRejection(error);\n            }\n        }\n    };\n    const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n    function handleUnhandledRejection(e) {\n        api.onUnhandledError(e);\n        try {\n            const handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n            if (typeof handler === 'function') {\n                handler.call(this, e);\n            }\n        }\n        catch (err) {\n        }\n    }\n    function isThenable(value) {\n        return value && value.then;\n    }\n    function forwardResolution(value) {\n        return value;\n    }\n    function forwardRejection(rejection) {\n        return ZoneAwarePromise.reject(rejection);\n    }\n    const symbolState = __symbol__('state');\n    const symbolValue = __symbol__('value');\n    const symbolFinally = __symbol__('finally');\n    const symbolParentPromiseValue = __symbol__('parentPromiseValue');\n    const symbolParentPromiseState = __symbol__('parentPromiseState');\n    const source = 'Promise.then';\n    const UNRESOLVED = null;\n    const RESOLVED = true;\n    const REJECTED = false;\n    const REJECTED_NO_CATCH = 0;\n    function makeResolver(promise, state) {\n        return (v) => {\n            try {\n                resolvePromise(promise, state, v);\n            }\n            catch (err) {\n                resolvePromise(promise, false, err);\n            }\n            // Do not return value or you will break the Promise spec.\n        };\n    }\n    const once = function () {\n        let wasCalled = false;\n        return function wrapper(wrappedFunction) {\n            return function () {\n                if (wasCalled) {\n                    return;\n                }\n                wasCalled = true;\n                wrappedFunction.apply(null, arguments);\n            };\n        };\n    };\n    const TYPE_ERROR = 'Promise resolved with itself';\n    const CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace');\n    // Promise Resolution\n    function resolvePromise(promise, state, value) {\n        const onceWrapper = once();\n        if (promise === value) {\n            throw new TypeError(TYPE_ERROR);\n        }\n        if (promise[symbolState] === UNRESOLVED) {\n            // should only get value.then once based on promise spec.\n            let then = null;\n            try {\n                if (typeof value === 'object' || typeof value === 'function') {\n                    then = value && value.then;\n                }\n            }\n            catch (err) {\n                onceWrapper(() => {\n                    resolvePromise(promise, false, err);\n                })();\n                return promise;\n            }\n            // if (value instanceof ZoneAwarePromise) {\n            if (state !== REJECTED && value instanceof ZoneAwarePromise &&\n                value.hasOwnProperty(symbolState) && value.hasOwnProperty(symbolValue) &&\n                value[symbolState] !== UNRESOLVED) {\n                clearRejectedNoCatch(value);\n                resolvePromise(promise, value[symbolState], value[symbolValue]);\n            }\n            else if (state !== REJECTED && typeof then === 'function') {\n                try {\n                    then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n                }\n                catch (err) {\n                    onceWrapper(() => {\n                        resolvePromise(promise, false, err);\n                    })();\n                }\n            }\n            else {\n                promise[symbolState] = state;\n                const queue = promise[symbolValue];\n                promise[symbolValue] = value;\n                if (promise[symbolFinally] === symbolFinally) {\n                    // the promise is generated by Promise.prototype.finally\n                    if (state === RESOLVED) {\n                        // the state is resolved, should ignore the value\n                        // and use parent promise value\n                        promise[symbolState] = promise[symbolParentPromiseState];\n                        promise[symbolValue] = promise[symbolParentPromiseValue];\n                    }\n                }\n                // record task information in value when error occurs, so we can\n                // do some additional work such as render longStackTrace\n                if (state === REJECTED && value instanceof Error) {\n                    // check if longStackTraceZone is here\n                    const trace = Zone.currentTask && Zone.currentTask.data &&\n                        Zone.currentTask.data[creationTrace];\n                    if (trace) {\n                        // only keep the long stack trace into error when in longStackTraceZone\n                        ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, { configurable: true, enumerable: false, writable: true, value: trace });\n                    }\n                }\n                for (let i = 0; i < queue.length;) {\n                    scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n                }\n                if (queue.length == 0 && state == REJECTED) {\n                    promise[symbolState] = REJECTED_NO_CATCH;\n                    let uncaughtPromiseError = value;\n                    try {\n                        // Here we throws a new Error to print more readable error log\n                        // and if the value is not an error, zone.js builds an `Error`\n                        // Object here to attach the stack information.\n                        throw new Error('Uncaught (in promise): ' + readableObjectToString(value) +\n                            (value && value.stack ? '\\n' + value.stack : ''));\n                    }\n                    catch (err) {\n                        uncaughtPromiseError = err;\n                    }\n                    if (isDisableWrappingUncaughtPromiseRejection) {\n                        // If disable wrapping uncaught promise reject\n                        // use the value instead of wrapping it.\n                        uncaughtPromiseError.throwOriginal = true;\n                    }\n                    uncaughtPromiseError.rejection = value;\n                    uncaughtPromiseError.promise = promise;\n                    uncaughtPromiseError.zone = Zone.current;\n                    uncaughtPromiseError.task = Zone.currentTask;\n                    _uncaughtPromiseErrors.push(uncaughtPromiseError);\n                    api.scheduleMicroTask(); // to make sure that it is running\n                }\n            }\n        }\n        // Resolving an already resolved promise is a noop.\n        return promise;\n    }\n    const REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n    function clearRejectedNoCatch(promise) {\n        if (promise[symbolState] === REJECTED_NO_CATCH) {\n            // if the promise is rejected no catch status\n            // and queue.length > 0, means there is a error handler\n            // here to handle the rejected promise, we should trigger\n            // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n            // eventHandler\n            try {\n                const handler = Zone[REJECTION_HANDLED_HANDLER];\n                if (handler && typeof handler === 'function') {\n                    handler.call(this, { rejection: promise[symbolValue], promise: promise });\n                }\n            }\n            catch (err) {\n            }\n            promise[symbolState] = REJECTED;\n            for (let i = 0; i < _uncaughtPromiseErrors.length; i++) {\n                if (promise === _uncaughtPromiseErrors[i].promise) {\n                    _uncaughtPromiseErrors.splice(i, 1);\n                }\n            }\n        }\n    }\n    function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n        clearRejectedNoCatch(promise);\n        const promiseState = promise[symbolState];\n        const delegate = promiseState ?\n            (typeof onFulfilled === 'function') ? onFulfilled : forwardResolution :\n            (typeof onRejected === 'function') ? onRejected :\n                forwardRejection;\n        zone.scheduleMicroTask(source, () => {\n            try {\n                const parentPromiseValue = promise[symbolValue];\n                const isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n                if (isFinallyPromise) {\n                    // if the promise is generated from finally call, keep parent promise's state and value\n                    chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n                    chainPromise[symbolParentPromiseState] = promiseState;\n                }\n                // should not pass value to finally callback\n                const value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution ?\n                    [] :\n                    [parentPromiseValue]);\n                resolvePromise(chainPromise, true, value);\n            }\n            catch (error) {\n                // if error occurs, should always return this error\n                resolvePromise(chainPromise, false, error);\n            }\n        }, chainPromise);\n    }\n    const ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n    const noop = function () { };\n    const AggregateError = global.AggregateError;\n    class ZoneAwarePromise {\n        static toString() {\n            return ZONE_AWARE_PROMISE_TO_STRING;\n        }\n        static resolve(value) {\n            return resolvePromise(new this(null), RESOLVED, value);\n        }\n        static reject(error) {\n            return resolvePromise(new this(null), REJECTED, error);\n        }\n        static any(values) {\n            if (!values || typeof values[Symbol.iterator] !== 'function') {\n                return Promise.reject(new AggregateError([], 'All promises were rejected'));\n            }\n            const promises = [];\n            let count = 0;\n            try {\n                for (let v of values) {\n                    count++;\n                    promises.push(ZoneAwarePromise.resolve(v));\n                }\n            }\n            catch (err) {\n                return Promise.reject(new AggregateError([], 'All promises were rejected'));\n            }\n            if (count === 0) {\n                return Promise.reject(new AggregateError([], 'All promises were rejected'));\n            }\n            let finished = false;\n            const errors = [];\n            return new ZoneAwarePromise((resolve, reject) => {\n                for (let i = 0; i < promises.length; i++) {\n                    promises[i].then(v => {\n                        if (finished) {\n                            return;\n                        }\n                        finished = true;\n                        resolve(v);\n                    }, err => {\n                        errors.push(err);\n                        count--;\n                        if (count === 0) {\n                            finished = true;\n                            reject(new AggregateError(errors, 'All promises were rejected'));\n                        }\n                    });\n                }\n            });\n        }\n        ;\n        static race(values) {\n            let resolve;\n            let reject;\n            let promise = new this((res, rej) => {\n                resolve = res;\n                reject = rej;\n            });\n            function onResolve(value) {\n                resolve(value);\n            }\n            function onReject(error) {\n                reject(error);\n            }\n            for (let value of values) {\n                if (!isThenable(value)) {\n                    value = this.resolve(value);\n                }\n                value.then(onResolve, onReject);\n            }\n            return promise;\n        }\n        static all(values) {\n            return ZoneAwarePromise.allWithCallback(values);\n        }\n        static allSettled(values) {\n            const P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n            return P.allWithCallback(values, {\n                thenCallback: (value) => ({ status: 'fulfilled', value }),\n                errorCallback: (err) => ({ status: 'rejected', reason: err })\n            });\n        }\n        static allWithCallback(values, callback) {\n            let resolve;\n            let reject;\n            let promise = new this((res, rej) => {\n                resolve = res;\n                reject = rej;\n            });\n            // Start at 2 to prevent prematurely resolving if .then is called immediately.\n            let unresolvedCount = 2;\n            let valueIndex = 0;\n            const resolvedValues = [];\n            for (let value of values) {\n                if (!isThenable(value)) {\n                    value = this.resolve(value);\n                }\n                const curValueIndex = valueIndex;\n                try {\n                    value.then((value) => {\n                        resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n                        unresolvedCount--;\n                        if (unresolvedCount === 0) {\n                            resolve(resolvedValues);\n                        }\n                    }, (err) => {\n                        if (!callback) {\n                            reject(err);\n                        }\n                        else {\n                            resolvedValues[curValueIndex] = callback.errorCallback(err);\n                            unresolvedCount--;\n                            if (unresolvedCount === 0) {\n                                resolve(resolvedValues);\n                            }\n                        }\n                    });\n                }\n                catch (thenErr) {\n                    reject(thenErr);\n                }\n                unresolvedCount++;\n                valueIndex++;\n            }\n            // Make the unresolvedCount zero-based again.\n            unresolvedCount -= 2;\n            if (unresolvedCount === 0) {\n                resolve(resolvedValues);\n            }\n            return promise;\n        }\n        constructor(executor) {\n            const promise = this;\n            if (!(promise instanceof ZoneAwarePromise)) {\n                throw new Error('Must be an instanceof Promise.');\n            }\n            promise[symbolState] = UNRESOLVED;\n            promise[symbolValue] = []; // queue;\n            try {\n                const onceWrapper = once();\n                executor &&\n                    executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n            }\n            catch (error) {\n                resolvePromise(promise, false, error);\n            }\n        }\n        get [Symbol.toStringTag]() {\n            return 'Promise';\n        }\n        get [Symbol.species]() {\n            return ZoneAwarePromise;\n        }\n        then(onFulfilled, onRejected) {\n            var _a;\n            // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n            // may be an object without a prototype (created through `Object.create(null)`); thus\n            // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n            // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n            // object and copies promise properties into that object (within the `getOrCreateLoad`\n            // function). The zone.js then checks if the resolved value has the `then` method and invokes\n            // it with the `value` context. Otherwise, this will throw an error: `TypeError: Cannot read\n            // properties of undefined (reading 'Symbol(Symbol.species)')`.\n            let C = (_a = this.constructor) === null || _a === void 0 ? void 0 : _a[Symbol.species];\n            if (!C || typeof C !== 'function') {\n                C = this.constructor || ZoneAwarePromise;\n            }\n            const chainPromise = new C(noop);\n            const zone = Zone.current;\n            if (this[symbolState] == UNRESOLVED) {\n                this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n            }\n            else {\n                scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n            }\n            return chainPromise;\n        }\n        catch(onRejected) {\n            return this.then(null, onRejected);\n        }\n        finally(onFinally) {\n            var _a;\n            // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n            let C = (_a = this.constructor) === null || _a === void 0 ? void 0 : _a[Symbol.species];\n            if (!C || typeof C !== 'function') {\n                C = ZoneAwarePromise;\n            }\n            const chainPromise = new C(noop);\n            chainPromise[symbolFinally] = symbolFinally;\n            const zone = Zone.current;\n            if (this[symbolState] == UNRESOLVED) {\n                this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n            }\n            else {\n                scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n            }\n            return chainPromise;\n        }\n    }\n    // Protect against aggressive optimizers dropping seemingly unused properties.\n    // E.g. Closure Compiler in advanced mode.\n    ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n    ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n    ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n    ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n    const NativePromise = global[symbolPromise] = global['Promise'];\n    global['Promise'] = ZoneAwarePromise;\n    const symbolThenPatched = __symbol__('thenPatched');\n    function patchThen(Ctor) {\n        const proto = Ctor.prototype;\n        const prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n        if (prop && (prop.writable === false || !prop.configurable)) {\n            // check Ctor.prototype.then propertyDescriptor is writable or not\n            // in meteor env, writable is false, we should ignore such case\n            return;\n        }\n        const originalThen = proto.then;\n        // Keep a reference to the original method.\n        proto[symbolThen] = originalThen;\n        Ctor.prototype.then = function (onResolve, onReject) {\n            const wrapped = new ZoneAwarePromise((resolve, reject) => {\n                originalThen.call(this, resolve, reject);\n            });\n            return wrapped.then(onResolve, onReject);\n        };\n        Ctor[symbolThenPatched] = true;\n    }\n    api.patchThen = patchThen;\n    function zoneify(fn) {\n        return function (self, args) {\n            let resultPromise = fn.apply(self, args);\n            if (resultPromise instanceof ZoneAwarePromise) {\n                return resultPromise;\n            }\n            let ctor = resultPromise.constructor;\n            if (!ctor[symbolThenPatched]) {\n                patchThen(ctor);\n            }\n            return resultPromise;\n        };\n    }\n    if (NativePromise) {\n        patchThen(NativePromise);\n        patchMethod(global, 'fetch', delegate => zoneify(delegate));\n    }\n    // This is not part of public API, but it is useful for tests, so we expose it.\n    Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n    return ZoneAwarePromise;\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// override Function.prototype.toString to make zone.js patched function\n// look like native function\nZone.__load_patch('toString', (global) => {\n    // patch Func.prototype.toString to let them look like native\n    const originalFunctionToString = Function.prototype.toString;\n    const ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n    const PROMISE_SYMBOL = zoneSymbol('Promise');\n    const ERROR_SYMBOL = zoneSymbol('Error');\n    const newFunctionToString = function toString() {\n        if (typeof this === 'function') {\n            const originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n            if (originalDelegate) {\n                if (typeof originalDelegate === 'function') {\n                    return originalFunctionToString.call(originalDelegate);\n                }\n                else {\n                    return Object.prototype.toString.call(originalDelegate);\n                }\n            }\n            if (this === Promise) {\n                const nativePromise = global[PROMISE_SYMBOL];\n                if (nativePromise) {\n                    return originalFunctionToString.call(nativePromise);\n                }\n            }\n            if (this === Error) {\n                const nativeError = global[ERROR_SYMBOL];\n                if (nativeError) {\n                    return originalFunctionToString.call(nativeError);\n                }\n            }\n        }\n        return originalFunctionToString.call(this);\n    };\n    newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n    Function.prototype.toString = newFunctionToString;\n    // patch Object.prototype.toString to let them look like native\n    const originalObjectToString = Object.prototype.toString;\n    const PROMISE_OBJECT_TO_STRING = '[object Promise]';\n    Object.prototype.toString = function () {\n        if (typeof Promise === 'function' && this instanceof Promise) {\n            return PROMISE_OBJECT_TO_STRING;\n        }\n        return originalObjectToString.call(this);\n    };\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nlet passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        const options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n            }\n        });\n        // Note: We pass the `options` object as the event handler too. This is not compatible with the\n        // signature of `addEventListener` or `removeEventListener` but enables us to remove the handler\n        // without an actual handler.\n        window.addEventListener('test', options, options);\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\n// an identifier to tell ZoneTask do not create a new invoke closure\nconst OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n    useG: true\n};\nconst zoneSymbolEventNames = {};\nconst globalSources = {};\nconst EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\nconst IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol('propagationStopped');\nfunction prepareEventNames(eventName, eventNameToString) {\n    const falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n    const trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n    const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n    const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n    zoneSymbolEventNames[eventName] = {};\n    zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n    zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n}\nfunction patchEventTarget(_global, api, apis, patchOptions) {\n    const ADD_EVENT_LISTENER = (patchOptions && patchOptions.add) || ADD_EVENT_LISTENER_STR;\n    const REMOVE_EVENT_LISTENER = (patchOptions && patchOptions.rm) || REMOVE_EVENT_LISTENER_STR;\n    const LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.listeners) || 'eventListeners';\n    const REMOVE_ALL_LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.rmAll) || 'removeAllListeners';\n    const zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n    const ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n    const PREPEND_EVENT_LISTENER = 'prependListener';\n    const PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n    const invokeTask = function (task, target, event) {\n        // for better performance, check isRemoved which is set\n        // by removeEventListener\n        if (task.isRemoved) {\n            return;\n        }\n        const delegate = task.callback;\n        if (typeof delegate === 'object' && delegate.handleEvent) {\n            // create the bind version of handleEvent when invoke\n            task.callback = (event) => delegate.handleEvent(event);\n            task.originalDelegate = delegate;\n        }\n        // invoke static task.invoke\n        // need to try/catch error here, otherwise, the error in one event listener\n        // will break the executions of the other event listeners. Also error will\n        // not remove the event listener when `once` options is true.\n        let error;\n        try {\n            task.invoke(task, target, [event]);\n        }\n        catch (err) {\n            error = err;\n        }\n        const options = task.options;\n        if (options && typeof options === 'object' && options.once) {\n            // if options.once is true, after invoke once remove listener here\n            // only browser need to do this, nodejs eventEmitter will cal removeListener\n            // inside EventEmitter.once\n            const delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n            target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate, options);\n        }\n        return error;\n    };\n    function globalCallback(context, event, isCapture) {\n        // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n        // event will be undefined, so we need to use window.event\n        event = event || _global.event;\n        if (!event) {\n            return;\n        }\n        // event.target is needed for Samsung TV and SourceBuffer\n        // || global is needed https://github.com/angular/zone.js/issues/190\n        const target = context || event.target || _global;\n        const tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n        if (tasks) {\n            const errors = [];\n            // invoke all tasks which attached to current target with given event.type and capture = false\n            // for performance concern, if task.length === 1, just invoke\n            if (tasks.length === 1) {\n                const err = invokeTask(tasks[0], target, event);\n                err && errors.push(err);\n            }\n            else {\n                // https://github.com/angular/zone.js/issues/836\n                // copy the tasks array before invoke, to avoid\n                // the callback will remove itself or other listener\n                const copyTasks = tasks.slice();\n                for (let i = 0; i < copyTasks.length; i++) {\n                    if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n                        break;\n                    }\n                    const err = invokeTask(copyTasks[i], target, event);\n                    err && errors.push(err);\n                }\n            }\n            // Since there is only one error, we don't need to schedule microTask\n            // to throw the error.\n            if (errors.length === 1) {\n                throw errors[0];\n            }\n            else {\n                for (let i = 0; i < errors.length; i++) {\n                    const err = errors[i];\n                    api.nativeScheduleMicroTask(() => {\n                        throw err;\n                    });\n                }\n            }\n        }\n    }\n    // global shared zoneAwareCallback to handle all event callback with capture = false\n    const globalZoneAwareCallback = function (event) {\n        return globalCallback(this, event, false);\n    };\n    // global shared zoneAwareCallback to handle all event callback with capture = true\n    const globalZoneAwareCaptureCallback = function (event) {\n        return globalCallback(this, event, true);\n    };\n    function patchEventTargetMethods(obj, patchOptions) {\n        if (!obj) {\n            return false;\n        }\n        let useGlobalCallback = true;\n        if (patchOptions && patchOptions.useG !== undefined) {\n            useGlobalCallback = patchOptions.useG;\n        }\n        const validateHandler = patchOptions && patchOptions.vh;\n        let checkDuplicate = true;\n        if (patchOptions && patchOptions.chkDup !== undefined) {\n            checkDuplicate = patchOptions.chkDup;\n        }\n        let returnTarget = false;\n        if (patchOptions && patchOptions.rt !== undefined) {\n            returnTarget = patchOptions.rt;\n        }\n        let proto = obj;\n        while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n            proto = ObjectGetPrototypeOf(proto);\n        }\n        if (!proto && obj[ADD_EVENT_LISTENER]) {\n            // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n            proto = obj;\n        }\n        if (!proto) {\n            return false;\n        }\n        if (proto[zoneSymbolAddEventListener]) {\n            return false;\n        }\n        const eventNameToString = patchOptions && patchOptions.eventNameToString;\n        // a shared global taskData to pass data for scheduleEventTask\n        // so we do not need to create a new object just for pass some data\n        const taskData = {};\n        const nativeAddEventListener = proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER];\n        const nativeRemoveEventListener = proto[zoneSymbol(REMOVE_EVENT_LISTENER)] =\n            proto[REMOVE_EVENT_LISTENER];\n        const nativeListeners = proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] =\n            proto[LISTENERS_EVENT_LISTENER];\n        const nativeRemoveAllListeners = proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] =\n            proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER];\n        let nativePrependEventListener;\n        if (patchOptions && patchOptions.prepend) {\n            nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] =\n                proto[patchOptions.prepend];\n        }\n        /**\n         * This util function will build an option object with passive option\n         * to handle all possible input from the user.\n         */\n        function buildEventListenerOptions(options, passive) {\n            if (!passiveSupported && typeof options === 'object' && options) {\n                // doesn't support passive but user want to pass an object as options.\n                // this will not work on some old browser, so we just pass a boolean\n                // as useCapture parameter\n                return !!options.capture;\n            }\n            if (!passiveSupported || !passive) {\n                return options;\n            }\n            if (typeof options === 'boolean') {\n                return { capture: options, passive: true };\n            }\n            if (!options) {\n                return { passive: true };\n            }\n            if (typeof options === 'object' && options.passive !== false) {\n                return Object.assign(Object.assign({}, options), { passive: true });\n            }\n            return options;\n        }\n        const customScheduleGlobal = function (task) {\n            // if there is already a task for the eventName + capture,\n            // just return, because we use the shared globalZoneAwareCallback here.\n            if (taskData.isExisting) {\n                return;\n            }\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n        };\n        const customCancelGlobal = function (task) {\n            // if task is not marked as isRemoved, this call is directly\n            // from Zone.prototype.cancelTask, we should remove the task\n            // from tasksList of target first\n            if (!task.isRemoved) {\n                const symbolEventNames = zoneSymbolEventNames[task.eventName];\n                let symbolEventName;\n                if (symbolEventNames) {\n                    symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n                }\n                const existingTasks = symbolEventName && task.target[symbolEventName];\n                if (existingTasks) {\n                    for (let i = 0; i < existingTasks.length; i++) {\n                        const existingTask = existingTasks[i];\n                        if (existingTask === task) {\n                            existingTasks.splice(i, 1);\n                            // set isRemoved to data for faster invokeTask check\n                            task.isRemoved = true;\n                            if (existingTasks.length === 0) {\n                                // all tasks for the eventName + capture have gone,\n                                // remove globalZoneAwareCallback and remove the task cache from target\n                                task.allRemoved = true;\n                                task.target[symbolEventName] = null;\n                            }\n                            break;\n                        }\n                    }\n                }\n            }\n            // if all tasks for the eventName + capture have gone,\n            // we will really remove the global event callback,\n            // if not, return\n            if (!task.allRemoved) {\n                return;\n            }\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n        };\n        const customScheduleNonGlobal = function (task) {\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customSchedulePrepend = function (task) {\n            return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customCancelNonGlobal = function (task) {\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n        };\n        const customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n        const customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n        const compareTaskCallbackVsDelegate = function (task, delegate) {\n            const typeOfDelegate = typeof delegate;\n            return (typeOfDelegate === 'function' && task.callback === delegate) ||\n                (typeOfDelegate === 'object' && task.originalDelegate === delegate);\n        };\n        const compare = (patchOptions && patchOptions.diff) ? patchOptions.diff : compareTaskCallbackVsDelegate;\n        const unpatchedEvents = Zone[zoneSymbol('UNPATCHED_EVENTS')];\n        const passiveEvents = _global[zoneSymbol('PASSIVE_EVENTS')];\n        const makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget = false, prepend = false) {\n            return function () {\n                const target = this || _global;\n                let eventName = arguments[0];\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                let delegate = arguments[1];\n                if (!delegate) {\n                    return nativeListener.apply(this, arguments);\n                }\n                if (isNode && eventName === 'uncaughtException') {\n                    // don't patch uncaughtException of nodejs to prevent endless loop\n                    return nativeListener.apply(this, arguments);\n                }\n                // don't create the bind delegate function for handleEvent\n                // case here to improve addEventListener performance\n                // we will create the bind delegate when invoke\n                let isHandleEvent = false;\n                if (typeof delegate !== 'function') {\n                    if (!delegate.handleEvent) {\n                        return nativeListener.apply(this, arguments);\n                    }\n                    isHandleEvent = true;\n                }\n                if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n                    return;\n                }\n                const passive = passiveSupported && !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n                const options = buildEventListenerOptions(arguments[2], passive);\n                if (unpatchedEvents) {\n                    // check unpatched list\n                    for (let i = 0; i < unpatchedEvents.length; i++) {\n                        if (eventName === unpatchedEvents[i]) {\n                            if (passive) {\n                                return nativeListener.call(target, eventName, delegate, options);\n                            }\n                            else {\n                                return nativeListener.apply(this, arguments);\n                            }\n                        }\n                    }\n                }\n                const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n                const once = options && typeof options === 'object' ? options.once : false;\n                const zone = Zone.current;\n                let symbolEventNames = zoneSymbolEventNames[eventName];\n                if (!symbolEventNames) {\n                    prepareEventNames(eventName, eventNameToString);\n                    symbolEventNames = zoneSymbolEventNames[eventName];\n                }\n                const symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n                let existingTasks = target[symbolEventName];\n                let isExisting = false;\n                if (existingTasks) {\n                    // already have task registered\n                    isExisting = true;\n                    if (checkDuplicate) {\n                        for (let i = 0; i < existingTasks.length; i++) {\n                            if (compare(existingTasks[i], delegate)) {\n                                // same callback, same capture, same event name, just return\n                                return;\n                            }\n                        }\n                    }\n                }\n                else {\n                    existingTasks = target[symbolEventName] = [];\n                }\n                let source;\n                const constructorName = target.constructor['name'];\n                const targetSource = globalSources[constructorName];\n                if (targetSource) {\n                    source = targetSource[eventName];\n                }\n                if (!source) {\n                    source = constructorName + addSource +\n                        (eventNameToString ? eventNameToString(eventName) : eventName);\n                }\n                // do not create a new object as task.data to pass those things\n                // just use the global shared one\n                taskData.options = options;\n                if (once) {\n                    // if addEventListener with once options, we don't pass it to\n                    // native addEventListener, instead we keep the once setting\n                    // and handle ourselves.\n                    taskData.options.once = false;\n                }\n                taskData.target = target;\n                taskData.capture = capture;\n                taskData.eventName = eventName;\n                taskData.isExisting = isExisting;\n                const data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined;\n                // keep taskData into data to allow onScheduleEventTask to access the task information\n                if (data) {\n                    data.taskData = taskData;\n                }\n                const task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n                // should clear taskData.target to avoid memory leak\n                // issue, https://github.com/angular/angular/issues/20442\n                taskData.target = null;\n                // need to clear up taskData because it is a global object\n                if (data) {\n                    data.taskData = null;\n                }\n                // have to save those information to task in case\n                // application may call task.zone.cancelTask() directly\n                if (once) {\n                    options.once = true;\n                }\n                if (!(!passiveSupported && typeof task.options === 'boolean')) {\n                    // if not support passive, and we pass an option object\n                    // to addEventListener, we should save the options to task\n                    task.options = options;\n                }\n                task.target = target;\n                task.capture = capture;\n                task.eventName = eventName;\n                if (isHandleEvent) {\n                    // save original delegate for compare to check duplicate\n                    task.originalDelegate = delegate;\n                }\n                if (!prepend) {\n                    existingTasks.push(task);\n                }\n                else {\n                    existingTasks.unshift(task);\n                }\n                if (returnTarget) {\n                    return target;\n                }\n            };\n        };\n        proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n        if (nativePrependEventListener) {\n            proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n        }\n        proto[REMOVE_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const options = arguments[2];\n            const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n            const delegate = arguments[1];\n            if (!delegate) {\n                return nativeRemoveEventListener.apply(this, arguments);\n            }\n            if (validateHandler &&\n                !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n                return;\n            }\n            const symbolEventNames = zoneSymbolEventNames[eventName];\n            let symbolEventName;\n            if (symbolEventNames) {\n                symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n            }\n            const existingTasks = symbolEventName && target[symbolEventName];\n            if (existingTasks) {\n                for (let i = 0; i < existingTasks.length; i++) {\n                    const existingTask = existingTasks[i];\n                    if (compare(existingTask, delegate)) {\n                        existingTasks.splice(i, 1);\n                        // set isRemoved to data for faster invokeTask check\n                        existingTask.isRemoved = true;\n                        if (existingTasks.length === 0) {\n                            // all tasks for the eventName + capture have gone,\n                            // remove globalZoneAwareCallback and remove the task cache from target\n                            existingTask.allRemoved = true;\n                            target[symbolEventName] = null;\n                            // in the target, we have an event listener which is added by on_property\n                            // such as target.onclick = function() {}, so we need to clear this internal\n                            // property too if all delegates all removed\n                            if (typeof eventName === 'string') {\n                                const onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                                target[onPropertySymbol] = null;\n                            }\n                        }\n                        existingTask.zone.cancelTask(existingTask);\n                        if (returnTarget) {\n                            return target;\n                        }\n                        return;\n                    }\n                }\n            }\n            // issue 930, didn't find the event name or callback\n            // from zone kept existingTasks, the callback maybe\n            // added outside of zone, we need to call native removeEventListener\n            // to try to remove it.\n            return nativeRemoveEventListener.apply(this, arguments);\n        };\n        proto[LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const listeners = [];\n            const tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n            for (let i = 0; i < tasks.length; i++) {\n                const task = tasks[i];\n                let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                listeners.push(delegate);\n            }\n            return listeners;\n        };\n        proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (!eventName) {\n                const keys = Object.keys(target);\n                for (let i = 0; i < keys.length; i++) {\n                    const prop = keys[i];\n                    const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n                    let evtName = match && match[1];\n                    // in nodejs EventEmitter, removeListener event is\n                    // used for monitoring the removeListener call,\n                    // so just keep removeListener eventListener until\n                    // all other eventListeners are removed\n                    if (evtName && evtName !== 'removeListener') {\n                        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n                    }\n                }\n                // remove removeListener listener finally\n                this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n            }\n            else {\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                const symbolEventNames = zoneSymbolEventNames[eventName];\n                if (symbolEventNames) {\n                    const symbolEventName = symbolEventNames[FALSE_STR];\n                    const symbolCaptureEventName = symbolEventNames[TRUE_STR];\n                    const tasks = target[symbolEventName];\n                    const captureTasks = target[symbolCaptureEventName];\n                    if (tasks) {\n                        const removeTasks = tasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                    if (captureTasks) {\n                        const removeTasks = captureTasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                }\n            }\n            if (returnTarget) {\n                return this;\n            }\n        };\n        // for native toString patch\n        attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n        attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n        if (nativeRemoveAllListeners) {\n            attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n        }\n        if (nativeListeners) {\n            attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n        }\n        return true;\n    }\n    let results = [];\n    for (let i = 0; i < apis.length; i++) {\n        results[i] = patchEventTargetMethods(apis[i], patchOptions);\n    }\n    return results;\n}\nfunction findEventTasks(target, eventName) {\n    if (!eventName) {\n        const foundTasks = [];\n        for (let prop in target) {\n            const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n            let evtName = match && match[1];\n            if (evtName && (!eventName || evtName === eventName)) {\n                const tasks = target[prop];\n                if (tasks) {\n                    for (let i = 0; i < tasks.length; i++) {\n                        foundTasks.push(tasks[i]);\n                    }\n                }\n            }\n        }\n        return foundTasks;\n    }\n    let symbolEventName = zoneSymbolEventNames[eventName];\n    if (!symbolEventName) {\n        prepareEventNames(eventName);\n        symbolEventName = zoneSymbolEventNames[eventName];\n    }\n    const captureFalseTasks = target[symbolEventName[FALSE_STR]];\n    const captureTrueTasks = target[symbolEventName[TRUE_STR]];\n    if (!captureFalseTasks) {\n        return captureTrueTasks ? captureTrueTasks.slice() : [];\n    }\n    else {\n        return captureTrueTasks ? captureFalseTasks.concat(captureTrueTasks) :\n            captureFalseTasks.slice();\n    }\n}\nfunction patchEventPrototype(global, api) {\n    const Event = global['Event'];\n    if (Event && Event.prototype) {\n        api.patchMethod(Event.prototype, 'stopImmediatePropagation', (delegate) => function (self, args) {\n            self[IMMEDIATE_PROPAGATION_SYMBOL] = true;\n            // we need to call the native stopImmediatePropagation\n            // in case in some hybrid application, some part of\n            // application will be controlled by zone, some are not\n            delegate && delegate.apply(self, args);\n        });\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction patchCallbacks(api, target, targetName, method, callbacks) {\n    const symbol = Zone.__symbol__(method);\n    if (target[symbol]) {\n        return;\n    }\n    const nativeDelegate = target[symbol] = target[method];\n    target[method] = function (name, opts, options) {\n        if (opts && opts.prototype) {\n            callbacks.forEach(function (callback) {\n                const source = `${targetName}.${method}::` + callback;\n                const prototype = opts.prototype;\n                // Note: the `patchCallbacks` is used for patching the `document.registerElement` and\n                // `customElements.define`. We explicitly wrap the patching code into try-catch since\n                // callbacks may be already patched by other web components frameworks (e.g. LWC), and they\n                // make those properties non-writable. This means that patching callback will throw an error\n                // `cannot assign to read-only property`. See this code as an example:\n                // https://github.com/salesforce/lwc/blob/master/packages/@lwc/engine-core/src/framework/base-bridge-element.ts#L180-L186\n                // We don't want to stop the application rendering if we couldn't patch some\n                // callback, e.g. `attributeChangedCallback`.\n                try {\n                    if (prototype.hasOwnProperty(callback)) {\n                        const descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n                        if (descriptor && descriptor.value) {\n                            descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n                            api._redefineProperty(opts.prototype, callback, descriptor);\n                        }\n                        else if (prototype[callback]) {\n                            prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                        }\n                    }\n                    else if (prototype[callback]) {\n                        prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                    }\n                }\n                catch (_a) {\n                    // Note: we leave the catch block empty since there's no way to handle the error related\n                    // to non-writable property.\n                }\n            });\n        }\n        return nativeDelegate.call(target, name, opts, options);\n    };\n    api.attachOriginToPatched(target[method], nativeDelegate);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction filterProperties(target, onProperties, ignoreProperties) {\n    if (!ignoreProperties || ignoreProperties.length === 0) {\n        return onProperties;\n    }\n    const tip = ignoreProperties.filter(ip => ip.target === target);\n    if (!tip || tip.length === 0) {\n        return onProperties;\n    }\n    const targetIgnoreProperties = tip[0].ignoreProperties;\n    return onProperties.filter(op => targetIgnoreProperties.indexOf(op) === -1);\n}\nfunction patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n    // check whether target is available, sometimes target will be undefined\n    // because different browser or some 3rd party plugin.\n    if (!target) {\n        return;\n    }\n    const filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n    patchOnProperties(target, filteredProperties, prototype);\n}\n/**\n * Get all event name properties which the event name startsWith `on`\n * from the target object itself, inherited properties are not considered.\n */\nfunction getOnEventNames(target) {\n    return Object.getOwnPropertyNames(target)\n        .filter(name => name.startsWith('on') && name.length > 2)\n        .map(name => name.substring(2));\n}\nfunction propertyDescriptorPatch(api, _global) {\n    if (isNode && !isMix) {\n        return;\n    }\n    if (Zone[api.symbol('patchEvents')]) {\n        // events are already been patched by legacy patch.\n        return;\n    }\n    const ignoreProperties = _global['__Zone_ignore_on_properties'];\n    // for browsers that we can patch the descriptor:  Chrome & Firefox\n    let patchTargets = [];\n    if (isBrowser) {\n        const internalWindow = window;\n        patchTargets = patchTargets.concat([\n            'Document', 'SVGElement', 'Element', 'HTMLElement', 'HTMLBodyElement', 'HTMLMediaElement',\n            'HTMLFrameSetElement', 'HTMLFrameElement', 'HTMLIFrameElement', 'HTMLMarqueeElement', 'Worker'\n        ]);\n        const ignoreErrorProperties = isIE() ? [{ target: internalWindow, ignoreProperties: ['error'] }] : [];\n        // in IE/Edge, onProp not exist in window object, but in WindowPrototype\n        // so we need to pass WindowPrototype to check onProp exist or not\n        patchFilteredProperties(internalWindow, getOnEventNames(internalWindow), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow));\n    }\n    patchTargets = patchTargets.concat([\n        'XMLHttpRequest', 'XMLHttpRequestEventTarget', 'IDBIndex', 'IDBRequest', 'IDBOpenDBRequest',\n        'IDBDatabase', 'IDBTransaction', 'IDBCursor', 'WebSocket'\n    ]);\n    for (let i = 0; i < patchTargets.length; i++) {\n        const target = _global[patchTargets[i]];\n        target && target.prototype &&\n            patchFilteredProperties(target.prototype, getOnEventNames(target.prototype), ignoreProperties);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nZone.__load_patch('util', (global, Zone, api) => {\n    // Collect native event names by looking at properties\n    // on the global namespace, e.g. 'onclick'.\n    const eventNames = getOnEventNames(global);\n    api.patchOnProperties = patchOnProperties;\n    api.patchMethod = patchMethod;\n    api.bindArguments = bindArguments;\n    api.patchMacroTask = patchMacroTask;\n    // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS` to\n    // define which events will not be patched by `Zone.js`.\n    // In newer version (>=0.9.0), we change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep\n    // the name consistent with angular repo.\n    // The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be supported for\n    // backwards compatibility.\n    const SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n    const SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n    if (global[SYMBOL_UNPATCHED_EVENTS]) {\n        global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n    }\n    if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n        Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] =\n            global[SYMBOL_BLACK_LISTED_EVENTS];\n    }\n    api.patchEventPrototype = patchEventPrototype;\n    api.patchEventTarget = patchEventTarget;\n    api.isIEOrEdge = isIEOrEdge;\n    api.ObjectDefineProperty = ObjectDefineProperty;\n    api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n    api.ObjectCreate = ObjectCreate;\n    api.ArraySlice = ArraySlice;\n    api.patchClass = patchClass;\n    api.wrapWithCurrentZone = wrapWithCurrentZone;\n    api.filterProperties = filterProperties;\n    api.attachOriginToPatched = attachOriginToPatched;\n    api._redefineProperty = Object.defineProperty;\n    api.patchCallbacks = patchCallbacks;\n    api.getGlobalObjects = () => ({\n        globalSources,\n        zoneSymbolEventNames,\n        eventNames,\n        isBrowser,\n        isMix,\n        isNode,\n        TRUE_STR,\n        FALSE_STR,\n        ZONE_SYMBOL_PREFIX,\n        ADD_EVENT_LISTENER_STR,\n        REMOVE_EVENT_LISTENER_STR\n    });\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst taskSymbol = zoneSymbol('zoneTask');\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n    let setNative = null;\n    let clearNative = null;\n    setName += nameSuffix;\n    cancelName += nameSuffix;\n    const tasksByHandleId = {};\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[0] = function () {\n            return task.invoke.apply(this, arguments);\n        };\n        data.handleId = setNative.apply(window, data.args);\n        return task;\n    }\n    function clearTask(task) {\n        return clearNative.call(window, task.data.handleId);\n    }\n    setNative =\n        patchMethod(window, setName, (delegate) => function (self, args) {\n            if (typeof args[0] === 'function') {\n                const options = {\n                    isPeriodic: nameSuffix === 'Interval',\n                    delay: (nameSuffix === 'Timeout' || nameSuffix === 'Interval') ? args[1] || 0 :\n                        undefined,\n                    args: args\n                };\n                const callback = args[0];\n                args[0] = function timer() {\n                    try {\n                        return callback.apply(this, arguments);\n                    }\n                    finally {\n                        // issue-934, task will be cancelled\n                        // even it is a periodic task such as\n                        // setInterval\n                        // https://github.com/angular/angular/issues/40387\n                        // Cleanup tasksByHandleId should be handled before scheduleTask\n                        // Since some zoneSpec may intercept and doesn't trigger\n                        // scheduleFn(scheduleTask) provided here.\n                        if (!(options.isPeriodic)) {\n                            if (typeof options.handleId === 'number') {\n                                // in non-nodejs env, we remove timerId\n                                // from local cache\n                                delete tasksByHandleId[options.handleId];\n                            }\n                            else if (options.handleId) {\n                                // Node returns complex objects as handleIds\n                                // we remove task reference from timer object\n                                options.handleId[taskSymbol] = null;\n                            }\n                        }\n                    }\n                };\n                const task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n                if (!task) {\n                    return task;\n                }\n                // Node.js must additionally support the ref and unref functions.\n                const handle = task.data.handleId;\n                if (typeof handle === 'number') {\n                    // for non nodejs env, we save handleId: task\n                    // mapping in local cache for clearTimeout\n                    tasksByHandleId[handle] = task;\n                }\n                else if (handle) {\n                    // for nodejs env, we save task\n                    // reference in timerId Object for clearTimeout\n                    handle[taskSymbol] = task;\n                }\n                // check whether handle is null, because some polyfill or browser\n                // may return undefined from setTimeout/setInterval/setImmediate/requestAnimationFrame\n                if (handle && handle.ref && handle.unref && typeof handle.ref === 'function' &&\n                    typeof handle.unref === 'function') {\n                    task.ref = handle.ref.bind(handle);\n                    task.unref = handle.unref.bind(handle);\n                }\n                if (typeof handle === 'number' || handle) {\n                    return handle;\n                }\n                return task;\n            }\n            else {\n                // cause an error by calling it directly.\n                return delegate.apply(window, args);\n            }\n        });\n    clearNative =\n        patchMethod(window, cancelName, (delegate) => function (self, args) {\n            const id = args[0];\n            let task;\n            if (typeof id === 'number') {\n                // non nodejs env.\n                task = tasksByHandleId[id];\n            }\n            else {\n                // nodejs env.\n                task = id && id[taskSymbol];\n                // other environments.\n                if (!task) {\n                    task = id;\n                }\n            }\n            if (task && typeof task.type === 'string') {\n                if (task.state !== 'notScheduled' &&\n                    (task.cancelFn && task.data.isPeriodic || task.runCount === 0)) {\n                    if (typeof id === 'number') {\n                        delete tasksByHandleId[id];\n                    }\n                    else if (id) {\n                        id[taskSymbol] = null;\n                    }\n                    // Do not cancel already canceled functions\n                    task.zone.cancelTask(task);\n                }\n            }\n            else {\n                // cause an error by calling it directly.\n                delegate.apply(window, args);\n            }\n        });\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction patchCustomElements(_global, api) {\n    const { isBrowser, isMix } = api.getGlobalObjects();\n    if ((!isBrowser && !isMix) || !_global['customElements'] || !('customElements' in _global)) {\n        return;\n    }\n    const callbacks = ['connectedCallback', 'disconnectedCallback', 'adoptedCallback', 'attributeChangedCallback'];\n    api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction eventTargetPatch(_global, api) {\n    if (Zone[api.symbol('patchEventTarget')]) {\n        // EventTarget is already patched.\n        return;\n    }\n    const { eventNames, zoneSymbolEventNames, TRUE_STR, FALSE_STR, ZONE_SYMBOL_PREFIX } = api.getGlobalObjects();\n    //  predefine all __zone_symbol__ + eventName + true/false string\n    for (let i = 0; i < eventNames.length; i++) {\n        const eventName = eventNames[i];\n        const falseEventName = eventName + FALSE_STR;\n        const trueEventName = eventName + TRUE_STR;\n        const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n        const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n        zoneSymbolEventNames[eventName] = {};\n        zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n        zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n    }\n    const EVENT_TARGET = _global['EventTarget'];\n    if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n        return;\n    }\n    api.patchEventTarget(_global, api, [EVENT_TARGET && EVENT_TARGET.prototype]);\n    return true;\n}\nfunction patchEvent(global, api) {\n    api.patchEventPrototype(global, api);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nZone.__load_patch('legacy', (global) => {\n    const legacyPatch = global[Zone.__symbol__('legacyPatch')];\n    if (legacyPatch) {\n        legacyPatch();\n    }\n});\nZone.__load_patch('queueMicrotask', (global, Zone, api) => {\n    api.patchMethod(global, 'queueMicrotask', delegate => {\n        return function (self, args) {\n            Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n        };\n    });\n});\nZone.__load_patch('timers', (global) => {\n    const set = 'set';\n    const clear = 'clear';\n    patchTimer(global, set, clear, 'Timeout');\n    patchTimer(global, set, clear, 'Interval');\n    patchTimer(global, set, clear, 'Immediate');\n});\nZone.__load_patch('requestAnimationFrame', (global) => {\n    patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n    patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n    patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n});\nZone.__load_patch('blocking', (global, Zone) => {\n    const blockingMethods = ['alert', 'prompt', 'confirm'];\n    for (let i = 0; i < blockingMethods.length; i++) {\n        const name = blockingMethods[i];\n        patchMethod(global, name, (delegate, symbol, name) => {\n            return function (s, args) {\n                return Zone.current.run(delegate, global, args, name);\n            };\n        });\n    }\n});\nZone.__load_patch('EventTarget', (global, Zone, api) => {\n    patchEvent(global, api);\n    eventTargetPatch(global, api);\n    // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n    const XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n    if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n        api.patchEventTarget(global, api, [XMLHttpRequestEventTarget.prototype]);\n    }\n});\nZone.__load_patch('MutationObserver', (global, Zone, api) => {\n    patchClass('MutationObserver');\n    patchClass('WebKitMutationObserver');\n});\nZone.__load_patch('IntersectionObserver', (global, Zone, api) => {\n    patchClass('IntersectionObserver');\n});\nZone.__load_patch('FileReader', (global, Zone, api) => {\n    patchClass('FileReader');\n});\nZone.__load_patch('on_property', (global, Zone, api) => {\n    propertyDescriptorPatch(api, global);\n});\nZone.__load_patch('customElements', (global, Zone, api) => {\n    patchCustomElements(global, api);\n});\nZone.__load_patch('XHR', (global, Zone) => {\n    // Treat XMLHttpRequest as a macrotask.\n    patchXHR(global);\n    const XHR_TASK = zoneSymbol('xhrTask');\n    const XHR_SYNC = zoneSymbol('xhrSync');\n    const XHR_LISTENER = zoneSymbol('xhrListener');\n    const XHR_SCHEDULED = zoneSymbol('xhrScheduled');\n    const XHR_URL = zoneSymbol('xhrURL');\n    const XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol('xhrErrorBeforeScheduled');\n    function patchXHR(window) {\n        const XMLHttpRequest = window['XMLHttpRequest'];\n        if (!XMLHttpRequest) {\n            // XMLHttpRequest is not available in service worker\n            return;\n        }\n        const XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n        function findPendingTask(target) {\n            return target[XHR_TASK];\n        }\n        let oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n        let oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n        if (!oriAddListener) {\n            const XMLHttpRequestEventTarget = window['XMLHttpRequestEventTarget'];\n            if (XMLHttpRequestEventTarget) {\n                const XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget.prototype;\n                oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n            }\n        }\n        const READY_STATE_CHANGE = 'readystatechange';\n        const SCHEDULED = 'scheduled';\n        function scheduleTask(task) {\n            const data = task.data;\n            const target = data.target;\n            target[XHR_SCHEDULED] = false;\n            target[XHR_ERROR_BEFORE_SCHEDULED] = false;\n            // remove existing event listener\n            const listener = target[XHR_LISTENER];\n            if (!oriAddListener) {\n                oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n            }\n            if (listener) {\n                oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n            }\n            const newListener = target[XHR_LISTENER] = () => {\n                if (target.readyState === target.DONE) {\n                    // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n                    // readyState=4 multiple times, so we need to check task state here\n                    if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n                        // check whether the xhr has registered onload listener\n                        // if that is the case, the task should invoke after all\n                        // onload listeners finish.\n                        // Also if the request failed without response (status = 0), the load event handler\n                        // will not be triggered, in that case, we should also invoke the placeholder callback\n                        // to close the XMLHttpRequest::send macroTask.\n                        // https://github.com/angular/angular/issues/38795\n                        const loadTasks = target[Zone.__symbol__('loadfalse')];\n                        if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n                            const oriInvoke = task.invoke;\n                            task.invoke = function () {\n                                // need to load the tasks again, because in other\n                                // load listener, they may remove themselves\n                                const loadTasks = target[Zone.__symbol__('loadfalse')];\n                                for (let i = 0; i < loadTasks.length; i++) {\n                                    if (loadTasks[i] === task) {\n                                        loadTasks.splice(i, 1);\n                                    }\n                                }\n                                if (!data.aborted && task.state === SCHEDULED) {\n                                    oriInvoke.call(task);\n                                }\n                            };\n                            loadTasks.push(task);\n                        }\n                        else {\n                            task.invoke();\n                        }\n                    }\n                    else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n                        // error occurs when xhr.send()\n                        target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n                    }\n                }\n            };\n            oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n            const storedTask = target[XHR_TASK];\n            if (!storedTask) {\n                target[XHR_TASK] = task;\n            }\n            sendNative.apply(target, data.args);\n            target[XHR_SCHEDULED] = true;\n            return task;\n        }\n        function placeholderCallback() { }\n        function clearTask(task) {\n            const data = task.data;\n            // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n            // to prevent it from firing. So instead, we store info for the event listener.\n            data.aborted = true;\n            return abortNative.apply(data.target, data.args);\n        }\n        const openNative = patchMethod(XMLHttpRequestPrototype, 'open', () => function (self, args) {\n            self[XHR_SYNC] = args[2] == false;\n            self[XHR_URL] = args[1];\n            return openNative.apply(self, args);\n        });\n        const XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n        const fetchTaskAborting = zoneSymbol('fetchTaskAborting');\n        const fetchTaskScheduling = zoneSymbol('fetchTaskScheduling');\n        const sendNative = patchMethod(XMLHttpRequestPrototype, 'send', () => function (self, args) {\n            if (Zone.current[fetchTaskScheduling] === true) {\n                // a fetch is scheduling, so we are using xhr to polyfill fetch\n                // and because we already schedule macroTask for fetch, we should\n                // not schedule a macroTask for xhr again\n                return sendNative.apply(self, args);\n            }\n            if (self[XHR_SYNC]) {\n                // if the XHR is sync there is no task to schedule, just execute the code.\n                return sendNative.apply(self, args);\n            }\n            else {\n                const options = { target: self, url: self[XHR_URL], isPeriodic: false, args: args, aborted: false };\n                const task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n                if (self && self[XHR_ERROR_BEFORE_SCHEDULED] === true && !options.aborted &&\n                    task.state === SCHEDULED) {\n                    // xhr request throw error when send\n                    // we should invoke task instead of leaving a scheduled\n                    // pending macroTask\n                    task.invoke();\n                }\n            }\n        });\n        const abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', () => function (self, args) {\n            const task = findPendingTask(self);\n            if (task && typeof task.type == 'string') {\n                // If the XHR has already completed, do nothing.\n                // If the XHR has already been aborted, do nothing.\n                // Fix #569, call abort multiple times before done will cause\n                // macroTask task count be negative number\n                if (task.cancelFn == null || (task.data && task.data.aborted)) {\n                    return;\n                }\n                task.zone.cancelTask(task);\n            }\n            else if (Zone.current[fetchTaskAborting] === true) {\n                // the abort is called from fetch polyfill, we need to call native abort of XHR.\n                return abortNative.apply(self, args);\n            }\n            // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n            // task\n            // to cancel. Do nothing.\n        });\n    }\n});\nZone.__load_patch('geolocation', (global) => {\n    /// GEO_LOCATION\n    if (global['navigator'] && global['navigator'].geolocation) {\n        patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n    }\n});\nZone.__load_patch('PromiseRejectionEvent', (global, Zone) => {\n    // handle unhandled promise rejection\n    function findPromiseRejectionHandler(evtName) {\n        return function (e) {\n            const eventTasks = findEventTasks(global, evtName);\n            eventTasks.forEach(eventTask => {\n                // windows has added unhandledrejection event listener\n                // trigger the event listener\n                const PromiseRejectionEvent = global['PromiseRejectionEvent'];\n                if (PromiseRejectionEvent) {\n                    const evt = new PromiseRejectionEvent(evtName, { promise: e.promise, reason: e.rejection });\n                    eventTask.invoke(evt);\n                }\n            });\n        };\n    }\n    if (global['PromiseRejectionEvent']) {\n        Zone[zoneSymbol('unhandledPromiseRejectionHandler')] =\n            findPromiseRejectionHandler('unhandledrejection');\n        Zone[zoneSymbol('rejectionHandledHandler')] =\n            findPromiseRejectionHandler('rejectionhandled');\n    }\n});\n"], "x_google_ignoreList": [0, 1, 2]}