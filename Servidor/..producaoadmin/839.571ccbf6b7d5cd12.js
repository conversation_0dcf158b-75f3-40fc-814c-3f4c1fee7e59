"use strict";(self.webpackChunksorteieme_js=self.webpackChunksorteieme_js||[]).push([[839],{1839:(Fn,w,d)=>{d.r(w),d.d(w,{SiteEmpresaModule:()=>kn});var s=d(94666),g=d(72433),e=d(22560),C=d(70932),G=d(34497),I=d(44578);let Q=(()=>{class n{constructor(t){this.deviceService=t,this.styleTag=this.buildStyleElement()}disable(){this.deviceService.isMobile()&&(document.body.style.top=`-${window.scrollY}px`,document.body.style.position="fixed"),document.body.appendChild(this.styleTag)}enable(){if(this.deviceService.isMobile()){const t=document.body.style.top;document.body.style.position="",document.body.style.top="",window.scrollTo(0,-1*parseInt(t||"0"))}document.body.removeChild(this.styleTag)}buildStyleElement(){let t=document.createElement("style");return t.type="text/css",t.setAttribute("data-debug","Injected by WindowScrolling service."),t.textContent="\n            body {\n                overflow: hidden !important ;\n            }\n        ",t}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275inject(I.x0))},n.\u0275prov=e.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var k=d(63642),x=d(13711),S=d(7038);function q(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",46),e.\u0275\u0275element(1,"img",47),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("src","https://www.promokit.com.br/images/empresa/",t.imagens[0].linkImagem,"",e.\u0275\u0275sanitizeUrl)}}function U(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"h3",48),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" R$ ",t.preco.toFixed(2).replace(".",",")," ")}}function W(n,a){1&n&&(e.\u0275\u0275elementStart(0,"h4",48),e.\u0275\u0275text(1," Super Oferta "),e.\u0275\u0275elementEnd())}const F=function(n,a){return{"col-md-4":n,"col-md-3":a}};function J(n,a){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",38)(1,"div",39)(2,"div",40)(3,"div",41),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",42),e.\u0275\u0275listener("click",function(){const r=e.\u0275\u0275restoreView(t).$implicit,l=e.\u0275\u0275nextContext(4);return e.\u0275\u0275resetView(l.exibaProdutoSelecionado(r))}),e.\u0275\u0275template(6,q,2,1,"div",43),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"div",44),e.\u0275\u0275template(8,U,2,1,"h3",45),e.\u0275\u0275template(9,W,2,0,"h4",45),e.\u0275\u0275elementEnd()()()()}if(2&n){const t=a.$implicit,o=e.\u0275\u0275nextContext(4);e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction2(5,F,o.empresa.instagram,!o.empresa.instagram)),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(t.nome),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.imagens&&t.imagens.length>0),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.exibirPrecoNoSite&&t.preco>0),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.exibirPrecoNoSite||0==t.preco)}}function Y(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",35)(1,"div",36),e.\u0275\u0275template(2,J,10,8,"div",37),e.\u0275\u0275elementEnd()()),2&n){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.empresa.destaques)}}function X(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",33)(1,"h4"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(3,Y,3,1,"div",34),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.empresa.tituloDestaques," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.carregou)}}function Z(n,a){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",51)(1,"div",39)(2,"div",52)(3,"div",53)(4,"div",46)(5,"img",54),e.\u0275\u0275listener("click",function(){const r=e.\u0275\u0275restoreView(t).index,l=e.\u0275\u0275nextContext(4);return e.\u0275\u0275resetView(l.exibirSlide(r))}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(6,"div",44)(7,"h4",48)(8,"div",41),e.\u0275\u0275text(9),e.\u0275\u0275elementEnd()()()()()()}if(2&n){const t=a.$implicit,o=e.\u0275\u0275nextContext(4);e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction2(3,F,o.empresa.instagram,!o.empresa.instagram)),e.\u0275\u0275advance(5),e.\u0275\u0275propertyInterpolate1("src","https://www.promokit.com.br/images/empresa/",t.link,"",e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(t.titulo)}}function K(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",35)(1,"div",36),e.\u0275\u0275template(2,Z,10,6,"div",50),e.\u0275\u0275elementEnd()()),2&n){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.empresa.ambiente)}}function ee(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",49)(1,"h4"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(3,K,3,1,"div",34),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.empresa.tituloFotos," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.carregou)}}function te(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",65)(1,"a",66),e.\u0275\u0275element(2,"img",67),e.\u0275\u0275elementEnd()()),2&n){const t=a.$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("href",t.link,e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("src",t.imagem,e.\u0275\u0275sanitizeUrl)}}const ne=function(){return["fill"]};function oe(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",55)(1,"div",33)(2,"h4"),e.\u0275\u0275text(3," Mais sobre n\xf3s "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",56)(5,"div",57),e.\u0275\u0275element(6,"iframe",58),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"div",59)(8,"div",60),e.\u0275\u0275template(9,te,3,2,"div",61),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",62)(11,"a",63),e.\u0275\u0275element(12,"div",64),e.\u0275\u0275text(13," Veja mais no Instagram "),e.\u0275\u0275elementEnd()()()()()),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(6),e.\u0275\u0275property("src",t.urlMaps,e.\u0275\u0275sanitizeResourceUrl),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",t.postsInstagram),e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate1("href","https://instagram.com/",t.empresa.instagram,"",e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(1),e.\u0275\u0275property("inlineSVG","/assets/fidelidade/icones/instagram.svg")("removeSVGAttributes",e.\u0275\u0275pureFunction0(5,ne))}}const ae=function(n){return{fechado:n}},ie=function(){return["fill","width","height"]},re=function(n){return{"col-md-8":n}};function le(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",5)(1,"a",6),e.\u0275\u0275element(2,"i",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(3,"div",8)(4,"div",9),e.\u0275\u0275elementStart(5,"div",10)(6,"div",11),e.\u0275\u0275element(7,"img",12),e.\u0275\u0275elementStart(8,"div",13)(9,"span",14),e.\u0275\u0275text(10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"span",15),e.\u0275\u0275text(12),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(13,"a",16),e.\u0275\u0275element(14,"img",17),e.\u0275\u0275elementStart(15,"span"),e.\u0275\u0275text(16),e.\u0275\u0275pipe(17,"mask"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(18,"div",18),e.\u0275\u0275text(19),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(20,"div",19)(21,"div",20),e.\u0275\u0275element(22,"div",21),e.\u0275\u0275elementStart(23,"div",22),e.\u0275\u0275text(24),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(25,"a",23)(26,"div",24)(27,"div",25),e.\u0275\u0275element(28,"div",26),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(29,"span",27),e.\u0275\u0275text(30," Meu Cart\xe3o "),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(31,"div",28)(32,"div",19)(33,"div",29),e.\u0275\u0275template(34,X,4,2,"div",30),e.\u0275\u0275template(35,ee,4,2,"div",31),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(36,oe,14,6,"div",32),e.\u0275\u0275elementEnd()()()),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("href","http://wa.me/55"+(null==t.empresa?null:t.empresa.whatsapp),e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(2),e.\u0275\u0275styleProp("background-image",'url("/images/empresa/'+t.empresa.capa+'")'),e.\u0275\u0275advance(1),e.\u0275\u0275styleProp("background-image",'url("/images/empresa/'+t.empresa.capa+'")'),e.\u0275\u0275advance(3),e.\u0275\u0275propertyInterpolate1("src","/images/empresa/",t.empresa.logo,"",e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",t.empresa.nome," "),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.empresa.endereco," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("href","http://wa.me/55"+(null==t.empresa.numeroWhatsapp?null:t.empresa.numeroWhatsapp.whatsapp),e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind2(17,19,null==t.empresa.numeroWhatsapp?null:t.empresa.numeroWhatsapp.whatsapp,"(99) 9-9999-9999")," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",t.empresa.descricao," "),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(22,ae,!t.estaAberto)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.descricaoHorario),e.\u0275\u0275advance(4),e.\u0275\u0275property("inlineSVG","/assets/fidelidade/icones/icon-fidelidade-pink.svg")("removeSVGAttributes",e.\u0275\u0275pureFunction0(24,ie)),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(25,re,t.empresa.instagram)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.empresa.destaques&&t.empresa.destaques.length>0),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.empresa.ambiente&&t.empresa.ambiente.length>0),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.empresa.instagram)}}const T=function(){return["width","height"]};function se(n,a){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div")(1,"div",68),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.fecharSlides())}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(2,"h3"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()()}if(2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("inlineSVG","/assets/fidelidade/icones/icon-fechar.svg")("removeSVGAttributes",e.\u0275\u0275pureFunction0(3,T)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.empresa.tituloFotos)}}const ce=function(){return{minWidth:400}};function de(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"h2",71),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(2,"img",72)),2&n){const t=a.item;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t.titulo),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("src",t.url,e.\u0275\u0275sanitizeUrl),e.\u0275\u0275propertyInterpolate("alt",t.titulo),e.\u0275\u0275property("ngStyle",e.\u0275\u0275pureFunction0(4,ce))}}function pe(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",69)(1,"kendo-scrollview",70),e.\u0275\u0275template(2,de,3,5,"ng-template"),e.\u0275\u0275elementEnd()()),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("data",t.fotosDaEmpresa)("width","100%")("endless",!0)("height",t.alturaSlide)("arrows",!0)("pageable",!0)}}function me(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"DIV",59),e.\u0275\u0275element(1,"img",47),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("src","/images/empresa/",t.produtoSelecionado.imagens[0].linkImagem,"",e.\u0275\u0275sanitizeUrl)}}function ge(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"span",79),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"currency"),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind2(2,1,t.produtoSelecionado.preco,"BRL")," ")}}function ue(n,a){1&n&&(e.\u0275\u0275elementStart(0,"span",79),e.\u0275\u0275text(1," Super Oferta "),e.\u0275\u0275elementEnd())}function fe(n,a){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",73)(1,"div",74)(2,"div",69)(3,"div",68),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.fecharSlidesItens())}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"h3"),e.\u0275\u0275text(5,"Detalhes do Item"),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(6,me,2,1,"DIV",75),e.\u0275\u0275elementStart(7,"h4"),e.\u0275\u0275text(8),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"span"),e.\u0275\u0275text(10),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(11,ge,3,4,"span",76),e.\u0275\u0275template(12,ue,2,0,"span",76),e.\u0275\u0275elementStart(13,"a",77)(14,"div",78),e.\u0275\u0275element(15,"img",17),e.\u0275\u0275text(16," Pedir pelo WhatsApp "),e.\u0275\u0275elementEnd()()()()()}if(2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275property("inlineSVG","/assets/fidelidade/icones/icon-fechar.svg")("removeSVGAttributes",e.\u0275\u0275pureFunction0(8,T)),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",t.produtoSelecionado.imagens&&t.produtoSelecionado.imagens.length>0),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.produtoSelecionado.nome," "),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.produtoSelecionado.descricao),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.produtoSelecionado.exibirPrecoNoSite&&t.produtoSelecionado.preco>0),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.produtoSelecionado.exibirPrecoNoSite||0==t.produtoSelecionado.preco),e.\u0275\u0275advance(1),e.\u0275\u0275property("href","http://wa.me/55"+(null==t.empresa?null:t.empresa.whatsapp)+"?text="+t.produtoSelecionado.mensagemPedido,e.\u0275\u0275sanitizeUrl)}}let _e=(()=>{class n{constructor(t,o,i,r){this.clienteService=t,this.sanitizer=o,this.windowScrolling=i,this.dectorDevice=r,this.empresaLogada="fibo",this.empresa={},this.carregou=!1,this.exibirSlideShow=!1,this.exibirSlidesItens=!1,this.diasDaSemana=["Dom","Seg","Ter","Qua","Qui","Sex","S\xe1b"],this.alturaSlide="400px",this.alturaSlide=this.dectorDevice.isMobile()?"300px":"500px"}ngOnInit(){this.clienteService.obtenhaEmpresa().then(t=>{this.empresa=t.empresa,this.carregueInformacoesDeHorarioDeFuncionamento(),this.urlMaps=this.sanitizer.bypassSecurityTrustResourceUrl(this.empresa.linkMaps),this.atualizeArraysFotos(),this.clienteService.obtenhaImagensInstagram().then(o=>{this.possuiInstagram=!0,this.postsInstagram=o.posts,this.carregou=!0}).catch(o=>{this.possuiInstagram=!1,this.carregou=!0,console.log(o)})}).catch(t=>{this.carregou=!0,console.log(this.mensagemErro),this.mensagemErro="N\xe3o foi poss\xedvel carregar a empresa. Erro: "+t})}abrirWhatsapp(){window.location.href="http://wa.me/55"+this.empresa.numeroWhatsapp.whatsapp}abrirGoogleMaps(){window.location.href=this.empresa.linkMaps}carregueInformacoesDeHorarioDeFuncionamento(){if(this.estaAberto=this.empresa.estaAberta,!this.empresa.horariosHoje||0===this.empresa.horariosHoje.length)return void(this.descricaoHorario="Fechado");this.descricaoHorario=this.diasDaSemana[this.empresa.horariosHoje[0].diaDaSemana];let t=[];for(let o=0;o<this.empresa.horariosHoje.length;o++){let i=this.empresa.horariosHoje[o];if(!i.funciona){t.push("Fechado");break}t.push(i.horarioAbertura.substr(0,5)+" - "+i.horarioFechamento.substr(0,5))}this.descricaoHorario+=" "+t.join(" | ")}fecharSlides(){this.exibirSlideShow=!1,this.atualizeArraysFotos(),this.windowScrolling.enable()}exibirSlide(t){this.windowScrolling.disable();for(let o=0;o<t;o++)this.arrayRotate();setTimeout(()=>{this.exibirSlideShow=!0},0)}arrayRotate(){this.fotosDaEmpresa.push(this.fotosDaEmpresa.shift()),this.objetosFoto.push(this.objetosFoto.shift())}atualizeArraysFotos(){this.fotosDaEmpresa=this.empresa.ambiente.map(t=>({url:"https://www.promokit.com.br/images/empresa/"+t.link,titulo:t.titulo})),this.objetosFoto=this.empresa.ambiente.map(t=>t)}fecharSlidesItens(){this.windowScrolling.enable(),this.produtoSelecionado=null}exibaProdutoSelecionado(t){this.windowScrolling.disable(),this.produtoSelecionado=t}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(C.$),e.\u0275\u0275directiveInject(G.H7),e.\u0275\u0275directiveInject(Q),e.\u0275\u0275directiveInject(I.x0))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["app-site-empresa"]],decls:5,vars:5,consts:[["class","container_total",4,"ngIf"],[1,"slides-fotos",3,"hidden"],[4,"ngIf"],["class","cartao descricao",4,"ngIf"],["class","slides-produtos",4,"ngIf"],[1,"container_total"],["target","_blank",1,"float",3,"href"],[1,"fab","fa-whatsapp","my-float"],[1,"CoverImage","FlexEmbed","FlexEmbed--2by1"],[1,"CoverImage","FlexEmbed","desfocada","FlexEmbed--2by1"],[1,"cartao","conteudo","topo"],[1,"dados_empresa","linha"],[1,"imagem_empresa",3,"src"],[1,"detalhes_empresa"],[1,"nome_empresa"],[1,"endereco"],[1,"whatsapp",3,"href"],["src","/assets/fidelidade/icones/icon-whatsapp-mini.png",1,"icone","tam1"],[1,"descricao_empresa"],[1,"row"],[1,"col","horario"],[1,"bolinha",3,"ngClass"],[1,"descricao"],["routerLink","/cliente","routerLinkActive","active"],[1,"col","azul"],[1,"fidelidade"],[1,"coracao",2,"width","12px","height","auto",3,"inlineSVG","removeSVGAttributes"],[1,"texto_azul"],[1,"cartao","semborda"],[1,"col-12",3,"ngClass"],["class","menu",4,"ngIf"],["class","menu pt-3",4,"ngIf"],["class","col-12 col-md-4",4,"ngIf"],[1,"menu"],["class","container-scroll",4,"ngIf"],[1,"container-scroll"],[1,"row","flex-row","flex-nowrap","flex-md-wrap"],["class","col-8 ","style","height: 300px;",3,"ngClass",4,"ngFor","ngForOf"],[1,"col-8",2,"height","300px",3,"ngClass"],[1,"brinde","linha"],[1,"row","h-100","justify-content-center","align-items-center","caixa_brinde"],[1,"nome_brinde_pontos"],[1,"container_imagem",3,"click"],["class","",4,"ngIf"],[1,"preco_troca"],["class","mt-1",4,"ngIf"],[1,""],[1,"foto_brinde",3,"src"],[1,"mt-1"],[1,"menu","pt-3"],["class","col-8",3,"ngClass",4,"ngFor","ngForOf"],[1,"col-8",3,"ngClass"],[1,"row","h-100","caixa_brinde","justify-content-center"],[1,"container_imagem","justify-content-center","align-items-center",2,"height","180px"],[1,"foto_ambiente","img-fluid",3,"src","click"],[1,"col-12","col-md-4"],[1,"sobre_nos"],[1,"pb-1","linha"],["frameborder","0","allowfullscreen","",2,"border","0","width","100%","height","100%",3,"src"],[1,"linha"],[1,"row","mt-1","mb-1"],["class","col-6 p-0",4,"ngFor","ngForOf"],[1,"mt-1","mb-1","text-center","cinza"],["target","blank",3,"href"],[1,"icone","insta","tam1",2,"display","inline-block",3,"inlineSVG","removeSVGAttributes"],[1,"col-6","p-0"],["target","_blank",3,"href"],["alt","Foto instagram",1,"img-fluid","p-1",3,"src"],[1,"icon-fechar",2,"width","32px","height","auto",3,"inlineSVG","removeSVGAttributes","click"],[1,"cartao","descricao"],[2,"background","#000","text-align","center",3,"data","width","endless","height","arrows","pageable"],[1,"demo-title"],["draggable","false",1,"foto-brinde","img-fluid",3,"src","alt","ngStyle"],[1,"slides-produtos"],[1,"container-itens"],["class","linha",4,"ngIf"],["class","preco_troca azul grande mt-1","style","display: block",4,"ngIf"],[3,"href"],[1,"botao_produto","verde","mt-3"],[1,"preco_troca","azul","grande","mt-1",2,"display","block"]],template:function(t,o){1&t&&(e.\u0275\u0275template(0,le,37,27,"div",0),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275template(2,se,4,4,"div",2),e.\u0275\u0275template(3,pe,3,6,"div",3),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(4,fe,17,9,"div",4)),2&t&&(e.\u0275\u0275property("ngIf",o.carregou),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!o.carregou||!o.exibirSlideShow),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.carregou),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.carregou),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.carregou&&o.produtoSelecionado))},dependencies:[s.NgClass,s.NgForOf,s.NgIf,s.NgStyle,k.W$,g.yS,g.Od,x.d$,s.CurrencyPipe,S.Iq],styles:['.capa_empresa[_ngcontent-%COMP%]{height:200px;background-size:cover}.capa_empresa.centralizada[_ngcontent-%COMP%]{z-index:10;background-repeat:no-repeat;background-position-x:center;background-size:cover}.cartao[_ngcontent-%COMP%]{background:white;margin-left:auto;margin-right:auto;padding:15px}.cartao.conteudo[_ngcontent-%COMP%]{border-bottom-left-radius:10px;border-bottom-right-radius:10px;box-shadow:0 4px 10px -2px #e2e3e3}.cartao.conteudo.topo[_ngcontent-%COMP%]{margin-top:0;width:90%}.cartao.semborda[_ngcontent-%COMP%]{margin-top:20px;border:0 none;background-color:transparent;padding:0}.bg-content[_ngcontent-%COMP%]{display:none}.imagem_empresa[_ngcontent-%COMP%]{width:80px;height:80px;float:left}.detalhes_empresa[_ngcontent-%COMP%]{float:left;margin-left:10px;display:inline-block;width:calc(100% - 90px)}.nome_empresa[_ngcontent-%COMP%]{font-size:24px;color:#000;font-weight:500;display:block;line-height:1.2;max-height:1.2em;overflow:hidden}.endereco[_ngcontent-%COMP%]{font-size:11px}.whatsapp[_ngcontent-%COMP%]{display:block;margin-bottom:5px}.whatsapp[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#199d0f}.dados_empresa[_ngcontent-%COMP%]{min-height:90px;overflow:hidden}.linha[_ngcontent-%COMP%]{border-bottom:#EFEFEF solid 1px}.descricao_empresa[_ngcontent-%COMP%]{margin:10px;font-size:12px;font-weight:400}.menu[_ngcontent-%COMP%]{color:#525252;margin-top:15px}.brinde[_ngcontent-%COMP%]{margin-top:10px}.valor[_ngcontent-%COMP%]{position:absolute;color:#fff;font-size:20px;top:10px;width:100%;text-align:center}.row[_ngcontent-%COMP%]{padding-left:5px;padding-right:5px}.container_selo[_ngcontent-%COMP%]{position:relative}.brinde[_ngcontent-%COMP%]{text-align:center;position:relative}.preco_troca[_ngcontent-%COMP%]{font-weight:600}.nome_brinde[_ngcontent-%COMP%]{display:inline-block;margin-top:5px;font-size:16px;background:#4b4b4b;color:#fff;margin-left:2px;padding:5px 10px;border-radius:50px;font-weight:200}.faltam_selos[_ngcontent-%COMP%]{color:#3e48bc;margin-bottom:10px}.container-scroll[_ngcontent-%COMP%]{overflow-x:scroll;overflow-y:hidden;white-space:nowrap;-webkit-overflow-scrolling:touch;margin-right:-20px}.container-scroll[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{margin-left:5px;margin-right:5px}.foto_brinde[_ngcontent-%COMP%]{display:block;float:none;margin:5px auto 0;border-radius:30px;width:100%}.foto_ambiente[_ngcontent-%COMP%]{display:block;float:none;margin:0 auto;border-radius:20px}.container-scroll[_ngcontent-%COMP%]   .caixa_brinde[_ngcontent-%COMP%]{border-radius:5px;box-shadow:0 4px 10px -2px #e2e3e3;margin-bottom:10px;height:250px!important;margin-left:-3px;margin-right:-3px;padding-top:5px;background:white}.container-scroll[_ngcontent-%COMP%]   .foto_brinde[_ngcontent-%COMP%]{width:unset;width:100%}.nome_brinde_pontos[_ngcontent-%COMP%]{font-size:15px;font-weight:600}.container-scroll[_ngcontent-%COMP%]   .preco_troca[_ngcontent-%COMP%]{font-size:11px;font-weight:400;line-height:1em}.container-scroll[_ngcontent-%COMP%]   .preco_troca.nao_atingiu[_ngcontent-%COMP%]{color:#f67682}.container-scroll[_ngcontent-%COMP%]   .preco_troca.atingiu[_ngcontent-%COMP%]{color:#6db31b;margin-right:-20px}.botoes[_ngcontent-%COMP%]{margin:20px;text-align:center;font-size:16px;font-weight:400}.botao[_ngcontent-%COMP%]{padding:15px}.botao.verde[_ngcontent-%COMP%]{background:#6DB31B;color:#fff}.botao.azul[_ngcontent-%COMP%]{border:#1c95d4 solid 1px;margin-top:10px;color:#1c95d4}.icone.whatsapp[_ngcontent-%COMP%]{width:16px;display:inline-block;margin:0}.float[_ngcontent-%COMP%]{position:fixed;width:60px;height:60px;bottom:40px;right:40px;background-color:#25d366;color:#fff;border-radius:50px;text-align:center;font-size:30px;box-shadow:2px 2px 3px #999;z-index:100}.my-float[_ngcontent-%COMP%]{margin-top:16px}.container_imagem[_ngcontent-%COMP%]{overflow:hidden;display:flex;max-width:100%}.fidelidade[_ngcontent-%COMP%]{height:16px;width:24px;background:#3B86FF;text-align:center;float:left;line-height:1em;margin:2px 3px}.azul[_ngcontent-%COMP%]   .coracao[_ngcontent-%COMP%]{display:inline-block;fill:#fff;width:16px;height:16px;vertical-align:middle;margin:0}.azul[_ngcontent-%COMP%]{color:#3b86ff;font-weight:700}.bolinha[_ngcontent-%COMP%]{margin:5px;width:8px;height:8px;background:#6DB31B;border-radius:100px;float:left}.horario[_ngcontent-%COMP%]{padding-top:2px}.bolinha.fechado[_ngcontent-%COMP%]{background:red}.horario[_ngcontent-%COMP%]   .descricao[_ngcontent-%COMP%]{font-size:11px;font-weight:700}.icon-fechar[_ngcontent-%COMP%]{width:32px;height:32px;float:left;margin-left:5px}.slides-fotos[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .slides-produtos[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{text-align:center;line-height:32px}.slides-fotos[_ngcontent-%COMP%], .slides-produtos[_ngcontent-%COMP%]{position:fixed;overflow:auto;z-index:1000;top:0;background:white;height:100%;width:100%}.slides-produtos[_ngcontent-%COMP%]{background:rgba(255,255,255,.9);height:100%}.container-fotos[_ngcontent-%COMP%]{background:black}.cartao.descricao[_ngcontent-%COMP%]{margin-top:15px;border-radius:10px;box-shadow:0 4px 10px -2px #e2e3e3}.grande[_ngcontent-%COMP%]{font-size:18px;font-weight:700}.botao_produto[_ngcontent-%COMP%]{border:1px solid black;padding:15px;border-radius:30px;text-align:center;font-weight:500;font-size:14px}.botao_produto.verde[_ngcontent-%COMP%]{border:1px solid #3fad36;color:#3fad36}.titulo-produtos[_ngcontent-%COMP%]{background:white}.slides-produtos[_ngcontent-%COMP%]   .icon-fechar[_ngcontent-%COMP%]{width:32px;height:32px;float:left;margin-left:0;margin-top:9px}@media screen and (min-width: 768px){.cartao.conteudo[_ngcontent-%COMP%], .cartao.semborda[_ngcontent-%COMP%], .slides-fotos[_ngcontent-%COMP%], .slides-produtos[_ngcontent-%COMP%]{max-width:90%;margin-left:auto;margin-right:auto}.bg-content[_ngcontent-%COMP%]{z-index:0;position:absolute;left:0;top:0;right:0;width:120%;height:120%;margin-left:-10%;background-position:0px 0px,50% 50%;background-size:auto,cover;background-repeat:repeat,no-repeat;opacity:1;filter:blur(24px);display:block}.content[_ngcontent-%COMP%]{position:relative;z-index:10}.slides-fotos[_ngcontent-%COMP%], .slides-produtos[_ngcontent-%COMP%]{left:15%}.sobre_nos[_ngcontent-%COMP%]{border-radius:5px;box-shadow:0 4px 10px -2px #e2e3e3;padding:10px 10px 5px;background:white;margin-top:10px}.brinde[_ngcontent-%COMP%]{margin-top:0}.container-scroll[_ngcontent-%COMP%]{overflow-x:unset;overflow-y:unset;white-space:unset;-webkit-overflow-scrolling:touch;margin-right:0}.capa_empresa.centralizada[_ngcontent-%COMP%]{height:310px;max-width:90%;margin:0 auto;box-shadow:0 4px 10px -2px #a9aaaa}.capa_empresa.desfocada[_ngcontent-%COMP%]{height:305px}.cartao.conteudo[_ngcontent-%COMP%]{border-radius:0 0 30px 10px/0px 0px 30px 10px;box-shadow:0 4px 10px -2px #e2e3e3}.cartao.conteudo.topo[_ngcontent-%COMP%]{margin-top:0}}.icone.insta[_ngcontent-%COMP%]{fill:#525252}.cinza[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#525252}.FlexEmbed[_ngcontent-%COMP%]{display:block;overflow:hidden;position:relative}.FlexEmbed[_ngcontent-%COMP%]:before{content:"";display:block;width:100%}.FlexEmbed--2by1[_ngcontent-%COMP%]:before{padding-bottom:25%}.FlexEmbed.desfocada[_ngcontent-%COMP%]{background-position:0px 0px,50% 50%;background-size:auto,cover;background-repeat:repeat,no-repeat;opacity:1;filter:blur(12px);z-index:-1;position:absolute;width:100%;max-width:100%;display:block;top:0}.CoverImage[_ngcontent-%COMP%]{background-position:50%;background-repeat:no-repeat;background-size:cover;margin:0 auto;max-height:300px;max-width:90%}@media (max-width: 992px){.nome_empresa[_ngcontent-%COMP%]{font-size:16px!important}.cartao.conteudo.topo[_ngcontent-%COMP%]{width:95%}.cartao[_ngcontent-%COMP%]{padding:10px}}@media (min-width: 992px){.cartao.conteudo[_ngcontent-%COMP%], .cartao.semborda[_ngcontent-%COMP%], .slides-fotos[_ngcontent-%COMP%], .slides-produtos[_ngcontent-%COMP%]{max-width:70%;margin-left:auto;margin-right:auto}.capa_empresa.centralizada[_ngcontent-%COMP%]{height:310px;max-width:70%;margin:0 auto;box-shadow:0 4px 10px -2px #a9aaaa}.CoverImage[_ngcontent-%COMP%]{max-width:70%}}.demo-title[_ngcontent-%COMP%]{position:absolute;top:0;left:0;right:0;margin:0;padding:15px;color:#fff;background-color:#0006;text-align:center;font-size:24px}.k-scrollview-wrap[_ngcontent-%COMP%]{margin:0 auto}']}),n})();var O=d(21882),z=d(90733);const he=["*"];let V=(()=>{class n{constructor(){this.fns=[],this.commands=[],this.ing=!1}start(){!0!==this.ing&&(this.ing=!0,this.nextTime=+new Date,this.process())}process(){for(;this.commands.length;)this.commands.shift()();let t=+new Date-this.nextTime;const o=1+Math.floor(t/100);let i,r,l,c;for(t=100-t%100,this.nextTime+=100*o,l=0,c=this.fns.length;l<c;l+=2)i=this.fns[l+1],0===i?this.fns[l](o):(i+=2*o-1,r=Math.floor(i/20),r>0&&this.fns[l](r),this.fns[l+1]=i%20+1);this.ing&&setTimeout(()=>this.process(),t)}add(t,o){this.commands.push(()=>{this.fns.push(t),this.fns.push(1e3===o?1:0),this.ing=!0})}remove(t){this.commands.push(()=>{const o=this.fns.indexOf(t);-1!==o&&this.fns.splice(o,2),this.ing=this.fns.length>0})}}return n.\u0275fac=function(t){return new(t||n)},n.\u0275prov=e.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac}),n})();class h{constructor(){this.demand=!1,this.leftTime=0,this.template="$!h!\u65f6$!m!\u5206$!s!\u79d2",this.effect="normal",this.varRegular=/\$\!([\-\w]+)\!/g,this.clock=["d",100,2,"h",24,2,"m",60,2,"s",60,2,"u",10,1]}}h.\u0275fac=function(a){return new(a||h)},h.\u0275prov=e.\u0275\u0275defineInjectable({token:h,factory:h.\u0275fac,providedIn:"root"}),h.ngInjectableDef=(0,e.defineInjectable)({factory:function(){return new h},token:h,providedIn:"root"});let D=(()=>{class n{constructor(t,o,i){this.el=t,this.timer=o,this.cog=i,this.frequency=1e3,this._notify={},this.hands=[],this.left=0,this.paused=!1,this.stoped=!1,this.start=new e.EventEmitter,this.finished=new e.EventEmitter,this.notify=new e.EventEmitter,this.event=new e.EventEmitter}begin(){this.paused=!1,this.start.emit(),this.callEvent("start")}restart(){this.stoped||this.destroy(),this.init(),this.callEvent("restart")}stop(){this.stoped||(this.stoped=!0,this.destroy(),this.callEvent("stop"))}pause(){this.stoped||this.paused||(this.paused=!0,this.callEvent("pause"))}resume(){this.stoped||!this.paused||(this.paused=!1,this.callEvent("resume"))}callEvent(t){this.event.emit({action:t,left:this.left})}init(){const t=this;t.config=Object.assign({},new h,t.cog,t.config);const o=t.el.nativeElement;t.paused=t.config.demand,t.stoped=!1;const i=o.innerHTML||t.config.template;t.config.varRegular.lastIndex=0,o.innerHTML=i.replace(t.config.varRegular,(c,p)=>{("u"===p||"s-ext"===p)&&(t.frequency=100);let u="";return"s-ext"===p?(t.hands.push({type:"s"}),t.hands.push({type:"u"}),u=t.html("","s","handlet")+t.html(".","","digital")+t.html("","u","handlet")):t.hands.push({type:p}),t.html(u,p,"hand")});const r=t.config.clock;t.hands.forEach(c=>{const p=c.type;let f,u=100;for(c.node=o.querySelector(`.hand-${p}`),f=r.length-3;f>-1&&p!==r[f];f-=3)u*=r[f+1];c.base=u,c.radix=r[f+1],c.bits=r[f+2]}),t.getLeft(),t.reflow(0,!0);const l=t.reflow;return t.reflow=(c=0)=>l.apply(t,[c]),t.config.notify&&t.config.notify.forEach(c=>{if(c<1)throw new Error("the notify config must be a positive integer.");c*=1e3,t._notify[c-=c%t.frequency]=!0}),t.timer.add(t.reflow,t.frequency),o.style.display="inline",this.timer.start(),t}destroy(){return this.timer.remove(this.reflow),this}reflow(t=0,o=!1){const i=this;!o&&(i.paused||i.stoped)||(i.left=i.left-i.frequency*t,i.hands.forEach(r=>{r.lastValue=r.value,r.value=Math.floor(i.left/r.base)%r.radix}),i.repaint(),i._notify[i.left]&&(i.notify.emit(i.left),i.callEvent("notify")),i.left<1&&(i.finished.emit(0),i.stoped=!0,i.callEvent("finished"),i.destroy()))}repaint(){const t=this;if(t.config.repaint)return void t.config.repaint.apply(t);let o;t.hands.forEach(i=>{i.lastValue!==i.value&&(o="",t.toDigitals(i.value,i.bits).forEach(r=>{o+=t.html(r.toString(),"","digital")}),i.node.innerHTML=o)})}getLeft(){const t=this;let o=1e3*t.config.leftTime;const i=t.config.stopTime;!o&&i&&(o=i-(new Date).getTime()),t.left=o-o%t.frequency}html(t,o,i){switch(i){case"hand":case"handlet":o=i+" hand-"+o;break;case"digital":o="."===t?i+" "+i+"-point "+o:i+" "+i+"-"+t+" "+o}return'<span class="'+o+'">'+t+"</span>"}toDigitals(t,o){t=t<0?0:t;const i=[];for(;o--;)i[o]=t%10,t=Math.floor(t/10);return i}ngOnInit(){this.init(),this.config.demand||this.begin()}ngOnDestroy(){this.destroy()}ngOnChanges(t){t.config.firstChange||this.restart()}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(V),e.\u0275\u0275directiveInject(h))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["countdown"]],hostVars:2,hostBindings:function(t,o){2&t&&e.\u0275\u0275classProp("count-down",!0)},inputs:{config:"config"},outputs:{start:"start",finished:"finished",notify:"notify",event:"event"},features:[e.\u0275\u0275NgOnChangesFeature],ngContentSelectors:he,decls:1,vars:0,template:function(t,o){1&t&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},styles:["\n      countdown {\n        display: none;\n      }\n    "],encapsulation:2,changeDetection:0}),n})(),xe=(()=>{class n{}return n.\u0275fac=function(t){return new(t||n)},n.\u0275mod=e.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=e.\u0275\u0275defineInjector({providers:[V],imports:[s.CommonModule]}),n})();var ve=d(42228);function Ce(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"p"),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t.regra.descricao)}}function be(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"h5"),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(),o=t.index,i=t.$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate2("",o+1," - ",i.nome,"")}}function Me(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"li",14)(1,"p"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()),2&n){const t=a.$implicit;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t)}}function Pe(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"p",15),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&n){const t=a.$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t," ")}}function Se(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"ul",11),e.\u0275\u0275template(1,be,2,2,"h5",7),e.\u0275\u0275elementStart(2,"p"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(4,Me,3,1,"li",12),e.\u0275\u0275elementStart(5,"div",9),e.\u0275\u0275template(6,Pe,2,1,"p",13),e.\u0275\u0275elementEnd()()),2&n){const t=a.$implicit,o=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.regra.planos.length>1),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.descricao,""),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t.trocas),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.extras)}}function Oe(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"p"),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&n){const t=a.$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t," ")}}let Ee=(()=>{class n extends ve.H{constructor(t){super(),this.clienteService=t,this.regra={}}ngOnInit(){this.clienteService.obtenhaRegras().then(t=>{this.regra=t})}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(C.$))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["app-regras"]],features:[e.\u0275\u0275InheritDefinitionFeature],decls:15,vars:4,consts:[[1,"modal-content"],[1,"modal-header"],["id","myModalLabel",1,"modal-title"],["type","button","aria-label","Close",1,"close",3,"click"],["aria-hidden","true"],[1,"modal-body"],["pnl",""],[4,"ngIf"],["class"," list-group mt-2",4,"ngFor","ngForOf"],[1,"extras"],[4,"ngFor","ngForOf"],[1,"list-group","mt-2"],["class","list-group-item",4,"ngFor","ngForOf"],["class","mt-2",4,"ngFor","ngForOf"],[1,"list-group-item"],[1,"mt-2"]],template:function(t,o){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"h4",2),e.\u0275\u0275text(3," Regras "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"button",3),e.\u0275\u0275listener("click",function(){return o.fecheModal()}),e.\u0275\u0275elementStart(5,"span",4),e.\u0275\u0275text(6,"\xd7"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(7,"div",5,6)(9,"h3"),e.\u0275\u0275text(10),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(11,Ce,2,1,"p",7),e.\u0275\u0275template(12,Se,7,4,"ul",8),e.\u0275\u0275elementStart(13,"div",9),e.\u0275\u0275template(14,Oe,2,1,"p",10),e.\u0275\u0275elementEnd()()()),2&t&&(e.\u0275\u0275advance(10),e.\u0275\u0275textInterpolate(o.regra.titulo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.regra.descricao),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",o.regra.planos),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",o.regra.extras))},dependencies:[s.NgForOf,s.NgIf],styles:["li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .extras[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{padding-bottom:0;margin-bottom:0}"]}),n})();var ye=d(92782),we=d(54078),m=d(2508),E=d(25068),y=d(61603);const Ie=["frm"],ke=["txtTelefone"];function Fe(n,a){1&n&&(e.\u0275\u0275elementStart(0,"span",15),e.\u0275\u0275text(1,"Meu Cart\xe3o"),e.\u0275\u0275elementEnd())}function Te(n,a){1&n&&(e.\u0275\u0275elementStart(0,"span",15),e.\u0275\u0275text(1,"Meus Cart\xf5es"),e.\u0275\u0275elementEnd())}const ze=function(){return["fill"]};function Ve(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"span",16),e.\u0275\u0275element(1,"div",17),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"telefone"),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("inlineSVG","/assets/fidelidade/icones/user.svg")("removeSVGAttributes",e.\u0275\u0275pureFunction0(6,ze)),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate2(" ",t.cliente.nome," | ",e.\u0275\u0275pipeBind1(3,4,t.cliente.telefone),"")}}function De(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",18)(1,"a",19),e.\u0275\u0275element(2,"img",20),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",21)(4,"a",19)(5,"span",22),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"span",23),e.\u0275\u0275text(8),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"a",24),e.\u0275\u0275element(10,"img",25),e.\u0275\u0275elementStart(11,"span"),e.\u0275\u0275text(12),e.\u0275\u0275pipe(13,"mask"),e.\u0275\u0275elementEnd()()()()),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate1("src","/images/empresa/",t.empresa.logo,"",e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" ",t.empresa.nome," "),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.empresa.endereco," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("href","http://wa.me/55"+(null==t.empresa.numeroWhatsapp?null:t.empresa.numeroWhatsapp.whatsapp),e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind2(13,5,null==t.empresa||null==t.empresa.numeroWhatsapp?null:t.empresa.numeroWhatsapp.whatsapp,"(99) 9-9999-9999")," ")}}function Ae(n,a){1&n&&(e.\u0275\u0275elementStart(0,"div",26),e.\u0275\u0275element(1,"div",27)(2,"bR")(3,"br"),e.\u0275\u0275elementStart(4,"span"),e.\u0275\u0275text(5,"Carregando..."),e.\u0275\u0275elementEnd()())}function Re(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",26)(1,"span",28),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.mensagemDeErro)}}function je(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" Agora comece a juntar ",t.cliente.cartoes[0].plano.tipoDeAcumulo," para conseguir brindes, descontos. ")}}function Le(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",29)(1,"button",30)(2,"span",31),e.\u0275\u0275text(3,"\xd7"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"span"),e.\u0275\u0275text(5," Seu cartao foi criado com sucesso! "),e.\u0275\u0275template(6,je,2,1,"span",9),e.\u0275\u0275elementEnd()()),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(6),e.\u0275\u0275property("ngIf",(null==t.cliente.cartoes?null:t.cliente.cartoes.length)&&!t.cliente.cartoes[0].plano.cartaoConsumo)}}function Ne(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div"),e.\u0275\u0275element(1,"app-cartao-selo",33),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2).$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275property("cartao",t)}}function $e(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div"),e.\u0275\u0275template(1,Ne,2,1,"div",9),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext().$implicit,o=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!o.cartaoLink||o.cartaoLink==t.id)}}function Be(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div"),e.\u0275\u0275element(1,"app-cartao-consumo-selo",33),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2).$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275property("cartao",t)}}function He(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div"),e.\u0275\u0275template(1,Be,2,1,"div",9),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext().$implicit,o=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!o.cartaoLink||o.cartaoLink==t.id)}}function Ge(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",35),e.\u0275\u0275element(1,"app-cartao-pontos",36),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2).$implicit,o=e.\u0275\u0275nextContext();e.\u0275\u0275property("hidden",t.plano.tipoDeAcumulo==o.tipoSelo),e.\u0275\u0275advance(1),e.\u0275\u0275property("cartao",t)("empresa",o.empresa)}}function Qe(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div"),e.\u0275\u0275template(1,Ge,2,3,"div",34),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext().$implicit,o=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!o.cartaoLink||o.cartaoLink==t.id)}}function qe(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div"),e.\u0275\u0275element(1,"app-cartao-cashback",33),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2).$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275property("cartao",t)}}function Ue(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div"),e.\u0275\u0275template(1,qe,2,1,"div",9),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext().$implicit,o=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!o.cartaoLink||o.cartaoLink==t.id)}}function We(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",32),e.\u0275\u0275template(1,$e,2,1,"div",9),e.\u0275\u0275template(2,He,2,1,"div",9),e.\u0275\u0275template(3,Qe,2,1,"div",9),e.\u0275\u0275template(4,Ue,2,1,"div",9),e.\u0275\u0275elementEnd()),2&n){const t=a.$implicit,o=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.plano.tipoDeAcumulo===o.tipoSelo&&!t.plano.cartaoConsumo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.plano.tipoDeAcumulo===o.tipoSelo&&t.plano.cartaoConsumo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.plano.tipoDeAcumulo===o.tipoPontos&&!t.plano.cartaoConsumo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.plano.tipoDeAcumulo===o.tipoReais)}}function Je(n,a){1&n&&(e.\u0275\u0275elementStart(0,"p"),e.\u0275\u0275text(1,"Celular \xe9 obrigat\xf3rio"),e.\u0275\u0275elementEnd())}function Ye(n,a){1&n&&(e.\u0275\u0275elementStart(0,"p"),e.\u0275\u0275text(1,"Informe um n\xfamero de celular v\xe1lido"),e.\u0275\u0275elementEnd())}function Xe(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",72),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("innerHTML",t.mensagemErroEnvio,e.\u0275\u0275sanitizeHtml),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.mensagemErroEnvio," ")}}function Ze(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",72),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("innerHTML",t.mensagemFalhaEnvio,e.\u0275\u0275sanitizeHtml),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.mensagemErroEnvio," ")}}function Ke(n,a){1&n&&(e.\u0275\u0275elementStart(0,"p"),e.\u0275\u0275text(1,"Nome \xe9 obrigat\xf3rio"),e.\u0275\u0275elementEnd())}function et(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",72),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("innerHTML",t.mensagemErroCriarCartao,e.\u0275\u0275sanitizeHtml),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.mensagemErroCriarCartao," ")}}function tt(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",72),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("innerHTML",t.mensagemErroCriarCartao,e.\u0275\u0275sanitizeHtml),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.mensagemErroCriarCartao," ")}}function nt(n,a){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"a",73),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.reenviarCodigo())}),e.\u0275\u0275text(1," Renviar C\xf3digo "),e.\u0275\u0275elementEnd()}}const A=function(n,a){return{"needs-validation":n,"was-validated":a}},R=function(n){return{disabled:n}},ot=function(){return{leftTime:60,template:"$!m!:$!s!"}};function at(n,a){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div")(1,"div",35)(2,"h2"),e.\u0275\u0275text(3,"Informe seu telefone"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(4," Informe o seu n\xfamero de telefone "),e.\u0275\u0275elementStart(5,"form",37,38),e.\u0275\u0275listener("ngSubmit",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.onSubmit())}),e.\u0275\u0275elementStart(7,"div",39)(8,"input",40,41),e.\u0275\u0275listener("ngModelChange",function(i){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.dadosContato.telefone=i)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",42),e.\u0275\u0275template(12,Je,2,0,"p",9),e.\u0275\u0275template(13,Ye,2,0,"p",9),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(14,"div",43)(15,"button",44),e.\u0275\u0275text(16," Abrir meu Cart\xe3o "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(17,Xe,2,2,"div",45),e.\u0275\u0275template(18,Ze,2,2,"div",45),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(19,"div",35)(20,"div",46)(21,"div",47)(22,"label"),e.\u0275\u0275text(23,"Seu telefone"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(24,"div",48)(25,"div")(26,"div",49)(27,"div",50)(28,"h4",51)(29,"span",52),e.\u0275\u0275text(30),e.\u0275\u0275pipe(31,"telefone"),e.\u0275\u0275elementEnd()()()()()()()(),e.\u0275\u0275elementStart(32,"h3"),e.\u0275\u0275text(33,"Agora informe seus dados para completar o cadastro "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(34,"form",37,53),e.\u0275\u0275listener("ngSubmit",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.onSubmitCriarCartao())}),e.\u0275\u0275elementStart(36,"div",39)(37,"label",54),e.\u0275\u0275text(38," Informe seu nome "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(39,"input",55,56),e.\u0275\u0275listener("ngModelChange",function(i){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.dadosContato.nome=i)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(41,"div",42),e.\u0275\u0275template(42,Ke,2,0,"p",9),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(43,"div",39)(44,"label",57),e.\u0275\u0275text(45," Informe sua data de nascimento "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(46,"kendo-datepicker",58,59),e.\u0275\u0275listener("ngModelChange",function(i){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.dadosContato.dataNascimento=i)}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(48,"div",39)(49,"label"),e.\u0275\u0275text(50," Informe seu g\xeanero "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(51,"div",60)(52,"input",61),e.\u0275\u0275listener("ngModelChange",function(i){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.dadosContato.genero=i)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(53,"label",62),e.\u0275\u0275text(54,"Masculino"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(55,"input",63),e.\u0275\u0275listener("ngModelChange",function(i){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.dadosContato.genero=i)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(56,"label",64),e.\u0275\u0275text(57,"Feminino"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(58,"div",42),e.\u0275\u0275text(59," Sexo \xe9 obrigat\xf3rio "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(60,"div",43)(61,"button",65),e.\u0275\u0275text(62," Criar Cart\xe3o "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(63,et,2,2,"div",45),e.\u0275\u0275template(64,tt,2,2,"div",45),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(65,"div",66)(66,"div",67),e.\u0275\u0275text(67),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(68,"app-frm-valide-codigo",68),e.\u0275\u0275elementStart(69,"div",69)(70,"countdown",70),e.\u0275\u0275listener("finished",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.onFinished())}),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(71,"br"),e.\u0275\u0275template(72,nt,2,0,"a",71),e.\u0275\u0275elementEnd()()()}if(2&n){const t=e.\u0275\u0275reference(6),o=e.\u0275\u0275reference(9),i=e.\u0275\u0275reference(35),r=e.\u0275\u0275reference(40),l=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden","INFORMAR_TELEFONE"!==l.tela),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction2(32,A,!t.submitted,t.submitted)),e.\u0275\u0275advance(3),e.\u0275\u0275property("disabled",l.enviado&&l.mensagemAguarde)("ngModel",l.dadosContato.telefone),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngIf",null==o.errors?null:o.errors.required),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==o.errors?null:o.errors.campoTelefone),e.\u0275\u0275advance(2),e.\u0275\u0275property("disabled",l.enviando||l.enviado&&l.mensagemAguarde)("ngClass",e.\u0275\u0275pureFunction1(35,R,l.enviando||l.enviado&&l.mensagemAguarde)),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.submitted&&l.mensagemErroEnvio&&!t.valid),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.submitted&&l.mensagemFalhaEnvio),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden","INFORMAR_NOME"!==l.tela),e.\u0275\u0275advance(11),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(31,30,l.dadosContato.telefone)," "),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction2(37,A,!i.submitted,i.submitted)),e.\u0275\u0275advance(5),e.\u0275\u0275property("disabled",l.enviado&&l.mensagemAguarde)("ngModel",l.dadosContato.nome),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",null==r.errors?null:r.errors.required),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngModel",l.dadosContato.dataNascimento),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",l.dadosContato.genero),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngModel",l.dadosContato.genero),e.\u0275\u0275advance(6),e.\u0275\u0275property("disabled",l.enviando||l.enviado&&l.mensagemAguarde)("ngClass",e.\u0275\u0275pureFunction1(40,R,l.enviando||l.enviado&&l.mensagemAguarde)),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",i.submitted&&l.mensagemErroCriarCartao&&!i.valid),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",i.submitted&&l.mensagemErroCriarCartao),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden","INFORMAR_CODIGO"!==l.tela),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!l.enviado||!l.mensagemAguarde),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",l.mensagemAguarde," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("parent",l),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",l.validouCodigo),e.\u0275\u0275advance(1),e.\u0275\u0275property("config",e.\u0275\u0275pureFunction0(42,ot)),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",l.habiliteReenviarCodigo)}}function it(n,a){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",74),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.verRegras())}),e.\u0275\u0275text(1," Confira as regras "),e.\u0275\u0275elementEnd()}}function rt(n,a){1&n&&(e.\u0275\u0275elementStart(0,"div",75),e.\u0275\u0275text(1," Confira as regras "),e.\u0275\u0275elementEnd())}let v=(()=>{class n{constructor(t,o,i,r,l,c,p){this.route=t,this.clienteService=o,this.dialogService=i,this.fb=r,this.router=l,this.contatosService=c,this.armazenamentoService=p,this.component=!1,this.carregando=!0,this.carregou=!1,this.cliente={},this.tipoSelo=O.b.Selos,this.tipoPontos=O.b.Pontos,this.tipoReais=O.b.Reais,this.dadosContato={},this.habiliteReenviarCodigo=!1,this.validouCodigo=!1,this.selecionarCartao=!1,this.tela="INFORMAR_TELEFONE",this.mensagemErroCriarCartao="",this.criandoCartao=!1,this.codigo="",this.mask=["(",/\d/,/\d/,")"," ",/\d/,"-",/\d/,/\d/,/\d/,/\d/,"-",/\d/,/\d/,/\d/,/\d/]}ngAfterViewInit(){setTimeout(()=>{this.counter&&this.counter.stop(),this.telefoneCliente&&(this.maskedInputController=z.maskInput({inputElement:this.telefoneCliente.nativeElement,mask:this.mask}))})}ngOnInit(){if(this.component)return;let t=this.route.snapshot.params.token,o=this.route.snapshot.params.cartao;this.route.snapshot.params.c&&(this.codigo=this.route.snapshot.params.c),t?this.confirmeClientePeloToken(t,o):this.setCliente(this.route.snapshot.params.idCliente,o)}exibaCliente(t,o){this.cliente=t,this.empresa=o,this.carregou=!0,this.carregando=!1,this.cliente.cartoes.forEach(i=>{i.link=this.router.url,i.link.endsWith(i.id)||(i.link=i.link+"/"+i.id),i.plano.brindes=i.plano.brindes.sort((r,l)=>r.valorEmPontos>l.valorEmPontos?1:-1)})}setCliente(t,o){this.idCliente=t||this.armazenamentoService.carregue("idCliente",!1),this.clienteService.obtenhaCliente(this.idCliente).then(i=>{this.exibaCliente(i.cliente,i.empresa),this.cartaoLink=o}).catch(i=>{this.carregando=!1,this.carregou=!0,i.empresa?(this.empresa=i.empresa,this.buscarContato=!0):this.mensagemDeErro="Houve um erro ao carregar os dados do cliente: "+i})}confirmeClientePeloToken(t,o){this.clienteService.confirmarContato(t).then(i=>{this.armazenamentoService.salveSemExpirar("idCliente",i.id),this.armazenamentoService.salveSemExpirar(i.id,o),this.cartaoLink=o,this.setCliente(i.id,o),this.codigo="criado"}).catch(i=>{this.carregando=!1,this.carregou=!1,this.mensagemDeErro=i})}verRegras(){const t=this.dialogService.open({title:null,content:Ee,minWidth:250,width:window.innerWidth>700?700:window.innerWidth,maxHeight:window.innerHeight-100,cssClass:"bsModal"});ye.P.abriuPopupNgBootstrap(t)}solicitarBrinde(){}onSubmit(){if(this.enviando=!0,!this.frm.valid)return this.mensagemErroEnvio="Existem erros no preenchimento. Por favor, verifique e tente novamente!",window.scrollTo(0,0),void(this.enviando=!1);this.mensagemDeErro=null,this.mensagemErroEnvio=null,this.mensagemFalhaEnvio=null,this.clienteService.obtenhaClientePorTelefone(this.unmask(this.dadosContato.telefone)).then(o=>{console.log(o);let i=o.cliente;if(i){if(this.enviando=!1,this.contato=i,this.contato.id){this.armazenamentoService.salveSemExpirar("idCliente",i.id),this.validouCliente();const r="/cliente?t="+(new Date).getTime();window.location.href=r}}else this.enviando=!1,this.validouTelefone()}).catch(o=>{this.enviando=!1,this.mensagemFalhaEnvio=o})}unmask(t){return t.replace(/\D+/g,"")}onFinished(){return this.habiliteReenviarCodigo=!0,this.mensagemAguarde=null,this.counter.config={leftTime:60,demand:!1},!1}reenviarCodigo(){return this.enviado=!1,this.habiliteReenviarCodigo=!1,!1}validouCliente(){this.validouCodigo=!0}onSubmitCriarCartao(){this.criandoCartao=!0,this.contatosService.salveNovoCartao({contato:{nome:this.dadosContato.nome,telefone:this.unmask(this.dadosContato.telefone),genero:this.dadosContato.genero,dataNascimento:this.dadosContato.dataNascimento}}).then(t=>{this.criandoCartao=!1,console.log("resposta"),console.log(t),this.armazenamentoService.salveSemExpirar("idCliente",t.id);const o="/cliente?c=criado&t="+(new Date).getTime();window.location.href=o}).catch(t=>{this.criandoCartao=!1})}validouTelefone(){this.tela="INFORMAR_NOME"}exibaExtrato(){}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(g.gz),e.\u0275\u0275directiveInject(C.$),e.\u0275\u0275directiveInject(we.xA),e.\u0275\u0275directiveInject(m.QS),e.\u0275\u0275directiveInject(g.F0),e.\u0275\u0275directiveInject(E.v),e.\u0275\u0275directiveInject(y.d))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["app-cliente"]],viewQuery:function(t,o){if(1&t&&(e.\u0275\u0275viewQuery(Ie,5),e.\u0275\u0275viewQuery(ke,7),e.\u0275\u0275viewQuery(D,7)),2&t){let i;e.\u0275\u0275queryRefresh(i=e.\u0275\u0275loadQuery())&&(o.frm=i.first),e.\u0275\u0275queryRefresh(i=e.\u0275\u0275loadQuery())&&(o.telefoneCliente=i.first),e.\u0275\u0275queryRefresh(i=e.\u0275\u0275loadQuery())&&(o.counter=i.first)}},inputs:{component:"component"},decls:17,vars:12,consts:[[1,"dados_cliente"],[1,"caixa_titulo"],["class","titulo",4,"ngIf"],["class","subtitulo",4,"ngIf"],[1,"cartao","conteudo"],["class","dados_empresa linha",4,"ngIf"],["class","carregando",4,"ngIf"],["class","alert alert-success mt-1","role","alert",4,"ngIf"],["class","mt-3",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"cartao","semborda"],["class","botao azul",3,"click",4,"ngIf"],["class","botao cinza",4,"ngIf"],[1,"rodape"],[1,"logo",3,"inlineSVG"],[1,"titulo"],[1,"subtitulo"],[1,"icone","tam1",3,"inlineSVG","removeSVGAttributes"],[1,"dados_empresa","linha"],["routerLink","/"],[1,"imagem_empresa",3,"src"],[1,"detalhes_empresa"],[1,"nome_empresa"],[1,"endereco"],["target","_blank",1,"whatsapp",3,"href"],["src","/assets/fidelidade/icones/icon-whatsapp-mini.png",1,"icone","tam1"],[1,"carregando"],["role","status",1,"spinner-border"],[2,"color","red"],["role","alert",1,"alert","alert-success","mt-1"],["type","button","data-dismiss","alert","aria-label","Close",1,"close"],["aria-hidden","true"],[1,"mt-3"],[3,"cartao"],[3,"hidden",4,"ngIf"],[3,"hidden"],[3,"cartao","empresa"],["novalidate","",3,"ngClass","ngSubmit"],["frm","ngForm"],[1,"form-group","mb-2"],["type","tel","appAutoFocus","","placeholder","(__) _-____-____","required","","campoTelefone","","name","telefone",1,"form-control","ng-tns-c12-1","ui-inputtext","ui-widget","ui-state-default","ui-corner-all",3,"disabled","ngModel","ngModelChange"],["telefone","ngModel","txtTelefone",""],[1,"invalid-feedback"],[1,"form-group","mb-2","text-center"],["type","submit",1,"btn","btn-block",3,"disabled","ngClass"],["class","alert alert-danger mt-2","role","alert",3,"innerHTML",4,"ngIf"],[1,"card"],[1,"card-body",2,"padding","0.8rem"],["id","cardCollpase1",1,"collapse","show"],[1,"row","mt-1"],[1,"col"],[1,"mt-0"],[2,"font-weight","bold"],["frmCriarNome","ngForm"],["for","nome"],["id","nome","type","text","appAutoFocus","","placeholder","Seu nome","required","","name","nome",1,"form-control","ng-tns-c12-1","ui-inputtext","ui-widget","ui-state-default","ui-corner-all",3,"disabled","ngModel","ngModelChange"],["txtNome","ngModel"],["for","dataNascimento"],["id","dataNascimento","name","dataNascimento",1,"form-control",3,"ngModel","ngModelChange"],["txtDataNascimento","ngModel"],[1,"mt-1","mb-1",2,"width","250px"],["type","radio","name","sexo","id","sexohi","value","Homem","required","","kendoRadioButton","",1,"k-radio","right",3,"ngModel","ngModelChange"],["for","sexohi",1,"k-radio-label","mr-1"],["type","radio","name","sexo","id","sexomi","value","Mulher","required","","kendoRadioButton","",1,"k-radio","right",3,"ngModel","ngModelChange"],["for","sexomi",1,"k-radio-label"],["type","submit",1,"btn","btn-block","btn-success",3,"disabled","ngClass"],[1,"mt-2",3,"hidden"],[1,"form-group","mb-2","text-center",3,"hidden"],[3,"parent"],[2,"text-align","center",3,"hidden"],[1,"countdown-grande",3,"config","finished"],["href","#",3,"click",4,"ngIf"],["role","alert",1,"alert","alert-danger","mt-2",3,"innerHTML"],["href","#",3,"click"],[1,"botao","azul",3,"click"],[1,"botao","cinza"]],template:function(t,o){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275template(2,Fe,2,0,"span",2),e.\u0275\u0275template(3,Te,2,0,"span",2),e.\u0275\u0275template(4,Ve,4,7,"span",3),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",4),e.\u0275\u0275template(6,De,14,8,"div",5),e.\u0275\u0275template(7,Ae,6,0,"div",6),e.\u0275\u0275template(8,Re,3,1,"div",6),e.\u0275\u0275template(9,Le,7,1,"div",7),e.\u0275\u0275template(10,We,5,4,"div",8),e.\u0275\u0275template(11,at,73,43,"div",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",10),e.\u0275\u0275template(13,it,2,0,"div",11),e.\u0275\u0275template(14,rt,2,0,"div",12),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(15,"div",13),e.\u0275\u0275element(16,"div",14),e.\u0275\u0275elementEnd()),2&t&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!o.cliente.id||1==(null==o.cliente.cartoes?null:o.cliente.cartoes.length)||o.cartaoLink),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.cliente.cartoes&&(null==o.cliente.cartoes?null:o.cliente.cartoes.length)>1&&!o.cartaoLink),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.carregou),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",o.empresa),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.carregando),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!o.carregando&&!o.carregou),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","criado"===o.codigo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",o.cliente.cartoes),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.carregou&&!o.cliente.id),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",o.carregou),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.carregando),e.\u0275\u0275advance(2),e.\u0275\u0275property("inlineSVG","/assets/fidelidade/logo-promokit-vertical.svg"))},styles:[".dados_cliente[_ngcontent-%COMP%]{background-image:url(/assets/fidelidade/bg_roxo.png);min-height:300px}.caixa_titulo[_ngcontent-%COMP%]{padding-top:20px;text-align:center;color:#fff}.titulo[_ngcontent-%COMP%]{font-size:32px;font-weight:700;display:block;line-height:1.2}.subtitulo[_ngcontent-%COMP%]{font-size:12px}.col[_ngcontent-%COMP%], .row[_ngcontent-%COMP%]{padding-left:5px;padding-right:5px}.cartao[_ngcontent-%COMP%]{background:white;margin-left:10px;margin-right:10px;padding:15px}.cartao.conteudo[_ngcontent-%COMP%]{margin-top:-200px;border-radius:10px 45px 10px 10px;min-height:350px;box-shadow:0 4px 10px -2px #e2e3e3}.cartao.semborda[_ngcontent-%COMP%]{margin-top:20px;border:0 none;background-color:transparent;padding:0}.icone.tam1[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{width:11px!important;height:11px!important;margin-top:-4px}.icone[_ngcontent-%COMP%]{display:inline-block;fill:#fff;vertical-align:middle}.icone[_ngcontent-%COMP%]   .tam1[_ngcontent-%COMP%]{width:11px;height:11px}[_nghost-%COMP%]     .icone.tam1 svg{width:11px!important;height:11px!important;margin-top:-4px}@media screen and (min-width: 550px){.cartao.conteudo[_ngcontent-%COMP%], .cartao.semborda[_ngcontent-%COMP%]{max-width:500px;margin-left:auto;margin-right:auto}}.imagem_empresa[_ngcontent-%COMP%]{width:80px;height:80px;float:left}.detalhes_empresa[_ngcontent-%COMP%]{float:left;margin-left:10px;display:inline-block;width:calc(100% - 90px)}.nome_empresa[_ngcontent-%COMP%]{font-size:18px;color:#000;font-weight:500;display:block;line-height:1.2;max-height:1.2em;overflow:hidden}.endereco[_ngcontent-%COMP%]{font-size:11px}.whatsapp[_ngcontent-%COMP%]{display:block;margin-bottom:5px}.whatsapp[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#199d0f}.dados_empresa[_ngcontent-%COMP%]{min-height:95px;overflow:hidden}.linha[_ngcontent-%COMP%]{border-bottom:#EFEFEF solid 1px}.botao[_ngcontent-%COMP%]{border:1px solid black;padding:15px;border-radius:30px;text-align:center;font-weight:500;font-size:14px}.botao.azul[_ngcontent-%COMP%]{border:1px solid #3B86FF;color:#3b86ff}.botao.cinza[_ngcontent-%COMP%]{border:1px solid #808495;color:#808495}.rodape[_ngcontent-%COMP%]{margin-top:30px}.logo[_ngcontent-%COMP%]{width:100px;margin:0 auto}.carregando[_ngcontent-%COMP%]{text-align:center}.carrossel[_ngcontent-%COMP%]{padding-bottom:30px;outline:none}[_nghost-%COMP%]     .swiper-pagination{bottom:93%!important}.margem-fim[_ngcontent-%COMP%]{margin-bottom:15px}.nome_brinde_pontos[_ngcontent-%COMP%]{font-size:15px;font-weight:600}[_nghost-%COMP%]     .pode_trocar svg{height:12px!important}.btn[_ngcontent-%COMP%]{background:#3a44b9;font-size:18px}.btn[_ngcontent-%COMP%]:hover{color:#02c0ce}.btn-success[_ngcontent-%COMP%]:hover{color:#beebcd}.erro[_ngcontent-%COMP%]{color:red}.was-validated[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:valid, .form-control.is-valid[_ngcontent-%COMP%]{background:none!important}"]}),n})();function lt(n,a){1&n&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275element(1,"img",11),e.\u0275\u0275elementStart(2,"h3"),e.\u0275\u0275text(3,"Ativando"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p",12),e.\u0275\u0275text(5," Aguarde enquanto ativamos seu cart\xe3o... "),e.\u0275\u0275elementEnd()())}function st(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275element(1,"img",11),e.\u0275\u0275elementStart(2,"h3"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p",12),e.\u0275\u0275text(5," Seu cart\xe3o foi ativado com sucesso! "),e.\u0275\u0275elementStart(6,"a",13),e.\u0275\u0275text(7,"Clique aqui"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(8," para ver seu cart\xe3o!"),e.\u0275\u0275elementEnd()()),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("Parab\xe9ns, ",t.contato.nome,"!"),e.\u0275\u0275advance(3),e.\u0275\u0275property("routerLink","/cliente")}}function ct(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275element(1,"img",11),e.\u0275\u0275elementStart(2,"h3"),e.\u0275\u0275text(3,"Falha"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p",12),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate1(" Houve um erro ao tentar ativar o cart\xe3o: ",t.mensagemErro," ")}}let dt=(()=>{class n{constructor(t,o,i){this.clienteService=t,this.activatedRoute=o,this.armazenamentoService=i,this.confirmando=!0,this.confirmou=!1,this.idCartao=this.activatedRoute.snapshot.params.idCartao}ngOnInit(){this.clienteService.confirmarContato(this.idCartao).then(t=>{this.confirmando=!1,this.confirmou=!0,this.contato=t,this.armazenamentoService.salveSemExpirar("idCliente",t.id)}).catch(t=>{this.confirmando=!1,this.confirmou=!1,this.mensagemErro=t})}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(C.$),e.\u0275\u0275directiveInject(g.gz),e.\u0275\u0275directiveInject(y.d))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["app-confirmar"]],decls:13,vars:3,consts:[[1,"account-pages","mt-5","mb-5"],[1,"container"],[1,"row","justify-content-center"],[1,"col-md-8","col-lg-6","col-xl-5"],[1,"card","bg-pattern"],[1,"card-body","p-4"],[1,"text-center","w-75","m-auto"],["href","index.html"],["src","assets/images/logo-dark.png","alt","","height","22"],["class","mt-3 text-center",4,"ngIf"],[1,"mt-3","text-center"],["alt","","src","/assets/fidelidade/icones/loyalty_card.svg"],[1,"text-muted","font-14","mt-2"],[3,"routerLink"]],template:function(t,o){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"div",5)(6,"div",6)(7,"a",7)(8,"span"),e.\u0275\u0275element(9,"img",8),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(10,lt,6,0,"div",9),e.\u0275\u0275template(11,st,9,2,"div",9),e.\u0275\u0275template(12,ct,6,1,"div",9),e.\u0275\u0275elementEnd()()()()()()),2&t&&(e.\u0275\u0275advance(10),e.\u0275\u0275property("ngIf",o.confirmando),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!o.confirmando&&o.confirmou),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!o.confirmando&&!o.confirmou))},dependencies:[s.NgIf,g.yS]}),n})();var j=d(88032);const mt=["telaCliente"],gt=[{path:"",component:(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(t){return new(t||n)},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["app-site-empresa-router"]],decls:1,vars:0,template:function(t,o){1&t&&e.\u0275\u0275element(0,"router-outlet")},dependencies:[g.lC],encapsulation:2}),n})(),children:[{path:"cliente",component:v},{path:":empresa",component:v},{path:"cliente/ativar/:token",component:v},{path:"cliente/:numero/extrato/:token",component:(()=>{class n{constructor(t,o){this.route=t,this.clienteService=o,this.cliente={}}ngOnInit(){this.clienteService.obtenhaClientePorTelefone(this.route.snapshot.params.numero,this.route.snapshot.params.token).then(i=>{i.cliente&&(this.telaCliente.exibaCliente(i.cliente,i.empresa),this.telaCliente.exibaExtrato())})}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(g.gz),e.\u0275\u0275directiveInject(C.$))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["app-meus-cartoes"]],viewQuery:function(t,o){if(1&t&&e.\u0275\u0275viewQuery(mt,7),2&t){let i;e.\u0275\u0275queryRefresh(i=e.\u0275\u0275loadQuery())&&(o.telaCliente=i.first)}},decls:2,vars:1,consts:[[3,"component"],["telaCliente",""]],template:function(t,o){1&t&&e.\u0275\u0275element(0,"app-cliente",0,1),2&t&&e.\u0275\u0275property("component",!0)},dependencies:[v]}),n})()},{path:"cliente/:idCliente",canActivate:[j.P],component:v},{path:"cliente/:idCliente/:cartao",canActivate:[j.P],component:v},{path:"confirmar/:idCartao",component:dt},{path:"",component:_e}]}];let ut=(()=>{class n{}return n.\u0275fac=function(t){return new(t||n)},n.\u0275mod=e.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=e.\u0275\u0275defineInjector({imports:[g.Bz.forChild(gt),g.Bz]}),n})();var ft=d(70782),L=d(27209),N=d(73185);function _t(n,a){1&n&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275element(1,"img",11),e.\u0275\u0275elementStart(2,"h3"),e.\u0275\u0275text(3,"Ativando"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p",12),e.\u0275\u0275text(5," Aguarde enquanto ativamos seu cart\xe3o... "),e.\u0275\u0275elementEnd()())}function ht(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275element(1,"img",11),e.\u0275\u0275elementStart(2,"h3"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p",12),e.\u0275\u0275text(5," Seu cart\xe3o foi ativado com sucesso! "),e.\u0275\u0275elementStart(6,"a",13),e.\u0275\u0275text(7,"Clique aqui"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(8," para ver seu cart\xe3o!"),e.\u0275\u0275elementEnd()()),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("Parab\xe9ns, ",t.contato.nome,"!")}}function xt(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275element(1,"img",11),e.\u0275\u0275elementStart(2,"h3"),e.\u0275\u0275text(3,"Falha"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p",12),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate1(" Houve um erro ao tentar ativar o cart\xe3o: ",t.mensagemErro," ")}}let vt=(()=>{class n{constructor(){this.confirmando=!1,this.confirmou=!1}ngOnInit(){}}return n.\u0275fac=function(t){return new(t||n)},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["app-cartao-confirmado"]],inputs:{contato:"contato",confirmou:"confirmou"},decls:13,vars:3,consts:[[1,"account-pages","mt-5","mb-5"],[1,"container"],[1,"row","justify-content-center"],[1,"col-12"],[1,"card","bg-pattern"],[1,"card-body","p-4"],[1,"text-center","w-75","m-auto"],["href","index.html"],["src","assets/images/logo-dark.png","alt","","height","22"],["class","mt-3 text-center",4,"ngIf"],[1,"mt-3","text-center"],["alt","","src","/assets/fidelidade/icones/loyalty_card.svg"],[1,"text-muted","font-14","mt-2"],["href","/cliente"]],template:function(t,o){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"div",5)(6,"div",6)(7,"a",7)(8,"span"),e.\u0275\u0275element(9,"img",8),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(10,_t,6,0,"div",9),e.\u0275\u0275template(11,ht,9,1,"div",9),e.\u0275\u0275template(12,xt,6,1,"div",9),e.\u0275\u0275elementEnd()()()()()()),2&t&&(e.\u0275\u0275advance(10),e.\u0275\u0275property("ngIf",o.confirmando),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!o.confirmando&&o.confirmou),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!o.confirmando&&!o.confirmou))},dependencies:[s.NgIf]}),n})();const Ct=["frmValidarCodigo"],bt=["txtCodigo"];function Mt(n,a){1&n&&(e.\u0275\u0275elementStart(0,"p"),e.\u0275\u0275text(1,"C\xf3digo \xe9 obrigat\xf3rio"),e.\u0275\u0275elementEnd())}function Pt(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",21),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("innerHTML",t.mensagemErroEnvio,e.\u0275\u0275sanitizeHtml),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.mensagemErroEnvio," ")}}function St(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",21),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("innerHTML",t.mensagemFalhaEnvio,e.\u0275\u0275sanitizeHtml),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.mensagemErroEnvio," ")}}const Ot=function(n,a){return{"needs-validation":n,"was-validated":a}},Et=function(n){return{disabled:n}};function yt(n,a){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",2)(1,"div",3)(2,"div",4)(3,"label"),e.\u0275\u0275text(4,"Seu telefone"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",5)(6,"div")(7,"div",6)(8,"div",7)(9,"h4",8)(10,"span",9),e.\u0275\u0275text(11),e.\u0275\u0275elementEnd()()()()()()()(),e.\u0275\u0275elementStart(12,"h2"),e.\u0275\u0275text(13,"Verificar C\xf3digo"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(14," Informe o c\xf3digo que enviamos para o seu celular "),e.\u0275\u0275elementStart(15,"form",10,11),e.\u0275\u0275listener("ngSubmit",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.onSubmitValidarCodigo())}),e.\u0275\u0275elementStart(17,"div",12)(18,"input",13,14),e.\u0275\u0275listener("ngModelChange",function(i){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.codigo=i)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(21,"div",15),e.\u0275\u0275template(22,Mt,2,0,"p",16),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(23,"div",17)(24,"button",18),e.\u0275\u0275text(25," Verificar C\xf3digo "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(26,Pt,2,2,"div",19),e.\u0275\u0275template(27,St,2,2,"div",19),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"div",20),e.\u0275\u0275text(29," Se ainda n\xe3o possui cart\xe3o, visite nossa loja e crie seu cart\xe3o agora mesmo! "),e.\u0275\u0275elementEnd()()()}if(2&n){const t=e.\u0275\u0275reference(16),o=e.\u0275\u0275reference(19),i=e.\u0275\u0275nextContext();e.\u0275\u0275advance(11),e.\u0275\u0275textInterpolate(i.parent.dadosContato.telefone),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction2(9,Ot,!t.submitted,t.submitted)),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngModel",i.codigo),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngIf",null==o.errors?null:o.errors.required),e.\u0275\u0275advance(2),e.\u0275\u0275property("disabled",i.enviando||t.submitted&&i.mensagemAguarde)("ngClass",e.\u0275\u0275pureFunction1(12,Et,i.enviando||t.submitted&&i.mensagemAguarde)),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",i.mensagemErroEnvio&&!t.valid),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",i.mensagemFalhaEnvio),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",t.submitted&&i.mensagemAguarde)}}function wt(n,a){if(1&n&&e.\u0275\u0275element(0,"app-cartao-confirmado",22),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("contato",t.contato)("confirmou",null!=t.contato)}}let It=(()=>{class n{constructor(t,o){this.clienteService=t,this.armazenamentoService=o,this.codigo="",this.mensagemErroEnvio="",this.enviando=!1,this.confirmando=!1,this.confirmou=!0,this.maskCodigo=[/\d/,/\d/,/\d/,/\d/,/\d/,/\d/]}ngOnInit(){}ngAfterViewInit(){setTimeout(()=>{this.maskedInputController2=z.maskInput({inputElement:this.txtCodigo.nativeElement,mask:this.maskCodigo})})}onSubmitValidarCodigo(){if(this.enviando=!0,!this.frmValidarCodigo.valid)return this.mensagemErroEnvio="Existem erros no preenchimento. Por favor, verifique e tente novamente!",window.scrollTo(0,0),void(this.enviando=!1);this.clienteService.valideCodigo(this.unmask(this.codigo),this.parent.unmask(this.parent.dadosContato.telefone)).then(t=>{this.enviando=!1,this.contato=t,this.contato.id?(this.armazenamentoService.salveSemExpirar("idCliente",t.id),this.parent.validouCliente(),this.mensagemAguarde="Voc\xea receber\xe1 uma mensagem com um c\xf3digo para voc\xea acessar seu cart\xe3o. Informe o c\xf3digo no campo abaixo:"):this.parent.validouTelefone()}).catch(t=>{this.enviando=!1,this.mensagemFalhaEnvio=t})}unmask(t){return t.replace(/\D+/g,"")}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(C.$),e.\u0275\u0275directiveInject(y.d))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["app-frm-valide-codigo"]],viewQuery:function(t,o){if(1&t&&(e.\u0275\u0275viewQuery(Ct,5),e.\u0275\u0275viewQuery(bt,5)),2&t){let i;e.\u0275\u0275queryRefresh(i=e.\u0275\u0275loadQuery())&&(o.frmValidarCodigo=i.first),e.\u0275\u0275queryRefresh(i=e.\u0275\u0275loadQuery())&&(o.txtCodigo=i.first)}},inputs:{parent:"parent"},decls:2,vars:2,consts:[["class","mt-2",4,"ngIf"],[3,"contato","confirmou",4,"ngIf"],[1,"mt-2"],[1,"card"],[1,"card-body",2,"padding","0.8rem"],["id","cardCollpase1",1,"collapse","show"],[1,"row","mt-1"],[1,"col"],[1,"mt-0"],[2,"font-weight","bold"],["novalidate","",3,"ngClass","ngSubmit"],["frmValidarCodigo","ngForm"],[1,"form-group","mb-2"],["type","text","appAutoFocus","","placeholder","______","required","","name","codigo",1,"form-control","ng-tns-c12-1","ui-inputtext","ui-widget","ui-state-default","ui-corner-all",3,"ngModel","ngModelChange"],["ctrlCodigo","ngModel","txtCodigo",""],[1,"invalid-feedback"],[4,"ngIf"],[1,"form-group","mb-2","text-center"],["type","submit",1,"btn","btn-block",3,"disabled","ngClass"],["class","alert alert-danger mt-2","role","alert",3,"innerHTML",4,"ngIf"],[3,"hidden"],["role","alert",1,"alert","alert-danger","mt-2",3,"innerHTML"],[3,"contato","confirmou"]],template:function(t,o){1&t&&(e.\u0275\u0275template(0,yt,30,14,"div",0),e.\u0275\u0275template(1,wt,1,2,"app-cartao-confirmado",1)),2&t&&(e.\u0275\u0275property("ngIf",null==o.contato),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.contato))},dependencies:[s.NgClass,s.NgIf,m._Y,m.Fj,m.JJ,m.JL,m.Q7,m.On,m.F,N.U,vt],styles:[".dados_cliente[_ngcontent-%COMP%]{background-image:url(/assets/fidelidade/bg_roxo.png);min-height:300px}.caixa_titulo[_ngcontent-%COMP%]{padding-top:20px;text-align:center;color:#fff}.titulo[_ngcontent-%COMP%]{font-size:32px;font-weight:700;display:block;line-height:1.2}.subtitulo[_ngcontent-%COMP%]{font-size:12px}.icone[_ngcontent-%COMP%]{display:inline-block;fill:#fff;vertical-align:middle}.on[_ngcontent-%COMP%]   .icone.selo[_ngcontent-%COMP%]{fill:#6db31b}.off[_ngcontent-%COMP%]   .icone.selo[_ngcontent-%COMP%]{fill:#c2c4cc}.sem[_ngcontent-%COMP%]   .icone.selo[_ngcontent-%COMP%]{fill:transparent}.sem[_ngcontent-%COMP%]   .valor[_ngcontent-%COMP%]{display:none}.icone.selo[_ngcontent-%COMP%]{width:100%;text-align:center}.col[_ngcontent-%COMP%]{padding-left:5px;padding-right:5px}.icone[_ngcontent-%COMP%]   .tam1[_ngcontent-%COMP%]{width:11px;height:11px}.cartao[_ngcontent-%COMP%]{background:white;margin-left:10px;margin-right:10px;padding:15px}.cartao.conteudo[_ngcontent-%COMP%]{margin-top:-200px;border-radius:10px 45px 10px 10px;min-height:350px;box-shadow:0 4px 10px -2px #e2e3e3}.cartao.semborda[_ngcontent-%COMP%]{margin-top:20px;border:0 none;background-color:transparent;padding:0}@media screen and (min-width: 550px){.cartao.conteudo[_ngcontent-%COMP%], .cartao.semborda[_ngcontent-%COMP%]{max-width:500px;margin-left:auto;margin-right:auto}}.imagem_empresa[_ngcontent-%COMP%]{width:80px;height:80px;float:left}.detalhes_empresa[_ngcontent-%COMP%]{float:left;margin-left:10px;display:inline-block;width:calc(100% - 90px)}.nome_empresa[_ngcontent-%COMP%]{font-size:24px;color:#000;font-weight:500;display:block;line-height:1.2;max-height:1.2em;overflow:hidden}.endereco[_ngcontent-%COMP%]{font-size:11px}.whatsapp[_ngcontent-%COMP%]{display:block;margin-bottom:5px}.whatsapp[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#199d0f}.dados_empresa[_ngcontent-%COMP%]{min-height:90px;overflow:hidden}.linha[_ngcontent-%COMP%]{border-bottom:#EFEFEF solid 1px}.pontuacao[_ngcontent-%COMP%]{margin-top:15px;margin-bottom:15px}.brinde[_ngcontent-%COMP%]{margin-top:10px}.valor[_ngcontent-%COMP%]{position:absolute;color:#fff;font-size:20px;top:10px;width:100%;text-align:center}.row[_ngcontent-%COMP%]{padding-left:5px;padding-right:5px}.container_selo[_ngcontent-%COMP%]{position:relative}.brinde[_ngcontent-%COMP%]{text-align:center;position:relative}.preco_troca[_ngcontent-%COMP%]{font-weight:600}.nome_brinde[_ngcontent-%COMP%]{display:inline-block;margin-top:5px;font-size:16px;background:#4b4b4b;color:#fff;margin-left:2px;padding:5px 10px;border-radius:50px;font-weight:200}.faltam_selos[_ngcontent-%COMP%]{color:#3e48bc;margin-bottom:10px}.foto_brinde[_ngcontent-%COMP%]{display:block;float:none;margin:5px auto 0;width:150px;border-radius:30px}.botao[_ngcontent-%COMP%]{border:1px solid black;padding:15px;border-radius:30px;text-align:center;font-weight:500;font-size:14px}.botao.azul[_ngcontent-%COMP%]{border:1px solid #3B86FF;color:#3b86ff}.botao.cinza[_ngcontent-%COMP%]{border:1px solid #808495;color:#808495}.rodape[_ngcontent-%COMP%]{margin-top:30px}.logo[_ngcontent-%COMP%]{width:100px;margin:0 auto}.carregando[_ngcontent-%COMP%]{text-align:center}.carrossel[_ngcontent-%COMP%]{padding-bottom:30px;outline:none}.pontos[_ngcontent-%COMP%]{background:#6DB31B;color:#fff;text-align:center;margin:15px;padding:4px;border-radius:2px 20px}.pontos-interno[_ngcontent-%COMP%]{border:white solid 1px;line-height:1.2em;font-size:40px;font-weight:600;border-radius:2px 20px}.pontos-interno[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{position:absolute;top:5px;left:63px}.icone.estrela[_ngcontent-%COMP%]{position:absolute;left:35px;bottom:25%}.minimo-troca[_ngcontent-%COMP%]{text-align:center;font-size:16px;font-weight:500;line-height:1em}.margem-fim[_ngcontent-%COMP%]{margin-bottom:15px}.container-scroll[_ngcontent-%COMP%]{overflow-x:scroll;overflow-y:hidden;white-space:nowrap;-webkit-overflow-scrolling:touch}.container-scroll[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{margin-left:5px;margin-right:5px}.container-scroll[_ngcontent-%COMP%]   .caixa_brinde[_ngcontent-%COMP%]{border:1px solid #E2E3E3;border-radius:5px;box-shadow:0 4px 10px -2px #e2e3e3;margin-bottom:10px;padding:5px}.container-scroll[_ngcontent-%COMP%]   .foto_brinde[_ngcontent-%COMP%]{height:150px;width:unset;min-height:150px;margin-left:5px;margin-right:5px}.nome_brinde_pontos[_ngcontent-%COMP%]{font-size:15px;font-weight:600}.container-scroll[_ngcontent-%COMP%]   .preco_troca[_ngcontent-%COMP%]{font-size:11px;font-weight:400;line-height:1em}.container-scroll[_ngcontent-%COMP%]   .preco_troca.nao_atingiu[_ngcontent-%COMP%]{color:#f67682}.container-scroll[_ngcontent-%COMP%]   .preco_troca.atingiu[_ngcontent-%COMP%]{color:#6db31b}.botao_troca[_ngcontent-%COMP%]{margin-left:5px;margin-right:5px;margin-bottom:10px;border-radius:20px;font-size:10px;line-height:2.5em}.pode_trocar[_ngcontent-%COMP%]{background:#6DB31B;color:#fff}.container-scroll[_ngcontent-%COMP%]   .faltam_selos[_ngcontent-%COMP%]{border:1px solid #E2E3E3}.pode_trocar[_ngcontent-%COMP%]   .icone.estrela[_ngcontent-%COMP%]{position:absolute;bottom:11px;left:12px}.container_imagem[_ngcontent-%COMP%]{width:100%;height:150px;display:table;margin:5px 0}.container_imagem[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{text-align:center;vertical-align:middle;display:table-cell}.container_imagem[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:200px;max-height:150px;margin:0 auto}.btn[_ngcontent-%COMP%]{background:#3a44b9;font-size:18px}.btn[_ngcontent-%COMP%]:hover{color:#02c0ce}.erro[_ngcontent-%COMP%]{color:red}.was-validated[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:valid, .form-control.is-valid[_ngcontent-%COMP%]{background:none!important}.countdown-grande[_ngcontent-%COMP%]{font-size:32px;font-weight:500}"]}),n})();var _=d(89904),kt=d(27410);function Ft(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"span",13),e.\u0275\u0275element(1,"br"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.ano)}}function Tt(n,a){1&n&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,"-"),e.\u0275\u0275elementEnd())}const zt=function(n,a){return{"text-success":n,"text-danger":a}};function Vt(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",4)(1,"span",5),e.\u0275\u0275text(2),e.\u0275\u0275template(3,Ft,3,1,"span",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",7)(5,"div",8),e.\u0275\u0275element(6,"app-icone-acao",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"div",10)(8,"span"),e.\u0275\u0275text(9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",11)(11,"b"),e.\u0275\u0275template(12,Tt,2,0,"span",12),e.\u0275\u0275text(13),e.\u0275\u0275elementEnd()()()()()),2&n){const t=a.$implicit;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",t.horarioAbreviado," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.ano),e.\u0275\u0275advance(3),e.\u0275\u0275property("acao",t)("exibirDescricao",!1),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("",t.descricao," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction2(8,zt,t.credito,t.debito)),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.debito),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.acumulado,"")}}function Dt(n,a){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",14),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.carregueMais())}),e.\u0275\u0275text(1,"ver mais"),e.\u0275\u0275elementEnd()}}function At(n,a){1&n&&(e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275text(1,"isso \xe9 tudo"),e.\u0275\u0275elementEnd())}let b=(()=>{class n{constructor(t){this.contatosService=t,this.cartao={},this.acoes=[],this.carregando=!0,this.inicio=0,this.total=10,this.carregouTodos=!1}ngOnInit(){this.acoes.length||this.carregueAcoes()}carregueAcoes(){this.contatosService.obtenhaUltimasAcoes(null,this.cartao,this.inicio,this.total,!0).then(t=>{t.forEach(o=>this.acoes.push(o)),this.carregouTodos=t.length<this.total})}carregueMais(){this.inicio+=this.total,this.carregueAcoes()}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(E.v))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["app-extrato-pontos"]],inputs:{cartao:"cartao"},decls:4,vars:3,consts:[[1,"list-unstyled","timeline-sm"],["class","timeline-sm-item",4,"ngFor","ngForOf"],["class","btn btn-block  btn-rounded  btn-outline-light mt-2",3,"click",4,"ngIf"],["class","btn btn-block  btn-rounded  btn-outline-light mt-2",4,"ngIf"],[1,"timeline-sm-item"],[1,"timeline-sm-date","horario"],["class","text-muted",4,"ngIf"],[1,"row"],[1,"col-1"],[3,"acao","exibirDescricao"],[1,"col-11","text-muted"],[3,"ngClass"],[4,"ngIf"],[1,"text-muted"],[1,"btn","btn-block","btn-rounded","btn-outline-light","mt-2",3,"click"],[1,"btn","btn-block","btn-rounded","btn-outline-light","mt-2"]],template:function(t,o){1&t&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,Vt,14,11,"div",1),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(2,Dt,2,0,"button",2),e.\u0275\u0275template(3,At,2,0,"button",3)),2&t&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",o.acoes),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!o.carregouTodos),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.carregouTodos))},dependencies:[s.NgClass,s.NgForOf,s.NgIf,kt.D],styles:[".horario[_ngcontent-%COMP%]{font-weight:700;text-transform:uppercase}.row[_ngcontent-%COMP%]{height:60px}.btn-outline-light[_ngcontent-%COMP%]{color:#607d8b}  .timeline-sm .timeline-sm-item:after{border-color:#3a45b26b!important}"]}),n})();var $=d(33838);function Rt(n,a){1&n&&(e.\u0275\u0275elementStart(0,"tbody")(1,"tr")(2,"td",8)(3,"p"),e.\u0275\u0275text(4,"N\xe3o h\xe1 pontos a expirar para per\xedodo selecionado"),e.\u0275\u0275elementEnd()()()())}function jt(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"tr",9)(1,"td",10),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"date"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"td")(5,"b"),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd()()()),2&n){const t=a.$implicit,o=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind2(3,2,t.dataVencimento,"dd/MM/yyyy")," "),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(o.getPontos(t.pontos))}}let M=(()=>{class n{constructor(t){this.contatosService=t,this.cartao={},this.pontuacaoVencer=[],this.buscou=!1,this.periodo={},this.periodos=[],this.formato="YYYYMMDD"}ngOnInit(){if(this.cartao.plano.validade)this.adicionePeriodosFiltro(this.cartao.plano.validade);else{let t=moment(this.cartao.plano.vencimento),o=moment(t).diff(moment(),"d");this.adicionePeriodosFiltro(o)}this.periodo=this.periodos[this.periodos.length-1],this.busquePontuacaoVencidas()}adicionePeriodosFiltro(t){let o=t>29?Math.round(t/3):t;this.periodos.push({descricao:o+" dias",value:{inicio:0,fim:moment().add(o,"d").format(this.formato)}}),t>29&&(this.periodos.push({descricao:2*o+" dias",value:{inicio:0,fim:moment().add(2*o,"d").format(this.formato)}}),this.periodos.push({descricao:3*o+" dias",value:{inicio:0,fim:moment().add(3*o,"d").format(this.formato)}}))}getPontos(t){return this.cartao&&"Reais"===this.cartao.plano.tipoDeAcumulo?(0,s.formatCurrency)(t,"pt-BR","R$"):(0,s.formatNumber)(Math.floor(t),"pt-BR")}busquePontuacaoVencidas(){this.contatosService.obtenhaPontuacaoVencer(this.cartao,this.periodo.value.inicio,this.periodo.value.fim).then(t=>{this.pontuacaoVencer=t,this.buscou=!0})}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(E.v))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["app-cartao-proximos-vencimentos"]],inputs:{cartao:"cartao"},decls:17,vars:5,consts:[[1,"row"],[1,"col-2"],[1,"col-10"],[1,"form-group"],["name","periodo","textField","descricao",1,"form-control",3,"ngModel","data","ngModelChange"],[1,"table","table-striped","table-sm"],[4,"ngIf"],["class","timeline-sm-item",4,"ngFor","ngForOf"],["colspan","2"],[1,"timeline-sm-item"],[1,"horario"]],template:function(t,o){1&t&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275element(1,"div",1),e.\u0275\u0275elementStart(2,"div",2)(3,"div",3)(4,"label"),e.\u0275\u0275text(5," Filtrar por per\xedodo:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"kendo-dropdownlist",4),e.\u0275\u0275listener("ngModelChange",function(r){return o.periodo=r})("ngModelChange",function(){return o.busquePontuacaoVencidas()}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(7,"table",5)(8,"thead")(9,"tr")(10,"th"),e.\u0275\u0275text(11,"Data da expira\xe7\xe3o"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"th"),e.\u0275\u0275text(13),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(14,Rt,5,0,"tbody",6),e.\u0275\u0275elementStart(15,"tbody"),e.\u0275\u0275template(16,jt,7,5,"tr",7),e.\u0275\u0275elementEnd()()),2&t&&(e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",o.periodo)("data",o.periodos),e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate1(" ",o.cartao.plano.tipoDeAcumulo," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",0==o.pontuacaoVencer.length&&o.buscou),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",o.pontuacaoVencer.pontuacoes))},dependencies:[s.NgForOf,s.NgIf,m.JJ,m.On,$.rI,s.DatePipe],styles:[".k-widget.form-control[_ngcontent-%COMP%]{width:150px;display:inline-block;margin-left:10px}"]}),n})();const Lt=["tabs"];function Nt(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div")(1,"div",28),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()),2&n){const t=e.\u0275\u0275nextContext().$implicit,o=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("Faltam ",o.getPontosRestantes(t,o.cartao)," pontos")}}const $t=function(){return["fill","heigth","width"]};function Bt(n,a){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div")(1,"div",29),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext().$implicit,r=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(r.solicitarBrinde(i,r.cartao))}),e.\u0275\u0275element(2,"div",6),e.\u0275\u0275text(3," Solicitar brinde "),e.\u0275\u0275elementEnd()()}2&n&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("inlineSVG","/assets/fidelidade/icones/star-icone.svg")("removeSVGAttributes",e.\u0275\u0275pureFunction0(2,$t)))}const Ht=function(n){return{font11:n}},Gt=function(n,a){return{nao_atingiu:n,atingiu:a}};function Qt(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",18)(1,"div",19)(2,"div",20)(3,"div",21)(4,"p",22),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"p",23),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"div",24)(9,"div",25),e.\u0275\u0275element(10,"img",26),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(11,Nt,3,1,"div",27),e.\u0275\u0275template(12,Bt,4,3,"div",27),e.\u0275\u0275elementEnd()()),2&n){const t=a.$implicit,o=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(4),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(7,Ht,t.nome.length>50)),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t.nome),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction2(9,Gt,t.valorEmPontos>o.cartao.pontos,t.valorEmPontos<=o.cartao.pontos)),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.valorEmPontos," pontos "),e.\u0275\u0275advance(3),e.\u0275\u0275propertyInterpolate1("src","https://fibo.promokit.com.br/images/empresa/",t.linkImagem,"",e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.valorEmPontos>o.cartao.pontos),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.valorEmPontos<=o.cartao.pontos)}}function qt(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",13)(1,"div",3)(2,"div",14),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(4,"div",15)(5,"div",16),e.\u0275\u0275template(6,Qt,13,12,"div",17),e.\u0275\u0275elementEnd()()),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" Troque por brindes a partir de ",t.cartao.plano.brindes[0].valorEmPontos," pontos. "),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",t.cartao.plano.brindes)}}function Ut(n,a){if(1&n&&e.\u0275\u0275element(0,"app-extrato-pontos",30),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("cartao",t.cartao)}}function Wt(n,a){if(1&n&&e.\u0275\u0275element(0,"app-cartao-proximos-vencimentos",30),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("cartao",t.cartao)}}function Jt(n,a){1&n&&(e.\u0275\u0275elementStart(0,"kendo-tabstrip-tab",31),e.\u0275\u0275template(1,Wt,1,1,"ng-template",10),e.\u0275\u0275elementEnd()),2&n&&e.\u0275\u0275property("title","Expirando")("id","expirando")}const Yt=function(){return["fill"]};let B=(()=>{class n{constructor(){location.pathname.indexOf("/extrato/")>0&&this.selecioneTabExtrato()}selecioneTabExtrato(){setTimeout(()=>{this.tabs.selectTab(1)},0)}}return n.\u0275fac=function(t){return new(t||n)},n.\u0275dir=e.\u0275\u0275defineDirective({type:n,viewQuery:function(t,o){if(1&t&&e.\u0275\u0275viewQuery(Lt,7),2&t){let i;e.\u0275\u0275queryRefresh(i=e.\u0275\u0275loadQuery())&&(o.tabs=i.first)}}}),n})(),Xt=(()=>{class n extends B{constructor(t){super(),this.router=t,this.cartao={pontos:0},this.empresa={}}ngOnInit(){this.configurePontos()}solicitarBrinde(t,o){let i=o.plano.tipoDeAcumulo.toLowerCase(),r=String(`Gostaria de trocar meus *${i}* pelo brinde *${t.nome}*`),l=String(`https://wa.me/55${this.empresa?.whatsapp}?text=${r}`);window.open(l)}configurePontos(){}getPontosRestantes(t,o){return(t.valorEmPontos-o.pontos).toFixed(2)}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(g.F0))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["app-cartao-pontos"]],inputs:{cartao:"cartao",empresa:"empresa"},features:[e.\u0275\u0275InheritDefinitionFeature],decls:18,vars:14,consts:[[3,"routerLink"],[1,"linha"],[1,"row"],[1,"col"],[1,"pontos"],[1,"pontos-interno"],[1,"icone","estrela",3,"inlineSVG","removeSVGAttributes"],["id","tabs",1,"nav-bordered"],["tabs",""],[3,"title","id","selected"],["kendoTabContent",""],[3,"title","id"],["id","3",3,"title","id",4,"ngIf"],[1,"row","margem-fim"],[1,"minimo-troca"],[1,"container-scroll"],[1,"row","flex-row","flex-nowrap"],["class","col-sm-8 col-10",4,"ngFor","ngForOf"],[1,"col-sm-8","col-10"],[1,"brinde","linha"],[1,"caixa_brinde"],[1,"titulo"],[1,"nome_brinde_pontos",3,"ngClass"],[1,"preco_troca",3,"ngClass"],[1,"container_imagem"],[1,""],[1,"foto_brinde",3,"src"],[4,"ngIf"],[1,"botao_troca","faltam_selos"],[1,"botao_troca","pode_trocar","cpointer",3,"click"],[3,"cartao"],["id","3",3,"title","id"]],template:function(t,o){1&t&&(e.\u0275\u0275elementStart(0,"a",0)(1,"h4"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(3,"div",1)(4,"div",2)(5,"div",3)(6,"div",4)(7,"div",5),e.\u0275\u0275element(8,"div",6),e.\u0275\u0275text(9),e.\u0275\u0275pipe(10,"number"),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(11,"kendo-tabstrip",7,8)(13,"kendo-tabstrip-tab",9),e.\u0275\u0275template(14,qt,7,2,"ng-template",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(15,"kendo-tabstrip-tab",11),e.\u0275\u0275template(16,Ut,1,1,"ng-template",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(17,Jt,2,2,"kendo-tabstrip-tab",12),e.\u0275\u0275elementEnd()),2&t&&(e.\u0275\u0275propertyInterpolate("routerLink",o.cartao.link),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(null==o.cartao.plano?null:o.cartao.plano.tituloCartao),e.\u0275\u0275advance(6),e.\u0275\u0275property("inlineSVG","/assets/fidelidade/icones/star-icone.svg")("removeSVGAttributes",e.\u0275\u0275pureFunction0(13,Yt)),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(10,11,o.cartao.pontos)," "),e.\u0275\u0275advance(4),e.\u0275\u0275property("title","Brindes")("id","brindes")("selected",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("title","Extrato")("id","extrato"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",o.cartao.plano.validade||o.cartao.plano.vencimento))},dependencies:[s.NgClass,s.NgForOf,s.NgIf,g.yS,x.d$,_.tA,_.Xj,_.ZH,b,M,s.DecimalPipe],styles:[".pontos[_ngcontent-%COMP%]{background:#6DB31B;color:#fff;text-align:center;margin:15px;padding:4px;border-radius:2px 20px}.pontos-interno[_ngcontent-%COMP%]{border:white solid 1px;line-height:1.2em;font-size:40px;font-weight:600;border-radius:2px 20px}.pontos-interno[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{position:absolute;top:5px;left:63px}.icone.estrela[_ngcontent-%COMP%]{position:absolute;left:35px;bottom:25%}.icone[_ngcontent-%COMP%]{display:inline-block;fill:#fff;vertical-align:middle}.countdown-grande[_ngcontent-%COMP%]{font-size:32px;font-weight:500}.pode_trocar[_ngcontent-%COMP%]{background:#6DB31B;color:#fff}.pode_trocar[_ngcontent-%COMP%]   .icone.estrela[_ngcontent-%COMP%]{position:absolute;bottom:11px;left:12px}.botao_troca[_ngcontent-%COMP%]{margin-left:5px;margin-right:5px;margin-bottom:10px;border-radius:20px;font-size:10px;line-height:2.5em}.linha[_ngcontent-%COMP%]{border-bottom:#EFEFEF solid 1px}.brinde[_ngcontent-%COMP%]{margin-top:10px}.container_imagem[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{text-align:center;vertical-align:middle;display:table-cell}.container_imagem[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:200px;max-height:150px;margin:0 auto}.brinde[_ngcontent-%COMP%]{text-align:center;position:relative}.preco_troca[_ngcontent-%COMP%]{font-weight:600}.nome_brinde[_ngcontent-%COMP%]{display:inline-block;margin-top:5px;font-size:16px;background:#4b4b4b;color:#fff;margin-left:2px;padding:5px 10px;border-radius:50px;font-weight:200}.container_imagem[_ngcontent-%COMP%]{width:100%;height:150px;display:table;margin:5px 0}.container-scroll[_ngcontent-%COMP%]{overflow-x:scroll;overflow-y:hidden;white-space:nowrap;-webkit-overflow-scrolling:touch}.container-scroll[_ngcontent-%COMP%]   .preco_troca[_ngcontent-%COMP%]{font-weight:400;line-height:1em}.container-scroll[_ngcontent-%COMP%]   .preco_troca.atingiu[_ngcontent-%COMP%]{color:#6db31b}.container-scroll[_ngcontent-%COMP%]   .preco_troca.nao_atingiu[_ngcontent-%COMP%]{color:#f67682}.container-scroll[_ngcontent-%COMP%]   .faltam_selos[_ngcontent-%COMP%]{border:1px solid #E2E3E3}.container-scroll[_ngcontent-%COMP%]   .foto_brinde[_ngcontent-%COMP%]{display:block;float:none;margin:5px auto 0;width:150px;border-radius:30px}.container-scroll[_ngcontent-%COMP%]   .caixa_brinde[_ngcontent-%COMP%]{border:1px solid #E2E3E3;border-radius:5px;box-shadow:0 4px 10px -2px #e2e3e3;margin-bottom:10px;padding:5px}.container-scroll[_ngcontent-%COMP%]   .caixa_brinde[_ngcontent-%COMP%]   .titulo[_ngcontent-%COMP%]{word-break:break-all;display:block;white-space:normal;height:40px}.container-scroll[_ngcontent-%COMP%]   .caixa_brinde[_ngcontent-%COMP%]   .titulo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:0;overflow:hidden}.container-scroll[_ngcontent-%COMP%]   .caixa_brinde[_ngcontent-%COMP%]   .titulo[_ngcontent-%COMP%]   .nome_brinde_pontos[_ngcontent-%COMP%]{max-height:3em;margin-bottom:0}.minimo-troca[_ngcontent-%COMP%]{font-size:16px;font-weight:500;line-height:1em;padding-bottom:15px}.link-extrato[_ngcontent-%COMP%]{color:#72747b;display:block;font-size:16px;line-height:3em}@media (max-width: 520px){.font11[_ngcontent-%COMP%]{font-size:11px!important}}"]}),n})();const Zt=function(n,a,t){return{on:n,off:a,sem:t}},Kt=function(){return["fill"]};function en(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",16)(1,"div",17),e.\u0275\u0275element(2,"div",18),e.\u0275\u0275elementStart(3,"div",19)(4,"span"),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()()()),2&n){const t=a.$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction3(4,Zt,1==t.status,0==t.status,-1==t.status)),e.\u0275\u0275advance(1),e.\u0275\u0275property("inlineSVG","/assets/fidelidade/icones/icon-selo.svg")("removeSVGAttributes",e.\u0275\u0275pureFunction0(8,Kt)),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.valor)}}const tn=function(n,a){return{"mb-1":n,"mb-2":a}};function nn(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",14),e.\u0275\u0275template(1,en,6,9,"div",15),e.\u0275\u0275elementEnd()),2&n){const t=a.$implicit,o=a.index,i=e.\u0275\u0275nextContext().$implicit,r=e.\u0275\u0275nextContext();e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction2(2,tn,o<r.obtenhaQuantidadeDeLinhas(i)-1,o==r.obtenhaQuantidadeDeLinhas(i)-1)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t)}}function on(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div")(1,"div",20),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()),2&n){const t=e.\u0275\u0275nextContext().$implicit,o=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("Junte mais ",t.valorEmPontos-o.cartao.pontos==1?"1 selo":t.valorEmPontos-o.cartao.pontos+" selos","")}}function an(n,a){1&n&&(e.\u0275\u0275elementStart(0,"div")(1,"div",20),e.\u0275\u0275text(2,"Parab\xe9ns! Voc\xea j\xe1 pode fazer a troca!"),e.\u0275\u0275elementEnd()())}function rn(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div")(1,"div",6)(2,"div",7),e.\u0275\u0275template(3,nn,2,5,"div",8),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",9)(5,"span",10),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"div")(8,"div",11),e.\u0275\u0275text(9),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(10,"img",12),e.\u0275\u0275template(11,on,3,1,"div",13),e.\u0275\u0275template(12,an,3,0,"div",13),e.\u0275\u0275elementEnd()()()()),2&n){const t=a.$implicit,o=a.index,i=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",i.listaMatrizSelos[o]),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2("Atinja ",t.valorEmPontos," selos e troque por ",t.artigo,""),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.nome),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("src","/images/empresa/",t.linkImagem,"",e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.valorEmPontos>i.cartao.pontos),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.valorEmPontos<=i.cartao.pontos)}}function ln(n,a){if(1&n&&e.\u0275\u0275element(0,"app-extrato-pontos",21),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("cartao",t.cartao)}}function sn(n,a){if(1&n&&e.\u0275\u0275element(0,"app-cartao-proximos-vencimentos",21),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("cartao",t.cartao)}}function cn(n,a){1&n&&(e.\u0275\u0275elementStart(0,"kendo-tabstrip-tab",22),e.\u0275\u0275template(1,sn,1,1,"ng-template",4),e.\u0275\u0275elementEnd()),2&n&&e.\u0275\u0275property("title","Expirando")}let dn=(()=>{class n{constructor(t){this.router=t,this.cartao={pontos:0},this.listaMatrizSelos=[]}ngOnInit(){this.configureSelos()}obtenhaMatrizSelos(t,o,i){let r=Math.ceil(i/5),l=Math.ceil(i/r);this.matrizSelos=[],r>10&&(r=10);for(let c=0;c<r;c++){let p=[];for(let u=0;u<l;u++){let f=u+c*l,P={status:1,valor:t+f+1};P.status=f<o?1:f<i?0:-1,p.push(P)}if(2===p.length){let u={status:-1,valor:0};p.unshift(u),u={status:-1,valor:0},p.push(u)}this.matrizSelos.push(p)}return this.matrizSelos}configureSelos(){if(this.listaMatrizSelos=[],!this.cartao.plano.brindes.length)return;let t=this.cartao.plano.brindes[0].valorEmPontos,o=this.cartao.pontos>t?t:this.cartao.pontos,i=o===t?this.cartao.pontos-t:0,r=0;this.listaMatrizSelos.push(this.obtenhaMatrizSelos(r,o,t)),r=t;for(let l=1;l<this.cartao.plano.brindes.length;l++)t=this.cartao.plano.brindes[l].valorEmPontos-r,o=i>t?t:i,i=i>t?i-t:0,this.listaMatrizSelos.push(this.obtenhaMatrizSelos(r,o,t)),r=this.cartao.plano.brindes[l].valorEmPontos}obtenhaQuantidadeDeLinhas(t){return Math.ceil(t.valorEmPontos/5)}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(g.F0))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["app-cartao-selo"]],inputs:{cartao:"cartao"},decls:8,vars:6,consts:[[3,"routerLink"],[4,"ngFor","ngForOf"],[1,"nav-bordered","mt-2"],[3,"title","selected"],["kendoTabContent",""],[3,"title",4,"ngIf"],[1,"slide"],[1,"pontuacao"],["class","row",3,"ngClass",4,"ngFor","ngForOf"],[1,"brinde","linha"],[1,"preco_troca"],[1,"nome_brinde"],[1,"foto_brinde",3,"src"],[4,"ngIf"],[1,"row",3,"ngClass"],["class","col","style","padding-left: 0px;padding-right: 0px;",4,"ngFor","ngForOf"],[1,"col",2,"padding-left","0px","padding-right","0px"],[1,"container_selo",3,"ngClass"],[1,"icone","selo",3,"inlineSVG","removeSVGAttributes"],[1,"valor"],[1,"faltam_selos"],[3,"cartao"],[3,"title"]],template:function(t,o){1&t&&(e.\u0275\u0275elementStart(0,"a",0)(1,"h4"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(3,rn,13,7,"div",1),e.\u0275\u0275elementStart(4,"kendo-tabstrip",2)(5,"kendo-tabstrip-tab",3),e.\u0275\u0275template(6,ln,1,1,"ng-template",4),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(7,cn,2,1,"kendo-tabstrip-tab",5),e.\u0275\u0275elementEnd()),2&t&&(e.\u0275\u0275propertyInterpolate("routerLink",o.cartao.link),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(null==o.cartao.plano?null:o.cartao.plano.tituloCartao),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",null==o.cartao.plano?null:o.cartao.plano.brindes),e.\u0275\u0275advance(2),e.\u0275\u0275property("title","Extrato")("selected",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",o.cartao.plano.validade||o.cartao.plano.vencimento))},dependencies:[s.NgClass,s.NgForOf,s.NgIf,g.yS,x.d$,_.tA,_.Xj,_.ZH,b,M],styles:[".pontuacao[_ngcontent-%COMP%]{margin-top:15px;margin-bottom:15px}.on[_ngcontent-%COMP%]   .icone.selo[_ngcontent-%COMP%]{fill:#6db31b}.off[_ngcontent-%COMP%]   .icone.selo[_ngcontent-%COMP%]{fill:#c2c4cc}.sem[_ngcontent-%COMP%]   .icone.selo[_ngcontent-%COMP%]{fill:transparent}.sem[_ngcontent-%COMP%]   .valor[_ngcontent-%COMP%]{display:none}.icone.selo[_ngcontent-%COMP%]{width:100%;text-align:center}.faltam_selos[_ngcontent-%COMP%]{color:#3e48bc;margin-bottom:10px}.icone[_ngcontent-%COMP%]{display:inline-block;fill:#fff;vertical-align:middle}.botao_troca[_ngcontent-%COMP%]{margin-left:5px;margin-right:5px;margin-bottom:10px;border-radius:20px;font-size:10px;line-height:2.5em}.linha[_ngcontent-%COMP%]{border-bottom:#EFEFEF solid 1px}.brinde[_ngcontent-%COMP%]{margin-top:10px}.valor[_ngcontent-%COMP%]{position:absolute;color:#fff;font-size:20px;top:10px;width:100%;text-align:center}.container_selo[_ngcontent-%COMP%]{position:relative}.brinde[_ngcontent-%COMP%]{text-align:center;position:relative}.preco_troca[_ngcontent-%COMP%]{font-weight:600}.nome_brinde[_ngcontent-%COMP%]{display:inline-block;margin-top:5px;font-size:16px;background:#4b4b4b;color:#fff;margin-left:2px;padding:5px 10px;border-radius:50px;font-weight:200}.foto_brinde[_ngcontent-%COMP%]{display:block;float:none;margin:5px auto 0;width:150px;border-radius:30px}[_nghost-%COMP%]     .swiper-slide{height:100%;overflow:auto}[_nghost-%COMP%]     .icone.selo svg{width:50px!important;height:50px!important}"]}),n})();function pn(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",13),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"currency"),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" Utilize seus cr\xe9ditos a partir de ",e.\u0275\u0275pipeBind2(2,1,t.cartao.plano.brindes[0].valorEmPontos,"BRL")," . ")}}function mn(n,a){if(1&n&&e.\u0275\u0275element(0,"app-extrato-pontos",14),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("cartao",t.cartao)}}function gn(n,a){if(1&n&&e.\u0275\u0275element(0,"app-cartao-proximos-vencimentos",14),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("cartao",t.cartao)}}function un(n,a){1&n&&(e.\u0275\u0275elementStart(0,"kendo-tabstrip-tab",15),e.\u0275\u0275template(1,gn,1,1,"ng-template",11),e.\u0275\u0275elementEnd()),2&n&&e.\u0275\u0275property("title","Expirando")}const fn=function(){return["fill"]};let _n=(()=>{class n{constructor(t){this.router=t,this.cartao={pontos:0}}ngOnInit(){}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(g.F0))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["app-cartao-cashback"]],inputs:{cartao:"cartao"},decls:18,vars:13,consts:[[3,"routerLink"],[1,"mb-2"],[1,"row"],[1,"col"],[1,"pontos"],[1,"pontos-interno"],[1,"icone","estrela",3,"inlineSVG","removeSVGAttributes"],[1,"row","margem-fim"],["class","minimo-troca",4,"ngIf"],[1,"nav-bordered","mt-2"],[3,"title","selected"],["kendoTabContent",""],[3,"title",4,"ngIf"],[1,"minimo-troca"],[3,"cartao"],[3,"title"]],template:function(t,o){1&t&&(e.\u0275\u0275elementStart(0,"a",0)(1,"h4"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(3,"div",1)(4,"div",2)(5,"div",3)(6,"div",4)(7,"div",5),e.\u0275\u0275element(8,"div",6),e.\u0275\u0275text(9),e.\u0275\u0275pipe(10,"currency"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(11,"div",7)(12,"div",3),e.\u0275\u0275template(13,pn,3,4,"div",8),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(14,"kendo-tabstrip",9)(15,"kendo-tabstrip-tab",10),e.\u0275\u0275template(16,mn,1,1,"ng-template",11),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(17,un,2,1,"kendo-tabstrip-tab",12),e.\u0275\u0275elementEnd()),2&t&&(e.\u0275\u0275propertyInterpolate("routerLink",o.cartao.link),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("Cart\xe3o ",null==o.cartao.plano?null:o.cartao.plano.nome,""),e.\u0275\u0275advance(6),e.\u0275\u0275property("inlineSVG","/assets/fidelidade/icones/star-icone.svg")("removeSVGAttributes",e.\u0275\u0275pureFunction0(12,fn)),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind2(10,9,o.cartao.pontos,"BRL")," "),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngIf",o.cartao.plano.brindes.length&&o.cartao.plano.brindes[0].valorEmPontos),e.\u0275\u0275advance(2),e.\u0275\u0275property("title","Extrato")("selected",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",o.cartao.plano.validade||o.cartao.plano.vencimento))},dependencies:[s.NgIf,g.yS,x.d$,_.tA,_.Xj,_.ZH,b,M,s.CurrencyPipe],styles:[".pontos[_ngcontent-%COMP%]{background:#6DB31B;color:#fff;text-align:center;margin:15px;padding:4px;border-radius:2px 20px}.pontos-interno[_ngcontent-%COMP%]{border:white solid 1px;line-height:1.2em;font-size:40px;font-weight:600;border-radius:2px 20px}.pontos-interno[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{position:absolute;top:5px;left:63px}.icone.estrela[_ngcontent-%COMP%]{position:absolute;left:35px;bottom:25%}.icone[_ngcontent-%COMP%]{display:inline-block;fill:#fff;vertical-align:middle}.countdown-grande[_ngcontent-%COMP%]{font-size:32px;font-weight:500}.pode_trocar[_ngcontent-%COMP%]{background:#6DB31B;color:#fff}.pode_trocar[_ngcontent-%COMP%]   .icone.estrela[_ngcontent-%COMP%]{position:absolute;bottom:11px;left:12px}.botao_troca[_ngcontent-%COMP%]{margin-left:5px;margin-right:5px;margin-bottom:10px;border-radius:20px;font-size:10px;line-height:2.5em}.linha[_ngcontent-%COMP%]{border-bottom:#EFEFEF solid 1px}.brinde[_ngcontent-%COMP%]{margin-top:10px}.container_imagem[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{text-align:center;vertical-align:middle;display:table-cell}.container_imagem[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:200px;max-height:150px;margin:0 auto}.brinde[_ngcontent-%COMP%]{text-align:center;position:relative}.preco_troca[_ngcontent-%COMP%]{font-weight:600}.nome_brinde[_ngcontent-%COMP%]{display:inline-block;margin-top:5px;font-size:16px;background:#4b4b4b;color:#fff;margin-left:2px;padding:5px 10px;border-radius:50px;font-weight:200}.container_imagem[_ngcontent-%COMP%]{width:100%;height:150px;display:table;margin:5px 0}.container-scroll[_ngcontent-%COMP%]{overflow-x:scroll;overflow-y:hidden;white-space:nowrap;-webkit-overflow-scrolling:touch}.container-scroll[_ngcontent-%COMP%]   .preco_troca[_ngcontent-%COMP%]{font-weight:400;line-height:1em}.container-scroll[_ngcontent-%COMP%]   .preco_troca.atingiu[_ngcontent-%COMP%]{color:#6db31b}.container-scroll[_ngcontent-%COMP%]   .preco_troca.nao_atingiu[_ngcontent-%COMP%]{color:#f67682}.container-scroll[_ngcontent-%COMP%]   .faltam_selos[_ngcontent-%COMP%]{border:1px solid #E2E3E3}.container-scroll[_ngcontent-%COMP%]   .foto_brinde[_ngcontent-%COMP%]{display:block;float:none;margin:5px auto 0;width:150px;border-radius:30px}.container-scroll[_ngcontent-%COMP%]   .caixa_brinde[_ngcontent-%COMP%]{border:1px solid #E2E3E3;border-radius:5px;box-shadow:0 4px 10px -2px #e2e3e3;margin-bottom:10px;padding:5px}.container-scroll[_ngcontent-%COMP%]   .caixa_brinde[_ngcontent-%COMP%]   .titulo[_ngcontent-%COMP%]{word-break:break-all;display:block;white-space:normal;height:40px}.container-scroll[_ngcontent-%COMP%]   .caixa_brinde[_ngcontent-%COMP%]   .titulo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:0;overflow:hidden}.container-scroll[_ngcontent-%COMP%]   .caixa_brinde[_ngcontent-%COMP%]   .titulo[_ngcontent-%COMP%]   .nome_brinde_pontos[_ngcontent-%COMP%]{max-height:3em;margin-bottom:0}.minimo-troca[_ngcontent-%COMP%]{text-align:center;font-size:16px;font-weight:500;line-height:1em}@media (max-width: 520px){.font11[_ngcontent-%COMP%]{font-size:11px!important}}"]}),n})();const hn=function(n,a,t){return{on:n,off:a,sem:t}},xn=function(){return["fill"]};function vn(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",12)(1,"div",13),e.\u0275\u0275element(2,"div",14),e.\u0275\u0275elementStart(3,"div",15)(4,"span"),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()()()),2&n){const t=a.$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction3(4,hn,1==t.status,0==t.status,-1==t.status)),e.\u0275\u0275advance(1),e.\u0275\u0275property("inlineSVG","/assets/fidelidade/icones/icon-selo.svg")("removeSVGAttributes",e.\u0275\u0275pureFunction0(8,xn)),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.valor)}}const Cn=function(n,a){return{"mb-1":n,"mb-2":a}};function bn(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275template(1,vn,6,9,"div",11),e.\u0275\u0275elementEnd()),2&n){const t=a.$implicit,o=a.index,i=e.\u0275\u0275nextContext();e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction2(2,Cn,o<i.obtenhaQuantidadeDeLinhas()-1,o==i.obtenhaQuantidadeDeLinhas()-1)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t)}}function Mn(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",17)(1,"span",18),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div")(4,"div",19),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"img",20),e.\u0275\u0275elementEnd()()),2&n){const t=a.$implicit;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate2("",t.valorEmPontos," ",t.valorEmPontos<=1?"selo":"selos",""),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.nome),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("src","/images/empresa/",t.linkImagem,"",e.\u0275\u0275sanitizeUrl)}}function Pn(n,a){if(1&n&&e.\u0275\u0275template(0,Mn,7,4,"div",16),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("ngForOf",null==t.cartao.plano?null:t.cartao.plano.brindes)}}function Sn(n,a){if(1&n&&e.\u0275\u0275element(0,"app-extrato-pontos",21),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("cartao",t.cartao)}}function On(n,a){if(1&n&&e.\u0275\u0275element(0,"app-cartao-proximos-vencimentos",21),2&n){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("cartao",t.cartao)}}function En(n,a){1&n&&(e.\u0275\u0275elementStart(0,"kendo-tabstrip-tab",8),e.\u0275\u0275template(1,On,1,1,"ng-template",7),e.\u0275\u0275elementEnd()),2&n&&e.\u0275\u0275property("title","Expirando")("id","expirando")}let yn=(()=>{class n extends B{constructor(){super(),this.cartao={pontos:0},this.listaMatrizSelos=[],this.pontosNecessarios=12,this.qtdePorLinha=5}ngOnInit(){this.configureSelos()}configureSelos(){this.listaMatrizSelos=[],this.cartao.pontos>this.pontosNecessarios&&(this.pontosNecessarios=this.cartao.pontos),this.listaMatrizSelos.push(this.obtenhaMatrizSelos(0,this.cartao.pontos))}obtenhaMatrizSelos(t,o){let i=Math.ceil(this.pontosNecessarios/this.qtdePorLinha),r=Math.ceil(this.pontosNecessarios/i);this.matrizSelos=[];for(let l=0;l<i;l++){let c=[];for(let p=0;p<r;p++){let u=p+l*r,f={status:1,valor:t+u+1};f.status=u<o?1:u<this.pontosNecessarios?0:-1,c.push(f)}if(2===c.length){let p={status:-1,valor:0};c.unshift(p),p={status:-1,valor:0},c.push(p)}this.matrizSelos.push(c)}return this.matrizSelos}obtenhaQuantidadeDeLinhas(){return Math.ceil(this.pontosNecessarios/this.qtdePorLinha)}}return n.\u0275fac=function(t){return new(t||n)},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["app-cartao-consumo-selo"]],inputs:{cartao:"cartao"},features:[e.\u0275\u0275InheritDefinitionFeature],decls:13,vars:9,consts:[[3,"routerLink"],[1,"slide"],[1,"pontuacao"],["class","row",3,"ngClass",4,"ngFor","ngForOf"],[1,"nav-bordered","mt-2"],["tabs",""],[3,"title","id","selected"],["kendoTabContent",""],[3,"title","id"],[3,"title","id",4,"ngIf"],[1,"row",3,"ngClass"],["class","col","style","padding-left: 0px;padding-right: 0px;",4,"ngFor","ngForOf"],[1,"col",2,"padding-left","0px","padding-right","0px"],[1,"container_selo",3,"ngClass"],[1,"icone","selo",3,"inlineSVG","removeSVGAttributes"],[1,"valor"],["class","brinde linha",4,"ngFor","ngForOf"],[1,"brinde","linha"],[1,"preco_troca"],[1,"nome_brinde"],[1,"foto_brinde",3,"src"],[3,"cartao"]],template:function(t,o){1&t&&(e.\u0275\u0275elementStart(0,"a",0)(1,"h4"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(3,"div",1)(4,"div",2),e.\u0275\u0275template(5,bn,2,5,"div",3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"kendo-tabstrip",4,5)(8,"kendo-tabstrip-tab",6),e.\u0275\u0275template(9,Pn,1,1,"ng-template",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"kendo-tabstrip-tab",8),e.\u0275\u0275template(11,Sn,1,1,"ng-template",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(12,En,2,2,"kendo-tabstrip-tab",9),e.\u0275\u0275elementEnd()()),2&t&&(e.\u0275\u0275propertyInterpolate("routerLink",o.cartao.link),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(null==o.cartao.plano?null:o.cartao.plano.tituloCartao),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",o.listaMatrizSelos[0]),e.\u0275\u0275advance(3),e.\u0275\u0275property("title","Brindes")("id","brindes")("selected",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("title","Extrato")("id","extrato"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!0))},dependencies:[s.NgClass,s.NgForOf,s.NgIf,g.yS,x.d$,_.tA,_.Xj,_.ZH,b,M],styles:[".pontuacao[_ngcontent-%COMP%]{margin-top:15px;margin-bottom:15px}.on[_ngcontent-%COMP%]   .icone.selo[_ngcontent-%COMP%]{fill:#6db31b}.off[_ngcontent-%COMP%]   .icone.selo[_ngcontent-%COMP%]{fill:#c2c4cc}.sem[_ngcontent-%COMP%]   .icone.selo[_ngcontent-%COMP%]{fill:transparent}.sem[_ngcontent-%COMP%]   .valor[_ngcontent-%COMP%]{display:none}.icone.selo[_ngcontent-%COMP%]{width:100%;text-align:center}.faltam_selos[_ngcontent-%COMP%]{color:#3e48bc;margin-bottom:10px}.icone[_ngcontent-%COMP%]{display:inline-block;fill:#fff;vertical-align:middle}.botao_troca[_ngcontent-%COMP%]{margin-left:5px;margin-right:5px;margin-bottom:10px;border-radius:20px;font-size:10px;line-height:2.5em}.linha[_ngcontent-%COMP%]{border-bottom:#EFEFEF solid 1px}.brinde[_ngcontent-%COMP%]{margin-top:10px}.valor[_ngcontent-%COMP%]{position:absolute;color:#fff;font-size:20px;top:10px;width:100%;text-align:center}.container_selo[_ngcontent-%COMP%]{position:relative}.brinde[_ngcontent-%COMP%]{text-align:center;position:relative}.preco_troca[_ngcontent-%COMP%]{font-weight:600}.nome_brinde[_ngcontent-%COMP%]{display:inline-block;margin-top:5px;font-size:16px;background:#4b4b4b;color:#fff;margin-left:2px;padding:5px 10px;border-radius:50px;font-weight:200}.foto_brinde[_ngcontent-%COMP%]{display:block;float:none;margin:5px auto 0;width:150px;border-radius:30px}[_nghost-%COMP%]     .swiper-slide{height:100%;overflow:auto}[_nghost-%COMP%]     .icone.selo svg{width:50px!important;height:50px!important}",".brinde.linha[_ngcontent-%COMP%]{padding-bottom:10px}"]}),n})();var wn=d(62609),H=d(45807),In=d(47098);let kn=(()=>{class n{}return n.\u0275fac=function(t){return new(t||n)},n.\u0275mod=e.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=e.\u0275\u0275defineInjector({imports:[s.CommonModule,k.j5,ut,x.vi,m.u5,xe,$.g9,m.UX,S.yI,ft.FidelidadeModule,L.I1,L.Ln,wn.x,H.I$,_.Du]}),n})();e.\u0275\u0275setComponentScope(v,[s.NgClass,s.NgForOf,s.NgIf,g.yS,x.d$,m._Y,m.Fj,m._,m.JJ,m.JL,m.Q7,m.On,m.F,D,N.U,H.Lr,It,dn,Xt,_n,yn],[S.Iq,In.X])}}]);
//# sourceMappingURL=839.571ccbf6b7d5cd12.js.map