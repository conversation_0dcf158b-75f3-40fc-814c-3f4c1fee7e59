{"version": 3, "file": "630.76845b496d001349.js", "mappings": "wMA0CA,IA8KMA,GAAe,MAArB,MAAMA,GAENA,SAAgBC,UAAO,SAAiCC,GAAK,OAAO,IAAKA,GAAKF,EAAoB,EAClGA,EAAgBG,UAAqBC,6BAAwB,CAAEC,KAAML,IACrEA,EAAgBM,UAAqBF,6BAAwB,CAAEG,QAAS,CAACC,KACjEC,KAAiBA,QALnBT,CAAe,iJCuarB,IAkUMU,GAAa,MAAnB,MAAMA,GAENA,SAAcT,UAAO,SAA+BC,GAAK,OAAO,IAAKA,GAAKQ,EAAkB,EAC5FA,EAAcP,UAAqBC,6BAAwB,CAAEC,KAAMK,IACnEA,EAAcJ,UAAqBF,6BAAwB,CAAEG,QAAS,CAACE,KAAiBA,QAJlFC,CAAa,oBC36BnB,SAASC,GAA0CC,EAAIC,GAGrD,GAHqE,EAALD,IAC9DR,6BACAA,sBAAiB,EAAG,SAAU,IACvB,EAALQ,EAAQ,CACV,MAAME,EAASV,4BACfA,wBAAmB,iBAAkB,sCAAwCU,EAAOC,uBAApFX,CAA4G,oBAAqBU,EAAOE,uBAAwB,KAAhKZ,CAAsK,mBAAoBU,EAAOG,0BAA2B,KAA5Nb,CAAkO,eAAgBU,EAAOI,wBAAyB,KAClRd,wBAAmB,IAAKU,EAAOK,mBACnC,CAAE,CACF,SAASC,GAA0CR,EAAIC,GAGrD,GAHqE,EAALD,IAC9DR,6BACAA,sBAAiB,EAAG,SAAU,IACvB,EAALQ,EAAQ,CACV,MAAMS,EAASjB,4BACfA,wBAAmB,oBAAqBiB,EAAOL,uBAAwB,KAAvEZ,CAA6E,mBAAoBiB,EAAOJ,0BAA2B,KAAnIb,CAAyI,eAAgBiB,EAAOH,wBAAyB,KACzLd,wBAAmB,IAAKiB,EAAOF,mBACnC,CAAE,CA0BF,MAAMG,GACFC,YAAYC,GACRC,KAAKD,YAAcA,CACvB,EAEJ,MAAME,IAA+BC,QAAWL,GAAwB,WAElEM,EAAuC,IAAIC,iBAAe,uCAAwC,CACpGC,WAAY,OACZC,QAGJ,SAASC,KACL,MAAO,CAAEC,SArBK,IAsBlB,IA+BA,MAAMC,UAA2BR,GAC7BH,YAAYC,EAAaW,EAAUC,EAAWC,EAAeC,GACzDC,MAAMf,GACNC,KAAKD,YAAcA,EACnBC,KAAKW,UAAYA,EACjBX,KAAKe,UA1DK,IA2DVf,KAAKgB,OAAS,EACdhB,KAAKiB,oBAAqB,EAE1BjB,KAAKkB,KAAO,cACZ,MAAMC,EAAmBV,EAAmBW,WAC5CpB,KAAKV,uBAAyBU,KAAKqB,4BAG9BF,EAAiBG,IAAIX,EAAUY,OAChCJ,EAAiBK,IAAIb,EAAUY,KAAM,IAAIE,IAAI,CApEvC,OAsEVzB,KAAKiB,mBAAqBP,EAASgB,MAAQhB,EAASiB,QACpD3B,KAAK4B,gBAAoC,mBAAlBhB,KAChBC,IAAaA,EAASgB,iBACzBhB,IACIA,EAASL,WACTR,KAAKQ,SAAWK,EAASL,UAEzBK,EAASiB,cACT9B,KAAK8B,YAAcjB,EAASiB,aAGxC,CAEItB,eAAa,OAAOR,KAAKe,SAAW,CACpCP,aAASuB,GACT/B,KAAKe,aAAYiB,MAAqBD,GACtC/B,KAAKV,uBAAyBU,KAAKqB,6BAE9BrB,KAAKiB,oBAAsBjB,KAAKiC,YACjCjC,KAAKkC,kBAEb,CAEIJ,kBACA,OAAO9B,KAAKmC,cAAgBnC,KAAKQ,SAAW,EAChD,CACIsB,gBAAYM,GACZpC,KAAKmC,gBAAeH,MAAqBI,EAC7C,CAEIA,YACA,MAAqB,gBAAdpC,KAAKkB,KAAyBlB,KAAKgB,OAAS,CACvD,CACIoB,UAAMC,GACNrC,KAAKgB,OAASsB,KAAKC,IAAI,EAAGD,KAAKE,IAAI,OAAKR,MAAqBK,IACjE,CACAI,WACI,MAAMC,EAAU1C,KAAKD,YAAY4C,cAIjC3C,KAAKiC,cAAaW,MAAeF,IAAY1C,KAAKW,UAAUY,KAC5DvB,KAAKkC,mBAILQ,EAAQG,UAAUC,IADK,qCAAqC9C,KAAKiB,mBAAqB,YAAc,eAExG,CAEAvB,mBACI,OAAQM,KAAKQ,SAnHK,IAmH2B,CACjD,CAEAuC,cACI,MAAMC,EAAoC,EAA1BhD,KAAKN,mBAAyBM,KAAK8B,YACnD,MAAO,OAAOkB,KAAWA,GAC7B,CAEAxD,0BACI,OAAO,EAAI8C,KAAKW,GAAKjD,KAAKN,kBAC9B,CAEAH,uBACI,MAAkB,gBAAdS,KAAKkB,KACElB,KAAKR,2BAA6B,IAAMQ,KAAKgB,QAAU,IAG9DhB,KAAKiB,oBAAoC,kBAAdjB,KAAKkB,KACQ,GAAjClB,KAAKR,0BAET,IACX,CAEAC,wBACI,OAAOO,KAAK8B,YAAc9B,KAAKQ,SAAW,GAC9C,CAEA0B,mBACI,MAAMgB,EAAYlD,KAAKiC,WACjBkB,EAAkBnD,KAAKe,UACvBqC,EAAY3C,EAAmBW,WACrC,IAAIiC,EAAsBD,EAAUE,IAAIJ,GACxC,IAAKG,IAAwBA,EAAoB/B,IAAI6B,GAAkB,CACnE,MAAMI,EAAWvD,KAAKW,UAAU6C,cAAc,SAC9CD,EAASE,aAAa,wBAAyBzD,KAAKV,wBACpDiE,EAASG,YAAc1D,KAAK2D,oBAC5BT,EAAUU,YAAYL,GACjBF,IACDA,EAAsB,IAAI5B,IAC1B2B,EAAU5B,IAAI0B,EAAWG,IAE7BA,EAAoBP,IAAIK,EAC5B,CACJ,CAEAQ,oBACI,MAAME,EAAsB7D,KAAKR,0BACjC,MA5IiC,46CA8I5BsE,QAAQ,eAAgB,GAAG,IAAOD,GAClCC,QAAQ,aAAc,GAAG,GAAMD,GAC/BC,QAAQ,YAAa,GAAG9D,KAAKV,yBACtC,CAEA+B,4BAGI,OAAOrB,KAAKQ,SAASuD,WAAWD,QAAQ,IAAK,IACjD,EAEJrD,EAAmBjC,UAAO,SAAoCC,GAAK,OAAO,IAAKA,GAAKgC,GAAoB9B,8BAAyBA,cAAoBA,8BAAyBqF,MAAkBrF,8BAAyBsF,WAAU,GAAItF,8BAAyBuF,wBAAuB,GAAIvF,8BAAyBwB,GAAwC,EAC5VM,EAAmB0D,UAAqBxF,8BAAyB,CAAEC,KAAM6B,EAAoB2D,UAAW,CAAC,CAAC,yBAA0BC,UAAW,CAAC,OAAQ,cAAe,WAAY,KAAM,EAAG,wBAAyBC,SAAU,GAAIC,aAAc,SAAyCpF,EAAIC,GAAgB,EAALD,IACjSR,wBAAmB,gBAA8B,gBAAbS,EAAI8B,KAAyB,EAAI,KAArEvC,CAA2E,gBAA8B,gBAAbS,EAAI8B,KAAyB,IAAM,KAA/HvC,CAAqI,gBAA8B,gBAAbS,EAAI8B,KAAyB9B,EAAIgD,MAAQ,KAA/LzD,CAAqM,OAAQS,EAAI8B,MACjNvC,wBAAmB,QAASS,EAAIoB,SAAU,KAA1C7B,CAAgD,SAAUS,EAAIoB,SAAU,MACxE7B,wBAAmB,0BAA2BS,EAAIwC,iBACpD,EAAG4C,OAAQ,CAAEC,MAAO,QAASvD,KAAM,OAAQV,SAAU,WAAYsB,YAAa,cAAeM,MAAO,SAAWsC,SAAU,CAAC,sBAAuBC,SAAU,CAAChG,wCAAoCiG,MAAO,EAAGC,KAAM,EAAGC,OAAQ,CAAC,CAAC,sBAAuB,gBAAiB,YAAa,QAAS,cAAe,OAAQ,EAAG,YAAa,CAAC,KAAM,MAAO,KAAM,MAAO,EAAG,iBAAkB,oBAAqB,mBAAoB,eAAgB,EAAG,gBAAiB,CAAC,KAAM,MAAO,KAAM,MAAO,EAAG,oBAAqB,mBAAoB,eAAgB,EAAG,gBAAiB,CAAC,KAAM,MAAO,KAAM,QAASC,SAAU,SAAqC5F,EAAIC,GAAgB,EAALD,IACnoBR,6BACAA,2BAAsB,EAAG,MAAO,GAChCA,uBAAkB,EAAGO,GAA2C,EAAG,EAAG,SAAU,GAChFP,uBAAkB,EAAGgB,GAA2C,EAAG,EAAG,SAAU,GAChFhB,4BACO,EAALQ,IACFR,wBAAmB,QAASS,EAAIoB,SAAU,KAA1C7B,CAAgD,SAAUS,EAAIoB,SAAU,MACxE7B,uBAAkB,WAAyB,kBAAbS,EAAI8B,MAClCvC,wBAAmB,UAAWS,EAAI2D,eAClCpE,sBAAiB,GACjBA,uBAAkB,gBAAgB,GAClCA,sBAAiB,GACjBA,uBAAkB,gBAAgB,GACpC,EAAGqG,aAAc,CAACC,WAAiBA,gBAAsBC,OAAQ,CAxM3D,+wGAwMkEC,cAAe,EAAGC,gBAAiB,IAOjH3E,EAAmBW,WAAa,IAAIiE,QAoDpC,IAsEMC,GAAwB,MAA9B,MAAMA,GAENA,SAAyB9G,UAAO,SAA0CC,GAAK,OAAO,IAAKA,GAAK6G,EAA6B,EAC7HA,EAAyB5G,UAAqBC,6BAAwB,CAAEC,KAAM0G,IAC9EA,EAAyBzG,UAAqBF,6BAAwB,CAAEG,QAAS,CAACE,KAAiBuG,eAAcvG,QAJ3GsG,CAAwB,kGC3T9B,MAMME,GAA+C,CACjDC,QAPgC,IAAIrF,iBAAe,+BAQnDsF,KAAM,CAACC,MACPC,WAPJ,SAASC,GAAoCC,GACzC,MAAO,IAAMA,EAAQC,iBAAiBC,WAAW,CAAEC,eArB5B,IAsB3B,GAoBA,IA6oBMC,GAAgB,MAAtB,MAAMA,GAENA,SAAiB1H,UAAO,SAAkCC,GAAK,OAAO,IAAKA,GAAKyH,EAAqB,EACrGA,EAAiBxH,UAAqBC,6BAAwB,CAAEC,KAAMsH,IACtEA,EAAiBrH,UAAqBF,6BAAwB,CAAEwH,UAAW,CAACX,IAA+C1G,QAAS,CAACsH,KAC7Hb,eACAc,KACArH,KAAiBA,KAAiBsH,QAPpCJ,CAAgB,kEClrBf,IAAMK,GAAgB,MAAvB,MAAOA,EASXzG,cAPQE,6BAA0B,IAAIwG,KAAkC,CACtEC,UAAW,KAINzG,uBAAkDA,KAAK0G,wBAAwBC,eAIpF3G,KAAK4G,yBACP,CAKQA,0BAENC,OAAOC,iBAAiB,UAAYC,IAQlC,GANIA,EAAMC,MAA4B,4BAApBD,EAAMC,KAAKpI,OAC3BqI,QAAQC,IAAI,uDAAkDH,EAAMC,KAAKG,SACzEnH,KAAKoH,0BAA0BL,EAAMC,KAAKG,UAIxCJ,EAAMC,MAA4B,uBAApBD,EAAMC,KAAKK,KAA+B,CAC1DJ,QAAQC,IAAI,mCAAoCH,EAAMC,KAAKG,SAE3D,MAAMG,EAAgBtH,KAAK0G,wBAAwBa,WAC7CC,EAAe,IAChBF,EACHG,aAAcV,EAAMC,KAAKG,QAAQO,MAAQJ,EAAcG,aACvDE,cAAeZ,EAAMC,KAAKG,QAAQS,UAAYN,EAAcK,eAG9D3H,KAAK0G,wBAAwBmB,KAAKL,KAGxC,CAKQJ,0BAA0BD,GAChC,MAAMG,EAAgBtH,KAAK0G,wBAAwBa,WAWnDvH,KAAK0G,wBAAwBmB,KARU,CACrCpB,UAAWU,EAAQV,WAAaa,EAAcb,UAC9CgB,aAAcN,EAAQM,cAAgBH,EAAcG,aACpDE,cAAeR,EAAQQ,eAAiBL,EAAcK,cACtDG,WAAYX,EAAQW,YAAcR,EAAcQ,YAKpD,CAKOC,oBAAoBC,GACzBhI,KAAK0G,wBAAwBmB,KAAKG,EACpC,CAKOC,kBAAkBC,GACvB,MAAMZ,EAAgBtH,KAAK0G,wBAAwBa,WAC7CY,EAAiB,IAAIb,EAAcb,UAAWyB,GAEpDlI,KAAK0G,wBAAwBmB,KAAK,IAC7BP,EACHb,UAAW0B,GAEf,CAKOC,kBACL,MAAMd,EAAgBtH,KAAK0G,wBAAwBa,WAEnDvH,KAAK0G,wBAAwBmB,KAAK,IAC7BP,EACHb,UAAW,IAEf,CAKO4B,mBACL,OAAOrI,KAAK0G,wBAAwBa,UACtC,+CAjGWhB,EAAgB,qDAAhBA,EAAgBjG,QAAhBiG,EAAgB/H,qBAFf,SAED+H,CAAgB,qBClDtB,IAAM+B,EAAY,MAAnB,MAAOA,UAAoBC,KAC/BzI,YAAsB0I,GACpB1H,MAAM0H,GADcxI,YAIdA,cAAW,YAFnB,CAIAyI,MAAMC,EAAc,IAClB,OAAO1I,KAAK2I,QAAQ3I,KAAK4I,SAAUF,EACrC,CAEAG,UAAUC,GACR,OAAO9I,KAAK2I,QAAQ,GAAG3I,KAAK4I,YAAYE,IAAM,GAChD,CAEAC,UAAUC,GACR,OAAIA,EAAKF,GAEA9I,KAAKiJ,QAAQ,GAAGjJ,KAAK4I,YAAYI,EAAKF,KAAME,GAG5ChJ,KAAKkJ,SAASlJ,KAAK4I,SAAUI,EAExC,CAEAG,WAAWL,GACT,OAAO9I,KAAKoJ,OAAO,GAAGpJ,KAAK4I,YAAYE,IAAM,GAC/C,CAEAO,qBAAqBC,GAEnB,OAAOtJ,KAAK2I,QAAQ,GAAG3I,KAAK4I,mBADR,CAAEU,YAExB,CAEAC,qBAAqBC,EAAYC,EAAuBH,GAMtD,OAAOtJ,KAAKkJ,SAAS,GAAGlJ,KAAK4I,oBALb,CACdY,QACAF,WACAG,aAAcA,GAAgB,MAGlC,CAEAC,gBAAgBC,GAEd,OAAO3J,KAAK2I,QAAQ,GAAG3I,KAAK4I,4BADb,CAAEe,OAEnB,CAEAC,iBAAiBC,GAEf,OAAO7J,KAAKkJ,SAAS,GAAGlJ,KAAK4I,6BADb,CAAEiB,SAEpB,CAEAC,cAAcC,EAAqBC,GAEjC,OAAOhK,KAAK2I,QAAQ,GAAG3I,KAAK4I,0BADb,CAAEmB,cAAaC,UAEhC,CAEAC,qBAAqBC,GAEnB,OAAOlK,KAAKkJ,SAAS,GAAGlJ,KAAK4I,kCADb,CAAEsB,QAEpB,CAIAC,YAAYC,GACV,OAAOpK,KAAK2I,QAAQ,GAAG3I,KAAK4I,YAAYwB,UAAgB,GAC1D,CAEAC,cAAcD,EAAgBE,GAC5B,OAAOtK,KAAKkJ,SAAS,GAAGlJ,KAAK4I,YAAYwB,UAAgBE,EAC3D,CAEAC,cAAcH,EAAgBI,EAAgBF,GAC5C,OAAOtK,KAAKiJ,QAAQ,GAAGjJ,KAAK4I,YAAYwB,WAAgBI,IAAUF,EACpE,CAEAG,YAAYL,EAAgBI,GAC1B,OAAOxK,KAAKoJ,OAAO,GAAGpJ,KAAK4I,YAAYwB,WAAgBI,IAAU,GACnE,CAEAE,mBAAmBN,EAAgB/C,GACjC,OAAOrH,KAAKoJ,OAAO,GAAGpJ,KAAK4I,YAAYwB,gBAAqB/C,IAAQ,GACtE,+CAnFWiB,GAAWqC,gFAAXrC,EAAWhI,QAAXgI,EAAW9J,YAAX8J,CAAY,+BCgBvBqC,uCAAsD,WAAtDA,CAAsD,YAGhDA,kCACFA,2BACAA,sCAA0BA,iDAAwBA,2BAClDA,qCAAwBA,sBAA4CA,yEAA5CA,yIAK5BA,uCAAsF,WAAtFA,CAAsF,YAGhFA,gCACFA,2BACAA,sCAA0BA,+CAAmBA,2BAC7CA,qCAAwBA,sBAAwEA,yEAAxEA,qKAmCpBA,qCAKEA,gCACAA,qCAAMA,iCAAQA,wEAJbA,4JAMHA,qCAIEA,gCACAA,qCAAMA,8BAAKA,wEAHVA,mJAKHA,qCAIEA,gCACAA,qCAAMA,8BAAKA,wEAHVA,mJAkBHA,uCAAwD,YAEpDA,gCACFA,2BACAA,uCAA0B,aACCA,iCAAQA,2BACjCA,wCAAyBA,sBAA8BA,0EAA9BA,yIAI7BA,uCAAqD,YAEjDA,gCACFA,2BACAA,uCAA0B,aACCA,8BAAKA,2BAC9BA,wCAAyBA,sBAA2BA,0EAA3BA,sIAI7BA,uCAAyD,YAErDA,gCACFA,2BACAA,uCAA0B,aACCA,kCAASA,2BAClCA,qCAE2BA,sBAA+BA,0EAFvDA,4JAEwBA,0IAa/BA,uCAAwD,YAEpDA,gCACFA,2BACAA,uCAA0B,aACCA,iCAAQA,2BACjCA,wCAAyBA,sBAA8BA,0EAA9BA,yIAI7BA,uCAA0D,YAEtDA,gCACFA,2BACAA,uCAA0B,aACCA,+BAAMA,2BAC/BA,wCAAyBA,sBAAgCA,0EAAhCA,2IAI7BA,uCAA8D,YAE1DA,+BACFA,2BACAA,uCAA0B,aACCA,8BAAKA,2BAC9BA,wCAAyBA,sBAAoCA,0EAApCA,+IAI7BA,uCAA2D,YAEvDA,gCACFA,2BACAA,uCAA0B,aACCA,0CAAWA,2BACpCA,wCAAyBA,sBAAiCA,0EAAjCA,4IAc/BA,uCAAuE,YAEnEA,gCACFA,2BACAA,uCAA8B,aACCA,yCAAgBA,2BAC7CA,wCAA6BA,sBAAyCA,0EAAzCA,oJAIjCA,uCAAmE,YAE/DA,gCACFA,2BACAA,uCAA8B,aACCA,kDAAgBA,2BAC7CA,wCAA6BA,sBAAqCA,0EAArCA,gJAIjCA,uCAA4E,YAExEA,gCACFA,2BACAA,uCAA8B,aACCA,6CAAiBA,2BAC9CA,wCAAuCA,sBAAqCA,0EAArCA,gJAhC/CA,uCAA6I,YAEzIA,gCACAA,mCAAIA,iCAAQA,6BAEdA,uCACEA,0CAUAA,0CAUAA,0CASFA,wEA7B8BA,yHAUAA,qHAUSA,gJAmBrCA,qCAIoD,YAEhDA,gCACFA,2BACAA,uCAA0B,aACAA,sBAAeA,2BACvCA,wCAA+BA,sBAAgCA,6BAEjEA,uCACEA,gCACFA,sFAbCA,yEAAyB,sCAIHA,iFAClBA,iFAGqBA,+DACOA,sGAhBvCA,uCAA0F,YAEtFA,gCACAA,mCAAIA,0CAAiBA,6BAEvBA,uCACEA,yCAgBFA,wEAhBsBA,yIAoBxBA,uCAAuD,SAAvDA,CAAuD,YAKjDA,gCACFA,2BACAA,uCAA6B,cACCA,gCAAOA,2BACnCA,yCAA0BA,sBAA0BA,6BAEtDA,wCACEA,iCACFA,0EAZCA,qJAQ2BA,qIAe5BA,wCAA8D,aAE1DA,iCACAA,qCAAMA,oDAAqBA,6BAE7BA,sCAAqBA,sBAAiCA,wEAAjCA,4IAGvBA,wCAAqE,aAEjEA,iCACAA,qCAAMA,kDAAsBA,6BAE9BA,sCAAqBA,sBAAwCA,wEAAxCA,mJAnB3BA,wCAAkG,YAE9FA,iCACAA,mCAAIA,0CAAWA,6BAEjBA,wCACEA,2CAQAA,2CAOFA,wEAf6BA,iHAQAA,mJAiB3BA,yCACEA,gCACAA,sBACFA,qDADEA,8FARNA,wCAAwH,YAEpHA,gCACAA,mCAAIA,mCAAUA,6BAEhBA,wCACEA,4CAIFA,wEAJmDA,yMAxQvDA,uCAA4F,WAA5FA,CAA4F,WAA5FA,CAA4F,WAA5FA,CAA4F,WAA5FA,CAA4F,WAQzDA,sBAAwDA,2BACjFA,uCACEA,gCACAA,qCAAMA,uBAAqDA,+BAK/DA,wCAA2B,aAEvBA,iCACAA,sCAAMA,uBAA8CA,6BAEtDA,wCACEA,iCACAA,sCAAMA,uBAAgDA,iCAM5DA,wCACEA,yCAQAA,yCAOAA,yCAOFA,+BAKJA,wCAAuB,YAAvBA,CAAuB,aAIjBA,iCACAA,oCAAIA,iCAAOA,6BAEbA,wCACEA,2CAUAA,2CAUAA,2CAWFA,6BAIFA,wCAAuB,aAEnBA,iCACAA,oCAAIA,oCAAOA,6BAEbA,wCACEA,2CAUAA,2CAUAA,2CAUAA,2CASFA,+BAKJA,2CAuCAA,2CA0BAA,4CAkBAA,2CAyBAA,2CAaFA,qEAvQmCA,2IAGjBA,wIAMiBA,sKAEjBA,gIAEiBA,+LAEjBA,gIAQNA,8GAQAA,8GAOAA,2GAmBoBA,8GAUAA,2GAUAA,+GAqBAA,8GAUAA,gHAUAA,oHAUAA,iHAcFA,2PAuCHA,4KA0BEA,0GAkBFA,sLAyBIA,uMCnS1B,IAAMC,GAAgB,MAAvB,MAAOA,EAcX9K,YACU+K,EACAC,EACAC,EACAC,GAHAhL,wBACAA,aACAA,cACAA,mBAhBVA,gBAAa,YAMbA,cAA0B,KAG1BA,qBAAiB,EACjBA,qBAAiB,CAOd,CAEHyC,WAEEzC,KAAK8K,MAAMG,SAASC,UAAUxC,IAC5B1I,KAAKsJ,SAAWZ,EAAOpF,IAAI,YAC3B2D,QAAQC,IAAI,8BAA+BlH,KAAKsJ,UAE5CtJ,KAAKsJ,SACPtJ,KAAKmL,sBAAsBnL,KAAKsJ,UAEhCtJ,KAAKoL,8BAIPC,WAAW,IAAMrL,KAAKsL,cAAe,IAAG,GAI1CtL,KAAK6K,iBAAiBU,kBAAkBL,UAAUlD,IAC5CA,GAAYA,EAASwD,YACvBxL,KAAKyL,eAAiBzD,EAASwD,YAIrC,CAKAE,wBACE,MAAMC,GAAY,IAAIC,MAAOC,qBAIvBC,EAAW,CAEf,CACErF,UAAW,CACT,CACE+C,MAAO,uEACPuC,UAAW,UACXC,QAASL,EACTtE,KAAM,WAER,CACEmC,MAAO,qFACPuC,UAAW,KACXC,QAASL,EACTtE,KAAM,SAER,CACEmC,MAAO,yEACPuC,UAAW,UACXC,QAASL,EACTtE,KAAM,YAGV4E,QAAS,gBACTrE,SAAU,iBACVsE,MAAO,sBAIT,CACEzF,UAAW,CACT,CACE+C,MAAO,yFACPuC,UAAW,UACXC,QAASL,EACTtE,KAAM,WAER,CACEmC,MAAO,kGACPuC,UAAW,KACXC,QAASL,EACTtE,KAAM,SAER,CACEmC,MAAO,wEACPuC,UAAW,UACXC,QAASL,EACTtE,KAAM,YAGV4E,QAAS,iBACTrE,SAAU,iBACVsE,MAAO,iBAIT,CACEzF,UAAW,CACT,CACE+C,MAAO,kFACPuC,UAAW,UACXC,QAASL,EACTtE,KAAM,WAER,CACEmC,MAAO,kFACPuC,UAAW,KACXC,QAASL,EACTtE,KAAM,SAER,CACEmC,MAAO,4DACPuC,UAAW,UACXC,QAASL,EACTtE,KAAM,YAGV4E,QAAS,iBACTrE,SAAU,iBACVsE,MAAO,eAKLC,EAAeL,EAASxJ,KAAK8J,MAAM9J,KAAK+J,SAAWP,EAASQ,SAG5DC,EAAqBvM,KAAKwM,wBAAwBL,EAAaF,QAASE,EAAaD,OAG3FlM,KAAK6K,iBAAiB9C,oBAAoB,CACxCtB,UAAW0F,EAAa1F,UACxBgB,aAAc0E,EAAaF,QAC3BtE,cAAewE,EAAavE,SAC5BE,WAAYqE,EAAaD,MACzBV,UAAWe,IAIbvM,KAAKyL,eAAiBc,EAGtBvM,KAAKyM,WAAaN,EAAaD,KACjC,CAKQM,wBAAwB9E,EAAcwE,GA0F5C,MAAO,IAxFQ,CACb,gBAAc,CACZQ,MAAO,4BACPC,MAAO,6BACPC,QAAS,wBACTC,SAAU,oBACVC,eAAgB,UAChBC,YAAa,mBACbC,UAAW,wBACXC,KAAM,yBACNC,oBAAqB,aACrBC,iBAAiB,IAAIvB,MAAOwB,qBAC5BC,WAAY,OACZC,UAAW,GACXC,mBAAoB,CAAC,sBAAoB,wBACzCC,gBAAiBxN,KAAKyN,gBAAgB,GACtCC,mBAAoB,yCACpBC,YAAa,yDACb9D,MAAO,CACL,CAAExC,KAAM,QAASsC,IAAK,uEAAwEiE,UAAW,uBAAqBC,MAAO,GACrI,CAAExG,KAAM,YAAasC,IAAK,6CAA8CiE,UAAW,sBAAuBC,MAAO,GACjH,CAAExG,KAAM,oBAAesC,IAAK,yCAAuCiE,UAAW,0BAAwBC,MAAO,KAGjH,iBAAkB,CAChBnB,MAAO,6BACPC,MAAO,kBACPC,QAAS,yBACTC,SAAU,oBACVC,eAAgB,UAChBC,YAAa,qBACbC,UAAW,yBACXc,SAAU,gCACVb,KAAM,uBACNC,oBAAqB,aACrBC,iBAAiB,IAAIvB,MAAOwB,qBAC5BC,WAAY,YACZC,UAAW,GACXC,mBAAoB,CAAC,sBAAuB,qBAC5CC,gBAAiBxN,KAAKyN,gBAAgB,GACtCE,YAAa,oCACb9D,MAAO,CACL,CAAExC,KAAM,OAAQsC,IAAK,+BAAgCiE,UAAW,kBAAmBC,MAAO,GAC1F,CAAExG,KAAM,WAAYsC,IAAK,cAAeiE,UAAW,wBAAyBC,MAAO,GACnF,CAAExG,KAAM,YAAasC,IAAK,8CAA+CiE,UAAW,2BAA4BC,MAAO,KAG3H,iBAAkB,CAChBnB,MAAO,uCACPC,MAAO,UACPC,QAAS,qBACTC,SAAU,oBACVC,eAAgB,WAChBC,YAAa,qBACbC,UAAW,qBACXc,SAAU,gCACVb,KAAM,+BACNC,oBAAqB,aACrBC,iBAAiB,IAAIvB,MAAOwB,qBAC5BC,WAAY,kBACZC,UAAW,GACXC,mBAAoB,CAAC,mBAAoB,wBACzCC,gBAAiBxN,KAAKyN,gBAAgB,GACtCC,mBAAoB,6EACpBC,YAAa,uEACb9D,MAAO,CACL,CAAExC,KAAM,OAAQsC,IAAK,uCAAwCiE,UAAW,yBAA0BC,MAAO,GACzG,CAAExG,KAAM,QAASsC,IAAK,yEAA0EiE,UAAW,oBAAqBC,MAAO,GACvI,CAAExG,KAAM,WAAYsC,IAAK,kDAAmDiE,UAAW,sBAAuBC,MAAO,GACrH,CAAExG,KAAM,cAAesC,IAAK,qCAAsCiE,UAAW,uBAAwBC,MAAO,MAM5FnG,IAAS,CAC7BgF,MAAO,GAAGhF,EAAKqG,cAAcjK,QAAQ,IAAK,iBAC1C6I,MAAO,kBACPC,QAAS,kBACTC,SAAU,oBACVC,eAAgB,UAChBI,qBAAqB,IAAItB,MAAOwB,qBAChCD,iBAAiB,IAAIvB,MAAOwB,qBAC5BC,WAAY,WACZC,UAAW,IAMX5F,KAAMA,EACNE,SAAU5H,KAAKgO,yBACflG,WAAYoE,EAEhB,CAKQuB,gBAAgBQ,GACtB,MAAMjH,EAAO,IAAI4E,KACjB5E,SAAKkH,QAAQlH,EAAKmH,UAAYF,GACvBjH,EAAKoG,oBACd,CAKQY,yBAIN,MAAO,MAHK1L,KAAK8J,MAAsB,GAAhB9J,KAAK+J,UAAiB,MAC9B/J,KAAK8J,MAAsB,IAAhB9J,KAAK+J,UAAmB,MACnC/J,KAAK8J,MAAsB,IAAhB9J,KAAK+J,UAAmB,KAEpD,CAMA+B,cAAcC,GACZ,OAAKA,EAEDA,GAAS,GAAW,UACpBA,GAAS,GAAW,UACjB,UAJY,MAKrB,CAKAC,cAAcD,GACZ,OAAKA,GAAmB,IAAVA,EACP,GAAGA,KADwB,KAEpC,CAKAE,eAAe3G,GACb,OAAKA,EAEE,mBADaA,EAAS9D,QAAQ,MAAO,MADtB,GAGxB,CAKA0K,gBAAgBxB,GACd,OAAKA,EAEE,yBADUA,EAAUlJ,QAAQ,IAAK,MADjB,GAGzB,CAKA2K,cAAcxB,GACZ,OAAKA,EACEA,EAAKyB,WAAW,QAAUzB,EAAO,WAAWA,IADjC,GAEpB,CAKA0B,YAAYtH,GAWV,MAV0C,CACxCuH,MAAS,iBACT,sBAAoB,iBACpBC,YAAe,6BACfC,SAAY,uBACZC,SAAY,kBACZ,sBAAe,uBACfC,KAAQ,cACRC,UAAa,mBAED5H,IAAS,YACzB,CAKA6H,aAAa7H,GAWX,MAVyC,CACvCuH,MAAS,UACT,sBAAoB,UACpBC,YAAe,UACfC,SAAY,UACZC,SAAY,UACZ,sBAAe,UACfC,KAAQ,UACRC,UAAa,WAEF5H,IAAS,SACxB,CAMA8H,WAAW7E,GACT,IAAKA,EAAKX,IAAK,MAAO,IAEtB,OAAQW,EAAKjD,UACN,WACH,OAAOrH,KAAKoP,qBAAqB9E,EAAKX,KAAG,IACtC,oBACH,OAAO3J,KAAKqP,wBAAwB/E,EAAKX,KAAG,QAE5C,OAAOW,EAAKX,IAAI+E,WAAW,QAAUpE,EAAKX,IAAM,WAAWW,EAAKX,MAEtE,CAKA2F,mBAAmBhF,GACjB,GAAIA,EAAKsD,UAAW,OAAOtD,EAAKsD,UAEhC,OAAQtD,EAAKjD,UACN,WACH,MAAO,eACJ,oBACH,MAAO,kBACJ,QACH,MAAO,wBACJ,sBACH,MAAO,yBACJ,cACH,MAAO,0BACJ,WACH,MAAO,wBAEP,OAAOiD,EAAKX,IAElB,CAKQyF,qBAAqBzF,GAE3B,GAAIA,EAAI4F,SAAS,UAAY5F,EAAI4F,SAAS,gBACxC,OAAO5F,EAIT,MAAM6F,EAAc7F,EAAI7F,QAAQ,MAAO,IACvC,OAAI0L,EAAYlD,QAAU,GACjB,mBAAmBkD,IAGrB7F,CACT,CAKQ0F,wBAAwB1F,GAE9B,OAAIA,EAAI4F,SAAS,oBAAsB5F,EAAI4F,SAAS,eAC3C5F,EAILA,EAAI4F,SAAS,MAAQ5F,EAAI4F,SAAS,QAAU5F,EAAI4F,SAAS,MACpD,kCAAkCE,mBAAmB9F,KAGvDA,CACT,CAKMwB,sBAAsB7B,GAAgB,qCAC1CrC,QAAQC,IAAI,iCAAkCoC,GAC9CoG,EAAKC,gBAAiB,EAEtB,IAEE,MAAMC,QAAiBF,EAAK1E,YAAYvC,MAAM,CAC5Ce,MAAOF,IAKT,GAFArC,QAAQC,IAAI,mCAAoC0I,GAE5CA,GAAYA,EAAS5I,MAAQ4I,EAAS5I,KAAKsF,OAAS,EAAG,CAEzD,MAAMtD,EAAO4G,EAAS5I,KAAK6I,KAAMC,GAC/BA,EAAEC,kBAAoBzG,GACtBwG,EAAEC,kBAAoBzG,EAASxF,QAAQ,IAAK,KAG1CkF,GAEF0G,EAAKjE,eAAiBiE,EAAKM,2BAA2BhH,GACtD0G,EAAKO,gBAAiB,EACtBhJ,QAAQC,IAAI,gCAAiCwI,EAAKjE,gBAGlDiE,EAAK7E,iBAAiB9C,oBAAoB,CACxCtB,UAAW,GACXgB,aAAcuB,EAAKkH,gBACnBvI,cAAeqB,EAAKpB,SACpBE,WAAYkB,EAAKkD,MACjBV,UAAWkE,EAAKjE,mBAIlBxE,QAAQC,IAAI,8DACZwI,EAAKO,gBAAiB,EACtBP,EAAKjE,oBAAiB0E,QAIxBlJ,QAAQC,IAAI,6CACZwI,EAAKO,gBAAiB,EACtBP,EAAKjE,oBAAiB0E,QAEjBC,GACPnJ,QAAQmJ,MAAM,uCAAwCA,GACtDV,EAAKO,gBAAiB,EACtBP,EAAKjE,oBAAiB0E,UAEtBT,EAAKC,gBAAiB,EACtB1I,QAAQC,IAAI,0CAA2CwI,EAAKO,eAAgB,kBAAmBP,EAAKC,iBAG/FD,EAAKO,gBAAkBP,EAAKpG,WAC/BrC,QAAQC,IAAI,yDACZwI,EAAK3E,OAAOsF,SAAS,CAAC,kBAAmB,CACvCC,YAAa,CAAEhH,SAAUoG,EAAKpG,aAGnC,EA5DyC,EA6D5C,CAKQ0G,2BAA2BhH,GAEjC/B,QAAQC,IAAI,8CAA+C8B,GAC3D/B,QAAQC,IAAI,2BAA4B8B,EAAK4D,SAC7C3F,QAAQC,IAAI,8BAA+B8B,EAAKuH,YAChDtJ,QAAQC,IAAI,mCAAiCsJ,OAAOC,KAAKzH,IAGzD,IAAI0H,EAA+B,GAC/B1H,EAAKa,OAAS8G,MAAMC,QAAQ5H,EAAKa,SACnC6G,EAAmB1H,EAAKa,MAAMgH,IAAKvG,KACjCjD,KAAMiD,EAAKjD,KACXsC,IAAKW,EAAKX,IACViE,UAAWtD,EAAKsD,UAChBC,MAAOvD,EAAKuD,SACViD,KAAK,CAACC,EAAaC,IAAgBD,EAAElD,MAAQmD,EAAEnD,QAIrD,IAAI9D,EAAc,GAEdf,EAAK4D,SAAW5D,EAAK4D,QAAQqE,QAC/BlH,EAAcf,EAAK4D,QAAQqE,OAC3BhK,QAAQC,IAAI,iCAAkC6C,IACrCf,EAAKuH,YAAY7I,MAAQsB,EAAKuH,WAAW7I,KAAKuJ,QACvDlH,EAAcf,EAAKuH,WAAW7I,KAAKuJ,OACnChK,QAAQC,IAAI,yCAA0C6C,IAC7Cf,EAAK+G,iBAEdhG,EAAcf,EAAK+G,gBAAgBjM,QAAQ,IAAK,IAAIA,QAAQ,SAAU,KAAKA,QAAQ,QAASgM,GAAKA,EAAEoB,eACnGjK,QAAQC,IAAI,4DAA6D6C,KAEzEA,EAAc,2BACd9C,QAAQC,IAAI,iEAGd,MAAMiK,EAAmB,CACvBzJ,KAAMsB,EAAKkH,gBACXtI,SAAUoB,EAAKpB,SACf8E,MAAO1D,EAAKuH,YAAY7D,OAAS,GACjCC,MAAO,kBACPC,QAAS7C,EACT8C,SAAU7D,EAAK6D,UAAY,oBAC3BC,eAAgB,UAChBC,YAAa/D,EAAKoI,eAAeC,UAAY,GAC7CrE,UAAWhE,EAAK+G,gBAAkB,IAAI/G,EAAK+G,kBAAoB,GAC/D9C,KAAMjE,EAAKsI,WAAatI,EAAKoI,eAAeG,SAAW,GACvDrE,oBAAqBlE,EAAKwI,YAAc,IAAI5F,KAAK5C,EAAKwI,aAAapE,qBAAuB,GAC1FD,gBAAiBnE,EAAKyI,oBAAsB,IAAI7F,KAAK5C,EAAKyI,qBAAqBrE,sBAAuB,IAAIxB,MAAOwB,qBACjHC,WAAYrE,EAAK0I,OACjBpE,UAAWtE,EAAKqF,MAChBvG,WAAYkB,EAAKkD,MACjBqB,mBAAoB,GACpBC,gBAAiBxE,EAAK2I,oBAAsB,IAAI/F,KAAK5C,EAAK2I,qBAAqBvE,qBAAuB,GACtGO,YAAa3E,EAAK2E,aAAe,GACjCiE,MAAO5I,EAAK4I,OAAS,GACrB/H,MAAO6G,GAGTzJ,eAAQC,IAAI,mDAA8CiK,GACnDA,CACT,CAKQ/F,8BACNpL,KAAKiQ,gBAAiB,EACtBjQ,KAAK0L,uBACP,CAMAJ,cACErE,QAAQC,IAAI,sCACZD,QAAQC,IAAI,YAAalH,KAAKsJ,UAC9BrC,QAAQC,IAAI,kBAAmBlH,KAAK2P,gBACpC1I,QAAQC,IAAI,kBAAmBlH,KAAKiQ,gBACpChJ,QAAQC,IAAI,kBAAmBlH,KAAKyL,eACtC,+CAzkBWb,GAAgBD,6LAAhBC,EAAgBxG,klGDV7BuG,sCAAgC,UAAhCA,CAAgC,UAAhCA,CAAgC,WAKxBA,+BACFA,2BACAA,sCAAwB,UACEA,6CAAoBA,2BAC5CA,uCAA6BA,4CAAgBA,+BAGjDA,uCAA4B,YAExBA,oCACAA,yCAA0BA,gCAAMA,iCAMtCA,2CAWAA,2CAWAA,6CAgRFA,kCAtSkCA,0EAWAA,yGAWCA,o/qBCjCtBC,CAAgB,qBCLtB,IAAMiH,EAAkB,MAAzB,MAAOA,UAA0BtJ,KACrCzI,YAAsB0I,GACpB1H,MAAM0H,GADcxI,YAIdA,cAAW,eAFnB,CAIAyI,MAAMC,EAAc,IAClB,OAAO1I,KAAK2I,QAAQ3I,KAAK4I,SAAUF,EACrC,CAEAG,UAAUC,GACR,OAAO9I,KAAK2I,QAAQ,GAAG3I,KAAK4I,YAAYE,IAAM,GAChD,CAEAgJ,aAAalF,GACX,OAAO5M,KAAK+R,MAAM/R,KAAK4I,SAAUgE,EACnC,CAEAoF,cAAclJ,GACZ,OAAO9I,KAAKoJ,OAAO,GAAGpJ,KAAK4I,YAAYE,IAAM,GAC/C,+CArBW+I,GAAiBlH,gFAAjBkH,EAAiBvR,QAAjBuR,EAAiBrT,YAAjBqT,CAAkB,qECEzBlH,0CAAgCA,kHAASA,iCAAM,GAC7CA,gCAA2BA,oCAC7BA,sDAYMA,0CAA2DA,sBAAiBA,qDAAvCA,wCAAsBA,2FAO3DA,0CAA8DA,sBAAkBA,qDAAzCA,wCAAuBA,iIAdxEA,uCAA8C,WAA9CA,CAA8C,WAA9CA,CAA8C,WAA9CA,CAA8C,UAA9CA,CAA8C,YAIvBA,8BAAKA,6BACpBA,0CAA6BA,sKAAyB,mGAAkBA,2CAAgB,GACtFA,0CAAiBA,wCAAeA,2BAChCA,8CACFA,6BAEFA,wCAAsB,WAAtBA,CAAsB,aACLA,gCAAMA,6BACrBA,2CAA6BA,uKAA0B,mGAAkBA,2CAAgB,GACvFA,2CAAiBA,0CAAgBA,2BACjCA,8CACFA,6BAEFA,wCAAsB,WAAtBA,CAAsB,aACLA,gCAAMA,6BACrBA,0CAAwCA,sKAAyB,mGACzCA,2CAAgB,GADxCA,6BAGFA,wCAAsB,WAAtBA,CAAsB,aACLA,uCAAUA,6BACzBA,wCAAwB,eAC0BA,2KAA8B,mGACtDA,2CAAgB,GADxCA,2BAEAA,0CAAgCA,yCAAYA,iFAtBjBA,yEAEDA,oEAKCA,0EAEAA,qEAKWA,yEAMUA,qGAsDxCA,sDACAA,gGAlCZA,uCAA4D,WAA5DA,CAA4D,WAGtDA,gCACAA,oDACFA,6BAEFA,uCAA4B,WAA5BA,CAA4B,WAA5BA,CAA4B,WAA5BA,CAA4B,aAA5BA,CAA4B,aAImBA,+CAAqBA,6BAC5DA,wCAAyB,YAAzBA,CAAyB,cAEUA,2BAACA,6BAElCA,0CAGOA,6KAHPA,iCAUNA,wCAAsB,YACbA,8BAAMA,2BACbA,wCAAwB,gBAGdA,mHAASA,iDAAsB,GAErCA,yCACAA,yCACAA,uBACFA,iCAINA,0CACEA,iCACAA,gIACFA,4EAxBeA,gFAA+B,gCAahCA,sGACwBA,6EACSA,4EACvCA,6IAiEJA,0CACEA,sBACFA,qDAF4CA,oCAC1CA,mGAiBFA,0CACEA,sBACFA,qDAFqCA,wCACnCA,oGAaFA,0CACEA,sBACFA,qDAFuCA,wCACrCA,oGAcFA,0CACEA,sBACFA,qDAF2CA,wCACzCA,0IAqEVA,uCAAkD,WAAlDA,CAAkD,WAAlDA,CAAkD,aAAlDA,CAAkD,YAGdA,yCAAgBA,6BAC9CA,4CAGUA,4HAAaA,qDAChC,GAE8DA,2BACrDA,yCACEA,gCACAA,iFACFA,8EAPUA,iHA4DQA,wCACEA,sBACFA,+EADEA,8IAbdA,wCAAqF,YAArFA,CAAqF,YAArFA,CAAqF,YAArFA,CAAqF,aAK3EA,iCAGAA,oCAAK,YACKA,sBAAsCA,2BAC9CA,8BACAA,0CAA0BA,uBAAcA,2BACxCA,4CAGFA,6BAEFA,yCAAuB,iBAGbA,+HAASA,kDAA8B,GAE7CA,kCACFA,2BACAA,4CAEQA,2HAASA,yCAAc,GAE7BA,kCACFA,gGAtBGA,wFADAA,kEAGOA,wFAEkBA,8DACpBA,+FAftBA,uCAAoF,QAC9EA,2CAAkBA,2BACtBA,uCACEA,2CAmCFA,wEAnC8CA,8GAiDtCA,0CACEA,sBACFA,qDAFuCA,wCACrCA,0IAvDdA,uCAEEA,0CA0CAA,uCAA6B,QACvBA,6CAAoBA,2BACxBA,uCAAiB,WAAjBA,CAAiB,WAAjBA,CAAiB,WAGJA,6BAAIA,2BACXA,2CACQA,4HAAaA,0CACvC,GACoBA,2CAAiBA,sCAAYA,2BAC7BA,8CAGFA,+BAGJA,wCAAsB,YAAtBA,CAAsB,YAEXA,6BAAGA,2BACVA,0CAEOA,4HAAaA,yCACtC,GAHkBA,+BAOJA,wCAAsB,YAAtBA,CAAsB,YAEXA,oDAAoBA,2BAC3BA,0CAEOA,4HAAaA,+CACtC,GAHkBA,+BAOJA,wCAAsB,YAAtBA,CAAsB,YAEXA,8BAAMA,2BACbA,2CAEQA,mHAASA,0CAAe,GAE9BA,iCAA2BA,qCAC7BA,kFAtFWA,kHAiDHA,2EAGmBA,uEAWpBA,0EAUAA,gFAWCA,+JA5W9BA,uCAA0C,WAA1CA,CAA0C,WAGpCA,gCACAA,sBACFA,6BAEFA,uCAAuB,gBACIA,qHAAYA,mCAAQ,GAG3CA,2CAgDAA,uCAAiB,YAAjBA,CAAiB,YAAjBA,CAAiB,cAAjBA,CAAiB,aAG0BA,kDAAqBA,6BAC1DA,0CAIOA,2HAAaA,4DAC7B,GALSA,+BAUJA,wCAAsB,YAAtBA,CAAsB,cAAtBA,CAAsB,aAEWA,mDAAyBA,6BACtDA,0CAIOA,2HAAaA,oDAC7B,GALSA,+BAUJA,wCAAsB,YAAtBA,CAAsB,cAAtBA,CAAsB,aAEYA,6CAAmBA,6BACjDA,0CAIOA,2HAAaA,qDAC7B,EADgBA,CAAsC,mGACrBA,6CAAkB,GAL1CA,iCAaNA,wCAAiB,YAAjBA,CAAiB,YAAjBA,CAAiB,cAAjBA,CAAiB,aAGqBA,qCAAWA,6BAC3CA,2CAGQA,2HAAaA,uDAC9B,GAAWA,2CAAyBA,sDAA4BA,2BACrDA,8CAGFA,2BACAA,0CAA0BA,gFAAmDA,iCAMnFA,wCAAiB,YAAjBA,CAAiB,YAAjBA,CAAiB,cAAjBA,CAAiB,aAGgBA,wCAAcA,6BACzCA,2CAGQA,2HAAaA,kDAC9B,GAAWA,8CAGFA,+BAIJA,wCAAsB,YAAtBA,CAAsB,cAAtBA,CAAsB,aAEUA,gCAAMA,6BAClCA,2CAGQA,2HAAaA,mDAC9B,GAAWA,8CAGFA,+BAIJA,wCAAsB,YAAtBA,CAAsB,cAAtBA,CAAsB,aAEYA,kCAAQA,6BACtCA,2CAGQA,2HAAaA,qDAC9B,GAAWA,2CAAiBA,sCAAYA,2BAC7BA,8CAGFA,+BAIJA,wCAAsB,YAAtBA,CAAsB,cAAtBA,CAAsB,aAESA,uCAAaA,6BACxCA,0CAIOA,2HAAaA,kDAC7B,GALSA,iCAaNA,wCAAiB,YAAjBA,CAAiB,YAAjBA,CAAiB,cAAjBA,CAAiB,aAGoBA,mCAASA,6BACxCA,wCAAyB,YAAzBA,CAAyB,cAEUA,2BAACA,6BAElCA,0CAIOA,2HAAaA,4DAC/B,EADkBA,CAA6C,mGAC5BA,8CAAmB,GAL3CA,iCAWNA,wCAAsB,YAAtBA,CAAsB,cAAtBA,CAAsB,aAEaA,qCAAWA,6BAC1CA,0CAIOA,2HAAaA,sDAC7B,GALSA,2BAMAA,0CAA0BA,gEAAsCA,+BAIpEA,wCAAsB,YAAtBA,CAAsB,cAAtBA,CAAsB,aAEkBA,8CAAoBA,6BACxDA,0CAIOA,2HAAaA,2DAC7B,GALSA,iCAYNA,2CAmBAA,wCAAiB,YAAjBA,CAAiB,YAAjBA,CAAiB,cAAjBA,CAAiB,cAGgBA,4CAAWA,6BACtCA,8CAGUA,2HAAaA,kDAC/B,GAC4DA,iCAM1DA,yCAAiB,aAAjBA,CAAiB,aAAjBA,CAAiB,aAAjBA,CAAiB,aAKPA,kCAA+BA,yCACjCA,2BACAA,4CAEQA,kHAASA,6CAAkB,GACjCA,kCAEAA,wBACFA,6BAGFA,6CA8FFA,+BAKJA,yCAAwC,iBACeA,kHAASA,qCAAU,GACtEA,kCAA4BA,qCAC9BA,2BACAA,4CAGEA,kCAA2BA,mCAC7BA,4GA7XFA,gHAOiCA,8EAwDpBA,6FAaAA,qFAaAA,sFAgBCA,wFACEA,gEACoBA,yEAiBtBA,mFACoBA,oEAapBA,oFACqBA,qEAarBA,sFAEuBA,uEAcxBA,mFAqBEA,8FAcFA,uFAaAA,4FAQKA,mFA0BFA,mFAkBQA,yFAA4C,qCAE1DA,yGAIoBA,4EAyGpBA,8FAShBA,wCACEA,iCACAA,sCAAgBA,4CAAmBA,qDAKnCA,wCAA6D,WAA7DA,CAA6D,aAGvDA,iCACAA,sCAAuBA,+CAAsBA,2BAC7CA,qCAAsBA,mEAA0CA,4DA0E9DA,yCAGEA,sBACFA,+EADEA,6IAMFA,wCAA0F,aAEtFA,iCACAA,yCAAiCA,sBAAmBA,6BAEtDA,sCAAoB,gBAEVA,2JAASA,uCAAe,GAE9BA,iCACAA,qCAAMA,6BAAIA,6BAEZA,uCAEEA,kCACFA,oHAZiCA,mEAS9BA,0IAiBHA,sCAGEA,iCACFA,gFAHGA,gGAXPA,wCAAiG,aAE7FA,iCACAA,yCAAiCA,sBAA2BA,6BAE9DA,wCAA0B,WAGtBA,iCACFA,2BACAA,yCAKFA,gHAZmCA,kFAG9BA,uHAICA,+FAUVA,wCACEA,iCACAA,2CAAwDA,sBAAwCA,gHAAxCA,qHAKxDA,yCACEA,iCACAA,sBACFA,+EADEA,sGAOAA,sCAOEA,6BACAA,yCAAiCA,sBAAsCA,sFAJtEA,0EAAyD,iBAHzDA,yFAA6C,sDAM3CA,2GAC8BA,mHAVvCA,wCAA8D,aAE1DA,0CAUFA,iFAVsBA,2FAcxBA,wCAGEA,iCACAA,uCAAQA,4CAAmBA,+LAvJnCA,wCAAwE,YAAxEA,CAAwE,YAAxEA,CAAwE,YAAxEA,CAAwE,aAmB9DA,6BACeA,yCAA4DA,sBAAgBA,6BAG7FA,wCAAuC,cAKnCA,uBACFA,2BAEAA,yCAAsB,iBAElBA,kCACFA,2BACAA,yCAA+C,YACXA,+HAASA,oCAAY,GACrDA,kCAA6CA,kCAC/CA,2BACAA,uCAA8CA,+HAASA,wCAAgB,GACrEA,kCAAiCA,mCACnCA,qCAQVA,yCAA2B,aAA3BA,CAA2B,aAIrBA,kCACAA,uBACFA,6BAIFA,yCAAmC,eAQ/BA,uBACFA,2BACAA,6CAKFA,2BAGAA,yCACEA,6CAmBAA,4CAiBFA,2BAGAA,4CAMAA,yCACEA,6CAIFA,2BAGAA,4CAgBAA,4CAQAA,yCACEA,kCACAA,uBACFA,6BAIFA,yCAAsC,aAAtCA,CAAsC,aAAtCA,CAAsC,iBAIxBA,+HAASA,oCAAY,GAE3BA,kCAAgCA,kCAClCA,6BAEFA,yCAAwB,iBAEdA,+HAASA,+CAAuB,GAEtCA,iCAAuCA,oCACzCA,gGA9KHA,mFAAyC,mCAAzCA,CAAyC,oCAAzCA,CAAyC,qCAAzCA,CAAyC,iDAQtCA,0FAAoD,yCAApDA,CAAoD,sCAApDA,CAAoD,0CAApDA,CAAoD,qDAApDA,CAAoD,sDASjDA,oGACwEA,gEAKrEA,4FAAkD,iBAGtDA,6EA0BFA,oFAOIA,2FAAqD,8CAArDA,CAAqD,4CAArDA,CAAqD,+CAArDA,CAAqD,0EAMzDA,0EAGKA,mEAQ8DA,mEAmBAA,0EAoBpDA,yEAO2DA,kEAO3DA,kFAiBbA,wEASJA,0HAxKVA,uCACEA,2CAUAA,6CAsLFA,qEAhM4BA,oFAU2BA,6EChclD,IAAMsH,GAAiB,MAAxB,MAAOA,EAgEXnS,YACUkL,EACAkH,EACAnH,GAFA/K,mBACAA,yBACAA,cAlEVA,WAAe,GACfA,oBAAwB,GACxBA,qBAAuB,GACvBA,iBAAa,EACbA,iBAAa,EAGbA,iBAAqB,GAGrBA,qBAAiB,EACjBA,iBAAc,GACdA,kBAAe,GACfA,iBAAc,GACdA,uBAAmB,EAEnBA,wBAAoB,EACpBA,uBAAoB,GAGpBA,wBAAoB,EACpBA,cAAW,CAAEqH,KAAM,GAAIsC,IAAK,GAAIiE,UAAW,IAC3C5N,eAAY,CACV,CAAEmS,MAAO,QAAS3I,MAAO,QAAS4I,MAAO,cAAeC,IAAK,WAC7D,CAAEF,MAAO,sBAAoB3I,MAAO,sBAAoB4I,MAAO,cAAeC,IAAK,WACnF,CAAEF,MAAO,cAAe3I,MAAO,cAAe4I,MAAO,0BAA2BC,IAAK,WACrF,CAAEF,MAAO,WAAY3I,MAAO,WAAY4I,MAAO,oBAAqBC,IAAK,WACzE,CAAEF,MAAO,WAAY3I,MAAO,WAAY4I,MAAO,cAAeC,IAAK,WACnE,CAAEF,MAAO,oBAAe3I,MAAO,oBAAe4I,MAAO,oBAAqBC,IAAK,WAC/E,CAAEF,MAAO,OAAQ3I,MAAO,OAAQ4I,MAAO,WAAYC,IAAK,WACxD,CAAEF,MAAO,YAAa3I,MAAO,YAAa4I,MAAO,eAAgBC,IAAK,YAIxErS,YAAS,CACP,CAAEmS,MAAO,mBAAc3I,MAAO,oBAC9B,CAAE2I,MAAO,qBAAgB3I,MAAO,sBAChC,CAAE2I,MAAO,gBAAW3I,MAAO,iBAC3B,CAAE2I,MAAO,aAAc3I,MAAO,cAC9B,CAAE2I,MAAO,QAAS3I,MAAO,SACzB,CAAE2I,MAAO,UAAW3I,MAAO,YAG7BxJ,aAAU,CACR,CAAEmS,MAAO,YAAa3I,MAAO,aAC7B,CAAE2I,MAAO,oBAAqB3I,MAAO,qBACrC,CAAE2I,MAAO,kBAAmB3I,MAAO,mBACnC,CAAE2I,MAAO,kBAAa3I,MAAO,mBAC7B,CAAE2I,MAAO,eAAgB3I,MAAO,gBAChC,CAAE2I,MAAO,SAAU3I,MAAO,WAG5BxJ,eAAY,CACV,CAAEmS,MAAO,cAAe3I,MAAO,eAC/B,CAAE2I,MAAO,WAAY3I,MAAO,YAC5B,CAAE2I,MAAO,aAAc3I,MAAO,cAC9B,CAAE2I,MAAO,eAAgB3I,MAAO,gBAChC,CAAE2I,MAAO,cAAe3I,MAAO,uBAC/B,CAAE2I,MAAO,MAAO3I,MAAO,cACvB,CAAE2I,MAAO,aAAc3I,MAAO,cAC9B,CAAE2I,MAAO,SAAU3I,MAAO,UAOzB,CAEH/G,WAEEzC,KAAKsS,mBAAmBC,KAAK,KAC3BvS,KAAKwS,QAAM,EAEf,CAEAF,mBACE,OAAOtS,KAAKkS,kBAAkBzJ,MAAM,CAAEgK,OAAO,IAAQF,KAAMG,IACzD1S,KAAK2S,YAAcD,EAAK1L,MAAMA,MAAQ0L,EAAK1L,MAAQ,GACnDC,QAAQC,IAAI,2BAA4BlH,KAAK2S,YAAW,GACvDC,MAAOC,IACR5L,QAAQmJ,MAAM,iCAAkCyC,GAChDC,MAAM,0FAAsF,EAEhG,CAEAN,SACExS,KAAK+S,YAAa,EAClB/S,KAAKgL,YAAYvC,MAAM,CAAEuK,OAAQ,EAAGC,MAAO,MAAOV,KAAMG,IACtD1S,KAAKkT,MAAQR,EAAK1L,MAAMA,MAAQ0L,EAAK1L,MAAQ,GAC7CC,QAAQC,IAAI,oBAAqBlH,KAAKkT,OACtCjM,QAAQC,IAAI,+BAA6BlH,KAAK2S,aAC9C3S,KAAKmT,iBACLnT,KAAK+S,YAAa,IACjBH,MAAM,IAAM5S,KAAK+S,YAAa,EACnC,CAGAK,gBACEpT,KAAKqT,gBAAkBrT,KAAKqT,cAC9B,CAEAF,iBACEnT,KAAKsT,eAAiBtT,KAAKkT,MAAMK,OAAOvK,IAOtC,GALIhJ,KAAKwT,aAAexK,EAAKkD,QAAUlM,KAAKwT,aAKxCxT,KAAKyT,cAAgBzK,EAAK0I,SAAW1R,KAAKyT,aAC5C,OAAO,EAIT,GAAIzT,KAAK0T,YAAa,CACpB,MAAMlK,EAAQxJ,KAAK0T,YAAY3F,cAM/B,KAJE/E,EAAKkH,iBAAiBnC,cAAcwB,SAAS/F,IAC7CR,EAAKpB,UAAUmG,cAAcwB,SAAS/F,IACtCR,EAAK+G,iBAAiBhC,cAAcwB,SAAS/F,IAG7C,OAAO,EAKX,QAAIxJ,KAAK2T,mBAAqB3T,KAAK4T,WAAW5K,GAAI,EAMtD,CAEA6K,OACE7T,KAAK8T,gBAAkB,CACrBhL,GAAI,KACJyH,WAAY,KACZL,gBAAiB,GACjBtD,QAAS,GACThF,SAAU,GACVmI,gBAAiB,GACjBuB,UAAW,GACXyC,SAAU,GACV7H,MAAO,mBACPwF,OAAQ,YACRrD,MAAO,EACP2F,eAAgB,EAChBnH,SAAU,GACV+E,MAAO,GACP/H,MAAO,IAET7J,KAAKiU,kBAAoB,GACzBjU,KAAKkU,YAAa,EAClBlU,KAAKmU,mBAAoB,EACzBnU,KAAKoU,SAAW,CAAE/M,KAAM,GAAIsC,IAAK,GAAIiE,UAAW,GAClD,CAEAyG,OAAOrL,GACL/B,QAAQC,IAAI,iBAAkB8B,GAC9BhJ,KAAK8T,gBAAkB,IAAK9K,GAGxBhJ,KAAK8T,gBAAgBzF,QACvBrO,KAAK8T,gBAAgBzF,MAAQiG,SAAStU,KAAK8T,gBAAgBzF,QAEzDrO,KAAK8T,gBAAgBE,iBACvBhU,KAAK8T,gBAAgBE,eAAiBO,WAAWvU,KAAK8T,gBAAgBE,kBAInEhU,KAAK8T,gBAAgBvD,YAAcvQ,KAAK8T,gBAAgBrK,eAC3DzJ,KAAK8T,gBAAgBvD,WAAavQ,KAAK2S,YAAY9C,KAAK2E,GAAKA,EAAE1L,IAAM9I,KAAK8T,gBAAgBrK,eAAiB,MAIxGzJ,KAAK8T,gBAAgBjK,QACxB7J,KAAK8T,gBAAgBjK,MAAQ,IAI3B7J,KAAK8T,gBAAgBhL,IACvB9I,KAAKgL,YAAYb,YAAYnK,KAAK8T,gBAAgBhL,IAAIyJ,KAAMkC,IAC1DzU,KAAK8T,gBAAgBjK,MAAQ4K,GAAY,GACzCxN,QAAQC,IAAI,oBAAqBlH,KAAK8T,gBAAgBjK,MAAK,GAC1D+I,MAAOC,IACR5L,QAAQmJ,MAAM,0BAA2ByC,GACzC7S,KAAK8T,gBAAgBjK,MAAQ,KAIjC5C,QAAQC,IAAI,sCAAiClH,KAAK8T,iBAClD9T,KAAKkU,YAAa,EAClBlU,KAAKmU,mBAAoB,EACzBnU,KAAKoU,SAAW,CAAE/M,KAAM,GAAIsC,IAAK,GAAIiE,UAAW,GAClD,CAEA8G,WACE1U,KAAK8T,gBAAkB,GACvB9T,KAAKkU,YAAa,CACpB,CAEAS,SAEM3U,KAAK8T,gBAAgBzF,QACvBrO,KAAK8T,gBAAgBzF,MAAQiG,SAAStU,KAAK8T,gBAAgBzF,QAIzDrO,KAAK8T,gBAAgBE,iBACvBhU,KAAK8T,gBAAgBE,eAAiBO,WAAWvU,KAAK8T,gBAAgBE,iBAIxE,MAAMY,EAAiB,IAClB5U,KAAK8T,gBACRrK,aAAczJ,KAAK8T,gBAAgBvD,YAAYzH,IAAM,aAIhD8L,EAAerE,WAEtBtJ,QAAQC,IAAI,wBAAyB0N,GACrC3N,QAAQC,IAAI,qCAAgC0N,EAAe9L,IAE3D9I,KAAKgL,YAAYjC,UAAU6L,GAAgBrC,KAAMkC,IAC/CxN,QAAQC,IAAI,wBAAyBuN,GACrC3B,MAAM,2BACN9S,KAAK0U,WACL1U,KAAKwS,QAAM,GACVI,MAAOC,IACR5L,QAAQmJ,MAAM,iBAAkByC,GAChC5L,QAAQmJ,MAAM,kBAAmByE,KAAKC,UAAUjC,EAAM,KAAM,IAC5D,IAAIkC,EAAe,oBAEflC,EAAKzC,OAASyC,EAAKzC,MAAMlI,SAC3B6M,EAAelC,EAAKzC,MAAMlI,SACjB2K,EAAKmC,QACdD,EAAelC,EAAKmC,QACK,iBAATnC,IAChBkC,EAAelC,GAGjBC,MAAM,wBAA0BiC,EAAY,EAEhD,CAEAE,QAAQnM,IACDoM,QAAQ,wCACblV,KAAKgL,YAAY7B,WAAWL,GAAIyJ,KAAK,IAAMvS,KAAKwS,SAClD,CAGA2C,aAAanM,GACX,MAAMoM,EAAc,CAAC,mBAAc,qBAAgB,gBAAW,aAAc,SACtEC,EAAaD,EAAYE,QAAQtM,EAAKkD,OAExCmJ,GAAc,GAAKA,EAAaD,EAAY9I,OAAS,IACvDtD,EAAKkD,MAAQkJ,EAAYC,EAAa,GACtCrV,KAAKgL,YAAYjC,UAAUC,GAAMuJ,KAAK,KACpCvS,KAAKwS,QAAM,GACVI,MAAOC,IACR5L,QAAQmJ,MAAM,4BAA0ByC,GACxCC,MAAM,2BAAuB,GAGnC,CAGAyC,mBACE,GAAIvV,KAAK8T,gBAAgBlM,SAAU,CACjC,IAAIA,EAAW5H,KAAK8T,gBAAgBlM,SAAS9D,QAAQ,MAAO,IACxD8D,EAAS0E,QAAU,KACrB1E,EAAWA,EAAS9D,QAAQ,yBAA0B,cACtD8D,EAAWA,EAAS9D,QAAQ,yBAA0B,eAExD9D,KAAK8T,gBAAgBlM,SAAWA,EAEpC,CAGA4N,oBACE,GAAIxV,KAAK8T,gBAAgB/D,gBAAiB,CACxC,IAAI0F,EAASzV,KAAK8T,gBAAgB/D,gBAAgBjM,QAAQ,IAAK,IAAImN,OACnEjR,KAAK8T,gBAAgB/D,gBAAkB0F,EAE3C,CAGAC,cAAcvD,GACZ,OAAKA,EACEA,EAAMwD,eAAe,QAAS,CAAEC,MAAO,WAAYC,SAAU,QADjD,KAErB,CAEAC,aAAa9O,GACX,OAAKA,GAC2B,iBAATA,EAAoB,IAAI4E,KAAK5E,GAAQA,GAC7CoG,mBAAmB,SAFhB,KAGpB,CAGAwG,WAAW5K,GACT,QAAKA,EAAK2I,qBACG,IAAI/F,KACA,IAAIA,KAAK5C,EAAK2I,oBAEjC,CAGAoE,YAAY1H,GACV,OAAKA,EACDA,GAAS,GAAW,UACpBA,GAAS,GAAW,UACjB,UAHY,SAIrB,CAGA2H,cAAc9J,GASZ,MARe,CACb,qBAAc,SACd,uBAAgB,YAChB,kBAAW,0BACX+J,WAAc,eACdC,MAAS,kBACTC,QAAW,mBAECjK,IAAU,WAC1B,CAGAkK,kBAAkBpN,GAChB,OAAIA,EAAKuH,YAAcvH,EAAKuH,WAAW7I,KAC9BsB,EAAKuH,WAAW7I,KAErBsB,EAAK4D,QACA5D,EAAK4D,QAEP,KACT,CAIAyJ,eAAezO,GACb,OAAKA,EAEE,mBADaA,EAAS9D,QAAQ,MAAO,MADtB,GAGxB,CAGA0K,gBAAgBiH,GACd,OAAKA,EACE,yBAAyBA,IADZ,GAEtB,CAGAa,UAAUtN,GAGRhJ,KAAKqU,OAAOrL,EACd,CAGAuN,kBAAkBvN,GAChB/B,QAAQC,IAAI,4BAA6B8B,GAEzChJ,KAAK+K,OAAOsF,SAAS,CAAC,aAAc,CAClCC,YAAa,CACXlG,OAAQpB,EAAKF,GACboH,gBAAiBlH,EAAKkH,gBACtBtD,QAAS5M,KAAKoW,kBAAkBpN,GAChCpB,SAAUoB,EAAKpB,SACfsE,MAAOlD,EAAKkD,MACZwF,OAAQ1I,EAAK0I,SAGnB,CAEArI,uBACE,IAAKrJ,KAAKiU,oBAAsBjU,KAAKiU,kBAAkBhD,OAErD,YADA6B,MAAM,mCAIR,MAAMxJ,EAAWtJ,KAAKiU,kBAAkBnQ,QAAQ,IAAK,IAAImN,OACzDjR,KAAKwW,mBAAoB,EAGpBxW,KAAK8T,kBACR9T,KAAK8T,gBAAkB,IAGzB7M,QAAQC,IAAI,qCAAsC,CAAEoC,aAEpDtJ,KAAKgL,YAAY3B,qBAAqBC,GACnCiJ,KAAMkC,IACLxN,QAAQC,IAAI,mCAAiCuN,GAG7C,MAAMgC,EAAgBhC,EAGtB,GAFAxN,QAAQC,IAAI,8BAA4BuP,IAEnCA,EACH,MAAM,IAAIC,MAAM,0DAiBlB,GAbA1W,KAAK8T,gBAAkB,IAClB9T,KAAK8T,gBACR5D,gBAAiBuG,EAAcvG,iBAAmBuG,EAAc/O,MAAQ,GACxEkF,QAAS6J,EAAc7J,SAAW,GAClChF,SAAU6O,EAAc7O,UAAY,GACpCmI,gBAAiB0G,EAAc1G,iBAAmBzG,EAClDgI,UAAWmF,EAAcnF,WAAa,GACtCyC,SAAU0C,EAAc1C,UAAY0C,EAAcE,KAAO,GACzD/E,MAAO6E,EAAc7E,OAAS,GAC9BF,OAAQ,aAIN+E,EAAclG,WAAY,CAE5B,MAAMqG,EAAoB5W,KAAK2S,YAAY9C,KAAK2E,GAAKA,EAAE1L,KAAO2N,EAAclG,WAAWzH,IACvF9I,KAAK8T,gBAAgBvD,WAAaqG,GAAqBH,EAAclG,WAGvEtJ,QAAQC,IAAI,0CAAwClH,KAAK8T,iBAGrD9T,KAAK8T,gBAAgBC,UACvB/T,KAAK6W,iBAAiB7W,KAAK8T,gBAAgBC,UAG7CjB,MAAM,8CACN9S,KAAKwW,mBAAoB,IAE1B5D,MAAOC,IACN5L,QAAQmJ,MAAM,qCAAsCyC,GACpDC,MAAM,uCAAyCD,EAAKmC,SAAW,sBAC/DhV,KAAKwW,mBAAoB,GAE/B,CAEAK,iBAAiBF,GACf,IAAKA,EAAK,OAEV,MAAMG,EAAWH,EAAI5I,cAEjB+I,EAASvH,SAAS,UAAYuH,EAASvH,SAAS,YAClDvP,KAAK8T,gBAAgBjH,SAAW,WACvBiK,EAASvH,SAAS,WAAauH,EAASvH,SAAS,UAC1DvP,KAAK8T,gBAAgBjH,SAAW,eACvBiK,EAASvH,SAAS,WAAauH,EAASvH,SAAS,aAC1DvP,KAAK8T,gBAAgBjH,SAAW,aACvBiK,EAASvH,SAAS,SAAWuH,EAASvH,SAAS,SAAWuH,EAASvH,SAAS,WACrFvP,KAAK8T,gBAAgBjH,SAAW,cACvBiK,EAASvH,SAAS,QAAUuH,EAASvH,SAAS,WAAauH,EAASvH,SAAS,UACtFvP,KAAK8T,gBAAgBjH,SAAW,MACvBiK,EAASvH,SAAS,eAAiBuH,EAASvH,SAAS,aAC9DvP,KAAK8T,gBAAgBjH,SAAW,cACvBiK,EAASvH,SAAS,gBAAkBuH,EAASvH,SAAS,WAAauH,EAASvH,SAAS,mBAC9FvP,KAAK8T,gBAAgBjH,SAAW,cAEpC,CAIAkK,mBACE/W,KAAKmU,mBAAqBnU,KAAKmU,iBACjC,CAEA9J,gBACE,IAAKrK,KAAKoU,SAAS/M,OAASrH,KAAKoU,SAASzK,IAExC,YADAmJ,MAAM,qCAIH9S,KAAK8T,gBAAgBjK,QACxB7J,KAAK8T,gBAAgBjK,MAAQ,IAI/B,MAAMmN,EAAgBhX,KAAK8T,gBAAgBjK,MAAMgG,KAAKvF,GAAQA,EAAKjD,OAASrH,KAAKoU,SAAS/M,MACtF2P,EACE9B,QAAQ,yDACV8B,EAAcrN,IAAM3J,KAAKoU,SAASzK,IAClCqN,EAAcpJ,UAAY5N,KAAKoU,SAASxG,WAG1C5N,KAAK8T,gBAAgBjK,MAAMoN,KAAK,CAC9B5P,KAAMrH,KAAKoU,SAAS/M,KACpBsC,IAAK3J,KAAKoU,SAASzK,IACnBiE,UAAW5N,KAAKoU,SAASxG,UACzBsJ,OAAO,EACPrJ,MAAO7N,KAAK8T,gBAAgBjK,MAAMyC,SAKtCtM,KAAKoU,SAAW,CAAE/M,KAAM,GAAIsC,IAAK,GAAIiE,UAAW,GAClD,CAEAnD,YAAY0M,GACNjC,QAAQ,8BACVlV,KAAK8T,gBAAgBjK,MAAMuN,OAAOD,EAAO,EAE7C,CAEAE,gBAAgBhQ,GACd,OAAOrH,KAAKsX,UAAUzH,KAAKpR,GAAKA,EAAE0T,QAAU9K,IAAS,CACnDmC,MAAOnC,EACP+K,MAAO,UACPC,IAAK,UAET,CAEAkF,gBAAgB5N,EAAatC,GAC3B,IAAKsC,EAAK,MAAO,GAGjB,OAAQtC,OACD,WACH,GAAIsC,EAAI4F,SAAS,UAAY5F,EAAI4F,SAAS,gBACxC,OAAO5F,EAET,MAAM6F,EAAc7F,EAAI7F,QAAQ,MAAO,IACvC,OAAO0L,EAAYlD,QAAU,GAAK,mBAAmBkD,IAAgB7F,MAElE,oBACH,OAAIA,EAAI4F,SAAS,oBAAsB5F,EAAI4F,SAAS,eAC3C5F,EAEF,kCAAkC8F,mBAAmB9F,KAAG,QAG/D,OAAOA,EAAI+E,WAAW,QAAU/E,EAAM,WAAWA,IAEvD,CAEA6N,UAAU7N,EAAatC,GACrB,MAAMoQ,EAAezX,KAAKuX,gBAAgB5N,EAAKtC,GAC/CR,OAAO6Q,KAAKD,EAAc,SAC5B,+CAhiBWxF,GAAiBtH,wJAAjBsH,EAAiB7N,inQDV9BuG,sCAA6B,UAA7BA,CAA6B,QAErBA,+BAA4BA,gDAAsBA,2BACtDA,sCAAoB,cAC6BA,iDAASvL,iBAAe,GACrEuL,+BAA6BA,kCAC/BA,2BACAA,4CAGFA,6BAIFA,2CAmCAA,6CAwYAA,0CAMAA,0CAkMFA,kCA1nBwDA,sEAO9BA,yEAmCAA,qEAwYOA,qEAMbA,owUCrbPsH,CAAiB,qECP1BtH,yCAAgCA,kHAASA,iCAAM,GAC7CA,+BAA2BA,uCAC7BA,4FAIFA,sCAA0C,UAA1CA,CAA0C,WAGpCA,gCACAA,sBACFA,6BAEFA,uCAAuB,gBACOA,qHAAYA,mCAAQ,GAC9CA,uCAAiB,WAAjBA,CAAiB,YAAjBA,CAAiB,cAAjBA,CAAiB,aAGeA,2CAAiBA,6BAC3CA,0CAGOA,2HAAaA,oDAC5B,GAJQA,+BASJA,wCAAsB,YAAtBA,CAAsB,cAAtBA,CAAsB,aAEQA,8BAAIA,6BAC9BA,0CAGOA,2HAAaA,oDAC5B,EADeA,CAAqC,mGACpBA,yCAAc,GAJtCA,+BASJA,wCAAsB,YAAtBA,CAAsB,cAAtBA,CAAsB,aAESA,gCAAMA,6BACjCA,2CAEQA,2HAAaA,qDAC9B,GAAWA,2CAAuBA,+BAAKA,2BAC5BA,2CAAwBA,iCAAOA,mCAMvCA,wCAAiB,YAAjBA,CAAiB,YAAjBA,CAAiB,cAAjBA,CAAiB,aAGmBA,kCAAQA,6BACtCA,0CAGOA,2HAAaA,wDAC5B,GAJQA,+BAQJA,wCAAsB,YAAtBA,CAAsB,cAAtBA,CAAsB,aAESA,gCAAMA,6BACjCA,0CAGOA,2HAAaA,qDAC5B,GAJQA,iCASNA,wCAAiB,YAAjBA,CAAiB,YAAjBA,CAAiB,cAAjBA,CAAiB,aAGmBA,qCAAQA,6BACtCA,6CAEUA,2HAAaA,wDAC/B,GACqDA,iCAKnDA,wCAAwC,gBACeA,kHAASA,qCAAU,GACtEA,iCAA4BA,oCAC9BA,2BACAA,2CACEA,iCAA2BA,kCAC7BA,4GAtFFA,yHAYaA,sFAYAA,qFAWCA,sFACEA,4DACAA,4DAaHA,yFAWAA,sFAYGA,yFAWgCA,iGAuB5CA,uCAAQA,sBAAmBA,qDAAnBA,0FAeRA,wCAEEA,sBACFA,qDAFMA,kEACJA,4JAOFA,0CACQA,+HAASA,oCAAgB,GAE/BA,gCACFA,2BACAA,0CACQA,+HAASA,wCAAoB,GAEnCA,gCACFA,sDA/CVA,uCAAsC,UAAtCA,CAAsC,WAEjBA,gCAA2BA,2CAAiBA,6BAE/DA,uCAAuB,kBAAvBA,CAAuB,0BASjBA,kDAGFA,2BAEAA,gDACoB,0BADpBA,CACoB,2BAQpBA,sDACEA,mDAMFA,2BAEAA,sDACEA,mDAYFA,2EA5CUA,mEAAiB,uBAAjBA,CAAiB,aAAjBA,CAAiB,cAAjBA,CAAiB,cAAjBA,CAAiB,iBAOkBA,6DAMAA,6DAGQA,6DAGLA,6DAGAA,4DASfA,8DCtIlC,IAAMgN,GAAuB,MAA9B,MAAOA,EAMX7X,YAAoBoS,4BALpBlS,cAAkB,GAClBA,wBAA0B,GAC1BA,iBAAa,EACbA,iBAAa,CAE8C,CAE3DyC,WACEzC,KAAKwS,QACP,CAEAA,SACExS,KAAK+S,YAAa,EAClB/S,KAAKkS,kBAAkBzJ,MAAM,CAAEuK,OAAQ,EAAGC,MAAO,MAAOV,KAAMG,IAC5D1S,KAAK4X,SAAWlF,EAAK1L,MAAQ0L,EAC7B1S,KAAK+S,YAAa,IACjBH,MAAM,IAAM5S,KAAK+S,YAAa,EACnC,CAEA8E,OACE7X,KAAK8X,mBAAqB,CACxBrF,OAAO,GAETzS,KAAKkU,YAAa,CACpB,CAEAG,OAAOzH,GACL5M,KAAK8X,mBAAqB,IAAKlL,GAC/B5M,KAAKkU,YAAa,CACpB,CAEAQ,WACE1U,KAAK8X,mBAAqB,GAC1B9X,KAAKkU,YAAa,CACpB,CAEAS,SACE3U,KAAKkS,kBAAkBJ,aAAa9R,KAAK8X,oBAAoBvF,KAAK,KAChEvS,KAAK0U,WACL1U,KAAKwS,QAAM,GACVI,MAAOC,IACRC,MAAM,mBAAqBD,EAAI,EAEnC,CAEAoC,QAAQnM,IACDoM,QAAQ,2CACblV,KAAKkS,kBAAkBF,cAAclJ,GAAIyJ,KAAK,IAAMvS,KAAKwS,SAC3D,CAGAuF,eACE,GAAI/X,KAAK8X,mBAAmB5N,KAAM,CAChC,IAAIA,EAAOlK,KAAK8X,mBAAmB5N,KAAKpG,QAAQ,MAAO,IACvDoG,EAAOA,EAAKpG,QAAQ,uCAAwC,kBAC5D9D,KAAK8X,mBAAmB5N,KAAOA,EAEnC,+CA1DWyN,GAAuBhN,mFAAvBgN,EAAuBvT,olEDRpCuG,sCAA6B,UAA7BA,CAA6B,QAErBA,+BAA+BA,sCAAYA,2BAC/CA,4CAGFA,2BAGAA,2CAiGAA,2CAqDFA,kCA5JsDA,sEAM5BA,qEAiGLA,uiBClGRgN,CAAuB,KCHvBK,GAAoB,MAA3B,MAAOA,EAHblY,cAIUE,oBAAsB,KACtBA,cAAmB,GAK3BiY,SAASC,EAAY5O,GACnBtJ,KAAKmY,eAAiBD,EACtBlY,KAAKsJ,SAAWA,EAChBrC,QAAQC,IAAI,wCAAyC,CAAEoC,WAAU4O,SACnE,CAKAE,WACE,OAAIpY,KAAKmY,eACA,CACLD,MAAOlY,KAAKmY,eACZ7O,SAAUtJ,KAAKsJ,UAGZ,IACT,CAKA+O,aACErY,KAAKmY,eAAiB,KACtBnY,KAAKsJ,SAAW,GAChBrC,QAAQC,IAAI,uCACd,CAKAoR,WACE,OAA+B,OAAxBtY,KAAKmY,cACd,+CAxCWH,EAAoB,qDAApBA,EAAoB1X,QAApB0X,EAAoBxZ,qBAFnB,SAEDwZ,CAAoB,4BCUfrN,qCAAsBA,gCAAOA,mDAC7BA,qCAAsBA,8BAAKA,mDAC3BA,qCAAsBA,6BAAIA,mDAC1BA,qCAAsBA,kCAAMA,mDAC5BA,qCAAsBA,kCAASA,4FAXnCA,uCAIEA,8HAASA,sCAAW,GACpBA,uCAAyBA,sBAAKA,2BAC9BA,uCACEA,2CACAA,2CACAA,2CACAA,2CACAA,2CACFA,uFATAA,0CAEyBA,0DAEhBA,8DACAA,8DACAA,8DACAA,8DACAA,qFAgBbA,sDACAA,sDACAA,sDACAA,sDACAA,gGASVA,uCAA4D,UAA5DA,CAA4D,YAGtDA,gCACAA,yCAGEA,qKAHFA,2BAMAA,0CAGEA,kHAASA,kDAAuB,GAGhCA,wCACFA,6EAVEA,wEAOAA,kIAiDcA,sDACAA,uDACAA,qCAA0BA,sCAAaA,mDACvCA,qCAAyBA,qCAAYA,sDAwCvCA,qCAA2DA,sBAAiCA,sEAAjCA,mHAe/DA,uCAA+D,WAE3DA,gCACAA,8CACFA,2BACAA,uCACEA,sBACFA,wEADEA,4HAlCdA,sCAA+D,UAA/DA,CAA+D,WAA/DA,CAA+D,WAA/DA,CAA+D,WAKrDA,gCACAA,0DACFA,2BAEAA,uCAA+B,WAA/BA,CAA+B,WAA/BA,CAA+B,YAGJA,iCAAiCA,uBAAiCA,2BACvFA,yCACAA,wCAAmB,cAEfA,iCACAA,wCAAQA,uBAA4BA,2BAAUA,sCAChDA,2BACAA,yCACEA,iCACAA,wCAAQA,uBAA+CA,2BAAUA,oCACnEA,iCAMNA,2CASFA,8EAzB4DA,iGAChCA,wFAIVA,mFAIAA,kJAOUA,yJAuI1BA,uCAAqG,WAArGA,CAAqG,eAE3EA,+CAAsBA,2BAC5CA,6CAGEA,4HAAaA,0CAC/B,GAEmEA,sBAAiCA,4EAHlFA,2EAGiDA,yJAKvDA,uCAA+C,WAA/CA,CAA+C,eAEbA,0CAAWA,2BACzCA,6CAGEA,4HAAaA,6CAC/B,GAE4DA,4EAH1CA,+IA3IhBA,sCAAsF,UAAtFA,CAAsF,WAAtFA,CAAsF,WAAtFA,CAAsF,WAK5EA,gCACAA,mDACFA,2BAEEA,sCAAiB,WAAjBA,CAAiB,WAAjBA,CAAiB,eAIgBA,kDAAqBA,2BAClDA,0CAIEA,4HAAaA,iDAC/B,GALgBA,+BAYJA,wCAAsB,YAAtBA,CAAsB,eAEGA,2CAAiBA,2BACtCA,0CAIEA,4HAAaA,yCAC/B,GALgBA,2BAQAA,0CACEA,iCACAA,mHACFA,+BAKJA,wCAAsB,YAAtBA,CAAsB,eAEEA,kCAAQA,2BAC5BA,0CAIEA,4HAAaA,wCAC/B,GALgBA,+BAYJA,wCAAsB,YAAtBA,CAAsB,eAEIA,oCAAUA,2BAChCA,wDAGEA,6KAA+B,2FAIvBA,2CAAgB,EAJxBA,CAA+B,mGAKhBA,8CAAwB,EALvCA,CAA+B,6FAMtBA,6CAAuB,GAGlCA,2BACAA,0CACEA,iCACAA,6EACFA,+BAKJA,wCAAsB,YAAtBA,CAAsB,eAEWA,8CAAoBA,2BACjDA,wCAAyB,YAAzBA,CAAyB,cAEUA,2BAACA,6BAElCA,0CAIEA,4HAAaA,iDACjC,GALkBA,iCAaNA,wCAAsB,YAAtBA,CAAsB,eAEGA,iCAAOA,2BAC5BA,2CAIEA,4HAAaA,yCAC/B,EADkBA,CAA0B,2FAGlBA,0CAAe,GAPzBA,+BAYJA,4CAcAA,4CAYAA,gFA/HMA,mFAeAA,0EAmBAA,yEAcAA,+EAA+B,0BA6B7BA,mFAgBFA,0EAQiBA,iIAcAA,4IAnPnCA,uCAA2D,UAA3DA,CAA2D,UAA3DA,CAA2D,WAA3DA,CAA2D,WAA3DA,CAA2D,WAQ/CA,gCACAA,qDACFA,2BACAA,qCACEA,wGACFA,2BAGAA,wCAAwB,eACSA,+CAAqBA,2BACpDA,wCAAyB,YAAzBA,CAAyB,cAEUA,2BAACA,6BAElCA,0CAIEA,mKAAsB,iGAGPA,oDAAyB,GAP1CA,2BAQAA,wCAAgC,gBAI5BA,kHAASA,oDAAyB,GAElCA,yCACAA,yCACAA,4CACAA,4CACFA,iCAMNA,wCAAwB,WAChBA,4BAAEA,6BAIVA,wCAAyB,gBAIrBA,kHAASA,oDAAyB,GAClCA,iCACAA,kDACFA,qCAQVA,4CA4CAA,4CAuJFA,uEA3OgBA,uEASEA,sFAC4BA,sEACOA,qEAC5BA,sEACAA,qEA2BLA,gGA4CAA,4IAmNFA,uDACAA,uDACAA,qCAAiCA,yCAAgBA,mDACjDA,qCAAgCA,sCAAaA,mDAUrDA,wCACEA,gCACAA,uCAAQA,wCAAeA,2BAAUA,6FACnCA,kDA4BMA,uDACAA,sDACAA,uDACAA,uDACAA,uDACAA,uDACAA,uDACAA,2NAlBJA,wCAS2C,aAEvCA,yCACAA,yCACAA,yCACAA,yCACAA,yCACAA,yCACAA,yCACAA,wCACFA,2BACAA,yCAA0B,cACDA,uBAAaA,2BACpCA,yCAA4BA,uBAAkBA,2BAC9CA,yCAAsBA,sDAA8DA,6BAEtFA,yCAA0B,iBAItBA,+HAASA,2CAAmB,GAE5BA,kCACFA,2BACAA,4CAGEA,+HAASA,4CAAoB,GAE7BA,kCACFA,2DApCFA,6DAAgD,sCAAhDA,CAAgD,8BAAhDA,CAAgD,+CAAhDA,CAAgD,0CAAhDA,CAAgD,gDAAhDA,CAAgD,6BAQlBA,4EACCA,6EACDA,yEACAA,uFACYA,+EACNA,qFACTA,wEACDA,6GAGDA,+DACKA,oEACNA,gKAsCtBA,2CACEA,sBACFA,qDAFiDA,wCAC/CA,0IAxEhBA,sCAAyE,UAAzEA,CAAyE,YAAzEA,CAAyE,WAAzEA,CAAyE,WAK/DA,gCACAA,sBACFA,2BAEAA,wCACEA,6CA0CFA,2BAGAA,wCAAkB,aAEdA,kCACAA,+CACFA,2BACAA,wCAAwB,gBAEpBA,wDACAA,2CAA0BA,wFAA8DA,6BAE1FA,4CAGEA,4HAAaA,6CAC7B,GACgBA,4CAAiBA,wDAA8BA,2BAC/CA,+CAGFA,2BACAA,0CACEA,iCACAA,qGACFA,oFAxEFA,+GAKmBA,8EAyDfA,+EAGgCA,2IAvJhDA,wCAA2D,UAA3DA,CAA2D,UAA3DA,CAA2D,YAA3DA,CAA2D,WAA3DA,CAA2D,WAQ/CA,gCACAA,qDACFA,2BACAA,wCAA+E,QAA/EA,CAA+E,eAEzCA,uBAAyBA,2BAC3DA,wCAAQA,uBAAwBA,2BAAUA,uBAC5CA,2BACAA,4CAAsDA,kHAASA,mCAAS,GAAE,GACxEA,iCAA4BA,kCAC9BA,qCASVA,uCAAiB,WAAjBA,CAAiB,aAAjBA,CAAiB,YAAjBA,CAAiB,YAKPA,kCACAA,uDACFA,2BACAA,sCACEA,wGACFA,2BAGAA,wCAAwB,gBACOA,4CAAkBA,2BAC/CA,wCAAyB,gBAKrBA,2HAAaA,yCAC7B,EADgBA,CAA0B,0FAGlBA,0CAAe,GAPzBA,2BAQAA,wCAAgC,gBAI5BA,kHAASA,gDAAqB,GAE9BA,0CACAA,yCACAA,4CACAA,4CACFA,+BAGJA,0CACEA,0HACFA,6BAIFA,4CAIFA,iCAMNA,4CAqFFA,uEAxJgDA,wFAC1BA,+EAAkCA,sGAiCxCA,2EASEA,iGAC0BA,6EACSA,4EAC5BA,6EACAA,4EAUgBA,wEAUrBA,mIAyGNA,wCAA8D,YACpDA,+BAAMA,2BAAUA,sBAC1BA,sEAD0BA,8HA6DtBA,uDACAA,uDACAA,qCAA8BA,qCAAYA,mDAC1CA,qCAA6BA,oCAAWA,4FAmB9CA,wCAAkE,gBAI9DA,mHAASA,uDAA4B,GACrCA,gCACAA,sBACFA,0EADEA,4IAwBIA,uDACAA,uDACAA,qCAAoCA,qCAAYA,mDAChDA,qCAAmCA,oCAAWA,mDAIhDA,sCACEA,iCACAA,4EACFA,sDAJFA,wCACEA,4CAIFA,iGAJUA,0HAUhBA,wCACEA,gCACAA,uCAAQA,wCAAeA,2BAAUA,6FACnCA,4FAeEA,2CAGEA,mHAASA,+CAAoB,GAE7BA,iCAA6BA,+CAC/BA,4FAIFA,wCAEEA,iCACAA,uCAAyB,YACfA,oCAAWA,2BAAUA,4EAC/BA,2BACAA,2CAGEA,yKAAsC,EAAK,GAC3CA,iCAA4BA,mCAC9BA,wDAkBIA,wCACEA,iCACAA,sBACFA,+EADEA,2GAIFA,wCACEA,iCACAA,sCAAOA,sBAAoBA,iFAApBA,iGAITA,wCACEA,iCACAA,sCAAOA,sBAAiBA,iFAAjBA,8FAITA,wCACEA,iCACAA,sCAAO,YAAQA,iCAAQA,2BAAUA,sBAAsBA,iFAAtBA,2GAInCA,wCACEA,iCACAA,sCAAO,YAAQA,mCAAUA,2BAAUA,sBAA2BA,iFAA3BA,gHAIrCA,wCACEA,gCACAA,sCAAO,YAAQA,mCAAOA,2BAAUA,sBAA0BA,iFAA1BA,+GAKhCA,wCAA+C,cAE3CA,sBACFA,iFAF2BA,gFACzBA,uGAGJA,wCAA2C,eACfA,gCAAmCA,sBAAcA,iFAAdA,+FAW/DA,uDACAA,iGApENA,wCAGsC,YAHtCA,CAGsC,cAGhCA,wDACFA,2BACAA,wCAAyBA,sBAAaA,6BAGxCA,wCAEEA,2CAMAA,2CAMAA,4CAMAA,4CAMAA,4CAMAA,4CAMAA,yCACEA,4CAKAA,4CAGFA,6BAEFA,yCAA0B,iBAMtBA,+HAASA,4CAAoB,GAC7BA,0CACAA,0CACAA,uBACFA,2BACAA,4CAGEA,+HAASA,+CAAuB,GAEhCA,kCACAA,wCACFA,2BACAA,4CAGEA,+HAASA,sDAA8B,GAEvCA,iCACAA,qCACFA,0FAnFFA,kDAE2BA,yFACvBA,2GAEuBA,+DAKDA,uEAMQA,sEAMJA,mEAMDA,wEAMEA,6EAMHA,oFAOEA,mEAKDA,gEASzBA,8EAAsC,8BAGbA,sEACDA,uEACxBA,qIAtGhBA,sCAAyE,UAAzEA,CAAyE,YAAzEA,CAAyE,WAAzEA,CAAyE,WAK/DA,gCACAA,sBACAA,8CAOFA,2BAGAA,2CAeAA,wCACEA,8CAyFFA,8EApHEA,+GAKGA,oEAOCA,qFAgBeA,+IAjM/BA,wCAA2D,UAA3DA,CAA2D,UAA3DA,CAA2D,YAA3DA,CAA2D,WAA3DA,CAA2D,WAQ/CA,gCACAA,6CACFA,2BACAA,wCAA6B,YAA7BA,CAA6B,aAEjBA,gCAAMA,2BAAUA,uBAC1BA,2BACAA,yCAA+D,UAE7DA,4CAGFA,2BACAA,4CAAsDA,kHAASA,mCAAS,GAAE,GACxEA,iCAA4BA,kCAC9BA,uCAUVA,uCAAiB,WAAjBA,CAAiB,aAAjBA,CAAiB,YAAjBA,CAAiB,YAKPA,kCACAA,qDACFA,2BACAA,sCACEA,8FACFA,2BAGAA,uCAAiB,aAAjBA,CAAiB,YAAjBA,CAAiB,gBAGeA,yCAAeA,2BACzCA,2CAIEA,2HAAaA,yCAC/B,GALgBA,+BASJA,yCAAsB,YAAtBA,CAAsB,gBAEOA,gCAAMA,2BAC/BA,2CAIEA,2HAAaA,wCAC/B,GALgBA,+BASJA,yCAAsB,YAAtBA,CAAsB,YAEXA,8BAAMA,2BACbA,4CAGEA,kHAASA,0CAAe,GAExBA,0CACAA,yCACAA,4CACAA,4CACFA,iCAMNA,yCAA6B,WACrBA,4BAAEA,6BAIVA,yCAAwC,aAEpCA,kCACAA,mDACFA,2BAGAA,4CAWAA,uCAAiB,aAAjBA,CAAiB,gBAEkBA,2DAAiCA,2BAChEA,wCAAyB,oBAIrBA,qKAAwB,4FAKfA,+CAAyB,GARpCA,2BAUAA,4CAGEA,kHAASA,6CAAkB,GAE3BA,0CACAA,yCACAA,4CACAA,4CACFA,6BAEFA,4CAMFA,+BAKJA,4CAIFA,iCAONA,4CA+HFA,qGAjRsCA,mIAIGA,oFAqCvBA,2EAYAA,yEAYAA,8GAC0BA,0EACSA,yEAC5BA,0EACAA,yEAmBkBA,sFAkBvBA,wEAWAA,6GAC0BA,gFACSA,+EAC5BA,gFACAA,+EAGoBA,6EAWNA,wFAWrBA,mIAiJNA,wCAAkE,YACxDA,yCAAaA,2BAAUA,sBACjCA,sEADiCA,qHAEjCA,yCAAoDA,sBAAiBA,sEAAjBA,yIAPxDA,wCAA+C,YAA/CA,CAA+C,YAA/CA,CAA+C,YAGnCA,8BAAKA,2BAAUA,sBACvBA,2CAGAA,4CACFA,2BACAA,2CAAsDA,mHAASA,mCAAS,GAAE,GACxEA,gCAA4BA,kCAC9BA,4EARyBA,kGACjBA,iFAGCA,gGAOXA,wCACEA,iCACAA,wGACFA,kDA2BIA,uDACAA,uDACAA,qCAAgCA,yCAAaA,mDAC7CA,qCAA+BA,oCAAWA,4FAT9CA,uCAAqE,eAIjEA,mHAASA,iDAAsB,GAE/BA,yCACAA,wCACAA,2CACAA,2CACFA,2BACAA,wCAAkB,eAEdA,8GACFA,4EATAA,6FAC0BA,4EACSA,2EAC5BA,4EACAA,4IAUXA,wCAAyG,gBAIrGA,mHAASA,iDAAsB,GAE/BA,iCACAA,2CACFA,0EAHEA,uGAOJA,wCACEA,gCACAA,uCAAQA,wCAAeA,2BAAUA,oGACnCA,sDA/CRA,sCAAmC,UAAnCA,CAAmC,YAAnCA,CAAmC,WAAnCA,CAAmC,WAKzBA,iCACAA,kEACFA,2BACAA,qCACEA,iHACFA,2BAGAA,2CAmBAA,4CAYAA,4CAIFA,4EAnC4BA,8FAmBKA,2HAYKA,kGAiBlCA,yCACEA,iCAA6BA,sDAC/BA,sDAIFA,wCACEA,iCACAA,uCAAQA,mDAAoBA,2BAC5BA,wDAA8BA,uCAAQA,sBAA4BA,2BAAUA,mDAC5EA,8BACAA,sCAAO,aACGA,iCAAOA,2BAAUA,uBACzBA,0CACEA,uBACFA,6BAEFA,+BACAA,2CACEA,sGACFA,wEAXsCA,mFAGXA,8HACAA,gHACvBA,gIAsBEA,yCACEA,iCAA4BA,oCAC9BA,sDAEAA,yCAIEA,sBACFA,8GAHMA,mEAA8C,qFAElDA,2GAkBJA,wCACEA,iCACAA,qCAAMA,sBAAkBA,iFAAlBA,qGAIRA,wCACEA,iCACAA,qCAAMA,sBAAoCA,iFAApCA,6HAIRA,wCACEA,iCACAA,qCAAMA,sBAAeA,iFAAfA,2FAIRA,wCACEA,iCACAA,qCAAMA,sBAA4BA,iFAA5BA,+GAIRA,wCACEA,iCACAA,qCAAMA,sBAAsBA,iFAAtBA,wIAhEZA,wCAIEA,+HAASA,iDAA0B,GAEnCA,wCAA0B,aACEA,sBAAUA,2BACpCA,wCACEA,iCACAA,sBAEAA,4CAIAA,4CAMFA,2BACAA,wCAA2B,iBAMvBA,+HAASA,iDAA0B,GAEnCA,kCACFA,+BAIJA,yCAEEA,4CAMAA,4CAMAA,4CAMAA,4CAMAA,4CAIFA,2BAGAA,yCAA+B,YACtBA,uBAAiCA,2BACxCA,8CAEEA,yGAAaA,wCAC/B,GAE+EA,qEAzEjEA,iDAI4BA,oEAGxBA,wEAEOA,oEAIAA,uEAWLA,4EAAqC,oCAWjBA,8DAMAA,uEAMAA,gEAMAA,sEAMAA,uEAQjBA,+FAGLA,yEAA+B,wFAxG/CA,sCAAyE,UAAzEA,CAAyE,YAAzEA,CAAyE,WAAzEA,CAAyE,WAK/DA,gCACAA,sBACAA,4CAGFA,2BAGAA,4CAiBAA,wCACEA,8CA+EFA,2BAGAA,yCAAkB,gBAEdA,kCACAA,qDACFA,2BACAA,2CAIEA,4HAAaA,iDAC3B,GALYA,2BAQAA,0CACEA,iCACAA,gGACFA,6BAIFA,yCAAkB,gBACeA,sEAAmCA,2BAClEA,8CAGEA,4HAAaA,mDAC3B,GAEgGA,kFAvIpFA,mHACOA,8EAMsBA,8EAmBTA,8EA0FlBA,kFAgBAA,qJAWZA,sCAA8F,UAA9FA,CAA8F,YAA9FA,CAA8F,aAItFA,iCACAA,mCAAIA,mDAAuBA,2BAC3BA,sCAAsBA,yHAA8EA,2BAEpGA,wCAAgD,iBAI5CA,mHAASA,iDAAsB,GAC/BA,kCACAA,4CACFA,2BACAA,4CAGEA,mHAASA,qDAA0B,GAEnCA,kCACAA,wDACFA,6BAGFA,wCAAwB,WAChBA,4BAAEA,6BAGVA,yCAAqC,YACRA,iEAAoCA,2BAC/DA,yCAA2D,gBAIvDA,2KAA6B,kGAGdA,iDAAsB,GANvCA,2BAOAA,4CAGEA,mHAASA,iDAAsB,GAE/BA,kCACAA,qCACFA,6BAEFA,2CACEA,iCACAA,gFACFA,kFAhBIA,8EAQAA,4IA3RhBA,wCAA2D,UAA3DA,CAA2D,UAA3DA,CAA2D,YAA3DA,CAA2D,WAA3DA,CAA2D,WAQ/CA,gCACAA,2CACFA,2BACAA,4CAcAA,2CAIFA,iCAONA,4CAsDAA,4CAqJAA,4CA0DFA,qEA9RwCA,oEAcIA,qEAWxBA,oEAsDAA,wGAqJAA,oJAsGFA,uDACAA,sDACAA,uDACAA,uDACAA,uDACAA,uDACAA,uDACAA,0DAIAA,wCAAmDA,sBAAkBA,+EAAlBA,qIAtBvDA,wCAS2C,aAEvCA,yCACAA,yCACAA,yCACAA,yCACAA,yCACAA,yCACAA,yCACAA,wCACFA,2BACAA,yCAA0B,cACDA,uBAAaA,2BACpCA,4CACAA,yCAAsBA,sDAA8DA,6BAEtFA,yCAA0B,iBAItBA,+HAASA,2CAAmB,GAE5BA,kCACFA,2BACAA,4CAGEA,+HAASA,4CAAoB,GAE7BA,kCACFA,2DApCFA,6DAAgD,sCAAhDA,CAAgD,8BAAhDA,CAAgD,+CAAhDA,CAAgD,0CAAhDA,CAAgD,gDAAhDA,CAAgD,6BAQlBA,4EACCA,6EACDA,yEACAA,uFACYA,+EACNA,qFACTA,wEACDA,6GAGDA,+DACMA,oEACPA,gKA1B9BA,wCAAiE,QAC3DA,gCAA4BA,sBAA+CA,2BAC/EA,wCACEA,6CA0CFA,wEA5CgCA,8GAGXA,sGAmDfA,yCAA+DA,kCAASA,sDAEtEA,qCAAwBA,sBAAkBA,+EAAlBA,qGACxBA,qCAA2BA,sBAAiBA,+EAAjBA,0GAC3BA,qCAAiCA,sBAA8BA,+EAA9BA,uHACjCA,qCAAiCA,sBAAuBA,+EAAvBA,gHAEnCA,wCAAiD,eACrBA,sBAAqBA,iFAArBA,iGAXhCA,wCAAuE,YAAvEA,CAAuE,YAE3DA,sBAAcA,2BACtBA,4CACAA,wCACEA,2CACAA,2CACAA,2CACAA,2CACFA,2BACAA,4CAGFA,uDAXUA,+DACDA,oEAEEA,8DACAA,gEACAA,sEACAA,sEAEHA,iGAMZA,wCAAiD,YACvCA,kDAAmBA,2BAC3BA,sCAA2BA,sBAA0BA,wEAA1BA,4GArB/BA,wCAAqF,QAC/EA,gCAA6BA,sBAAgDA,2BACjFA,wCACEA,4CAeFA,2BACAA,2CAIFA,sEAtBmCA,kHAERA,8EAgBnBA,4GAYJA,oCAA2B,YAAQA,qCAASA,2BAAUA,sBAAiBA,sEAAjBA,2GAL1DA,wCAA+C,QACzCA,gCAA+BA,0CAAgBA,2BACnDA,wCAA0B,QAA1BA,CAA0B,YACXA,8BAAKA,2BAAUA,sBAAaA,2BACzCA,oCAAK,aAAQA,kCAAQA,2BAAUA,uBAAgBA,2BAC/CA,2CACFA,wEAH8BA,4EACGA,+EACzBA,mGA0CNA,2CACEA,sBACFA,qDAFqCA,wCACnCA,oGAaFA,2CACEA,sBACFA,qDAFuCA,wCACrCA,oGAaFA,2CACEA,sBACFA,qDAF2CA,wCACzCA,0IAlLhBA,wCAA2D,UAA3DA,CAA2D,YAA3DA,CAA2D,YAA3DA,CAA2D,WAA3DA,CAA2D,WAS/CA,iCACAA,yCACFA,2BAGAA,wCAA6B,QACvBA,kCAA4BA,2CAAaA,2BAC7CA,yCAA0B,SAA1BA,CAA0B,aACXA,yCAAYA,2BAAUA,uBAAwBA,2BAC3DA,qCAAK,aAAQA,kCAAQA,2BAAUA,uBAAgBA,2BAC/CA,qCAAK,aAAQA,iCAAOA,2BAAUA,uBAAeA,2BAC7CA,qCAAK,aAAQA,mCAASA,2BAAUA,uBAAoCA,2BACpEA,qCAAK,aAAQA,oCAAUA,2BAAUA,uBAAyBA,2BAC1DA,qCAAK,aAAQA,kCAAQA,2BAAUA,uBAAmCA,+BAKtEA,4CAiDAA,4CA0BAA,6CAQFA,+BAKJA,yCAAsB,YAAtBA,CAAsB,YAAtBA,CAAsB,YAIdA,kCACAA,uDACFA,2BAGAA,wCAAwB,gBAEpBA,kCACAA,0CACFA,2BACAA,2CAIEA,2HAAaA,iDAC3B,GALYA,2BAOAA,0CACEA,wDACFA,6BAIFA,wCAAwB,gBACHA,wCAAcA,2BACjCA,4CAGEA,2HAAaA,uCAC3B,GACcA,+CAGFA,6BAIFA,wCAAwB,gBACFA,wCAAcA,2BAClCA,4CAGEA,2HAAaA,wCAC3B,GACcA,+CAGFA,6BAIFA,wCAAwB,gBACAA,kCAAQA,2BAC9BA,4CAGEA,2HAAaA,0CAC3B,GACcA,+CAGFA,6BAIFA,wCAAwB,gBACGA,2CAAWA,2BACpCA,8CAGEA,2HAAaA,6CAC3B,GAEwDA,6BAI9CA,yCAAwB,gBAKpBA,4KAJFA,2BAMAA,2CACEA,oDACFA,mFA5LqCA,wFACJA,+EACDA,8EACEA,oGACCA,wFACFA,mGAKLA,oFAiDAA,wGA0BAA,oEA+B1BA,mFAcAA,wEAE0BA,oEAY1BA,yEAE2BA,qEAY3BA,2EAE6BA,uEAY7BA,8EAYAA,gJAsBRA,0CAGEA,kHAASA,qCAAU,GAEnBA,iCACAA,iCACFA,sDAMAA,wCACEA,iCACAA,sBACFA,qEADEA,yIAIFA,2CAGEA,kHAASA,qCAAU,GAEnBA,iCACAA,sCACFA,4FAGAA,2CAGEA,kHAASA,qCAAU,GAEnBA,qCACAA,iCACFA,kDASEA,uDACAA,gGAPFA,2CAGEA,kHAASA,uCAAY,GAGrBA,yCACAA,wCACAA,sBACFA,uEALEA,gDAEwBA,sEACWA,qEACnCA,kJC50CR4N,GAAiB,CACrB,CAAEC,KAAM,OAAQC,UAAW7N,IAC3B,CAAE4N,KAAM,iBAAkBC,UAAW7N,IACrC,CAAE4N,KAAM,YAAaC,UCoBO,MAAxB,MAAOC,EAuOX5Y,YACUgL,EACAC,EACA4N,EACA3N,EACAkH,EACA0G,GALA5Y,aACAA,cACAA,4BACAA,mBACAA,yBACAA,WA1OVA,oBAAsB,KACtBA,cAAmB,GAGnBA,uBAAyB,KAGzBA,iBAAa,EACbA,wBAAoB,EACpBA,qBAAiB,EACjBA,UAAO,GACPA,wBAAoB,EACpBA,wBAAoB,EAIpBA,sBAA0B,GAG1BA,0BAA8B,GAG9BA,sBAA0B,GAG1BA,sBAA0B,GAC1BA,uBAAmB,EACnBA,qBAAiB,EAGjBA,gBAAa,GACbA,2BAAuB,EAGvBA,iBAAc,EACdA,gBAAa,EACbA,gBAAa,CACX6Y,aAAc,GACdC,iBAAkB,GAClBC,gBAAiB,KACjBC,kBAAmB,GACnBC,cAAe,IAIjBjZ,uBAAmB,EACnBA,kBAAc,EACdA,qBAAiB,EACjBA,iCAA6B,EAC7BA,yBAAqB,EAGrBA,UAAY,CACVkQ,gBAAiB,GACjBtD,QAAS,GACT5C,OAAQ,GACRkP,SAAU,GACVhP,KAAM,GACNtC,SAAU,GACVmI,gBAAiB,GACjBwB,QAAS,GACT4H,aAAc,GACdpF,SAAU,GACVqF,UAAW,GACXzL,YAAa,GACb0L,kBAAmB,GACnB3H,OAAQ,YACRxF,MAAO,mBACPW,SAAU,oBACVyM,YAAa,GACb7P,aAAc,MAIhBzJ,iBAAqB,GAGrBA,uBAA4B,GAG5BA,4BAAiC,GAGjCA,qBAA0B,GAoC1BA,YAAS,CACP,CAAEmS,MAAO,mBAAc3I,MAAO,oBAC9B,CAAE2I,MAAO,qBAAgB3I,MAAO,sBAChC,CAAE2I,MAAO,gBAAW3I,MAAO,iBAC3B,CAAE2I,MAAO,aAAc3I,MAAO,cAC9B,CAAE2I,MAAO,QAAS3I,MAAO,SACzB,CAAE2I,MAAO,UAAW3I,MAAO,YAG7BxJ,aAAU,CACR,CAAEmS,MAAO,YAAa3I,MAAO,aAC7B,CAAE2I,MAAO,oBAAqB3I,MAAO,qBACrC,CAAE2I,MAAO,kBAAmB3I,MAAO,mBACnC,CAAE2I,MAAO,kBAAa3I,MAAO,mBAC7B,CAAE2I,MAAO,eAAgB3I,MAAO,gBAChC,CAAE2I,MAAO,SAAU3I,MAAO,WAG5BxJ,eAAY,CACV,CAAEmS,MAAO,oBAAe3I,MAAO,qBAC/B,CAAE2I,MAAO,SAAU3I,MAAO,UAC1B,CAAE2I,MAAO,cAAY3I,MAAO,eAC5B,CAAE2I,MAAO,WAAS3I,MAAO,YACzB,CAAE2I,MAAO,iBAAY3I,MAAO,kBAC5B,CAAE2I,MAAO,SAAU3I,MAAO,WAG5BxJ,mBAAgB,CACd,CAAEmS,MAAO,WAAY3I,MAAO,WAAY4I,MAAO,cAAeC,IAAK,WACnE,CAAEF,MAAO,gBAAiB3I,MAAO,gBAAiB4I,MAAO,WAAYC,IAAK,WAC1E,CAAEF,MAAO,UAAW3I,MAAO,UAAW4I,MAAO,gBAAiBC,IAAK,WACnE,CAAEF,MAAO,YAAa3I,MAAO,YAAa4I,MAAO,eAAgBC,IAAK,WACtE,CAAEF,MAAO,gBAAc3I,MAAO,gBAAc4I,MAAO,0BAA2BC,IAAK,YAGrFrS,kBAAe,CACb,CAAEmS,MAAO,kBAAgB3I,MAAO,mBAChC,CAAE2I,MAAO,qBAAmB3I,MAAO,sBACnC,CAAE2I,MAAO,QAAS3I,MAAO,SACzB,CAAE2I,MAAO,eAAgB3I,MAAO,gBAChC,CAAE2I,MAAO,WAAY3I,MAAO,YAC5B,CAAE2I,MAAO,oBAAqB3I,MAAO,qBACrC,CAAE2I,MAAO,UAAW3I,MAAO,WAC3B,CAAE2I,MAAO,SAAU3I,MAAO,UAC1B,CAAE2I,MAAO,cAAe3I,MAAO,eAC/B,CAAE2I,MAAO,UAAW3I,MAAO,WAC3B,CAAE2I,MAAO,cAAe3I,MAAO,eAC/B,CAAE2I,MAAO,uBAAkB3I,MAAO,wBAClC,CAAE2I,MAAO,qBAAmB3I,MAAO,sBACnC,CAAE2I,MAAO,cAAe3I,MAAO,eAC/B,CAAE2I,MAAO,YAAa3I,MAAO,aAC7B,CAAE2I,MAAO,kBAAmB3I,MAAO,mBACnC,CAAE2I,MAAO,SAAU3I,MAAO,UAC1B,CAAE2I,MAAO,kBAAmB3I,MAAO,mBACnC,CAAE2I,MAAO,kBAAmB3I,MAAO,mBACnC,CAAE2I,MAAO,UAAW3I,MAAO,WAC3B,CAAE2I,MAAO,OAAQ3I,MAAO,QACxB,CAAE2I,MAAO,gBAAiB3I,MAAO,iBACjC,CAAE2I,MAAO,oBAAqB3I,MAAO,qBACrC,CAAE2I,MAAO,gBAAiB3I,MAAO,iBACjC,CAAE2I,MAAO,gBAAiB3I,MAAO,iBACjC,CAAE2I,MAAO,kBAAmB3I,MAAO,mBACnC,CAAE2I,MAAO,YAAa3I,MAAO,aAC7B,CAAE2I,MAAO,kBAAmB3I,MAAO,mBACnC,CAAE2I,MAAO,cAAe3I,MAAO,eAC/B,CAAE2I,MAAO,aAAc3I,MAAO,cAC9B,CAAE2I,MAAO,WAAY3I,MAAO,YAC5B,CAAE2I,MAAO,gBAAiB3I,MAAO,iBACjC,CAAE2I,MAAO,SAAU3I,MAAO,UAC1B,CAAE2I,MAAO,OAAQ3I,MAAO,QACxB,CAAE2I,MAAO,QAAS3I,MAAO,SACzB,CAAE2I,MAAO,cAAe3I,MAAO,eAC/B,CAAE2I,MAAO,cAAe3I,MAAO,eAC/B,CAAE2I,MAAO,gBAAiB3I,MAAO,iBACjC,CAAE2I,MAAO,UAAW3I,MAAO,WAC3B,CAAE2I,MAAO,0BAAwB3I,MAAO,2BACxC,CAAE2I,MAAO,YAAa3I,MAAO,aAC7B,CAAE2I,MAAO,QAAS3I,MAAO,SACzB,CAAE2I,MAAO,YAAa3I,MAAO,aAC7B,CAAE2I,MAAO,aAAc3I,MAAO,cAC9B,CAAE2I,MAAO,kBAAmB3I,MAAO,mBACnC,CAAE2I,MAAO,QAAS3I,MAAO,SACzB,CAAE2I,MAAO,YAAa3I,MAAO,aAC7B,CAAE2I,MAAO,gBAAiB3I,MAAO,iBACjC,CAAE2I,MAAO,UAAW3I,MAAO,WAC3B,CAAE2I,MAAO,iBAAkB3I,MAAO,kBAClC,CAAE2I,MAAO,eAAgB3I,MAAO,gBAChC,CAAE2I,MAAO,SAAU3I,MAAO,UAC1B,CAAE2I,MAAO,SAAU3I,MAAO,UAC1B,CAAE2I,MAAO,YAAa3I,MAAO,aAC7B,CAAE2I,MAAO,WAAY3I,MAAO,YAC5B,CAAE2I,MAAO,mBAAoB3I,MAAO,oBACpC,CAAE2I,MAAO,YAAa3I,MAAO,aAC7B,CAAE2I,MAAO,qBAAmB3I,MAAO,sBACnC,CAAE2I,MAAO,UAAW3I,MAAO,WAC3B,CAAE2I,MAAO,WAAY3I,MAAO,YAC5B,CAAE2I,MAAO,OAAQ3I,MAAO,QACxB,CAAE2I,MAAO,WAAY3I,MAAO,YAC5B,CAAE2I,MAAO,QAAS3I,MAAO,SACzB,CAAE2I,MAAO,cAAe3I,MAAO,eAC/B,CAAE2I,MAAO,aAAc3I,MAAO,cAC9B,CAAE2I,MAAO,QAAS3I,MAAO,SACzB,CAAE2I,MAAO,4BAA0B3I,MAAO,6BAC1C,CAAE2I,MAAO,cAAe3I,MAAO,eAC/B,CAAE2I,MAAO,oBAAqB3I,MAAO,qBACrC,CAAE2I,MAAO,aAAc3I,MAAO,cAC9B,CAAE2I,MAAO,OAAQ3I,MAAO,QAUvB,CArJC+P,iBACF,MAAM3M,EAAU5M,KAAKgJ,MAAM4D,SAASqE,QAAU,GACxCjH,EAAShK,KAAKgJ,MAAMgB,QAAQiH,QAAU,GAE5C,OAAIrE,GAAW5C,EACN,GAAG4C,OAAa5C,IACd4C,GAGF,EAEX,CAEI2M,eAAWnX,GACb,IAAKA,EAGH,OAFApC,KAAKgJ,KAAK4D,QAAU,QACpB5M,KAAKgJ,KAAKgB,OAAS,IAKrB,GAAI5H,EAAMmN,SAAS,OAAQ,CACzB,MAAMiK,EAAQpX,EAAMqX,MAAM,OAC1BzZ,KAAKgJ,KAAK4D,QAAU4M,EAAM,GAAGvI,OAC7BjR,KAAKgJ,KAAKgB,OAASwP,EAAME,MAAM,GAAGC,KAAK,OAAO1I,YAG9CjR,KAAKgJ,KAAK4D,QAAUxK,EAAM6O,MAG9B,CAyHAxO,WAEEwE,QAAQC,IAAI,gDAA0ClH,KAAKgJ,KAAK4D,QAAS,eAAgB5M,KAAKgJ,KAAK4D,SAGnG5M,KAAK8K,MAAMwF,YAAYpF,UAAUxC,IAC/B1I,KAAKsJ,SAAWZ,EAAOY,UAAe,GACtCrC,QAAQC,IAAI,mBAAoBlH,KAAKsJ,SAAQ,GAI/C,MAAMsQ,EAAe5Z,KAAK2Y,qBAAqBP,WAC3CwB,GACF5Z,KAAKmY,eAAiByB,EAAa1B,MACnClY,KAAKsJ,SAAWsQ,EAAatQ,SAC7BtJ,KAAK6Z,yCAEL5S,QAAQ6S,KAAK,kDAGT9Z,KAAKsJ,UACPrC,QAAQC,IAAI,4EACZlH,KAAK+Z,8BAGL/Z,KAAK+K,OAAOsF,SAAS,CAAC,eAK1BrQ,KAAKga,sBAGDha,KAAKgJ,KAAKpB,WACZ5H,KAAKia,kBAAoBja,KAAKuV,iBAAiBvV,KAAKgJ,KAAKpB,WAI3DX,QAAQC,IAAI,gDAA0ClH,KAAKgJ,KAAK4D,QAAS,eAAgB5M,KAAKgJ,KAAK4D,QACrG,CAKQiN,uCACN,IAAK7Z,KAAKmY,iBAAmBnY,KAAKmY,eAAe+B,KAE/C,YADAjT,QAAQ6S,KAAK,gEAA8D9Z,KAAKmY,gBAIlF,MAAM+B,EAAOla,KAAKmY,eAAe+B,KAEjCla,KAAKgJ,KAAKkH,gBAAkBgK,EAAKC,WAAaD,EAAK5Q,UAAY,GAC/DtJ,KAAKgJ,KAAK4D,QAAUsN,EAAKC,WAAaD,EAAK5Q,UAAY,GACvDtJ,KAAKgJ,KAAK+G,gBAAkBmK,EAAK5Q,UAAY,GAC7CtJ,KAAKgJ,KAAKuI,QAAU2I,EAAKE,cAAgB,GACzCpa,KAAKgJ,KAAK+K,SAAWmG,EAAKG,WAAa,GACvCra,KAAKgJ,KAAKoQ,UAAYc,EAAKG,WAAa,GACxCra,KAAKgJ,KAAKpB,SAAWsS,EAAKI,uBAAyB,GAG/Cta,KAAKgJ,KAAKpB,WACZ5H,KAAKia,kBAAoBja,KAAKuV,iBAAiBvV,KAAKgJ,KAAKpB,WAG3DX,QAAQC,IAAI,mDAAiDlH,KAAKgJ,MAGlE/B,QAAQC,IAAI,wEAAkElH,KAAKgJ,KAAK4D,QAAS,eAAgB5M,KAAKgJ,KAAK4D,QAC7H,CAMQ2N,qCAAqCC,GACtCA,GAKLvT,QAAQC,IAAI,wEAAsEsT,GAGlFxa,KAAKya,kBAAoBD,EAGzBxa,KAAKgJ,KAAKkH,gBAAkBsK,EAAetK,iBAAmB,GAC9DlQ,KAAKgJ,KAAK4D,QAAU4N,EAAe5N,SAAW,GAC9C5M,KAAKgJ,KAAKgB,OAASwQ,EAAexQ,QAAU,GAC5ChK,KAAKgJ,KAAKkQ,SAAWsB,EAAetB,UAAY,GAChDlZ,KAAKgJ,KAAKpB,SAAW4S,EAAe5S,UAAY,GAG5C5H,KAAKgJ,KAAKpB,WACZ5H,KAAKia,kBAAoBja,KAAKuV,iBAAiBvV,KAAKgJ,KAAKpB,WAI3DX,QAAQC,IAAI,wEAAkElH,KAAKgJ,KAAK4D,QAAS,eAAgB5M,KAAKgJ,KAAK4D,SAGvH4N,EAAetB,UACjBjS,QAAQC,IAAI,yDAAiDsT,EAAetB,UAE9ElZ,KAAKgJ,KAAK+G,gBAAkByK,EAAezK,iBAAmB,GAC9D/P,KAAKgJ,KAAKuI,QAAUiJ,EAAejJ,SAAYiJ,EAAepJ,eAAeG,SAAW,GACxFvR,KAAKgJ,KAAK+K,SAAWyG,EAAezG,UAAY,GAChD/T,KAAKgJ,KAAKoQ,UAAYoB,EAAezG,UAAY,GACjD/T,KAAKgJ,KAAK0I,OAAS8I,EAAe9I,QAAU,YAC5C1R,KAAKgJ,KAAKkD,MAAQsO,EAAetO,OAAS,mBAC1ClM,KAAKgJ,KAAKqF,MAAQmM,EAAenM,OAAS,EAIxCrO,KAAKgJ,KAAK6D,SADR2N,EAAepJ,eAAesJ,iBACX1a,KAAK2a,eAAeH,EAAepJ,cAAcsJ,kBAEjD,oBAInBF,EAAe/Q,eACjBzJ,KAAKgJ,KAAKS,aAAe+Q,EAAe/Q,cAItC+Q,EAAeI,WAAajK,MAAMC,QAAQ4J,EAAeI,aAC3D5a,KAAK6a,qBAAuBL,EAAeI,UAAU/J,IAAI,CAACjJ,EAAeuP,MACvErO,GAAI,QAAQqO,IACZ9P,KAAMO,EAASP,KACfyT,OAAQlT,EAASkT,OACjBlN,UAAWhG,EAASgG,WAAa,GACjCmN,gBAAiB/a,KAAKuV,iBAAiB3N,EAASkT,QAChD1I,MAAOpS,KAAKgb,iBAAiBpT,EAASP,MACtCgL,IAAKrS,KAAKib,eAAerT,EAASP,SAEpCJ,QAAQC,IAAI,qCAAsClH,KAAK6a,uBAIrDL,EAAe3Q,OAAS8G,MAAMC,QAAQ4J,EAAe3Q,SACvD7J,KAAK8Y,iBAAmB0B,EAAe3Q,MAAMgH,IAAI,CAACvG,EAAW6M,MAC3DrO,GAAIwB,EAAKxB,IAAM,QAAQqO,IACvB9P,KAAMiD,EAAKjD,KACXsC,IAAKW,EAAKX,IACViE,UAAWtD,EAAKsD,WAAa,GAC7BC,MAAOvD,EAAKuD,OAASsJ,EAAQ,EAC7BD,OAAsB,IAAf5M,EAAK4M,SAEdjQ,QAAQC,IAAI,iCAAkClH,KAAK8Y,kBAGnD9Y,KAAKkb,WAAWpC,iBAAmB,IAAI9Y,KAAK8Y,kBAG5C9Y,KAAKmb,kBAAmB,EAExBlU,QAAQC,IAAI,wCAAyClH,KAAK8Y,iBAAiBxM,SAIzEkO,EAAe5I,QACjB5R,KAAKgJ,KAAK2E,YAAc6M,EAAe5I,OAIrC4I,EAAepJ,gBACjBpR,KAAKmY,eAAiB,CACpB+B,KAAM,CACJ5Q,SAAUkR,EAAezK,gBACzBoK,UAAWK,EAAe5N,QAC1ByN,UAAWG,EAAezG,SAC1BuG,sBAAuBE,EAAe5S,SACtCwT,iBAAkB,CAAEC,MAAOb,EAAepJ,cAAckK,WACxDC,YAAa,CAAEF,MAAOb,EAAepJ,cAAcoK,WACnDC,oBAAkE,aAA7CjB,EAAepJ,cAAcsK,YAClDC,uBAAwBnB,EAAepJ,cAAcsJ,iBACrDN,aAAcI,EAAepJ,cAAcG,QAC3CqK,gBAAiBpB,EAAeqB,aAKtC5U,QAAQC,IAAI,gDAA8ClH,KAAKgJ,MAC/D/B,QAAQC,IAAI,8CAA+ClH,KAAKmY,iBA3G9DlR,QAAQ6S,KAAK,qCAAmCU,EA4GpD,CAKQG,eAAemB,GACrB,IAAKA,EAAW,MAAO,SAEvB,MAAMC,EAAiBD,EAAU/N,cAEjC,OAAIgO,EAAexM,SAAS,gBACxBwM,EAAexM,SAAS,WACxBwM,EAAexM,SAAS,SACxBwM,EAAexM,SAAS,UACxBwM,EAAexM,SAAS,WACxBwM,EAAexM,SAAS,YACxBwM,EAAexM,SAAS,QACxBwM,EAAexM,SAAS,eACxBwM,EAAexM,SAAS,YACnB,oBAGLwM,EAAexM,SAAS,SACxBwM,EAAexM,SAAS,WACxBwM,EAAexM,SAAS,QACnB,SAGLwM,EAAexM,SAAS,eACxBwM,EAAexM,SAAS,WACnB,cAGLwM,EAAexM,SAAS,aACxBwM,EAAexM,SAAS,WACxBwM,EAAexM,SAAS,cACxBwM,EAAexM,SAAS,cACnB,WAGLwM,EAAexM,SAAS,mBACxBwM,EAAexM,SAAS,cACxBwM,EAAexM,SAAS,WACxBwM,EAAexM,SAAS,SACnB,iBAGF,QACT,CAKcyK,sBAAmB,qCAC/B,IACE,MAAMpK,QAAiBF,EAAKwC,kBAAkBzJ,QAC1CmH,EAASoM,UACXtM,EAAKiD,YAAc/C,EAASsI,OAAS,UAEhC9H,GACPnJ,QAAQmJ,MAAM,iCAAkCA,GACjD,EAR8B,EASjC,CAKM6L,aAAU,qCACd,GAAKC,EAAKC,oBAIVD,GAAKnJ,YAAa,EAClBmJ,EAAKrJ,KAAO,GAGRqJ,EAAKjC,oBACPiC,EAAKlT,KAAKpB,SAAWsU,EAAKE,eAAeF,EAAKjC,oBAGhD,IAEE,IAAIoC,EAEJ,GAAIH,EAAKzB,kBAEP4B,EAAiB,IAAKH,EAAKzB,mBAG3B4B,EAAenM,gBAAkBgM,EAAKlT,KAAKkH,gBAC3CmM,EAAezP,QAAUsP,EAAKlT,KAAK4D,QACnCyP,EAAerS,OAASkS,EAAKlT,KAAKgB,OAClCqS,EAAenD,SAAWgD,EAAKlT,KAAKkQ,SACpCmD,EAAezU,SAAWsU,EAAKlT,KAAKpB,SACpCyU,EAAetM,gBAAkBmM,EAAKlT,KAAK+G,gBAC3CsM,EAAe9K,QAAU2K,EAAKlT,KAAKuI,QACnC8K,EAAelD,aAAe+C,EAAKlT,KAAKmQ,aACxCkD,EAAetI,SAAWmI,EAAKlT,KAAK+K,SACpCsI,EAAe3K,OAASwK,EAAKlT,KAAK0I,OAClC2K,EAAenQ,MAAQgQ,EAAKlT,KAAKkD,MACjCmQ,EAAexP,SAAWqP,EAAKlT,KAAK6D,SACpCwP,EAAe5S,aAAeyS,EAAKlT,KAAKS,aACxC4S,EAAe1O,YAAcuO,EAAKlT,KAAK2E,YACvC0O,EAAehD,kBAAoB6C,EAAKlT,KAAKqQ,kBAC7CgD,EAAe/C,YAAc4C,EAAKlT,KAAKsQ,mBAGhC+C,EAAevT,UACfuT,EAAe7K,mBACf6K,EAAeC,iBACfD,EAAeE,UAGlBL,EAAKpD,kBAAoBoD,EAAKpD,iBAAiBxM,OAAS,IAC1D+P,EAAexS,MAAQqS,EAAKpD,iBAC5B7R,QAAQC,IAAI,sDAAuDgV,EAAKpD,mBAItEoD,EAAKrB,sBAAwBqB,EAAKrB,qBAAqBvO,OAAS,IAClE+P,EAAezB,UAAYsB,EAAKrB,qBAAqBhK,IAAI2L,KACvDnV,KAAMmV,EAAInV,KACVyT,OAAQ0B,EAAI1B,OACZlN,UAAW4O,EAAI5O,aAEjB3G,QAAQC,IAAI,4CAA6CmV,EAAezB,YAItEsB,EAAKO,kBAAoBP,EAAKO,iBAAiBnQ,OAAS,IAC1D+P,EAAeI,iBAAmBP,EAAKO,iBACvCxV,QAAQC,IAAI,4CAA0CgV,EAAKO,iBAAiBnQ,aAGzE,CAKL,GAHA+P,EAAiB,IAAKH,EAAKlT,MAGvBkT,EAAK/D,gBAAkB+D,EAAK/D,eAAe+B,KAAM,CACnD,MAAMA,EAAOgC,EAAK/D,eAAe+B,KAGjCmC,EAAeR,UAAY3B,EAAK0B,gBAGhCS,EAAejL,cAAgB,CAC7BuF,IAAKuD,EAAKG,UACViB,UAAWpB,EAAKkB,kBAAkBC,MAClCG,UAAWtB,EAAKqB,aAAaF,MAC7BK,YAAaxB,EAAKuB,oBAAsB,WAAa,UACrDf,iBAAkBR,EAAKyB,wBAA0BzB,EAAKwC,cACtDrL,SAAU6I,EAAKyC,sBAAwB9H,KAAKC,UAAUoF,EAAKyC,4BAAyBxM,EACpFoB,QAAS2I,EAAKE,cAKd8B,EAAKpD,kBAAoBoD,EAAKpD,iBAAiBxM,OAAS,IAC1D+P,EAAexS,MAAQqS,EAAKpD,iBAC5B7R,QAAQC,IAAI,kDAAmDgV,EAAKpD,mBAIlEoD,EAAKrB,sBAAwBqB,EAAKrB,qBAAqBvO,OAAS,IAClE+P,EAAezB,UAAYsB,EAAKrB,qBAAqBhK,IAAI2L,KACvDnV,KAAMmV,EAAInV,KACVyT,OAAQ0B,EAAI1B,OACZlN,UAAW4O,EAAI5O,aAEjB3G,QAAQC,IAAI,wCAAyCmV,EAAezB,YAIlEsB,EAAKO,kBAAoBP,EAAKO,iBAAiBnQ,OAAS,IAC1D+P,EAAeI,iBAAmBP,EAAKO,iBACvCxV,QAAQC,IAAI,mDAAiDgV,EAAKO,iBAAiBnQ,SAIvFrF,QAAQC,IAAI,iBAAkBmV,GAC9BpV,QAAQC,IAAI,0BAA2BgV,EAAKU,mBAC5C3V,QAAQC,IAAI,iDAAkDmV,EAAexS,OAAOyC,QAAU,GAE1F+P,EAAexS,OAASwS,EAAexS,MAAMyC,OAAS,IACxDrF,QAAQC,IAAI,mCACZmV,EAAexS,MAAMgT,QAAQ,CAACvS,EAAW6M,KACvClQ,QAAQC,IAAI,KAAKiQ,EAAQ,MAAM7M,EAAKjD,SAASiD,EAAKX,QAAQW,EAAKsD,aAAY,IAK3EsO,EAAKU,oBACPP,EAAeO,mBAAoB,GAGrC,MAAME,QAAmBZ,EAAKlR,YAAYjC,UAAUsT,GAEpDpV,QAAQC,IAAI,2BAA4B4V,GAGxCZ,EAAKvD,qBAAqBN,aAG1B6D,EAAKnR,OAAOsF,SAAS,CAAC,YAAa6L,EAAK5S,iBACjC8G,GACPnJ,QAAQmJ,MAAM,uBAAwBA,GACtC8L,EAAKrJ,KAAO,+CAEZqJ,EAAKnJ,YAAa,GACnB,EA/Ia,EAgJhB,CAKQoJ,oBACN,OAAKnc,KAAKgJ,KAAKkH,gBAIVlQ,KAAKgJ,KAAK4D,QAIV5M,KAAKgJ,KAAKgB,SAIVhK,KAAKgJ,KAAK+G,kBACb/P,KAAK6S,KAAO,6CACL,IALP7S,KAAK6S,KAAO,yCACL,IALP7S,KAAK6S,KAAO,uCACL,IALP7S,KAAK6S,KAAO,8CACL,EAeX,CAKA6B,WACE1U,KAAK2Y,qBAAqBN,aAC1BrY,KAAK+K,OAAOsF,SAAS,CAAC,YAAarQ,KAAKsJ,UAC1C,CAKAyT,yBACE,OAAI/c,KAAKmY,gBAAgB+B,MAAM0B,gBACtB5b,KAAKmY,eAAe+B,KAAK0B,gBAE3B,mCACT,CAKAoB,yBACE,MAAM3B,EAAQrb,KAAKmY,gBAAgB+B,MAAMkB,kBAAkBC,MAC3D,OAAKA,EAEDA,GAAS,IACJ,IAAIA,EAAQ,KAAS4B,QAAQ,MAC3B5B,GAAS,IACX,IAAIA,EAAQ,KAAM4B,QAAQ,MAE5B5B,EAAMtX,WAPM,GAQrB,CAKAmZ,0BAEE,MAAMlI,EAAU,CACd3N,KAAM,yBACNiC,SAAUtJ,KAAKsJ,UAIbzC,OAAOsW,QAAUtW,OAAOsW,SAAWtW,QACrCA,OAAOsW,OAAOC,YAAY,CAAC/V,KAAM,gBAAiBgW,KAAMrI,GAAU,KAGpE/N,QAAQC,IAAI,0DAAqD8N,EACnE,CAKQ+E,6BACNlT,OAAOC,iBAAiB,UAAYC,IAE9BA,EAAMC,MAA4B,4BAApBD,EAAMC,KAAKK,OAC3BJ,QAAQC,IAAI,gCAAiCH,EAAMC,MACnDhH,KAAKsd,wBAAwBvW,EAAMC,OAIjCD,EAAMC,MAA4B,yBAApBD,EAAMC,KAAKK,OAC3BJ,QAAQmJ,MAAM,qCAAsCrJ,EAAMC,MAC1DhH,KAAKud,uBAAuBxW,EAAMC,MAAI,EAG5C,CAKcsW,wBAAwBnF,GAAmB,qCACvDlR,QAAQC,IAAI,gDAAiDiR,EAAe7O,UAE5E,IAEErC,QAAQC,IAAI,oDACZsW,EAAKzK,YAAa,EAElB,MAAMyH,QAAuBgD,EAAKxS,YAAYzB,qBAAqB4O,EAAesF,WAAY,KAAMtF,EAAe7O,UAEnHrC,QAAQC,IAAI,qCAAsCsT,GAE9CA,GAEFvT,QAAQC,IAAI,sCAAuCsT,GAGnDgD,EAAKlU,SAAW6O,EAAe7O,SAI/BkU,EAAKjD,qCAAqCC,GAG1CgD,EAAK7E,qBAAqBV,SAASuF,EAAKrF,eAAgBqF,EAAKlU,UAE7DrC,QAAQC,IAAI,8CAA4CsW,EAAKxU,MAC7D/B,QAAQC,IAAI,iCAAkCsW,EAAKrF,kBAEnDlR,QAAQmJ,MAAM,wCACdoN,EAAK3K,KAAO,sDAEPzC,GACPnJ,QAAQmJ,MAAM,0CAA2CA,GACzDoN,EAAK3K,KAAO,iEAEZ2K,EAAKzK,YAAa,EACnB,EArCsD,EAsCzD,CAKc2K,4BAA4BvF,GAAmB,qCAC3D,IACElR,QAAQC,IAAI,wCAAyCiR,EAAenR,MAEpE,MAAM4I,QAAiB+N,EAAK3S,YAAYzB,qBAAqB4O,EAAenR,KAAM,KAAMmR,EAAe7O,UAEnGsG,EAASoM,QACX/U,QAAQC,IAAI,sCAAuC0I,EAASsI,OAE5DjR,QAAQmJ,MAAM,2BAA4BR,EAASiD,YAE9CzC,GACPnJ,QAAQmJ,MAAM,8CAA+CA,GAC9D,EAb0D,EAc7D,CAKQmN,uBAAuBK,GAC7B3W,QAAQmJ,MAAM,qCAAsCwN,EAAUxN,OAC9DpQ,KAAK6S,KAAO,qDACd,CAKAgL,0BACE7d,KAAK8d,mBAAoB,EACzB7W,QAAQC,IAAI,+BACd,CAKA6W,gBACE,GAAI/d,KAAKgJ,KAAKuI,QAAS,CACrB,MAAMyM,EAAche,KAAKgJ,KAAKuI,QAC9BvR,KAAKgJ,KAAKuI,QAAUvR,KAAKie,cAAcje,KAAKgJ,KAAKuI,SAE7CyM,IAAgBhe,KAAKgJ,KAAKuI,SAC5BtK,QAAQC,IAAI,mBAAoB8W,EAAa,KAAMhe,KAAKgJ,KAAKuI,SAGnE,CAKA2M,iBAEEle,KAAKgJ,KAAKpB,SAAW5H,KAAKoc,eAAepc,KAAKia,mBAC9ChT,QAAQC,IAAI,sBAAuBlH,KAAKia,kBAAmB,YAAaja,KAAKgJ,KAAKpB,SACpF,CAKAuW,iBAAiB/b,GAEfpC,KAAKgJ,KAAKpB,SAAW5H,KAAKoc,eAAeha,GAAS,GACpD,CAKAgc,gBAAgBrX,GACdA,EAAMsX,iBAEN,MAAMC,EAAgBvX,EAAMuX,eAAeC,QAAQ,SAAW,GAC9DtX,QAAQC,IAAI,mBAAoBoX,GAGhC,MAAME,EAAmBxe,KAAKye,wBAAwBH,GAGtDte,KAAKgJ,KAAKpB,SAAW4W,EACrBxe,KAAKia,kBAAoBja,KAAKuV,iBAAiBiJ,GAE/CvX,QAAQC,IAAI,uBAAwBlH,KAAKia,mBAGzCja,KAAK4Y,IAAI8F,eACX,CAKQtC,eAAexU,GACrB,OAAKA,EACEA,EAAS9D,QAAQ,MAAO,IADT,EAExB,CAKQ2a,wBAAwBtM,GAE9B,IAAI3C,EAAc2C,EAAMrO,QAAQ,MAAO,IAGvC,OAAI0L,EAAYd,WAAW,OAASc,EAAYlD,OAAS,KACvDkD,EAAcA,EAAYmP,UAAU,GACpC1X,QAAQC,IAAI,8CAAyCsI,IAInDA,EAAYlD,OAAS,KACvBkD,EAAcA,EAAYmP,UAAUnP,EAAYlD,OAAS,IACzDrF,QAAQC,IAAI,+DAA0DsI,IAGjEA,CACT,CAKMoP,sBAAmB,qCACvB,GAAKC,EAAK7V,KAAKuI,QAAf,CAMAsN,EAAK7V,KAAKuI,QAAUsN,EAAKZ,cAAcY,EAAK7V,KAAKuI,SAGjD,IACE,IAAIuN,IAAID,EAAK7V,KAAKuI,eAGlB,YADAsN,EAAKhM,KAAO,0DAIdgM,EAAKE,mBAAoB,EACzBF,EAAKhM,KAAO,GACZgM,EAAKG,yBAEL,IACE/X,QAAQC,IAAI,+BAAgC2X,EAAK7V,KAAKuI,SAGtD,MAAM0N,QAAqBJ,EAAK7T,YAAYtB,gBAAgBmV,EAAK7V,KAAKuI,SAEtE,GAAI0N,EAAc,CAChBhY,QAAQC,IAAI,8BAA+B+X,GAGvCA,EAAaC,kBAAoBvO,MAAMC,QAAQqO,EAAaC,mBAC9DL,EAAK/F,iBAAmBmG,EAAaC,iBAAiBpO,KAAK,CAACC,EAAQC,IAAWD,EAAElD,MAAQmD,EAAEnD,OAC3F5G,QAAQC,IAAI,wCAAyC2X,EAAK/F,kBAC1D7R,QAAQC,IAAI,gCAAiC2X,EAAK/F,iBAAiBxM,QAGnEuS,EAAK/F,iBAAiB+D,QAAQ,CAACvS,EAAW6M,KACxClQ,QAAQC,IAAI,QAAQiQ,EAAQ,MAAM7M,EAAKjD,UAAUiD,EAAKX,QAAQW,EAAKsD,aAAY,KAGjF3G,QAAQ6S,KAAK,mEAAiEmF,EAAaC,kBAC3FL,EAAK/F,iBAAmB,IAI1B,MAAMqG,EAAeN,EAAK/F,iBAAiBjJ,KAAMvF,GAA4B,aAAdA,EAAKjD,MACpE,GAAI8X,IAAiBN,EAAK7V,KAAKpB,SAAU,CAEvC,MAAMwX,EAAcD,EAAaxV,IAAI0V,MAAM,eACvCD,IACFP,EAAK7V,KAAKpB,SAAWwX,EAAY,GAEjCP,EAAK5E,kBAAoB4E,EAAKtJ,iBAAiBsJ,EAAK7V,KAAKpB,WAK7D,MAAM0X,EAAgBT,EAAK/F,iBAAiBjJ,KAAMvF,GAA4B,cAAdA,EAAKjD,MACrE,GAAIiY,IAAkBT,EAAK7V,KAAK+G,gBAAiB,CAE/C,MAAMwP,EAAgBD,EAAc3V,IAAI0V,MAAM,8BAC1CE,IACFV,EAAK7V,KAAK+G,gBAAkBwP,EAAc,IAK9C,MAAMC,EAAeX,EAAK/F,iBAAiBjJ,KAAMvF,GACjC,wBAAdA,EAAKjD,MAA6C,gBAAdiD,EAAKjD,MAEvCmY,IACFX,EAAK7V,KAAKmQ,aAAeqG,EAAa7V,IACtC1C,QAAQC,IAAI,+CAA4CsY,EAAanY,UAAUmY,EAAa7V,QAG9F1C,QAAQC,IAAI,gDAA8C2X,EAAK7V,WAE/D6V,EAAKhM,KAAO,kEAEPzC,GACPnJ,QAAQmJ,MAAM,qCAAsCA,GACpDyO,EAAKhM,KAAO,yEAEZgM,EAAKE,mBAAoB,QAlFzBF,EAAKhM,KAAO,0CAmFb,EArFsB,EAsFzB,CAKQoL,cAActU,GAOpB,OANKA,IAGLA,EAAMA,EAAIsH,QAGFoO,MAAM,iBACL1V,EAIF,WAAWA,GACpB,CAKA6N,UAAU7N,GACR,MAAM8V,EAAiBzf,KAAKie,cAActU,GAC1C9C,OAAO6Q,KAAK+H,EAAgB,UAC5BxY,QAAQC,IAAI,gBAAiByC,EAAK,kBAAmB8V,EACvD,CAKM3V,gBAAa,qCACjB,GAAK4V,EAAK1W,KAAK4D,QAKf,GAAK8S,EAAK1W,KAAKgB,OAAf,CAKA0V,EAAKC,gBAAiB,EACtBD,EAAK7M,KAAO,GACZ6M,EAAKE,iBAAmB,GACxBF,EAAKG,4BAA6B,EAClCH,EAAKI,oBAEL,IACE7Y,QAAQC,IAAI,kCAAmCwY,EAAK1W,KAAK4D,QAAS,KAAM8S,EAAK1W,KAAKgB,QAGlF,MAAM4F,QAAiB8P,EAAK1U,YAAYlB,cAAc4V,EAAK1W,KAAK4D,QAAS8S,EAAK1W,KAAKgB,QAEnF/C,QAAQC,IAAI,iCAAkC0I,GAC9C3I,QAAQC,IAAI,2BAA4B0I,GACxC3I,QAAQC,IAAI,yBAA0BsJ,OAAOC,KAAKb,GAAY,KAG9D,MAAMsI,EAAQtI,EAEd3I,QAAQC,IAAI,sBAAoBgR,GAChCjR,QAAQC,IAAI,qBAAsBgR,GAAO0H,kBACzC3Y,QAAQC,IAAI,qBAAsBgR,GAAO6H,kBAGzC9Y,QAAQC,IAAI,kBAAmBgR,GAC/BjR,QAAQC,IAAI,mCAAoCgR,GAAO0H,kBACvD3Y,QAAQC,IAAI,qCAAmCyJ,MAAMC,QAAQsH,GAAO0H,mBACpE3Y,QAAQC,IAAI,mBAAoBgR,GAAO0H,kBAAkBtT,QAErD4L,GAASA,EAAM0H,kBAAoB1H,EAAM0H,iBAAiBtT,OAAS,GACrEoT,EAAKE,iBAAmB1H,EAAM0H,iBAAiB/O,IAAI,CAAC3G,EAAWiN,SAC1DjN,EACHpB,GAAI,QAAQqO,IACZ6I,aAAa,KAGf/Y,QAAQC,IAAI,qBAAsBgR,EAAM6H,kBACxC9Y,QAAQC,IAAI,6BAA8BwY,EAAKE,kBAC/C3Y,QAAQC,IAAI,uCAAwCwY,EAAKE,iBAAiBtT,QAC1ErF,QAAQC,IAAI,iBAAkBwY,EAAKE,iBAAiB,IAGpDF,EAAK9G,IAAI8F,gBAETrT,WAAW,KACTpE,QAAQC,IAAI,6CAA2CwY,EAAKE,iBAAiBtT,QAC7EoT,EAAK9G,IAAI8F,eAAa,EACrB,KAG4B,IAA3BxG,EAAM6H,kBAAkE,SAAxC7H,EAAM0H,iBAAiB,GAAGK,YAC5DP,EAAKQ,eAAeR,EAAKE,iBAAiB,IAC1C3Y,QAAQC,IAAI,sEAGdD,QAAQ6S,KAAK,kCAA6B,CACxC5B,QAASA,EACT0H,mBAAoB1H,GAAO0H,iBAC3BhP,QAASD,MAAMC,QAAQsH,GAAO0H,kBAC9BtT,OAAQ4L,GAAO0H,kBAAkBtT,OACjC6T,eAAgBjI,IAGlBwH,EAAK7M,KAAO,gCAAgC6M,EAAK1W,KAAK4D,eAAe8S,EAAK1W,KAAKgB,sFAC/E/C,QAAQ6S,KAAK,wDAER1J,GACPnJ,QAAQmJ,MAAM,2BAA4BA,GAC1CsP,EAAK7M,KAAO,yBAAyBzC,YAErCsP,EAAKC,gBAAiB,QA1EtBD,EAAK7M,KAAO,yDALZ6M,EAAK7M,KAAO,iDAgFb,EAlFgB,EAmFnB,CAKAqN,eAAenH,GAyBb,GAvBA/Y,KAAK4f,iBAAiB/C,QAAQ3S,IAC5BA,EAAK8V,YAAc9V,EAAKpB,KAAOiQ,EAAgBjQ,KAIjD9I,KAAKgJ,KAAKkB,KAAO6O,EAAgB7O,KAGjClK,KAAKogB,uBAAyBrH,EAAgBsH,aAAe,GAG7DrgB,KAAK6f,4BAA6B,EAClC7f,KAAK6S,KAAO,GAIZ5L,QAAQC,IAAI,yCAA0ClH,KAAKgJ,KAAK4D,SAC5DmM,EAAgBuH,cAAgBvH,EAAgBuH,eAAiBtgB,KAAKgJ,KAAK4D,UAC7E3F,QAAQC,IAAI,wCAAsC6R,EAAgBuH,cAClErZ,QAAQC,IAAI,gCAAiClH,KAAKgJ,KAAK4D,UAIrDmM,EAAgBsH,YAAa,CAC/B,MAAME,EAAkB,oBAAiBxH,EAAgBsH,cACpDrgB,KAAKgJ,KAAK2E,YAEH3N,KAAKgJ,KAAK2E,YAAY4B,SAAS,sBACzCvP,KAAKgJ,KAAK2E,YAAc,GAAG4S,QAAsBvgB,KAAKgJ,KAAK2E,eAF3D3N,KAAKgJ,KAAK2E,YAAc4S,EAOxBxH,EAAgBG,WAAalZ,KAAKgJ,KAAKkQ,WACzClZ,KAAKgJ,KAAKkQ,SAAWH,EAAgBG,SACrCjS,QAAQC,IAAI,0CAAwC6R,EAAgBG,WAGtEjS,QAAQC,IAAI,oBAAqB6R,EAAgB7O,MACjDjD,QAAQC,IAAI,sCAAoC,CAC9CgD,KAAMlK,KAAKgJ,KAAKkB,KAChB0C,QAAS5M,KAAKgJ,KAAK4D,QAAU,0BAC7BsM,SAAUlZ,KAAKgJ,KAAKkQ,UAExB,CAKAsH,qBACExgB,KAAK4f,iBAAiB/C,QAAQ3S,IAC5BA,EAAK8V,aAAc,IAErBhgB,KAAKgJ,KAAKkB,KAAO,GACjBlK,KAAKogB,uBAAyB,GAC9BpgB,KAAK6f,4BAA6B,EAClC5Y,QAAQC,IAAI,iCACd,CAKAuZ,kBAAkB1Z,GAChB,IAAIoL,EAAQpL,EAAM2Z,OAAOte,MAAM0B,QAAQ,MAAO,IAG1CqO,EAAM7F,QAAU,KAClB6F,EAAQA,EAAMrO,QAAQ,cAAe,SACrCqO,EAAQA,EAAMrO,QAAQ,uBAAwB,YAC9CqO,EAAQA,EAAMrO,QAAQ,gBAAiB,UACvCqO,EAAQA,EAAMrO,QAAQ,cAAe,UAIvCiD,EAAM2Z,OAAOte,MAAQ+P,EACrBnS,KAAK2gB,WAAaxO,CACpB,CAKMyO,mBAAgB,qCACpB,IAAKC,EAAKF,WAER,YADAE,EAAKhO,KAAO,kDAKd,MAAMiO,EAAYD,EAAKF,WAAW7c,QAAQ,MAAO,IACjD,GAAyB,KAArBgd,EAAUxU,OAAd,CAKAuU,EAAKE,sBAAuB,EAC5BF,EAAKhO,KAAO,GAEZ,IACE5L,QAAQC,IAAI,mCAAoC2Z,EAAKF,YAGrD,MAAM/Q,QAAiBiR,EAAK7V,YAAYf,qBAAqB6W,GAE7D,GAAIlR,GAAYA,EAAShD,QAAS,CAEhC,MAAMoU,EAAiB,CACrBlY,GAAI,cACJoB,KAAM4W,EACNG,cAAeJ,EAAKF,WACpBL,aAAc1Q,EAAShD,QAAQ0T,cAAgB1Q,EAAShD,QAAQyT,aAAe,wBAC/EA,YAAazQ,EAAShD,QAAQyT,aAAe,GAC7CnH,SAAUtJ,EAAShD,QAAQsM,UAAY,GACvCgI,cAAetR,EAAShD,QAAQsU,eAAiB,GACjDC,MAAOvR,EAAShD,QAAQuU,OAAS,GACjCC,SAAUxR,EAAShD,QAAQwU,UAAY,QACvCC,mBAAoBzR,EAAShD,QAAQ0U,kBAAoB1R,EAAShD,QAAQyU,oBAAsB,GAChGE,OAAQ3R,EAAS2R,OAAS3R,EAAS2R,OAAO1Q,IAAK2Q,GAAWA,EAAE9Z,MAAMgS,MAAM,EAAG,GAAK,GAChFuG,UAAW,SACXwB,MAAO,wBACPzB,aAAa,GAIfa,EAAKjB,iBAAmB,CAACoB,GAGzBH,EAAKX,eAAec,GAEpB/Z,QAAQC,IAAI,oCAAqC8Z,GAGjDH,EAAKF,WAAa,GAGlBE,EAAKjI,IAAI8F,qBAGTmC,EAAKhO,KAAO,qFACZ5L,QAAQ6S,KAAK,6CAA8CgH,SAEtD1Q,GACPnJ,QAAQmJ,MAAM,8BAA+BA,GAC7CyQ,EAAKhO,KAAO,iFAEZgO,EAAKE,sBAAuB,QAtD5BF,EAAKhO,KAAO,6BAuDb,EAhEmB,EAiEtB,CAKA6O,kBACE,IAAK1hB,KAAK2gB,WAER,YADA1Z,QAAQ6S,KAAK,qDAIG9Z,KAAK2gB,WAAW7c,QAAQ,MAAO,IAAjD,MAEM6d,EAAY,mCAAmClS,mBADlC,QAAQzP,KAAK2gB,gBAGhC1Z,QAAQC,IAAI,kCAAmClH,KAAK2gB,YACpD9Z,OAAO6Q,KAAKiK,EAAW,SACzB,CAKAC,yBACE,IAAK5hB,KAAKgJ,KAAK4D,QAEb,YADA3F,QAAQ6S,KAAK,yDAIf,MAAM+H,EAAa,GAAG7hB,KAAKgJ,KAAK4D,eAC1B+U,EAAY,mCAAmClS,mBAAmBoS,KAExE5a,QAAQC,IAAI,qCAAsC2a,GAClDhb,OAAO6Q,KAAKiK,EAAW,SACzB,CAKAG,6BACE,IAAK9hB,KAAKgJ,KAAK4D,QAEb,YADA3F,QAAQ6S,KAAK,yDAIf,IAAI+H,EAAa,GAAG7hB,KAAKgJ,KAAK4D,UAG1B5M,KAAKgJ,KAAKgB,SACZ6X,GAAc,IAAI7hB,KAAKgJ,KAAKgB,UAG9B6X,GAAc,QAEd,MAAMF,EAAY,mCAAmClS,mBAAmBoS,KAExE5a,QAAQC,IAAI,iCAAkC2a,GAC9Chb,OAAO6Q,KAAKiK,EAAW,SACzB,CAKAI,4BAGE9a,QAAQC,IAAI,oCACZL,OAAO6Q,KAHY,qFAGK,SAC1B,CAKAsK,2BAIMhiB,KAAKgJ,KAAKkB,MACZ+X,UAAUC,UAAUC,UAAUniB,KAAKgJ,KAAKkB,MACrCqI,KAAK,KACJtL,QAAQC,IAAI,iDAA4ClH,KAAKgJ,KAAKkB,KAAI,GAGvE0I,MAAMwP,IACLnb,QAAQmJ,MAAM,uBAAwBgS,EAAG,GAI/Cnb,QAAQC,IAAI,0DACZL,OAAO6Q,KAfY,qFAeK,SAC1B,CAKA2K,uBACE,IAAKriB,KAAKsiB,iBAAiBrR,OACzB,OAGF,MAAMsR,EAAc,CAClB7a,KAAM1H,KAAKsiB,gBAAgBrR,OAC3BuR,IAAK,KACLC,aAAc,KACd9V,MAAO,WACP+V,YAAa,KACbC,aAAc,KACdC,WAAW,EACXjV,YAAa,wBACbkV,aAAc,GACdC,cAAe,wCACfC,QAAQ,GAIV/iB,KAAKyc,iBAAmB,CAAC8F,GAGzBviB,KAAKgJ,KAAKkH,gBAAkBqS,EAAY7a,KACxC1H,KAAKgjB,oBAAqB,EAG1BhjB,KAAKsiB,gBAAkB,GAGvBtiB,KAAK4Y,IAAI8F,gBAETzX,QAAQC,IAAI,mCAAiCqb,EAAY7a,MACzDT,QAAQC,IAAI,uCAAqClH,KAAKgJ,KAAKkH,gBAC7D,CAKA+S,yBAAyB/Y,GAEvB,MAAM2X,EAAa,GADC3X,EAAKoW,cAAgBpW,EAAKmW,aAAe,kBACnBnW,EAAKA,OACzCyX,EAAY,mCAAmClS,mBAAmBoS,KAExE5a,QAAQC,IAAI,4CAA6C2a,GACzDhb,OAAO6Q,KAAKiK,EAAW,SACzB,CAMAuB,sBACEljB,KAAK6f,4BAA6B,EAClC7f,KAAKwgB,qBACLxgB,KAAK6S,KAAO,GACZ5L,QAAQC,IAAI,4DACd,CAKMic,WAAWjZ,GAAY,0BAC3B,UACQ+X,UAAUC,UAAUC,UAAUjY,GACpCjD,QAAQC,IAAI,gBAAiBgD,SACtBkG,GACPnJ,QAAQmJ,MAAM,uBAAwBA,GACvC,EAN0B,EAO7B,CAKAgT,aAAalZ,GAGX,MAAMP,EAAM,oBADMO,EAAKpG,QAAQ,SAAU,MAEzC+C,OAAO6Q,KAAK/N,EAAK,UACjB1C,QAAQC,IAAI,yBAA0BgD,EAAM,UAAWP,EACzD,CAKA0Z,gBAAgBpD,GACd,OAAQA,OACD,OAAQ,MAAO,cACf,QAAS,MAAO,cAChB,QAAS,MAAO,iBACZ,MAAO,YAEpB,CAKAqD,kBAAkBrD,GAChB,OAAQA,OACD,OAAQ,MAAO,sBACf,QAAS,MAAO,4BAChB,QAAS,MAAO,0BACZ,MAAO,qBAEpB,CAKMsD,WAAW5Z,GAAW,qCAC1B,IACE,MAAM8V,EAAiB+D,EAAKvF,cAActU,SACpCsY,UAAUC,UAAUC,UAAU1C,GACpCxY,QAAQC,IAAI,gBAAiByC,EAAK,kBAAmB8V,SAE9CrP,GACPnJ,QAAQmJ,MAAM,uBAAwBA,GAEtC,MAAMqP,EAAiB+D,EAAKvF,cAActU,GACpC8Z,EAAWC,SAASlgB,cAAc,YACxCigB,EAASrhB,MAAQqd,EACjBiE,SAASC,KAAK/f,YAAY6f,GAC1BA,EAASG,SACTF,SAASG,YAAY,QACrBH,SAASC,KAAKG,YAAYL,GAC3B,EAhByB,EAiB5B,CAKAlO,iBAAiBuF,GACf,IAAKA,EAAQ,MAAO,GAEpB,MAAMtL,EAAcsL,EAAOhX,QAAQ,MAAO,IAE1C,OAA2B,KAAvB0L,EAAYlD,OAEPkD,EAAY1L,QAAQ,yBAA0B,cACrB,KAAvB0L,EAAYlD,OAEdkD,EAAY1L,QAAQ,yBAA0B,cAGhDgX,CACT,CAKAE,iBAAiB3T,GACf,MAAM0c,EAAW/jB,KAAKgkB,cAAcnU,KAAKpR,GAAKA,EAAE0T,QAAU9K,GAC1D,OAAO0c,EAAWA,EAAS3R,MAAQ,UACrC,CAKA6I,eAAe5T,GACb,MAAM0c,EAAW/jB,KAAKgkB,cAAcnU,KAAKpR,GAAKA,EAAE0T,QAAU9K,GAC1D,OAAO0c,EAAWA,EAAS1R,IAAM,SACnC,CAKA4R,cAAcrc,GACZ,GAAsB,aAAlBA,EAASP,MAAyC,YAAlBO,EAASP,KAAoB,CAE/D,MAAM6c,EAAc,mBADAtc,EAASkT,OAAOhX,QAAQ,MAAO,MAEnD+C,OAAO6Q,KAAKwM,EAAa,eAGzBlkB,KAAKmkB,eAAevc,EAASkT,OAEjC,CAKMqJ,eAAerJ,GAAc,qCACjC,IACE,MAAMC,EAAkBqJ,EAAK7O,iBAAiBuF,SACxCmH,UAAUC,UAAUC,UAAUpH,GACpC9T,QAAQC,IAAI,oBAAqB6T,SAE1B3K,GACPnJ,QAAQmJ,MAAM,2BAA4BA,GAC3C,EARgC,EASnC,CAKMnG,uBAAoB,qCACxB,GAAKoa,EAAKrb,KAAKkB,KAAf,CAKAma,EAAKC,kBAAmB,EACxBD,EAAKxR,KAAO,GACZwR,EAAKE,gBAAiB,EACtBF,EAAKrB,oBAAqB,EAE1B,IACE/b,QAAQC,IAAI,gCAA8Bmd,EAAKrb,KAAKkB,MAIpD,MAAM0F,QAAiByU,EAAKrZ,YAAYf,qBAAqBoa,EAAKrb,KAAKkB,MAKvE,GAHAjD,QAAQC,IAAI,+CAA0C0I,GAGlDA,GAAYA,EAAS2R,OAAQ,CAsB/B,GArBAta,QAAQC,IAAI,mBAAoB0I,GAChC3I,QAAQC,IAAI,sBAAoB0I,EAAS2R,QAGrC5Q,MAAMC,QAAQhB,EAAS2R,QACzB8C,EAAK5H,iBAAmB7M,EAAS2R,QAEjCta,QAAQmJ,MAAM,wCAAmCR,EAAS2R,QAC1D8C,EAAK5H,iBAAmB,IAG1BxV,QAAQC,IAAI,mDAA8Cmd,EAAK5H,kBAC/DxV,QAAQC,IAAI,2BAAyBmd,EAAK5H,iBAAiBnQ,QAC3DrF,QAAQC,IAAI,kBAAmBmd,EAAKE,gBAGpCF,EAAK5H,iBAAiBI,QAAQ,CAAC2H,EAAOrN,KACpClQ,QAAQC,IAAI,YAASiQ,KAAUqN,EAAK,GAIlCH,EAAK5H,iBAAiBnQ,OAAS,EAAG,CACpC,MAAMmY,EAAiBJ,EAAK5H,iBAAiB5M,KAAK2U,GAASA,EAAM5B,WAE7D6B,GAEFJ,EAAKrb,KAAKkH,gBAAkBuU,EAAe/c,KAC3C2c,EAAKrB,oBAAqB,EAE1B/b,QAAQC,IAAI,6DAAoDud,EAAe/c,MAC/ET,QAAQC,IAAI,aAAaud,EAAe5B,oBACxC5b,QAAQC,IAAI,cAAcud,EAAe3B,iBACzC7b,QAAQC,IAAI,0CAAwCmd,EAAKrb,KAAKkH,mBAG9DmU,EAAK5H,iBAAiB,GAAGmG,WAAY,EACrCyB,EAAKrb,KAAKkH,gBAAkBmU,EAAK5H,iBAAiB,GAAG/U,KACrD2c,EAAKrB,oBAAqB,EAC1B/b,QAAQC,IAAI,oEAAwDmd,EAAK5H,iBAAiB,GAAG/U,OAI/F2c,EAAKzL,IAAI8F,gBAeX,GAXA2F,EAAKzL,IAAI8F,gBAGTrT,WAAW,KACTpE,QAAQC,IAAI,sCAAoCmd,EAAK5H,kBACrDxV,QAAQC,IAAI,4BAA0Bmd,EAAK5H,iBAAiBnQ,QAC5DrF,QAAQC,IAAI,0CAAwCmd,EAAKrb,KAAKkH,iBAC9DmU,EAAKzL,IAAI8F,eAAa,EACrB,KAGC9O,EAAShD,QAAS,CACpB3F,QAAQC,IAAI,uCAAkC0I,EAAShD,SAGvD,MAAM8X,EAAc9U,EAAShD,QAC7B,IAAI+X,EAAoB,GAEpBD,EAAYrE,aAAeqE,EAAYrE,cAAgBgE,EAAKrb,KAAK4D,UACnE+X,GAAqB,oBAAiBD,EAAYrE,iBAEhDqE,EAAYxD,gBACdyD,GAAqB,mBAAmBD,EAAYxD,mBAElDwD,EAAYvD,QACdwD,GAAqB,UAAUD,EAAYvD,WAEzCuD,EAAYpD,mBACdqD,GAAqB,yBAAsBD,EAAYpD,sBAEjC,QAApBoD,EAAYE,MACdD,GAAqB,cAGnBA,IAAsBN,EAAKrb,KAAK2E,aAAa4B,SAAS,sBACxD8U,EAAKrb,KAAK2E,YAAcgX,GAAqBN,EAAKrb,KAAK2E,aAAe,KAK1E,GAAqC,IAAjC0W,EAAK5H,iBAAiBnQ,OAAc,CAEtC,MAAMyM,EAAkBsL,EAAKzE,iBAAiB/P,KAAK3F,GAAQA,EAAKA,OAASma,EAAKrb,KAAKkB,MACnF,GAAI6O,GAAmBA,EAAgBwI,QAAUxI,EAAgBwI,OAAOjV,OAAS,EAAG,CAElF,MAAMuY,EAAgB9L,EAAgBwI,OACtC8C,EAAK5H,iBAAmBoI,EAAchU,IAAI,CAACnJ,EAAcyP,MACvDzP,KAAMA,EACN8a,IAAK,KACLC,aAAc,KACd9V,MAAO,WACP+V,YAAa,KACbC,aAAc,KACdC,UAAqB,IAAVzL,EACXxJ,YAAa,GACbkV,aAAwB,IAAV1L,EAAc,GAAK,GACjC2L,cAAyB,IAAV3L,EAAwC,IAAzB0N,EAAcvY,OAAe,+BAA2B,oBAAuB,4BAI3G+X,EAAK5H,iBAAiBnQ,OAAS,IACjC+X,EAAKrb,KAAKkH,gBAAkBmU,EAAK5H,iBAAiB,GAAG/U,KACrD2c,EAAKrB,oBAAqB,EAEW,IAAjCqB,EAAK5H,iBAAiBnQ,QACxBrF,QAAQC,IAAI,sFAAuEmd,EAAK5H,iBAAiB,GAAG/U,MAC5GT,QAAQC,IAAI,0CAEZD,QAAQC,IAAI,sEAA0Dmd,EAAK5H,iBAAiB,GAAG/U,MAC/FT,QAAQC,IAAI,yCAEdD,QAAQC,IAAI,0CAAwCmd,EAAKrb,KAAKkH,iBAG9DmU,EAAKzL,IAAI8F,iBAGXzX,QAAQC,IAAI,mDAA8Cmd,EAAK5H,yBAInExV,QAAQ6S,KAAK,2DAAyDlK,GACtEyU,EAAK5H,iBAAmB,GACxB4H,EAAKrB,oBAAqB,EAGF,iBAAbpT,IACTyU,EAAKxR,KAAOjD,SAGTQ,GACPnJ,QAAQmJ,MAAM,4BAA0BA,GACxCiU,EAAKxR,KAAO,mEACZwR,EAAK5H,iBAAmB,GACxB4H,EAAKrB,oBAAqB,UAE1BqB,EAAKC,kBAAmB,QAlKxBD,EAAKxR,KAAO,yBAmKb,EArKuB,EAsK1B,CAKQiS,mBAAmBrC,GACzB,IAAKA,EAAc,OAAO,EAC1B,MAAMpD,EAAQoD,EAAapD,MAAM,mBACjC,OAAOA,EAAQ9K,WAAW8K,EAAM,IAAM,CACxC,CAKA0F,2CACE,IAAK/kB,KAAKyc,kBAAqD,IAAjCzc,KAAKyc,iBAAiBnQ,OAElD,YADArF,QAAQC,IAAI,qEAId,IAAIud,EAAiB,KACjBO,EAAgB,GAGpB,MAAMC,EAAqB,CACzB,aACA,qBACA,MACA,UACA,gBACA,yBACA,UACA,uBAGF,UAAWtY,KAASsY,EAIlB,GAHAR,EAAiBzkB,KAAKyc,iBAAiB5M,KAAK2R,GAC1CA,EAAE7U,OAAOoB,cAAcwB,SAAS5C,IAE9B8X,EAAgB,CAClBO,EAAgB,UAAUP,EAAe9X,QACzC,MAKJ,IAAK8X,EAAgB,CACnB,MAAMS,EAA4BllB,KAAKyc,iBAAiB0I,OAAO,CAACC,EAAMC,KACpE,MAAMC,EAAmBtlB,KAAK8kB,mBAAmBM,EAAK3C,eAAiB,EAEvE,OAD4BziB,KAAK8kB,mBAAmBO,EAAQ5C,eAAiB,GAChD6C,EAAmBD,EAAUD,IAGlCplB,KAAK8kB,mBAAmBI,EAA0BzC,cACpD,IACtBgC,EAAiBS,EACjBF,EAAgB,6BAAuBE,EAA0BzC,gBAKrE,IAAKgC,EAAgB,CACnB,MAAMc,EAA4B,CAAC,gBAAiB,UAAW,iBAC/D,UAAW5C,KAAgB4C,EAIzB,GAHAd,EAAiBzkB,KAAKyc,iBAAiB5M,KAAK2R,GAC1CA,EAAEmB,cAAc5U,cAAcwB,SAASoT,IAErC8B,EAAgB,CAClBO,EAAgB,uBAAiBP,EAAe9B,eAChD,OAMD8B,IACHA,EAAiBzkB,KAAKyc,iBAAiB,GACvCuI,EAAgB,qBAIdP,IACFxd,QAAQC,IAAI,oDAAiDud,EAAe/c,sBAAmBsd,MAC/FhlB,KAAKwlB,oBAAoBf,GAGzBA,EAAegB,4BAA6B,EAC5ChB,EAAeiB,gBAAkBV,EAErC,CAKAQ,oBAAoBG,GAElB3lB,KAAKyc,iBAAiBI,QAAQ2H,IAC5BA,EAAM5B,WAAY,EACd4B,IAAUmB,IACZnB,EAAMiB,4BAA6B,EACnCjB,EAAMkB,gBAAkB,QAK5BC,EAAiB/C,WAAY,EAGxB+C,EAAiBF,6BACpBE,EAAiBC,wBAAyB,GAI5C5lB,KAAKgJ,KAAKkH,gBAAkByV,EAAiBje,KAG7C1H,KAAKgjB,oBAAqB,EAC1BhjB,KAAK6S,KAAO,GAEZ5L,QAAQC,IAAI,mCAAiCye,EAAiBje,MAC9DT,QAAQC,IAAI,uCAAqClH,KAAKgJ,KAAKkH,iBAGvDyV,EAAiB9C,eACnB5b,QAAQC,IAAI,kCAA+Bye,EAAiB9C,oBAC5D5b,QAAQC,IAAI,4BAAsBye,EAAiB7C,iBAEvD,CAKA+C,oBACE,OAAO7lB,KAAKyc,kBAAkB5M,KAAK2U,GAASA,EAAM5B,UACpD,CAKAkD,eAAezX,GACb,OAAIA,GAAS,GAAW,gBACpBA,GAAS,GAAW,gBACpBA,GAAS,GAAW,aACjB,iBACT,CAKA0X,sBAAsBC,GACpB,OAAKA,EAGEA,EAAOC,OAAO,GAAG/U,cAAgB8U,EAAOtM,MAAM,GAHjC,EAItB,CAMAwM,wBAGE,GAFAjf,QAAQC,IAAI,yDAAmDlH,KAAKgJ,KAAK4D,UAEpE5M,KAAKgJ,KAAK4D,SAASqE,OAEtB,YADAhK,QAAQ6S,KAAK,yDAIf,IAAI+H,EAAa7hB,KAAKgJ,KAAK4D,QAAQqE,OAG/BjR,KAAKgJ,KAAKgB,QAAQiH,SACpB4Q,GAAc,IAAI7hB,KAAKgJ,KAAKgB,OAAOiH,UAKrC,MAAM0Q,EAAY,mCADMlS,mBAAmBoS,KAG3C5a,QAAQC,IAAI,6BAA8B2a,GAC1C5a,QAAQC,IAAI,OAAQya,GAGpB9a,OAAO6Q,KAAKiK,EAAW,SACzB,CAOAwE,WAEOnmB,KAAKomB,cAMVpmB,KAAKqmB,sBACLrmB,KAAKsmB,cACLtmB,KAAKumB,gBAAiB,EACtBvmB,KAAK6S,KAAO,GACZ5L,QAAQC,IAAI,2BAAyBlH,KAAKsmB,cATxCtmB,KAAKwmB,0BAUT,CAKAC,WACMzmB,KAAKsmB,YAAc,IACrBtmB,KAAKqmB,sBACLrmB,KAAKsmB,cACLrf,QAAQC,IAAI,uBAAwBlH,KAAKsmB,aAE7C,CAKAI,SAASC,GACHA,GAAQ,GAAKA,GAAQ3mB,KAAK4mB,aAC5B5mB,KAAKqmB,sBACLrmB,KAAKsmB,YAAcK,EACnB1f,QAAQC,IAAI,mBAAoBlH,KAAKsmB,aAEzC,CAKAO,qBACE,MAAMC,EAAY,GAElB,OAAK9mB,KAAKgJ,KAAKkH,iBAAiBe,QAC9B6V,EAAU7P,KAAK,0BAEZjX,KAAKgJ,KAAK4D,SAASqE,QACtB6V,EAAU7P,KAAK,mBAEZjX,KAAKgJ,KAAKgB,QAAQiH,QACrB6V,EAAU7P,KAAK,UAEZjX,KAAKgJ,KAAK+G,iBAAiBkB,QAC9B6V,EAAU7P,KAAK,aAIbjX,KAAKia,oBACPja,KAAKgJ,KAAKpB,SAAW5H,KAAKoc,eAAepc,KAAKia,oBAE3Cja,KAAKgJ,KAAKpB,UAAUqJ,QACvB6V,EAAU7P,KAAK,YAGV6P,CACT,CAKAV,aACE,OAAQpmB,KAAKsmB,kBACN,EAEH,OAAItmB,KAAKia,oBACPja,KAAKgJ,KAAKpB,SAAW5H,KAAKoc,eAAepc,KAAKia,uBAEtCja,KAAKgJ,KAAKkH,iBAAmBlQ,KAAKgJ,KAAK4D,SAAW5M,KAAKgJ,KAAKgB,QAAUhK,KAAKgJ,KAAK+G,iBAAmB/P,KAAKgJ,KAAKpB,UAAUqJ,QAAI,KAClI,EAEH,OAAOjR,KAAKmb,mBAAqBnb,KAAKgJ,KAAKuI,SAAWvR,KAAKumB,oBACxD,EAEH,OAAIvmB,KAAK4f,iBAAiBtT,OAAS,IACxBtM,KAAKgJ,KAAKkB,MAAQlK,KAAK6f,2BAG3B7f,KAAK+mB,cAAgB/mB,KAAKgJ,KAAK4D,UAAY5M,KAAKgJ,KAAKgB,QAAUhK,KAAKumB,oBACxE,EAEH,OAAOvmB,KAAKgjB,qBAAuBhjB,KAAKgJ,KAAKkB,MAAQlK,KAAKumB,oBACvD,EACH,OAAO,UAEP,OAAO,EAEb,CAKAS,gBAAgBL,GACd,OAAQA,QACD,EACH,SAAU3mB,KAAKgJ,KAAKkH,iBAAmBlQ,KAAKgJ,KAAK4D,SAAW5M,KAAKgJ,KAAKgB,aACnE,EACH,OAAOhK,KAAK8Y,iBAAiBxM,OAAS,OACnC,EACH,QAAStM,KAAKgJ,KAAKkB,UAChB,EACH,OAAOlK,KAAKgjB,2BAEZ,OAAO,EAEb,CAKAiE,aAAaN,GACX,OAAIA,IAAS3mB,KAAKsmB,YAAoB,SAClCK,EAAO3mB,KAAKsmB,aAAetmB,KAAKgnB,gBAAgBL,GAAc,YAC3D,SACT,CAKAN,sBACE,OAAQrmB,KAAKsmB,kBACN,EACHtmB,KAAKkb,WAAWrC,aAAe,CAC7B3I,gBAAiBlQ,KAAKgJ,KAAKkH,gBAC3BtD,QAAS5M,KAAKgJ,KAAK4D,QACnB5C,OAAQhK,KAAKgJ,KAAKgB,OAClBpC,SAAU5H,KAAKgJ,KAAKpB,SACpBmI,gBAAiB/P,KAAKgJ,KAAK+G,gBAC3BwB,QAASvR,KAAKgJ,KAAKuI,QACnBwC,SAAU/T,KAAKgJ,KAAK+K,UAEtB,WACG,EACH/T,KAAKkb,WAAWpC,iBAAmB,IAAI9Y,KAAK8Y,kBAC5C,WACG,EACH9Y,KAAKkb,WAAWnC,gBAAkB/Y,KAAKgJ,KAAKkB,KAAO,CACjDA,KAAMlK,KAAKgJ,KAAKkB,KAChB0C,QAAS5M,KAAKgJ,KAAK4D,QACnBsM,SAAUlZ,KAAKgJ,KAAKkQ,UAClB,KACJ,WACG,EACHlZ,KAAKkb,WAAWlC,kBAAoB,IAAIhZ,KAAKyc,kBAC7C,WACG,EACHzc,KAAKkb,WAAWjC,cAAgB,CAC9B/M,MAAOlM,KAAKgJ,KAAKkD,MACjBwF,OAAQ1R,KAAKgJ,KAAK0I,OAClB7E,SAAU7M,KAAKgJ,KAAK6D,SACpBc,YAAa3N,KAAKgJ,KAAK2E,YACvB0L,kBAAmBrZ,KAAKgJ,KAAKqQ,kBAC7BuD,kBAAmB5c,KAAK4c,mBAI9B3V,QAAQC,IAAI,wBAAyBlH,KAAKsmB,YAAa,IAAKtmB,KAAKkb,WACnE,CAKAgM,WACMlnB,KAAKsmB,YAActmB,KAAK4mB,YAAc5mB,KAAKmnB,uBAC7ClgB,QAAQC,IAAI,iBAAkBlH,KAAKsmB,aACnCtmB,KAAKumB,gBAAiB,EACtBvmB,KAAKmmB,WAET,CAKAiB,sBACE,OAAQpnB,KAAKsmB,kBACN,EAAG,MAAO,kCACV,EAAG,MAAO,+BACV,EAAG,MAAO,iCACV,EAAG,MAAO,mCACV,EAAG,MAAO,yBACN,MAAO,qBAEpB,CAKAa,qBACE,OAA4B,IAArBnnB,KAAKsmB,aAA0C,IAArBtmB,KAAKsmB,aAA0C,IAArBtmB,KAAKsmB,WAClE,CAKAE,2BACE,OAAQxmB,KAAKsmB,kBACN,EACH,MAAMe,EAAkBrnB,KAAK6mB,qBACzBQ,EAAgB/a,OAAS,IAC3BtM,KAAK6S,KAAO,uCAAoCwU,EAAgB1N,KAAK,SAEvE,WACG,EACC3Z,KAAKgJ,KAAKuI,UAAYvR,KAAKmb,mBAC7Bnb,KAAK6S,KAAO,2EAEd,WACG,EACC7S,KAAK4f,iBAAiBtT,OAAS,IAAMtM,KAAKgJ,KAAKkB,OAASlK,KAAK6f,2BAC/D7f,KAAK6S,KAAO,sGACH7S,KAAKgJ,KAAK4D,SAAW5M,KAAKgJ,KAAKgB,SAAWhK,KAAK+mB,cACxD/mB,KAAK6S,KAAO,iFAEd,WACG,EACC7S,KAAKgJ,KAAKkB,MAAQlK,KAAKukB,iBAAmBvkB,KAAKgjB,mBACjDhjB,KAAK6S,KAAO,qGACH7S,KAAKgJ,KAAKkB,OAASlK,KAAKukB,iBACjCvkB,KAAK6S,KAAO,uFAEd,cAEA7S,KAAK6S,KAAO,oDAElB,CAKAmM,yBACEhf,KAAKmb,kBAAmB,EACxBnb,KAAK6S,KAAO,EACd,CAKAiN,oBACE9f,KAAK+mB,aAAc,EACnB/mB,KAAK6S,KAAO,EACd,+CA/lEW6F,GAAiB/N,iRAAjB+N,EAAiBtU,61aFjC9BuG,sCAA8B,UAA9BA,CAA8B,UAA9BA,CAA8B,UAA9BA,CAA8B,UAA9BA,CAA8B,UAA9BA,CAA8B,WAQhBA,yCAcFA,qCAQVA,sCAA0B,UAA1BA,CAA0B,WAA1BA,CAA0B,WAA1BA,CAA0B,WAKhBA,yCACAA,yCACAA,yCACAA,yCACAA,yCACAA,uBACFA,mCAORA,2CAuBAA,wCAA4B,YAIxBA,4CAwQAA,6CAwKAA,6CAiSAA,4CA4SAA,6CAqNFA,6BAIFA,wCAA2B,WAA3BA,CAA2B,WAA3BA,CAA2B,WAA3BA,CAA2B,aAMjBA,8CAUAA,mCAGAA,2CAMAA,8CAUAA,8CAUAA,8CAUFA,4CA/0CoBA,8FA0BWA,0EACLA,0EACGA,0EACFA,0EACOA,0EAChCA,yFAQsBA,wGA2BtBA,0EAwQAA,0EAwKAA,0EAiSAA,0EA4SAA,0EAmOGA,wEASwCA,+DAUxCA,+EAUAA,mFAWAA,gr4DEv0CD,IACV2c,MAAQ,YAAa,IACnBC,MAAM,YAAU3R,MAAM,CACpB4R,OAAQ,MACRC,QAAS,EACTC,SAAU,aACV,EACFH,MAAM,UAAQ3R,MAAM,CAClB4R,OAAQ,IACRC,QAAS,EACTC,SAAU,cACV,EACFC,MAAW,iBAAkB,IAC3BC,MAAQ,wBACT,EACDD,MAAW,iBAAkB,IAC3BC,MAAQ,6BAKHlP,CAAiB,MDnB5B,CAAEF,KAAM,QAASC,UAAWxG,IAC5B,CAAEuG,KAAM,WAAYC,UAAWd,IAC/B,CAAEa,KAAM,QAASC,UAAWoP,MAC5B,CAAErP,KAAM,GAAIsP,WAAY,QAASC,UAAW,SAOvC,IAAMC,GAAgB,MAAvB,MAAOA,kDAAgB,iDAAhBA,uDAHDC,cAAsB1P,IACtB0P,QAECD,CAAgB,KEZhBE,GAAgB,MAAvB,MAAOA,EAkGXpoB,cAhGQE,uBAA8C,CACpD,WAAS,CACP,2FACA,0FAEFmoB,KAAQ,CACN,2EACA,+FAEF,YAAU,CACR,gGACA,4DAEF7O,YAAe,CACb,iIACA,yGAEF8O,YAAe,CACb,sEACA,gGAEFjW,MAAS,CACP,wGACA,uFAEFkW,SAAY,CACV,8HACA,0GAEFC,MAAS,CACP,+FACA,yGAKItoB,uBAA8D,CACpE,qBAAc,CACZuoB,WAAc,CACZ,4JACA,iLACA,yHAEF,cAAY,CACV,yJACA,0HACA,wHAGJ,uBAAgB,CACdA,WAAc,CACZ,8MACA,8LACA,qKAEF,aAAW,CACT,0LACA,4KACA,qJAGJ,kBAAW,CACTA,WAAc,CACZ,sPACA,4JACA,2KAEF,aAAW,CACT,6MACA,6LACA,wKAGJtS,WAAc,CACZsS,WAAc,CACZ,mMACA,6LACA,yJAEFC,QAAW,CACT,2IACA,uKACA,sJAMExoB,wBAA+B,CACrC,0CACA,mHACA,oFACA,2FACA,2HAGc,CAKhByoB,eAAezgB,GACbf,QAAQC,IAAI,+CAAgDc,GAE5D,IACE,MAAM0gB,EAAgC,GAChCxc,EAAQlE,EAASF,YAAc,mBAC/B6gB,EAAM3gB,EAAS4gB,aAAe,aAUpC,GAPI5oB,KAAK6oB,kBAAkB3c,IAAUlM,KAAK6oB,kBAAkB3c,GAAOyc,IACjE3oB,KAAK6oB,kBAAkB3c,GAAOyc,GAAK9L,QAAQrT,IACzCkf,EAAUzR,KAAK,CAAEzN,SAAO,GAKH,IAArBkf,EAAUpc,QAAgBtM,KAAK6oB,kBAAkB3c,GAAQ,CAC3D,MAAM4c,EAAkBtY,OAAOC,KAAKzQ,KAAK6oB,kBAAkB3c,IACvD4c,EAAgBxc,OAAS,GAE3BtM,KAAK6oB,kBAAkB3c,GADA4c,EAAgB,IACOjM,QAAQrT,IACpDkf,EAAUzR,KAAK,CAAEzN,SAAO,GAM9B,GAAyB,IAArBkf,EAAUpc,QAAgBtE,EAASvB,WAAauB,EAASvB,UAAU6F,OAAS,EAAG,CACjF,MAAMyc,EAAmB/gB,EAASvB,UAC/BiT,OAAM,GACN7I,IAAImY,GAAOA,EAAIxf,MAAMuE,eAExB,UAAYkb,EAAcC,KAAc1Y,OAAO2Y,QAAQnpB,KAAKopB,mBAC1D,UAAWlhB,KAAY6gB,EACrB,GAAI7gB,EAASqH,SAAS0Z,GAAe,CACnCC,EAAUrM,QAAQpI,IAChBiU,EAAUzR,KAAK,CACbzN,MAAOiL,EACPwU,gBACD,GAEH,OAOiB,IAArBP,EAAUpc,QACZtM,KAAKqpB,mBAAmBxM,QAAQrT,IAC9Bkf,EAAUzR,KAAK,CAAEzN,SAAO,GAK5B,MAAM8f,EAAqBtpB,KAAKupB,kBAAkBb,GAAWhP,MAAM,EAAG,GACtEzS,eAAQC,IAAI,wBAAsBoiB,IAAkB,EAE7CE,MAAGF,SACHlZ,GACPnJ,eAAQmJ,MAAM,8BAA4BA,IAAK,EAExCoZ,MAAG,CACR,CAAEhgB,MAAO,8BACT,CAAEA,MAAO,iDACT,CAAEA,MAAO,yDAGf,CAKQ+f,kBAAkBb,GACxB,MAAMe,EAAe,IAAIhoB,IACzB,OAAOinB,EAAUnV,OAAOmW,IAClBD,EAAanoB,IAAIooB,EAASlgB,SAG9BigB,EAAa3mB,IAAI4mB,EAASlgB,QACnB,GAEX,CAKAmgB,oBAAoB3hB,GAGlB,OAAOhI,KAAKyoB,eAAezgB,EAC7B,+CAlMWkgB,EAAgB,qDAAhBA,EAAgB5nB,QAAhB4nB,EAAgB1pB,qBAFf,SAED0pB,CAAgB,KC6ChB0B,GAAS,MAAhB,MAAOA,kDAAS,iDAATA,wDARA,CACTrjB,GACA2hB,GACA5f,EACAuJ,EACAmG,IACDlZ,SAnBCyG,eACAskB,KACAC,KACAvrB,GACAwrB,MACA9qB,GACAqG,GACAY,GACAnH,KACAipB,GACAgC,MACAC,SAUSL,CAAS", "names": ["MatButtonModule", "ɵfac", "t", "ɵmod", "ɵngcc0", "type", "ɵinj", "imports", "MatRippleModule", "MatCommonModule", "MatIconModule", "MatProgressSpinner__svg_circle_1_Template", "rf", "ctx", "ctx_r0", "_spinnerAnimationLabel", "_getStrokeDashOffset", "_getStrokeCircumference", "_getCircleStrokeWidth", "_getCircleRadius", "MatProgressSpinner__svg_circle_2_Template", "ctx_r1", "MatProgressSpinnerBase", "constructor", "_elementRef", "this", "_MatProgressSpinnerMixinBase", "mixinColor", "MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS", "InjectionToken", "providedIn", "factory", "MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY", "diameter", "MatProgressSpinner", "platform", "_document", "animationMode", "defaults", "super", "_diameter", "_value", "_fallbackAnimation", "mode", "trackedDiameters", "_diameters", "_getSpinnerAnimationLabel", "has", "head", "set", "Set", "EDGE", "TRIDENT", "_noopAnimations", "_forceAnimations", "strokeWidth", "size", "coerceNumberProperty", "_styleRoot", "_attachStyleNode", "_strokeWidth", "value", "newValue", "Math", "max", "min", "ngOnInit", "element", "nativeElement", "_getShadowRoot", "classList", "add", "_getViewBox", "viewBox", "PI", "styleRoot", "currentDiameter", "diameters", "diametersForElement", "get", "styleTag", "createElement", "setAttribute", "textContent", "_getAnimationText", "append<PERSON><PERSON><PERSON>", "strokeCircumference", "replace", "toString", "ɵngcc1", "DOCUMENT", "ANIMATION_MODULE_TYPE", "ɵcmp", "selectors", "hostAttrs", "hostVars", "hostBindings", "inputs", "color", "exportAs", "features", "decls", "vars", "consts", "template", "dependencies", "ɵngcc2", "styles", "encapsulation", "changeDetection", "WeakMap", "MatProgressSpinnerModule", "CommonModule", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER", "provide", "deps", "Overlay", "useFactory", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY", "overlay", "scrollStrategies", "reposition", "scrollThrottle", "MatTooltipModule", "providers", "A11yModule", "OverlayModule", "CdkScrollableModule", "ConversasService", "BehaviorSubject", "mensagens", "contextoConversaSubject", "asObservable", "configurarEventListener", "window", "addEventListener", "event", "data", "console", "log", "payload", "atualizarContextoConversa", "tipo", "contextoAtual", "getValue", "novoContexto", "contatoAtual", "nome", "telefoneAtual", "telefone", "next", "etapaFunil", "setContextoConversa", "contexto", "adicionarMensagem", "mensagem", "novasMensagens", "limparMensagens", "getContextoAtual", "LeadService", "ServerService", "http", "liste", "params", "obtenha", "endpoint", "selecione", "id", "salveLead", "lead", "facaPut", "facaPost", "removaLead", "remova", "buscarDadosInstagram", "username", "enviarDadosInstagram", "texto", "crmEmpresaId", "analisarWebsite", "url", "categorizarLinks", "links", "descobrirCnpj", "nomeEmpresa", "cidade", "buscarDetalhesSocios", "cnpj", "listarLinks", "leadId", "adicionarLink", "link", "atualizarLink", "linkId", "removerLink", "removerLinkPorTipo", "i0", "CrmHomeComponent", "conversasService", "route", "router", "leadService", "paramMap", "subscribe", "buscarLeadPorUsername", "inicializarModoDemonstracao", "setTimeout", "debugEstado", "contextoConversa$", "dadosLead", "dadosLeadAtual", "simularNovasMensagens", "horaAtual", "Date", "toLocaleTimeString", "cenarios", "remetente", "horario", "contato", "etapa", "cenarioAtual", "floor", "random", "length", "dadosLeadSimulados", "gerarDadosLeadSimulados", "etapaAtual", "email", "cargo", "empresa", "segmento", "tamanhoEmpresa", "localizacao", "instagram", "site", "dataPrimeiroContato", "ultimaInteracao", "toLocaleDateString", "origemLead", "scoreLead", "interess<PERSON><PERSON><PERSON><PERSON><PERSON>", "proximoFollowUp", "gerarDataFutura", "historicoPropostas", "observacoes", "descricao", "ordem", "linkedin", "toLowerCase", "gerarTelefoneAleatorio", "dias<PERSON><PERSON>e", "setDate", "getDate", "getCorDoScore", "score", "formatarScore", "getWhatsAppUrl", "getInstagramUrl", "getWebsiteUrl", "startsWith", "getLinkIcon", "Ifood", "Concorrente", "<PERSON><PERSON><PERSON>", "WhatsApp", "Site", "Instagram", "getLinkColor", "getLinkUrl", "formatarWhatsAppLink", "formatarLocalizacaoLink", "getLinkDisplayText", "includes", "numeroLimpo", "encodeURIComponent", "_this", "carregandoLead", "response", "find", "l", "instagramHandle", "converterLeadParaDadosLead", "leadEncontrado", "nomeResponsavel", "undefined", "error", "navigate", "queryParams", "crmEmpresa", "Object", "keys", "linksConvertidos", "Array", "isArray", "map", "sort", "a", "b", "trim", "toUpperCase", "dadosConvertidos", "instagramData", "location", "linkInsta", "website", "dataCriacao", "dataUltimaInteracao", "origem", "dataProximoFollowup", "notas", "CrmEmpresaService", "salveEmpresa", "salve", "removaEmpresa", "LeadCrudComponent", "crmEmpresaService", "valor", "icone", "cor", "carregarEmpresas", "then", "listar", "ativa", "resp", "crmEmpresas", "catch", "erro", "alert", "carregando", "inicio", "total", "leads", "aplicarFiltros", "toggleFilt<PERSON>", "mostrarFiltros", "leadsFiltrados", "filter", "filtroEtapa", "filtroOrigem", "filtroTexto", "filtroPendencias", "isAtrasado", "novo", "leadSelecionado", "bioInsta", "valorPotencial", "instagramUsername", "modoEdicao", "mostrarSecaoLinks", "novoLink", "editar", "parseInt", "parseFloat", "e", "resposta", "cancelar", "salvar", "dadosParaEnvio", "JSON", "stringify", "mensagemErro", "message", "remover", "confirm", "avancarEtapa", "etapasOrdem", "indexAtual", "indexOf", "formatarTelefone", "formatarInstagram", "handle", "formatarValor", "toLocaleString", "style", "currency", "formatarData", "getCorScore", "getIconeEtapa", "Fechamento", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getNomeCrmEmpresa", "getWhatsappUrl", "abrirLead", "abrirDetalhesLead", "buscandoInstagram", "leadInstagram", "Error", "bio", "empresaEncontrada", "detectarSegmento", "bioLower", "toggleSecaoLinks", "linkExistente", "push", "ativo", "index", "splice", "getTipoLinkInfo", "tiposLink", "formatarUrlLink", "abrirLink", "urlFormatada", "open", "CrmEmpresaCrudComponent", "empresas", "nova", "empresaSelecionada", "formatarCnpj", "InstagramDataService", "setDados", "dados", "dadosInstagram", "getDados", "clearDados", "hasDados", "routes", "path", "component", "NovoLeadComponent", "instagramDataService", "cdr", "dadosBasicos", "linksEncontrados", "cnpjSelecionado", "sociosEncontrados", "configuracoes", "endereco", "linkCardapio", "biografia", "observacoesSocios", "concorrente", "searchTerm", "parts", "split", "slice", "join", "dadosService", "preencherFormularioComDadosInstagram", "warn", "setupInstagramDataListener", "carregarCrmEmpresas", "telefoneFormatado", "user", "full_name", "external_url", "biography", "business_phone_number", "preencherFormularioComLeadProcessado", "leadProcessado", "leadProcessadoAPI", "businessCategory", "mapearSegmento", "telefones", "telefonesEncontrados", "numero", "numeroFormatado", "getIconeTelefone", "getCorTelefone", "wizardData", "websiteAnalisado", "edge_followed_by", "count", "followers", "edge_follow", "following", "is_business_account", "accountType", "business_category_name", "profile_pic_url", "avatarUrl", "categoria", "categoria<PERSON><PERSON>er", "sucesso", "salvarLead", "_this2", "validarFormulario", "limparTelefone", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createdAt", "updatedAt", "tel", "sociosDetal<PERSON>os", "category_name", "business_address_json", "sincronizarBitrix", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "getFotoPerfilInstagram", "getSeguidoresFormatado", "toFixed", "solicitarDadosInstagram", "parent", "postMessage", "text", "processarDadosInstagram", "processarErroInstagram", "_this3", "textoInsta", "enviarDadosInstagramParaAPI", "_this4", "errorData", "mostrarFormularioManual", "mostrarFormulario", "onWebsiteBlur", "urlOriginal", "normalizarUrl", "onTelefoneBlur", "onTelefoneChange", "onTelefonePaste", "preventDefault", "clipboardData", "getData", "numeroProcessado", "processarTelefoneColado", "detectChanges", "substring", "carregarDadosDoLink", "_this5", "URL", "carregandoWebsite", "marcarWebsiteAnalisado", "dadosWebsite", "linksCategorized", "whatsappLink", "numeroMatch", "match", "instagramLink", "usernameMatch", "cardapioLink", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_this6", "carregandoCnpj", "cnpjsEncontrados", "cnpjRecusadoExplicitamente", "marcarCnpjBuscado", "totalEncontrados", "selecionado", "confianca", "selecionarCnpj", "dadosCompletos", "razaoSocialSelecionada", "razaoSocial", "nomeFantasia", "observacaoExtra", "removerSelecaoCnpj", "formatarCnpjInput", "target", "cnpjManual", "buscarCnpjManual", "_this7", "cnpjLimpo", "carregandoCnpjManual", "cnpjEncontrado", "cnpjFormatado", "capitalSocial", "porte", "situacao", "atividadePrincipal", "naturezaJuridica", "socios", "s", "fonte", "abrirGoogleCnpj", "url<PERSON><PERSON><PERSON>", "abrirGoogleEmpresaCnpj", "termoBusca", "abrirGoogleEmpresaCompleta", "abrirGoogleReceitaFederal", "abrirConsultaCNPJReceita", "navigator", "clipboard", "writeText", "err", "adicionarSocioM<PERSON>al", "nomeSocioManual", "socioManual", "cpf", "participacao", "dataEntrada", "qualificacao", "principal", "scoreAnalise", "motivoSelecao", "manual", "parceirSelecionado", "abrirGoogleVerificarCnpj", "confirmarNenhumCnpj", "copiarCnpj", "abrirCnpjBiz", "getCorConfianca", "getIconeConfianca", "copiarLink", "_this8", "textArea", "document", "body", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "tipoInfo", "tiposTelefone", "abrirTelefone", "whatsappUrl", "copiarTelefone", "_this9", "_this10", "carregandoSocios", "sociosBuscados", "socio", "socioPrincipal", "infoEmpresa", "observacoesExtras", "mei", "sociosBasicos", "extrairPorcentagem", "identificarSocioPrincipalAutomaticamente", "criterioUsado", "cargosPrioritarios", "socioComMaiorParticipacao", "reduce", "prev", "current", "prevParticipacao", "qualificacoesPrioritarias", "marcarComoPrincipal", "selecionadoAutomaticamente", "criterioSelecao", "socioSelecionado", "selecionadoManualmente", "getSocioPrincipal", "getScoreClasse", "formatarMotivoSelecao", "motivo", "char<PERSON>t", "buscarEmpresaNoGoogle", "nextStep", "canAdvance", "saveCurrentStepData", "currentStep", "etapaFoiPulada", "mostrarMensagemValidacao", "prevStep", "goToStep", "step", "totalSteps", "getCamposFaltantes", "faltantes", "cnpjBuscado", "isStepCompleted", "getStepClass", "skipStep", "canSkipCurrentStep", "getCurrentStepTitle", "camposFaltantes", "trigger", "state", "height", "opacity", "overflow", "transition", "animate", "TelaCrmLeadsComponent", "redirectTo", "pathMatch", "CrmRoutingModule", "RouterModule", "SugestoesService", "demo", "interessado", "problema", "prazo", "Consultivo", "Urgente", "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sugestoes", "tom", "tomConversa", "sugestoesPorEtapa", "tomsDisponiveis", "ultimasMensagens", "msg", "palavraChave", "respostas", "entries", "mapaPalavrasChave", "sugestoesGenericas", "sugestoesFiltradas", "removerDuplicatas", "of", "textosUnicos", "sugestao", "gerarSugestoesComIA", "CrmModule", "FormsModule", "ReactiveFormsModule", "MatCardModule", "GridModule", "MaskedTextBoxModule"], "sourceRoot": "webpack:///", "sources": ["./node_modules/@angular/material/__ivy_ngcc__/fesm2015/button.js", "./node_modules/@angular/material/__ivy_ngcc__/fesm2015/icon.js", "./node_modules/@angular/material/__ivy_ngcc__/fesm2015/progress-spinner.js", "./node_modules/@angular/material/__ivy_ngcc__/fesm2015/tooltip.js", "./src/app/crm/services/conversas.service.ts", "./src/app/crm/services/lead.service.ts", "./src/app/crm/crm-home/crm-home.component.html", "./src/app/crm/crm-home/crm-home.component.ts", "./src/app/crm/services/crm-empresa.service.ts", "./src/app/crm/lead-crud/lead-crud.component.html", "./src/app/crm/lead-crud/lead-crud.component.ts", "./src/app/crm/crm-empresa-crud/crm-empresa-crud.component.html", "./src/app/crm/crm-empresa-crud/crm-empresa-crud.component.ts", "./src/app/crm/services/instagram-data.service.ts", "./src/app/crm/novo-lead/novo-lead.component.html", "./src/app/crm/crm-routing.module.ts", "./src/app/crm/novo-lead/novo-lead.component.ts", "./src/app/crm/services/sugestoes.service.ts", "./src/app/crm/crm.module.ts"], "sourcesContent": ["import { Component, ViewEncapsulation, ChangeDetectionStrategy, ElementRef, Optional, Inject, ViewChild, Input, NgModule } from '@angular/core';\nimport { mixinColor, mixinDisabled, mixinDisableRipple, MatRipple, MatRippleModule, MatCommonModule } from '@angular/material/core';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Default color palette for round buttons (mat-fab and mat-mini-fab) */\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/cdk/a11y';\nimport * as ɵngcc2 from '@angular/material/core';\n\nconst _c0 = [\"mat-button\", \"\"];\nconst _c1 = [\"*\"];\nconst _c2 = \".mat-button .mat-button-focus-overlay,.mat-icon-button .mat-button-focus-overlay{opacity:0}.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:.04}@media(hover: none){.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:0}}.mat-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-button.mat-button-disabled,.mat-icon-button.mat-button-disabled,.mat-stroked-button.mat-button-disabled,.mat-flat-button.mat-button-disabled{cursor:default}.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-button.cdk-program-focused .mat-button-focus-overlay,.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-flat-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-raised-button{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button.mat-button-disabled{cursor:default}.mat-raised-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-raised-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-raised-button::-moz-focus-inner{border:0}._mat-animation-noopable.mat-raised-button{transition:none;animation:none}.mat-stroked-button{border:1px solid currentColor;padding:0 15px;line-height:34px}.mat-stroked-button .mat-button-ripple.mat-ripple,.mat-stroked-button .mat-button-focus-overlay{top:-1px;left:-1px;right:-1px;bottom:-1px}.mat-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:56px;height:56px;padding:0;flex-shrink:0}.mat-fab::-moz-focus-inner{border:0}.mat-fab.mat-button-disabled{cursor:default}.mat-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-fab::-moz-focus-inner{border:0}._mat-animation-noopable.mat-fab{transition:none;animation:none}.mat-fab .mat-button-wrapper{padding:16px 0;display:inline-block;line-height:24px}.mat-mini-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:40px;height:40px;padding:0;flex-shrink:0}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab.mat-button-disabled{cursor:default}.mat-mini-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-mini-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-mini-fab::-moz-focus-inner{border:0}._mat-animation-noopable.mat-mini-fab{transition:none;animation:none}.mat-mini-fab .mat-button-wrapper{padding:8px 0;display:inline-block;line-height:24px}.mat-icon-button{padding:0;min-width:0;width:40px;height:40px;flex-shrink:0;line-height:40px;border-radius:50%}.mat-icon-button i,.mat-icon-button .mat-icon{line-height:24px}.mat-button-ripple.mat-ripple,.mat-button-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-button-ripple.mat-ripple:not(:empty){transform:translateZ(0)}.mat-button-focus-overlay{opacity:0;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1),background-color 200ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-button-focus-overlay{transition:none}.mat-button-ripple-round{border-radius:50%;z-index:1}.mat-button .mat-button-wrapper>*,.mat-flat-button .mat-button-wrapper>*,.mat-stroked-button .mat-button-wrapper>*,.mat-raised-button .mat-button-wrapper>*,.mat-icon-button .mat-button-wrapper>*,.mat-fab .mat-button-wrapper>*,.mat-mini-fab .mat-button-wrapper>*{vertical-align:middle}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{display:inline-flex;justify-content:center;align-items:center;font-size:inherit;width:2.5em;height:2.5em}.cdk-high-contrast-active .mat-button,.cdk-high-contrast-active .mat-flat-button,.cdk-high-contrast-active .mat-raised-button,.cdk-high-contrast-active .mat-icon-button,.cdk-high-contrast-active .mat-fab,.cdk-high-contrast-active .mat-mini-fab{outline:solid 1px}.cdk-high-contrast-active .mat-button-base.cdk-keyboard-focused,.cdk-high-contrast-active .mat-button-base.cdk-program-focused{outline:solid 3px}\\n\";\nconst DEFAULT_ROUND_BUTTON_COLOR = 'accent';\n/**\n * List of classes to add to MatButton instances based on host attributes to\n * style as different variants.\n */\nconst BUTTON_HOST_ATTRIBUTES = [\n    'mat-button',\n    'mat-flat-button',\n    'mat-icon-button',\n    'mat-raised-button',\n    'mat-stroked-button',\n    'mat-mini-fab',\n    'mat-fab',\n];\n// Boilerplate for applying mixins to MatButton.\n/** @docs-private */\nclass MatButtonBase {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}\nconst _MatButtonMixinBase = mixinColor(mixinDisabled(mixinDisableRipple(MatButtonBase)));\n/**\n * Material design button.\n */\nclass MatButton extends _MatButtonMixinBase {\n    constructor(elementRef, _focusMonitor, _animationMode) {\n        super(elementRef);\n        this._focusMonitor = _focusMonitor;\n        this._animationMode = _animationMode;\n        /** Whether the button is round. */\n        this.isRoundButton = this._hasHostAttributes('mat-fab', 'mat-mini-fab');\n        /** Whether the button is icon button. */\n        this.isIconButton = this._hasHostAttributes('mat-icon-button');\n        // For each of the variant selectors that is present in the button's host\n        // attributes, add the correct corresponding class.\n        for (const attr of BUTTON_HOST_ATTRIBUTES) {\n            if (this._hasHostAttributes(attr)) {\n                this._getHostElement().classList.add(attr);\n            }\n        }\n        // Add a class that applies to all buttons. This makes it easier to target if somebody\n        // wants to target all Material buttons. We do it here rather than `host` to ensure that\n        // the class is applied to derived classes.\n        elementRef.nativeElement.classList.add('mat-button-base');\n        if (this.isRoundButton) {\n            this.color = DEFAULT_ROUND_BUTTON_COLOR;\n        }\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this._elementRef, true);\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    /** Focuses the button. */\n    focus(origin, options) {\n        if (origin) {\n            this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n        }\n        else {\n            this._getHostElement().focus(options);\n        }\n    }\n    _getHostElement() {\n        return this._elementRef.nativeElement;\n    }\n    _isRippleDisabled() {\n        return this.disableRipple || this.disabled;\n    }\n    /** Gets whether the button has one of the given attributes. */\n    _hasHostAttributes(...attributes) {\n        return attributes.some(attribute => this._getHostElement().hasAttribute(attribute));\n    }\n}\nMatButton.ɵfac = function MatButton_Factory(t) { return new (t || MatButton)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.FocusMonitor), ɵngcc0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8)); };\nMatButton.ɵcmp = /*@__PURE__*/ ɵngcc0.ɵɵdefineComponent({ type: MatButton, selectors: [[\"button\", \"mat-button\", \"\"], [\"button\", \"mat-raised-button\", \"\"], [\"button\", \"mat-icon-button\", \"\"], [\"button\", \"mat-fab\", \"\"], [\"button\", \"mat-mini-fab\", \"\"], [\"button\", \"mat-stroked-button\", \"\"], [\"button\", \"mat-flat-button\", \"\"]], viewQuery: function MatButton_Query(rf, ctx) { if (rf & 1) {\n        ɵngcc0.ɵɵviewQuery(MatRipple, 5);\n    } if (rf & 2) {\n        let _t;\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx.ripple = _t.first);\n    } }, hostAttrs: [1, \"mat-focus-indicator\"], hostVars: 5, hostBindings: function MatButton_HostBindings(rf, ctx) { if (rf & 2) {\n        ɵngcc0.ɵɵattribute(\"disabled\", ctx.disabled || null);\n        ɵngcc0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-button-disabled\", ctx.disabled);\n    } }, inputs: { disabled: \"disabled\", disableRipple: \"disableRipple\", color: \"color\" }, exportAs: [\"matButton\"], features: [ɵngcc0.ɵɵInheritDefinitionFeature], attrs: _c0, ngContentSelectors: _c1, decls: 4, vars: 5, consts: [[1, \"mat-button-wrapper\"], [\"matRipple\", \"\", 1, \"mat-button-ripple\", 3, \"matRippleDisabled\", \"matRippleCentered\", \"matRippleTrigger\"], [1, \"mat-button-focus-overlay\"]], template: function MatButton_Template(rf, ctx) { if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵelementStart(0, \"span\", 0);\n        ɵngcc0.ɵɵprojection(1);\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelement(2, \"span\", 1)(3, \"span\", 2);\n    } if (rf & 2) {\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵclassProp(\"mat-button-ripple-round\", ctx.isRoundButton || ctx.isIconButton);\n        ɵngcc0.ɵɵproperty(\"matRippleDisabled\", ctx._isRippleDisabled())(\"matRippleCentered\", ctx.isIconButton)(\"matRippleTrigger\", ctx._getHostElement());\n    } }, dependencies: [ɵngcc2.MatRipple], styles: [_c2], encapsulation: 2, changeDetection: 0 });\nMatButton.ctorParameters = () => [\n    { type: ElementRef },\n    { type: FocusMonitor },\n    { type: String, decorators: [{ type: Optional }, { type: Inject, args: [ANIMATION_MODULE_TYPE,] }] }\n];\nMatButton.propDecorators = {\n    ripple: [{ type: ViewChild, args: [MatRipple,] }]\n};\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(MatButton, [{\n        type: Component,\n        args: [{ selector: `button[mat-button], button[mat-raised-button], button[mat-icon-button],\n             button[mat-fab], button[mat-mini-fab], button[mat-stroked-button],\n             button[mat-flat-button]`, exportAs: 'matButton', host: {\n                    '[attr.disabled]': 'disabled || null',\n                    '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                    // Add a class for disabled button styling instead of the using attribute\n                    // selector or pseudo-selector.  This allows users to create focusabled\n                    // disabled buttons without recreating the styles.\n                    '[class.mat-button-disabled]': 'disabled',\n                    'class': 'mat-focus-indicator'\n                }, template: \"<span class=\\\"mat-button-wrapper\\\"><ng-content></ng-content></span>\\n<span matRipple class=\\\"mat-button-ripple\\\"\\n      [class.mat-button-ripple-round]=\\\"isRoundButton || isIconButton\\\"\\n      [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n      [matRippleCentered]=\\\"isIconButton\\\"\\n      [matRippleTrigger]=\\\"_getHostElement()\\\"></span>\\n<span class=\\\"mat-button-focus-overlay\\\"></span>\\n\", inputs: ['disabled', 'disableRipple', 'color'], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-button .mat-button-focus-overlay,.mat-icon-button .mat-button-focus-overlay{opacity:0}.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:.04}@media(hover: none){.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:0}}.mat-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-button.mat-button-disabled,.mat-icon-button.mat-button-disabled,.mat-stroked-button.mat-button-disabled,.mat-flat-button.mat-button-disabled{cursor:default}.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-button.cdk-program-focused .mat-button-focus-overlay,.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-flat-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-raised-button{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button.mat-button-disabled{cursor:default}.mat-raised-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-raised-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-raised-button::-moz-focus-inner{border:0}._mat-animation-noopable.mat-raised-button{transition:none;animation:none}.mat-stroked-button{border:1px solid currentColor;padding:0 15px;line-height:34px}.mat-stroked-button .mat-button-ripple.mat-ripple,.mat-stroked-button .mat-button-focus-overlay{top:-1px;left:-1px;right:-1px;bottom:-1px}.mat-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:56px;height:56px;padding:0;flex-shrink:0}.mat-fab::-moz-focus-inner{border:0}.mat-fab.mat-button-disabled{cursor:default}.mat-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-fab::-moz-focus-inner{border:0}._mat-animation-noopable.mat-fab{transition:none;animation:none}.mat-fab .mat-button-wrapper{padding:16px 0;display:inline-block;line-height:24px}.mat-mini-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:40px;height:40px;padding:0;flex-shrink:0}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab.mat-button-disabled{cursor:default}.mat-mini-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-mini-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-mini-fab::-moz-focus-inner{border:0}._mat-animation-noopable.mat-mini-fab{transition:none;animation:none}.mat-mini-fab .mat-button-wrapper{padding:8px 0;display:inline-block;line-height:24px}.mat-icon-button{padding:0;min-width:0;width:40px;height:40px;flex-shrink:0;line-height:40px;border-radius:50%}.mat-icon-button i,.mat-icon-button .mat-icon{line-height:24px}.mat-button-ripple.mat-ripple,.mat-button-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-button-ripple.mat-ripple:not(:empty){transform:translateZ(0)}.mat-button-focus-overlay{opacity:0;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1),background-color 200ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-button-focus-overlay{transition:none}.mat-button-ripple-round{border-radius:50%;z-index:1}.mat-button .mat-button-wrapper>*,.mat-flat-button .mat-button-wrapper>*,.mat-stroked-button .mat-button-wrapper>*,.mat-raised-button .mat-button-wrapper>*,.mat-icon-button .mat-button-wrapper>*,.mat-fab .mat-button-wrapper>*,.mat-mini-fab .mat-button-wrapper>*{vertical-align:middle}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{display:inline-flex;justify-content:center;align-items:center;font-size:inherit;width:2.5em;height:2.5em}.cdk-high-contrast-active .mat-button,.cdk-high-contrast-active .mat-flat-button,.cdk-high-contrast-active .mat-raised-button,.cdk-high-contrast-active .mat-icon-button,.cdk-high-contrast-active .mat-fab,.cdk-high-contrast-active .mat-mini-fab{outline:solid 1px}.cdk-high-contrast-active .mat-button-base.cdk-keyboard-focused,.cdk-high-contrast-active .mat-button-base.cdk-program-focused{outline:solid 3px}\\n\"] }]\n    }], function () { return [{ type: ɵngcc0.ElementRef }, { type: ɵngcc1.FocusMonitor }, { type: String, decorators: [{\n                type: Optional\n            }, {\n                type: Inject,\n                args: [ANIMATION_MODULE_TYPE]\n            }] }]; }, { ripple: [{\n            type: ViewChild,\n            args: [MatRipple]\n        }] }); })();\n/**\n * Material design anchor button.\n */\nclass MatAnchor extends MatButton {\n    constructor(focusMonitor, elementRef, animationMode) {\n        super(elementRef, focusMonitor, animationMode);\n    }\n    _haltDisabledEvents(event) {\n        // A disabled button shouldn't apply any actions\n        if (this.disabled) {\n            event.preventDefault();\n            event.stopImmediatePropagation();\n        }\n    }\n}\nMatAnchor.ɵfac = function MatAnchor_Factory(t) { return new (t || MatAnchor)(ɵngcc0.ɵɵdirectiveInject(ɵngcc1.FocusMonitor), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8)); };\nMatAnchor.ɵcmp = /*@__PURE__*/ ɵngcc0.ɵɵdefineComponent({ type: MatAnchor, selectors: [[\"a\", \"mat-button\", \"\"], [\"a\", \"mat-raised-button\", \"\"], [\"a\", \"mat-icon-button\", \"\"], [\"a\", \"mat-fab\", \"\"], [\"a\", \"mat-mini-fab\", \"\"], [\"a\", \"mat-stroked-button\", \"\"], [\"a\", \"mat-flat-button\", \"\"]], hostAttrs: [1, \"mat-focus-indicator\"], hostVars: 7, hostBindings: function MatAnchor_HostBindings(rf, ctx) { if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function MatAnchor_click_HostBindingHandler($event) { return ctx._haltDisabledEvents($event); });\n    } if (rf & 2) {\n        ɵngcc0.ɵɵattribute(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex || 0)(\"disabled\", ctx.disabled || null)(\"aria-disabled\", ctx.disabled.toString());\n        ɵngcc0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-button-disabled\", ctx.disabled);\n    } }, inputs: { disabled: \"disabled\", disableRipple: \"disableRipple\", color: \"color\", tabIndex: \"tabIndex\" }, exportAs: [\"matButton\", \"matAnchor\"], features: [ɵngcc0.ɵɵInheritDefinitionFeature], attrs: _c0, ngContentSelectors: _c1, decls: 4, vars: 5, consts: [[1, \"mat-button-wrapper\"], [\"matRipple\", \"\", 1, \"mat-button-ripple\", 3, \"matRippleDisabled\", \"matRippleCentered\", \"matRippleTrigger\"], [1, \"mat-button-focus-overlay\"]], template: function MatAnchor_Template(rf, ctx) { if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵelementStart(0, \"span\", 0);\n        ɵngcc0.ɵɵprojection(1);\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelement(2, \"span\", 1)(3, \"span\", 2);\n    } if (rf & 2) {\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵclassProp(\"mat-button-ripple-round\", ctx.isRoundButton || ctx.isIconButton);\n        ɵngcc0.ɵɵproperty(\"matRippleDisabled\", ctx._isRippleDisabled())(\"matRippleCentered\", ctx.isIconButton)(\"matRippleTrigger\", ctx._getHostElement());\n    } }, dependencies: [ɵngcc2.MatRipple], styles: [_c2], encapsulation: 2, changeDetection: 0 });\nMatAnchor.ctorParameters = () => [\n    { type: FocusMonitor },\n    { type: ElementRef },\n    { type: String, decorators: [{ type: Optional }, { type: Inject, args: [ANIMATION_MODULE_TYPE,] }] }\n];\nMatAnchor.propDecorators = {\n    tabIndex: [{ type: Input }]\n};\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(MatAnchor, [{\n        type: Component,\n        args: [{ selector: `a[mat-button], a[mat-raised-button], a[mat-icon-button], a[mat-fab],\n             a[mat-mini-fab], a[mat-stroked-button], a[mat-flat-button]`, exportAs: 'matButton, matAnchor', host: {\n                    // Note that we ignore the user-specified tabindex when it's disabled for\n                    // consistency with the `mat-button` applied on native buttons where even\n                    // though they have an index, they're not tabbable.\n                    '[attr.tabindex]': 'disabled ? -1 : (tabIndex || 0)',\n                    '[attr.disabled]': 'disabled || null',\n                    '[attr.aria-disabled]': 'disabled.toString()',\n                    '(click)': '_haltDisabledEvents($event)',\n                    '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                    '[class.mat-button-disabled]': 'disabled',\n                    'class': 'mat-focus-indicator'\n                }, inputs: ['disabled', 'disableRipple', 'color'], template: \"<span class=\\\"mat-button-wrapper\\\"><ng-content></ng-content></span>\\n<span matRipple class=\\\"mat-button-ripple\\\"\\n      [class.mat-button-ripple-round]=\\\"isRoundButton || isIconButton\\\"\\n      [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n      [matRippleCentered]=\\\"isIconButton\\\"\\n      [matRippleTrigger]=\\\"_getHostElement()\\\"></span>\\n<span class=\\\"mat-button-focus-overlay\\\"></span>\\n\", encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-button .mat-button-focus-overlay,.mat-icon-button .mat-button-focus-overlay{opacity:0}.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:.04}@media(hover: none){.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:0}}.mat-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-button.mat-button-disabled,.mat-icon-button.mat-button-disabled,.mat-stroked-button.mat-button-disabled,.mat-flat-button.mat-button-disabled{cursor:default}.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-button.cdk-program-focused .mat-button-focus-overlay,.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-flat-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-raised-button{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button.mat-button-disabled{cursor:default}.mat-raised-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-raised-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-raised-button::-moz-focus-inner{border:0}._mat-animation-noopable.mat-raised-button{transition:none;animation:none}.mat-stroked-button{border:1px solid currentColor;padding:0 15px;line-height:34px}.mat-stroked-button .mat-button-ripple.mat-ripple,.mat-stroked-button .mat-button-focus-overlay{top:-1px;left:-1px;right:-1px;bottom:-1px}.mat-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:56px;height:56px;padding:0;flex-shrink:0}.mat-fab::-moz-focus-inner{border:0}.mat-fab.mat-button-disabled{cursor:default}.mat-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-fab::-moz-focus-inner{border:0}._mat-animation-noopable.mat-fab{transition:none;animation:none}.mat-fab .mat-button-wrapper{padding:16px 0;display:inline-block;line-height:24px}.mat-mini-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:transparent;display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:40px;height:40px;padding:0;flex-shrink:0}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab.mat-button-disabled{cursor:default}.mat-mini-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-mini-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-mini-fab::-moz-focus-inner{border:0}._mat-animation-noopable.mat-mini-fab{transition:none;animation:none}.mat-mini-fab .mat-button-wrapper{padding:8px 0;display:inline-block;line-height:24px}.mat-icon-button{padding:0;min-width:0;width:40px;height:40px;flex-shrink:0;line-height:40px;border-radius:50%}.mat-icon-button i,.mat-icon-button .mat-icon{line-height:24px}.mat-button-ripple.mat-ripple,.mat-button-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-button-ripple.mat-ripple:not(:empty){transform:translateZ(0)}.mat-button-focus-overlay{opacity:0;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1),background-color 200ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-button-focus-overlay{transition:none}.mat-button-ripple-round{border-radius:50%;z-index:1}.mat-button .mat-button-wrapper>*,.mat-flat-button .mat-button-wrapper>*,.mat-stroked-button .mat-button-wrapper>*,.mat-raised-button .mat-button-wrapper>*,.mat-icon-button .mat-button-wrapper>*,.mat-fab .mat-button-wrapper>*,.mat-mini-fab .mat-button-wrapper>*{vertical-align:middle}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{display:inline-flex;justify-content:center;align-items:center;font-size:inherit;width:2.5em;height:2.5em}.cdk-high-contrast-active .mat-button,.cdk-high-contrast-active .mat-flat-button,.cdk-high-contrast-active .mat-raised-button,.cdk-high-contrast-active .mat-icon-button,.cdk-high-contrast-active .mat-fab,.cdk-high-contrast-active .mat-mini-fab{outline:solid 1px}.cdk-high-contrast-active .mat-button-base.cdk-keyboard-focused,.cdk-high-contrast-active .mat-button-base.cdk-program-focused{outline:solid 3px}\\n\"] }]\n    }], function () { return [{ type: ɵngcc1.FocusMonitor }, { type: ɵngcc0.ElementRef }, { type: String, decorators: [{\n                type: Optional\n            }, {\n                type: Inject,\n                args: [ANIMATION_MODULE_TYPE]\n            }] }]; }, { tabIndex: [{\n            type: Input\n        }] }); })();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatButtonModule {\n}\nMatButtonModule.ɵfac = function MatButtonModule_Factory(t) { return new (t || MatButtonModule)(); };\nMatButtonModule.ɵmod = /*@__PURE__*/ ɵngcc0.ɵɵdefineNgModule({ type: MatButtonModule });\nMatButtonModule.ɵinj = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjector({ imports: [MatRippleModule,\n        MatCommonModule, MatCommonModule] });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(MatButtonModule, [{\n        type: NgModule,\n        args: [{\n                imports: [\n                    MatRippleModule,\n                    MatCommonModule,\n                ],\n                exports: [\n                    MatButton,\n                    MatAnchor,\n                    MatCommonModule,\n                ],\n                declarations: [\n                    MatButton,\n                    MatAnchor,\n                ]\n            }]\n    }], null, null); })();\n(function () { (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(MatButtonModule, { declarations: function () { return [MatButton, MatAnchor]; }, imports: function () { return [MatRippleModule,\n        MatCommonModule]; }, exports: function () { return [MatButton, MatAnchor, MatCommonModule]; } }); })();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatAnchor, MatButton, MatButtonModule };\n\n", "import { <PERSON><PERSON>ontex<PERSON>, ɵɵdefineInjectable, ɵɵinject, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Injectable, Optional, Inject, SkipSelf, InjectionToken, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, ElementRef, Attribute, Input, NgModule } from '@angular/core';\nimport { mixinColor, MatCommonModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { DOCUMENT } from '@angular/common';\nimport { of, throwError, forkJoin, Subscription } from 'rxjs';\nimport { tap, map, catchError, finalize, share, take } from 'rxjs/operators';\nimport { HttpClient } from '@angular/common/http';\nimport { DomSanitizer } from '@angular/platform-browser';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Returns an exception to be thrown in the case when attempting to\n * load an icon with a name that cannot be found.\n * @docs-private\n */\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/common/http';\nimport * as ɵngcc2 from '@angular/platform-browser';\n\nconst _c0 = [\"*\"];\nfunction getMatIconNameNotFoundError(iconName) {\n    return Error(`Unable to find icon with the name \"${iconName}\"`);\n}\n/**\n * Returns an exception to be thrown when the consumer attempts to use\n * `<mat-icon>` without including @angular/common/http.\n * @docs-private\n */\nfunction getMatIconNoHttpProviderError() {\n    return Error('Could not find HttpClient provider for use with Angular Material icons. ' +\n        'Please include the HttpClientModule from @angular/common/http in your ' +\n        'app imports.');\n}\n/**\n * Returns an exception to be thrown when a URL couldn't be sanitized.\n * @param url URL that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeUrlError(url) {\n    return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL ` +\n        `via Angular's DomSanitizer. Attempted URL was \"${url}\".`);\n}\n/**\n * Returns an exception to be thrown when a HTML string couldn't be sanitized.\n * @param literal HTML that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeLiteralError(literal) {\n    return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by ` +\n        `Angular's DomSanitizer. Attempted literal was \"${literal}\".`);\n}\n/**\n * Configuration for an icon, including the URL and possibly the cached SVG element.\n * @docs-private\n */\nclass SvgIconConfig {\n    constructor(url, svgText, options) {\n        this.url = url;\n        this.svgText = svgText;\n        this.options = options;\n    }\n}\n/**\n * Service to register and display icons used by the `<mat-icon>` component.\n * - Registers icon URLs by namespace and name.\n * - Registers icon set URLs by namespace.\n * - Registers aliases for CSS classes, for use with icon fonts.\n * - Loads icons from URLs and extracts individual icons from icon sets.\n */\nclass MatIconRegistry {\n    constructor(_httpClient, _sanitizer, document, _errorHandler) {\n        this._httpClient = _httpClient;\n        this._sanitizer = _sanitizer;\n        this._errorHandler = _errorHandler;\n        /**\n         * URLs and cached SVG elements for individual icons. Keys are of the format \"[namespace]:[icon]\".\n         */\n        this._svgIconConfigs = new Map();\n        /**\n         * SvgIconConfig objects and cached SVG elements for icon sets, keyed by namespace.\n         * Multiple icon sets can be registered under the same namespace.\n         */\n        this._iconSetConfigs = new Map();\n        /** Cache for icons loaded by direct URLs. */\n        this._cachedIconsByUrl = new Map();\n        /** In-progress icon fetches. Used to coalesce multiple requests to the same URL. */\n        this._inProgressUrlFetches = new Map();\n        /** Map from font identifiers to their CSS class names. Used for icon fonts. */\n        this._fontCssClassesByAlias = new Map();\n        /** Registered icon resolver functions. */\n        this._resolvers = [];\n        /**\n         * The CSS class to apply when an `<mat-icon>` component has no icon name, url, or font specified.\n         * The default 'material-icons' value assumes that the material icon font has been loaded as\n         * described at http://google.github.io/material-design-icons/#icon-font-for-the-web\n         */\n        this._defaultFontSetClass = 'material-icons';\n        this._document = document;\n    }\n    /**\n     * Registers an icon by URL in the default namespace.\n     * @param iconName Name under which the icon should be registered.\n     * @param url\n     */\n    addSvgIcon(iconName, url, options) {\n        return this.addSvgIconInNamespace('', iconName, url, options);\n    }\n    /**\n     * Registers an icon using an HTML string in the default namespace.\n     * @param iconName Name under which the icon should be registered.\n     * @param literal SVG source of the icon.\n     */\n    addSvgIconLiteral(iconName, literal, options) {\n        return this.addSvgIconLiteralInNamespace('', iconName, literal, options);\n    }\n    /**\n     * Registers an icon by URL in the specified namespace.\n     * @param namespace Namespace in which the icon should be registered.\n     * @param iconName Name under which the icon should be registered.\n     * @param url\n     */\n    addSvgIconInNamespace(namespace, iconName, url, options) {\n        return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig(url, null, options));\n    }\n    /**\n     * Registers an icon resolver function with the registry. The function will be invoked with the\n     * name and namespace of an icon when the registry tries to resolve the URL from which to fetch\n     * the icon. The resolver is expected to return a `SafeResourceUrl` that points to the icon,\n     * an object with the icon URL and icon options, or `null` if the icon is not supported. Resolvers\n     * will be invoked in the order in which they have been registered.\n     * @param resolver Resolver function to be registered.\n     */\n    addSvgIconResolver(resolver) {\n        this._resolvers.push(resolver);\n        return this;\n    }\n    /**\n     * Registers an icon using an HTML string in the specified namespace.\n     * @param namespace Namespace in which the icon should be registered.\n     * @param iconName Name under which the icon should be registered.\n     * @param literal SVG source of the icon.\n     */\n    addSvgIconLiteralInNamespace(namespace, iconName, literal, options) {\n        const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n        // TODO: add an ngDevMode check\n        if (!cleanLiteral) {\n            throw getMatIconFailedToSanitizeLiteralError(literal);\n        }\n        return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig('', cleanLiteral, options));\n    }\n    /**\n     * Registers an icon set by URL in the default namespace.\n     * @param url\n     */\n    addSvgIconSet(url, options) {\n        return this.addSvgIconSetInNamespace('', url, options);\n    }\n    /**\n     * Registers an icon set using an HTML string in the default namespace.\n     * @param literal SVG source of the icon set.\n     */\n    addSvgIconSetLiteral(literal, options) {\n        return this.addSvgIconSetLiteralInNamespace('', literal, options);\n    }\n    /**\n     * Registers an icon set by URL in the specified namespace.\n     * @param namespace Namespace in which to register the icon set.\n     * @param url\n     */\n    addSvgIconSetInNamespace(namespace, url, options) {\n        return this._addSvgIconSetConfig(namespace, new SvgIconConfig(url, null, options));\n    }\n    /**\n     * Registers an icon set using an HTML string in the specified namespace.\n     * @param namespace Namespace in which to register the icon set.\n     * @param literal SVG source of the icon set.\n     */\n    addSvgIconSetLiteralInNamespace(namespace, literal, options) {\n        const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n        if (!cleanLiteral) {\n            throw getMatIconFailedToSanitizeLiteralError(literal);\n        }\n        return this._addSvgIconSetConfig(namespace, new SvgIconConfig('', cleanLiteral, options));\n    }\n    /**\n     * Defines an alias for a CSS class name to be used for icon fonts. Creating an matIcon\n     * component with the alias as the fontSet input will cause the class name to be applied\n     * to the `<mat-icon>` element.\n     *\n     * @param alias Alias for the font.\n     * @param className Class name override to be used instead of the alias.\n     */\n    registerFontClassAlias(alias, className = alias) {\n        this._fontCssClassesByAlias.set(alias, className);\n        return this;\n    }\n    /**\n     * Returns the CSS class name associated with the alias by a previous call to\n     * registerFontClassAlias. If no CSS class has been associated, returns the alias unmodified.\n     */\n    classNameForFontAlias(alias) {\n        return this._fontCssClassesByAlias.get(alias) || alias;\n    }\n    /**\n     * Sets the CSS class name to be used for icon fonts when an `<mat-icon>` component does not\n     * have a fontSet input value, and is not loading an icon by name or URL.\n     *\n     * @param className\n     */\n    setDefaultFontSetClass(className) {\n        this._defaultFontSetClass = className;\n        return this;\n    }\n    /**\n     * Returns the CSS class name to be used for icon fonts when an `<mat-icon>` component does not\n     * have a fontSet input value, and is not loading an icon by name or URL.\n     */\n    getDefaultFontSetClass() {\n        return this._defaultFontSetClass;\n    }\n    /**\n     * Returns an Observable that produces the icon (as an `<svg>` DOM element) from the given URL.\n     * The response from the URL may be cached so this will not always cause an HTTP request, but\n     * the produced element will always be a new copy of the originally fetched icon. (That is,\n     * it will not contain any modifications made to elements previously returned).\n     *\n     * @param safeUrl URL from which to fetch the SVG icon.\n     */\n    getSvgIconFromUrl(safeUrl) {\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n        if (!url) {\n            throw getMatIconFailedToSanitizeUrlError(safeUrl);\n        }\n        const cachedIcon = this._cachedIconsByUrl.get(url);\n        if (cachedIcon) {\n            return of(cloneSvg(cachedIcon));\n        }\n        return this._loadSvgIconFromConfig(new SvgIconConfig(safeUrl, null)).pipe(tap(svg => this._cachedIconsByUrl.set(url, svg)), map(svg => cloneSvg(svg)));\n    }\n    /**\n     * Returns an Observable that produces the icon (as an `<svg>` DOM element) with the given name\n     * and namespace. The icon must have been previously registered with addIcon or addIconSet;\n     * if not, the Observable will throw an error.\n     *\n     * @param name Name of the icon to be retrieved.\n     * @param namespace Namespace in which to look for the icon.\n     */\n    getNamedSvgIcon(name, namespace = '') {\n        const key = iconKey(namespace, name);\n        let config = this._svgIconConfigs.get(key);\n        // Return (copy of) cached icon if possible.\n        if (config) {\n            return this._getSvgFromConfig(config);\n        }\n        // Otherwise try to resolve the config from one of the resolver functions.\n        config = this._getIconConfigFromResolvers(namespace, name);\n        if (config) {\n            this._svgIconConfigs.set(key, config);\n            return this._getSvgFromConfig(config);\n        }\n        // See if we have any icon sets registered for the namespace.\n        const iconSetConfigs = this._iconSetConfigs.get(namespace);\n        if (iconSetConfigs) {\n            return this._getSvgFromIconSetConfigs(name, iconSetConfigs);\n        }\n        return throwError(getMatIconNameNotFoundError(key));\n    }\n    ngOnDestroy() {\n        this._resolvers = [];\n        this._svgIconConfigs.clear();\n        this._iconSetConfigs.clear();\n        this._cachedIconsByUrl.clear();\n    }\n    /**\n     * Returns the cached icon for a SvgIconConfig if available, or fetches it from its URL if not.\n     */\n    _getSvgFromConfig(config) {\n        if (config.svgText) {\n            // We already have the SVG element for this icon, return a copy.\n            return of(cloneSvg(this._svgElementFromConfig(config)));\n        }\n        else {\n            // Fetch the icon from the config's URL, cache it, and return a copy.\n            return this._loadSvgIconFromConfig(config).pipe(map(svg => cloneSvg(svg)));\n        }\n    }\n    /**\n     * Attempts to find an icon with the specified name in any of the SVG icon sets.\n     * First searches the available cached icons for a nested element with a matching name, and\n     * if found copies the element to a new `<svg>` element. If not found, fetches all icon sets\n     * that have not been cached, and searches again after all fetches are completed.\n     * The returned Observable produces the SVG element if possible, and throws\n     * an error if no icon with the specified name can be found.\n     */\n    _getSvgFromIconSetConfigs(name, iconSetConfigs) {\n        // For all the icon set SVG elements we've fetched, see if any contain an icon with the\n        // requested name.\n        const namedIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n        if (namedIcon) {\n            // We could cache namedIcon in _svgIconConfigs, but since we have to make a copy every\n            // time anyway, there's probably not much advantage compared to just always extracting\n            // it from the icon set.\n            return of(namedIcon);\n        }\n        // Not found in any cached icon sets. If there are icon sets with URLs that we haven't\n        // fetched, fetch them now and look for iconName in the results.\n        const iconSetFetchRequests = iconSetConfigs\n            .filter(iconSetConfig => !iconSetConfig.svgText)\n            .map(iconSetConfig => {\n            return this._loadSvgIconSetFromConfig(iconSetConfig).pipe(catchError((err) => {\n                const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, iconSetConfig.url);\n                // Swallow errors fetching individual URLs so the\n                // combined Observable won't necessarily fail.\n                const errorMessage = `Loading icon set URL: ${url} failed: ${err.message}`;\n                this._errorHandler.handleError(new Error(errorMessage));\n                return of(null);\n            }));\n        });\n        // Fetch all the icon set URLs. When the requests complete, every IconSet should have a\n        // cached SVG element (unless the request failed), and we can check again for the icon.\n        return forkJoin(iconSetFetchRequests).pipe(map(() => {\n            const foundIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n            // TODO: add an ngDevMode check\n            if (!foundIcon) {\n                throw getMatIconNameNotFoundError(name);\n            }\n            return foundIcon;\n        }));\n    }\n    /**\n     * Searches the cached SVG elements for the given icon sets for a nested icon element whose \"id\"\n     * tag matches the specified name. If found, copies the nested element to a new SVG element and\n     * returns it. Returns null if no matching element is found.\n     */\n    _extractIconWithNameFromAnySet(iconName, iconSetConfigs) {\n        // Iterate backwards, so icon sets added later have precedence.\n        for (let i = iconSetConfigs.length - 1; i >= 0; i--) {\n            const config = iconSetConfigs[i];\n            // Parsing the icon set's text into an SVG element can be expensive. We can avoid some of\n            // the parsing by doing a quick check using `indexOf` to see if there's any chance for the\n            // icon to be in the set. This won't be 100% accurate, but it should help us avoid at least\n            // some of the parsing.\n            if (config.svgText && config.svgText.indexOf(iconName) > -1) {\n                const svg = this._svgElementFromConfig(config);\n                const foundIcon = this._extractSvgIconFromSet(svg, iconName, config.options);\n                if (foundIcon) {\n                    return foundIcon;\n                }\n            }\n        }\n        return null;\n    }\n    /**\n     * Loads the content of the icon URL specified in the SvgIconConfig and creates an SVG element\n     * from it.\n     */\n    _loadSvgIconFromConfig(config) {\n        return this._fetchIcon(config).pipe(tap(svgText => config.svgText = svgText), map(() => this._svgElementFromConfig(config)));\n    }\n    /**\n     * Loads the content of the icon set URL specified in the\n     * SvgIconConfig and attaches it to the config.\n     */\n    _loadSvgIconSetFromConfig(config) {\n        if (config.svgText) {\n            return of(null);\n        }\n        return this._fetchIcon(config).pipe(tap(svgText => config.svgText = svgText));\n    }\n    /**\n     * Searches the cached element of the given SvgIconConfig for a nested icon element whose \"id\"\n     * tag matches the specified name. If found, copies the nested element to a new SVG element and\n     * returns it. Returns null if no matching element is found.\n     */\n    _extractSvgIconFromSet(iconSet, iconName, options) {\n        // Use the `id=\"iconName\"` syntax in order to escape special\n        // characters in the ID (versus using the #iconName syntax).\n        const iconSource = iconSet.querySelector(`[id=\"${iconName}\"]`);\n        if (!iconSource) {\n            return null;\n        }\n        // Clone the element and remove the ID to prevent multiple elements from being added\n        // to the page with the same ID.\n        const iconElement = iconSource.cloneNode(true);\n        iconElement.removeAttribute('id');\n        // If the icon node is itself an <svg> node, clone and return it directly. If not, set it as\n        // the content of a new <svg> node.\n        if (iconElement.nodeName.toLowerCase() === 'svg') {\n            return this._setSvgAttributes(iconElement, options);\n        }\n        // If the node is a <symbol>, it won't be rendered so we have to convert it into <svg>. Note\n        // that the same could be achieved by referring to it via <use href=\"#id\">, however the <use>\n        // tag is problematic on Firefox, because it needs to include the current page path.\n        if (iconElement.nodeName.toLowerCase() === 'symbol') {\n            return this._setSvgAttributes(this._toSvgElement(iconElement), options);\n        }\n        // createElement('SVG') doesn't work as expected; the DOM ends up with\n        // the correct nodes, but the SVG content doesn't render. Instead we\n        // have to create an empty SVG node using innerHTML and append its content.\n        // Elements created using DOMParser.parseFromString have the same problem.\n        // http://stackoverflow.com/questions/23003278/svg-innerhtml-in-firefox-can-not-display\n        const svg = this._svgElementFromString('<svg></svg>');\n        // Clone the node so we don't remove it from the parent icon set element.\n        svg.appendChild(iconElement);\n        return this._setSvgAttributes(svg, options);\n    }\n    /**\n     * Creates a DOM element from the given SVG string.\n     */\n    _svgElementFromString(str) {\n        const div = this._document.createElement('DIV');\n        div.innerHTML = str;\n        const svg = div.querySelector('svg');\n        // TODO: add an ngDevMode check\n        if (!svg) {\n            throw Error('<svg> tag not found');\n        }\n        return svg;\n    }\n    /**\n     * Converts an element into an SVG node by cloning all of its children.\n     */\n    _toSvgElement(element) {\n        const svg = this._svgElementFromString('<svg></svg>');\n        const attributes = element.attributes;\n        // Copy over all the attributes from the `symbol` to the new SVG, except the id.\n        for (let i = 0; i < attributes.length; i++) {\n            const { name, value } = attributes[i];\n            if (name !== 'id') {\n                svg.setAttribute(name, value);\n            }\n        }\n        for (let i = 0; i < element.childNodes.length; i++) {\n            if (element.childNodes[i].nodeType === this._document.ELEMENT_NODE) {\n                svg.appendChild(element.childNodes[i].cloneNode(true));\n            }\n        }\n        return svg;\n    }\n    /**\n     * Sets the default attributes for an SVG element to be used as an icon.\n     */\n    _setSvgAttributes(svg, options) {\n        svg.setAttribute('fit', '');\n        svg.setAttribute('height', '100%');\n        svg.setAttribute('width', '100%');\n        svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');\n        svg.setAttribute('focusable', 'false'); // Disable IE11 default behavior to make SVGs focusable.\n        if (options && options.viewBox) {\n            svg.setAttribute('viewBox', options.viewBox);\n        }\n        return svg;\n    }\n    /**\n     * Returns an Observable which produces the string contents of the given icon. Results may be\n     * cached, so future calls with the same URL may not cause another HTTP request.\n     */\n    _fetchIcon(iconConfig) {\n        var _a;\n        const { url: safeUrl, options } = iconConfig;\n        const withCredentials = (_a = options === null || options === void 0 ? void 0 : options.withCredentials) !== null && _a !== void 0 ? _a : false;\n        if (!this._httpClient) {\n            throw getMatIconNoHttpProviderError();\n        }\n        // TODO: add an ngDevMode check\n        if (safeUrl == null) {\n            throw Error(`Cannot fetch icon from URL \"${safeUrl}\".`);\n        }\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n        // TODO: add an ngDevMode check\n        if (!url) {\n            throw getMatIconFailedToSanitizeUrlError(safeUrl);\n        }\n        // Store in-progress fetches to avoid sending a duplicate request for a URL when there is\n        // already a request in progress for that URL. It's necessary to call share() on the\n        // Observable returned by http.get() so that multiple subscribers don't cause multiple XHRs.\n        const inProgressFetch = this._inProgressUrlFetches.get(url);\n        if (inProgressFetch) {\n            return inProgressFetch;\n        }\n        const req = this._httpClient.get(url, { responseType: 'text', withCredentials }).pipe(finalize(() => this._inProgressUrlFetches.delete(url)), share());\n        this._inProgressUrlFetches.set(url, req);\n        return req;\n    }\n    /**\n     * Registers an icon config by name in the specified namespace.\n     * @param namespace Namespace in which to register the icon config.\n     * @param iconName Name under which to register the config.\n     * @param config Config to be registered.\n     */\n    _addSvgIconConfig(namespace, iconName, config) {\n        this._svgIconConfigs.set(iconKey(namespace, iconName), config);\n        return this;\n    }\n    /**\n     * Registers an icon set config in the specified namespace.\n     * @param namespace Namespace in which to register the icon config.\n     * @param config Config to be registered.\n     */\n    _addSvgIconSetConfig(namespace, config) {\n        const configNamespace = this._iconSetConfigs.get(namespace);\n        if (configNamespace) {\n            configNamespace.push(config);\n        }\n        else {\n            this._iconSetConfigs.set(namespace, [config]);\n        }\n        return this;\n    }\n    /** Parses a config's text into an SVG element. */\n    _svgElementFromConfig(config) {\n        if (!config.svgElement) {\n            const svg = this._svgElementFromString(config.svgText);\n            this._setSvgAttributes(svg, config.options);\n            config.svgElement = svg;\n        }\n        return config.svgElement;\n    }\n    /** Tries to create an icon config through the registered resolver functions. */\n    _getIconConfigFromResolvers(namespace, name) {\n        for (let i = 0; i < this._resolvers.length; i++) {\n            const result = this._resolvers[i](name, namespace);\n            if (result) {\n                return isSafeUrlWithOptions(result) ?\n                    new SvgIconConfig(result.url, null, result.options) :\n                    new SvgIconConfig(result, null);\n            }\n        }\n        return undefined;\n    }\n}\nMatIconRegistry.ɵfac = function MatIconRegistry_Factory(t) { return new (t || MatIconRegistry)(ɵngcc0.ɵɵinject(ɵngcc1.HttpClient, 8), ɵngcc0.ɵɵinject(ɵngcc2.DomSanitizer), ɵngcc0.ɵɵinject(DOCUMENT, 8), ɵngcc0.ɵɵinject(ɵngcc0.ErrorHandler)); };\nMatIconRegistry.ɵprov = ɵɵdefineInjectable({ factory: function MatIconRegistry_Factory() { return new MatIconRegistry(ɵɵinject(HttpClient, 8), ɵɵinject(DomSanitizer), ɵɵinject(DOCUMENT, 8), ɵɵinject(ErrorHandler)); }, token: MatIconRegistry, providedIn: \"root\" });\nMatIconRegistry.ctorParameters = () => [\n    { type: HttpClient, decorators: [{ type: Optional }] },\n    { type: DomSanitizer },\n    { type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [DOCUMENT,] }] },\n    { type: ErrorHandler }\n];\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(MatIconRegistry, [{\n        type: Injectable,\n        args: [{ providedIn: 'root' }]\n    }], function () { return [{ type: ɵngcc1.HttpClient, decorators: [{\n                type: Optional\n            }] }, { type: ɵngcc2.DomSanitizer }, { type: undefined, decorators: [{\n                type: Optional\n            }, {\n                type: Inject,\n                args: [DOCUMENT]\n            }] }, { type: ɵngcc0.ErrorHandler }]; }, null); })();\n/** @docs-private */\nfunction ICON_REGISTRY_PROVIDER_FACTORY(parentRegistry, httpClient, sanitizer, errorHandler, document) {\n    return parentRegistry || new MatIconRegistry(httpClient, sanitizer, document, errorHandler);\n}\n/** @docs-private */\nconst ICON_REGISTRY_PROVIDER = {\n    // If there is already an MatIconRegistry available, use that. Otherwise, provide a new one.\n    provide: MatIconRegistry,\n    deps: [\n        [new Optional(), new SkipSelf(), MatIconRegistry],\n        [new Optional(), HttpClient],\n        DomSanitizer,\n        ErrorHandler,\n        [new Optional(), DOCUMENT],\n    ],\n    useFactory: ICON_REGISTRY_PROVIDER_FACTORY,\n};\n/** Clones an SVGElement while preserving type information. */\nfunction cloneSvg(svg) {\n    return svg.cloneNode(true);\n}\n/** Returns the cache key to use for an icon namespace and name. */\nfunction iconKey(namespace, name) {\n    return namespace + ':' + name;\n}\nfunction isSafeUrlWithOptions(value) {\n    return !!(value.url && value.options);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatIcon.\n/** @docs-private */\nclass MatIconBase {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}\nconst _MatIconMixinBase = mixinColor(MatIconBase);\n/**\n * Injection token used to provide the current location to `MatIcon`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_ICON_LOCATION = new InjectionToken('mat-icon-location', {\n    providedIn: 'root',\n    factory: MAT_ICON_LOCATION_FACTORY\n});\n/** @docs-private */\nfunction MAT_ICON_LOCATION_FACTORY() {\n    const _document = inject(DOCUMENT);\n    const _location = _document ? _document.location : null;\n    return {\n        // Note that this needs to be a function, rather than a property, because Angular\n        // will only resolve it once, but we want the current path on each call.\n        getPathname: () => _location ? (_location.pathname + _location.search) : ''\n    };\n}\n/** SVG attributes that accept a FuncIRI (e.g. `url(<something>)`). */\nconst funcIriAttributes = [\n    'clip-path',\n    'color-profile',\n    'src',\n    'cursor',\n    'fill',\n    'filter',\n    'marker',\n    'marker-start',\n    'marker-mid',\n    'marker-end',\n    'mask',\n    'stroke'\n];\nconst ɵ0 = attr => `[${attr}]`;\n/** Selector that can be used to find all elements that are using a `FuncIRI`. */\nconst funcIriAttributeSelector = funcIriAttributes.map(ɵ0).join(', ');\n/** Regex that can be used to extract the id out of a FuncIRI. */\nconst funcIriPattern = /^url\\(['\"]?#(.*?)['\"]?\\)$/;\n/**\n * Component to display an icon. It can be used in the following ways:\n *\n * - Specify the svgIcon input to load an SVG icon from a URL previously registered with the\n *   addSvgIcon, addSvgIconInNamespace, addSvgIconSet, or addSvgIconSetInNamespace methods of\n *   MatIconRegistry. If the svgIcon value contains a colon it is assumed to be in the format\n *   \"[namespace]:[name]\", if not the value will be the name of an icon in the default namespace.\n *   Examples:\n *     `<mat-icon svgIcon=\"left-arrow\"></mat-icon>\n *     <mat-icon svgIcon=\"animals:cat\"></mat-icon>`\n *\n * - Use a font ligature as an icon by putting the ligature text in the content of the `<mat-icon>`\n *   component. By default the Material icons font is used as described at\n *   http://google.github.io/material-design-icons/#icon-font-for-the-web. You can specify an\n *   alternate font by setting the fontSet input to either the CSS class to apply to use the\n *   desired font, or to an alias previously registered with MatIconRegistry.registerFontClassAlias.\n *   Examples:\n *     `<mat-icon>home</mat-icon>\n *     <mat-icon fontSet=\"myfont\">sun</mat-icon>`\n *\n * - Specify a font glyph to be included via CSS rules by setting the fontSet input to specify the\n *   font, and the fontIcon input to specify the icon. Typically the fontIcon will specify a\n *   CSS class which causes the glyph to be displayed via a :before selector, as in\n *   https://fortawesome.github.io/Font-Awesome/examples/\n *   Example:\n *     `<mat-icon fontSet=\"fa\" fontIcon=\"alarm\"></mat-icon>`\n */\nclass MatIcon extends _MatIconMixinBase {\n    constructor(elementRef, _iconRegistry, ariaHidden, _location, _errorHandler) {\n        super(elementRef);\n        this._iconRegistry = _iconRegistry;\n        this._location = _location;\n        this._errorHandler = _errorHandler;\n        this._inline = false;\n        /** Subscription to the current in-progress SVG icon request. */\n        this._currentIconFetch = Subscription.EMPTY;\n        // If the user has not explicitly set aria-hidden, mark the icon as hidden, as this is\n        // the right thing to do for the majority of icon use-cases.\n        if (!ariaHidden) {\n            elementRef.nativeElement.setAttribute('aria-hidden', 'true');\n        }\n    }\n    /**\n     * Whether the icon should be inlined, automatically sizing the icon to match the font size of\n     * the element the icon is contained in.\n     */\n    get inline() {\n        return this._inline;\n    }\n    set inline(inline) {\n        this._inline = coerceBooleanProperty(inline);\n    }\n    /** Name of the icon in the SVG icon set. */\n    get svgIcon() { return this._svgIcon; }\n    set svgIcon(value) {\n        if (value !== this._svgIcon) {\n            if (value) {\n                this._updateSvgIcon(value);\n            }\n            else if (this._svgIcon) {\n                this._clearSvgElement();\n            }\n            this._svgIcon = value;\n        }\n    }\n    /** Font set that the icon is a part of. */\n    get fontSet() { return this._fontSet; }\n    set fontSet(value) {\n        const newValue = this._cleanupFontValue(value);\n        if (newValue !== this._fontSet) {\n            this._fontSet = newValue;\n            this._updateFontIconClasses();\n        }\n    }\n    /** Name of an icon within a font set. */\n    get fontIcon() { return this._fontIcon; }\n    set fontIcon(value) {\n        const newValue = this._cleanupFontValue(value);\n        if (newValue !== this._fontIcon) {\n            this._fontIcon = newValue;\n            this._updateFontIconClasses();\n        }\n    }\n    /**\n     * Splits an svgIcon binding value into its icon set and icon name components.\n     * Returns a 2-element array of [(icon set), (icon name)].\n     * The separator for the two fields is ':'. If there is no separator, an empty\n     * string is returned for the icon set and the entire value is returned for\n     * the icon name. If the argument is falsy, returns an array of two empty strings.\n     * Throws an error if the name contains two or more ':' separators.\n     * Examples:\n     *   `'social:cake' -> ['social', 'cake']\n     *   'penguin' -> ['', 'penguin']\n     *   null -> ['', '']\n     *   'a:b:c' -> (throws Error)`\n     */\n    _splitIconName(iconName) {\n        if (!iconName) {\n            return ['', ''];\n        }\n        const parts = iconName.split(':');\n        switch (parts.length) {\n            case 1: return ['', parts[0]]; // Use default namespace.\n            case 2: return parts;\n            default: throw Error(`Invalid icon name: \"${iconName}\"`); // TODO: add an ngDevMode check\n        }\n    }\n    ngOnInit() {\n        // Update font classes because ngOnChanges won't be called if none of the inputs are present,\n        // e.g. <mat-icon>arrow</mat-icon> In this case we need to add a CSS class for the default font.\n        this._updateFontIconClasses();\n    }\n    ngAfterViewChecked() {\n        const cachedElements = this._elementsWithExternalReferences;\n        if (cachedElements && cachedElements.size) {\n            const newPath = this._location.getPathname();\n            // We need to check whether the URL has changed on each change detection since\n            // the browser doesn't have an API that will let us react on link clicks and\n            // we can't depend on the Angular router. The references need to be updated,\n            // because while most browsers don't care whether the URL is correct after\n            // the first render, Safari will break if the user navigates to a different\n            // page and the SVG isn't re-rendered.\n            if (newPath !== this._previousPath) {\n                this._previousPath = newPath;\n                this._prependPathToReferences(newPath);\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._currentIconFetch.unsubscribe();\n        if (this._elementsWithExternalReferences) {\n            this._elementsWithExternalReferences.clear();\n        }\n    }\n    _usingFontIcon() {\n        return !this.svgIcon;\n    }\n    _setSvgElement(svg) {\n        this._clearSvgElement();\n        // Workaround for IE11 and Edge ignoring `style` tags inside dynamically-created SVGs.\n        // See: https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/10898469/\n        // Do this before inserting the element into the DOM, in order to avoid a style recalculation.\n        const styleTags = svg.querySelectorAll('style');\n        for (let i = 0; i < styleTags.length; i++) {\n            styleTags[i].textContent += ' ';\n        }\n        // Note: we do this fix here, rather than the icon registry, because the\n        // references have to point to the URL at the time that the icon was created.\n        const path = this._location.getPathname();\n        this._previousPath = path;\n        this._cacheChildrenWithExternalReferences(svg);\n        this._prependPathToReferences(path);\n        this._elementRef.nativeElement.appendChild(svg);\n    }\n    _clearSvgElement() {\n        const layoutElement = this._elementRef.nativeElement;\n        let childCount = layoutElement.childNodes.length;\n        if (this._elementsWithExternalReferences) {\n            this._elementsWithExternalReferences.clear();\n        }\n        // Remove existing non-element child nodes and SVGs, and add the new SVG element. Note that\n        // we can't use innerHTML, because IE will throw if the element has a data binding.\n        while (childCount--) {\n            const child = layoutElement.childNodes[childCount];\n            // 1 corresponds to Node.ELEMENT_NODE. We remove all non-element nodes in order to get rid\n            // of any loose text nodes, as well as any SVG elements in order to remove any old icons.\n            if (child.nodeType !== 1 || child.nodeName.toLowerCase() === 'svg') {\n                layoutElement.removeChild(child);\n            }\n        }\n    }\n    _updateFontIconClasses() {\n        if (!this._usingFontIcon()) {\n            return;\n        }\n        const elem = this._elementRef.nativeElement;\n        const fontSetClass = this.fontSet ?\n            this._iconRegistry.classNameForFontAlias(this.fontSet) :\n            this._iconRegistry.getDefaultFontSetClass();\n        if (fontSetClass != this._previousFontSetClass) {\n            if (this._previousFontSetClass) {\n                elem.classList.remove(this._previousFontSetClass);\n            }\n            if (fontSetClass) {\n                elem.classList.add(fontSetClass);\n            }\n            this._previousFontSetClass = fontSetClass;\n        }\n        if (this.fontIcon != this._previousFontIconClass) {\n            if (this._previousFontIconClass) {\n                elem.classList.remove(this._previousFontIconClass);\n            }\n            if (this.fontIcon) {\n                elem.classList.add(this.fontIcon);\n            }\n            this._previousFontIconClass = this.fontIcon;\n        }\n    }\n    /**\n     * Cleans up a value to be used as a fontIcon or fontSet.\n     * Since the value ends up being assigned as a CSS class, we\n     * have to trim the value and omit space-separated values.\n     */\n    _cleanupFontValue(value) {\n        return typeof value === 'string' ? value.trim().split(' ')[0] : value;\n    }\n    /**\n     * Prepends the current path to all elements that have an attribute pointing to a `FuncIRI`\n     * reference. This is required because WebKit browsers require references to be prefixed with\n     * the current path, if the page has a `base` tag.\n     */\n    _prependPathToReferences(path) {\n        const elements = this._elementsWithExternalReferences;\n        if (elements) {\n            elements.forEach((attrs, element) => {\n                attrs.forEach(attr => {\n                    element.setAttribute(attr.name, `url('${path}#${attr.value}')`);\n                });\n            });\n        }\n    }\n    /**\n     * Caches the children of an SVG element that have `url()`\n     * references that we need to prefix with the current path.\n     */\n    _cacheChildrenWithExternalReferences(element) {\n        const elementsWithFuncIri = element.querySelectorAll(funcIriAttributeSelector);\n        const elements = this._elementsWithExternalReferences =\n            this._elementsWithExternalReferences || new Map();\n        for (let i = 0; i < elementsWithFuncIri.length; i++) {\n            funcIriAttributes.forEach(attr => {\n                const elementWithReference = elementsWithFuncIri[i];\n                const value = elementWithReference.getAttribute(attr);\n                const match = value ? value.match(funcIriPattern) : null;\n                if (match) {\n                    let attributes = elements.get(elementWithReference);\n                    if (!attributes) {\n                        attributes = [];\n                        elements.set(elementWithReference, attributes);\n                    }\n                    attributes.push({ name: attr, value: match[1] });\n                }\n            });\n        }\n    }\n    /** Sets a new SVG icon with a particular name. */\n    _updateSvgIcon(rawName) {\n        this._svgNamespace = null;\n        this._svgName = null;\n        this._currentIconFetch.unsubscribe();\n        if (rawName) {\n            const [namespace, iconName] = this._splitIconName(rawName);\n            if (namespace) {\n                this._svgNamespace = namespace;\n            }\n            if (iconName) {\n                this._svgName = iconName;\n            }\n            this._currentIconFetch = this._iconRegistry.getNamedSvgIcon(iconName, namespace)\n                .pipe(take(1))\n                .subscribe(svg => this._setSvgElement(svg), (err) => {\n                const errorMessage = `Error retrieving icon ${namespace}:${iconName}! ${err.message}`;\n                this._errorHandler.handleError(new Error(errorMessage));\n            });\n        }\n    }\n}\nMatIcon.ɵfac = function MatIcon_Factory(t) { return new (t || MatIcon)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(MatIconRegistry), ɵngcc0.ɵɵinjectAttribute('aria-hidden'), ɵngcc0.ɵɵdirectiveInject(MAT_ICON_LOCATION), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ErrorHandler)); };\nMatIcon.ɵcmp = /*@__PURE__*/ ɵngcc0.ɵɵdefineComponent({ type: MatIcon, selectors: [[\"mat-icon\"]], hostAttrs: [\"role\", \"img\", 1, \"mat-icon\", \"notranslate\"], hostVars: 7, hostBindings: function MatIcon_HostBindings(rf, ctx) { if (rf & 2) {\n        ɵngcc0.ɵɵattribute(\"data-mat-icon-type\", ctx._usingFontIcon() ? \"font\" : \"svg\")(\"data-mat-icon-name\", ctx._svgName || ctx.fontIcon)(\"data-mat-icon-namespace\", ctx._svgNamespace || ctx.fontSet);\n        ɵngcc0.ɵɵclassProp(\"mat-icon-inline\", ctx.inline)(\"mat-icon-no-color\", ctx.color !== \"primary\" && ctx.color !== \"accent\" && ctx.color !== \"warn\");\n    } }, inputs: { color: \"color\", inline: \"inline\", svgIcon: \"svgIcon\", fontSet: \"fontSet\", fontIcon: \"fontIcon\" }, exportAs: [\"matIcon\"], features: [ɵngcc0.ɵɵInheritDefinitionFeature], ngContentSelectors: _c0, decls: 1, vars: 0, template: function MatIcon_Template(rf, ctx) { if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵprojection(0);\n    } }, styles: [\".mat-icon{background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\\n\"], encapsulation: 2, changeDetection: 0 });\nMatIcon.ctorParameters = () => [\n    { type: ElementRef },\n    { type: MatIconRegistry },\n    { type: String, decorators: [{ type: Attribute, args: ['aria-hidden',] }] },\n    { type: undefined, decorators: [{ type: Inject, args: [MAT_ICON_LOCATION,] }] },\n    { type: ErrorHandler }\n];\nMatIcon.propDecorators = {\n    inline: [{ type: Input }],\n    svgIcon: [{ type: Input }],\n    fontSet: [{ type: Input }],\n    fontIcon: [{ type: Input }]\n};\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(MatIcon, [{\n        type: Component,\n        args: [{ template: '<ng-content></ng-content>', selector: 'mat-icon', exportAs: 'matIcon', inputs: ['color'], host: {\n                    'role': 'img',\n                    'class': 'mat-icon notranslate',\n                    '[attr.data-mat-icon-type]': '_usingFontIcon() ? \"font\" : \"svg\"',\n                    '[attr.data-mat-icon-name]': '_svgName || fontIcon',\n                    '[attr.data-mat-icon-namespace]': '_svgNamespace || fontSet',\n                    '[class.mat-icon-inline]': 'inline',\n                    '[class.mat-icon-no-color]': 'color !== \"primary\" && color !== \"accent\" && color !== \"warn\"'\n                }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-icon{background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\\n\"] }]\n    }], function () { return [{ type: ɵngcc0.ElementRef }, { type: MatIconRegistry }, { type: String, decorators: [{\n                type: Attribute,\n                args: ['aria-hidden']\n            }] }, { type: undefined, decorators: [{\n                type: Inject,\n                args: [MAT_ICON_LOCATION]\n            }] }, { type: ɵngcc0.ErrorHandler }]; }, { inline: [{\n            type: Input\n        }], svgIcon: [{\n            type: Input\n        }], fontSet: [{\n            type: Input\n        }], fontIcon: [{\n            type: Input\n        }] }); })();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatIconModule {\n}\nMatIconModule.ɵfac = function MatIconModule_Factory(t) { return new (t || MatIconModule)(); };\nMatIconModule.ɵmod = /*@__PURE__*/ ɵngcc0.ɵɵdefineNgModule({ type: MatIconModule });\nMatIconModule.ɵinj = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjector({ imports: [MatCommonModule, MatCommonModule] });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(MatIconModule, [{\n        type: NgModule,\n        args: [{\n                imports: [MatCommonModule],\n                exports: [MatIcon, MatCommonModule],\n                declarations: [MatIcon]\n            }]\n    }], null, null); })();\n(function () { (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(MatIconModule, { declarations: function () { return [MatIcon]; }, imports: function () { return [MatCommonModule]; }, exports: function () { return [MatIcon, MatCommonModule]; } }); })();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ICON_REGISTRY_PROVIDER, ICON_REGISTRY_PROVIDER_FACTORY, MAT_ICON_LOCATION, MAT_ICON_LOCATION_FACTORY, MatIcon, MatIconModule, MatIconRegistry, getMatIconFailedToSanitizeLiteralError, getMatIconFailedToSanitizeUrlError, getMatIconNameNotFoundError, getMatIconNoHttpProviderError, ɵ0 };\n\n", "import { InjectionToken, Component, ChangeDetectionStrategy, ViewEncapsulation, ElementRef, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { mixinColor, MatCommonModule } from '@angular/material/core';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { _getShadowRoot, Platform } from '@angular/cdk/platform';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Base reference size of the spinner.\n * @docs-private\n */\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/cdk/platform';\nimport * as ɵngcc2 from '@angular/common';\n\nfunction MatProgressSpinner__svg_circle_1_Template(rf, ctx) { if (rf & 1) {\n    ɵngcc0.ɵɵnamespaceSVG();\n    ɵngcc0.ɵɵelement(0, \"circle\", 3);\n} if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵstyleProp(\"animation-name\", \"mat-progress-spinner-stroke-rotate-\" + ctx_r0._spinnerAnimationLabel)(\"stroke-dashoffset\", ctx_r0._getStrokeDashOffset(), \"px\")(\"stroke-dasharray\", ctx_r0._getStrokeCircumference(), \"px\")(\"stroke-width\", ctx_r0._getCircleStrokeWidth(), \"%\");\n    ɵngcc0.ɵɵattribute(\"r\", ctx_r0._getCircleRadius());\n} }\nfunction MatProgressSpinner__svg_circle_2_Template(rf, ctx) { if (rf & 1) {\n    ɵngcc0.ɵɵnamespaceSVG();\n    ɵngcc0.ɵɵelement(0, \"circle\", 3);\n} if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵstyleProp(\"stroke-dashoffset\", ctx_r1._getStrokeDashOffset(), \"px\")(\"stroke-dasharray\", ctx_r1._getStrokeCircumference(), \"px\")(\"stroke-width\", ctx_r1._getCircleStrokeWidth(), \"%\");\n    ɵngcc0.ɵɵattribute(\"r\", ctx_r1._getCircleRadius());\n} }\nfunction MatSpinner__svg_circle_1_Template(rf, ctx) { if (rf & 1) {\n    ɵngcc0.ɵɵnamespaceSVG();\n    ɵngcc0.ɵɵelement(0, \"circle\", 3);\n} if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵstyleProp(\"animation-name\", \"mat-progress-spinner-stroke-rotate-\" + ctx_r0._spinnerAnimationLabel)(\"stroke-dashoffset\", ctx_r0._getStrokeDashOffset(), \"px\")(\"stroke-dasharray\", ctx_r0._getStrokeCircumference(), \"px\")(\"stroke-width\", ctx_r0._getCircleStrokeWidth(), \"%\");\n    ɵngcc0.ɵɵattribute(\"r\", ctx_r0._getCircleRadius());\n} }\nfunction MatSpinner__svg_circle_2_Template(rf, ctx) { if (rf & 1) {\n    ɵngcc0.ɵɵnamespaceSVG();\n    ɵngcc0.ɵɵelement(0, \"circle\", 3);\n} if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵstyleProp(\"stroke-dashoffset\", ctx_r1._getStrokeDashOffset(), \"px\")(\"stroke-dasharray\", ctx_r1._getStrokeCircumference(), \"px\")(\"stroke-width\", ctx_r1._getCircleStrokeWidth(), \"%\");\n    ɵngcc0.ɵɵattribute(\"r\", ctx_r1._getCircleRadius());\n} }\nconst _c0 = \".mat-progress-spinner{display:block;position:relative;overflow:hidden}.mat-progress-spinner svg{position:absolute;transform:rotate(-90deg);top:0;left:0;transform-origin:center;overflow:visible}.mat-progress-spinner circle{fill:transparent;transform-origin:center;transition:stroke-dashoffset 225ms linear}._mat-animation-noopable.mat-progress-spinner circle{transition:none;animation:none}.cdk-high-contrast-active .mat-progress-spinner circle{stroke:currentColor;stroke:CanvasText}.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] svg{animation:mat-progress-spinner-linear-rotate 2000ms linear infinite}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] svg{transition:none;animation:none}.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] circle{transition-property:stroke;animation-duration:4000ms;animation-timing-function:cubic-bezier(0.35, 0, 0.25, 1);animation-iteration-count:infinite}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] circle{transition:none;animation:none}.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] svg{animation:mat-progress-spinner-stroke-rotate-fallback 10000ms cubic-bezier(0.87, 0.03, 0.33, 1) infinite}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] svg{transition:none;animation:none}.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] circle{transition-property:stroke}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] circle{transition:none;animation:none}@keyframes mat-progress-spinner-linear-rotate{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes mat-progress-spinner-stroke-rotate-100{0%{stroke-dashoffset:268.606171575px;transform:rotate(0)}12.5%{stroke-dashoffset:56.5486677px;transform:rotate(0)}12.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(72.5deg)}25%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(72.5deg)}25.0001%{stroke-dashoffset:268.606171575px;transform:rotate(270deg)}37.5%{stroke-dashoffset:56.5486677px;transform:rotate(270deg)}37.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(161.5deg)}50%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(161.5deg)}50.0001%{stroke-dashoffset:268.606171575px;transform:rotate(180deg)}62.5%{stroke-dashoffset:56.5486677px;transform:rotate(180deg)}62.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(251.5deg)}75%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(251.5deg)}75.0001%{stroke-dashoffset:268.606171575px;transform:rotate(90deg)}87.5%{stroke-dashoffset:56.5486677px;transform:rotate(90deg)}87.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(341.5deg)}100%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(341.5deg)}}@keyframes mat-progress-spinner-stroke-rotate-fallback{0%{transform:rotate(0deg)}25%{transform:rotate(1170deg)}50%{transform:rotate(2340deg)}75%{transform:rotate(3510deg)}100%{transform:rotate(4680deg)}}\\n\";\nconst BASE_SIZE = 100;\n/**\n * Base reference stroke width of the spinner.\n * @docs-private\n */\nconst BASE_STROKE_WIDTH = 10;\n// Boilerplate for applying mixins to MatProgressSpinner.\n/** @docs-private */\nclass MatProgressSpinnerBase {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}\nconst _MatProgressSpinnerMixinBase = mixinColor(MatProgressSpinnerBase, 'primary');\n/** Injection token to be used to override the default options for `mat-progress-spinner`. */\nconst MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS = new InjectionToken('mat-progress-spinner-default-options', {\n    providedIn: 'root',\n    factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY,\n});\n/** @docs-private */\nfunction MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY() {\n    return { diameter: BASE_SIZE };\n}\n// .0001 percentage difference is necessary in order to avoid unwanted animation frames\n// for example because the animation duration is 4 seconds, .1% accounts to 4ms\n// which are enough to see the flicker described in\n// https://github.com/angular/components/issues/8984\nconst INDETERMINATE_ANIMATION_TEMPLATE = `\n @keyframes mat-progress-spinner-stroke-rotate-DIAMETER {\n    0%      { stroke-dashoffset: START_VALUE;  transform: rotate(0); }\n    12.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(0); }\n    12.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(72.5deg); }\n    25%     { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(72.5deg); }\n\n    25.0001%   { stroke-dashoffset: START_VALUE;  transform: rotate(270deg); }\n    37.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(270deg); }\n    37.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(161.5deg); }\n    50%     { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(161.5deg); }\n\n    50.0001%  { stroke-dashoffset: START_VALUE;  transform: rotate(180deg); }\n    62.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(180deg); }\n    62.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(251.5deg); }\n    75%     { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(251.5deg); }\n\n    75.0001%  { stroke-dashoffset: START_VALUE;  transform: rotate(90deg); }\n    87.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(90deg); }\n    87.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(341.5deg); }\n    100%    { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(341.5deg); }\n  }\n`;\n/**\n * `<mat-progress-spinner>` component.\n */\nclass MatProgressSpinner extends _MatProgressSpinnerMixinBase {\n    constructor(_elementRef, platform, _document, animationMode, defaults) {\n        super(_elementRef);\n        this._elementRef = _elementRef;\n        this._document = _document;\n        this._diameter = BASE_SIZE;\n        this._value = 0;\n        this._fallbackAnimation = false;\n        /** Mode of the progress circle */\n        this.mode = 'determinate';\n        const trackedDiameters = MatProgressSpinner._diameters;\n        this._spinnerAnimationLabel = this._getSpinnerAnimationLabel();\n        // The base size is already inserted via the component's structural styles. We still\n        // need to track it so we don't end up adding the same styles again.\n        if (!trackedDiameters.has(_document.head)) {\n            trackedDiameters.set(_document.head, new Set([BASE_SIZE]));\n        }\n        this._fallbackAnimation = platform.EDGE || platform.TRIDENT;\n        this._noopAnimations = animationMode === 'NoopAnimations' &&\n            (!!defaults && !defaults._forceAnimations);\n        if (defaults) {\n            if (defaults.diameter) {\n                this.diameter = defaults.diameter;\n            }\n            if (defaults.strokeWidth) {\n                this.strokeWidth = defaults.strokeWidth;\n            }\n        }\n    }\n    /** The diameter of the progress spinner (will set width and height of svg). */\n    get diameter() { return this._diameter; }\n    set diameter(size) {\n        this._diameter = coerceNumberProperty(size);\n        this._spinnerAnimationLabel = this._getSpinnerAnimationLabel();\n        // If this is set before `ngOnInit`, the style root may not have been resolved yet.\n        if (!this._fallbackAnimation && this._styleRoot) {\n            this._attachStyleNode();\n        }\n    }\n    /** Stroke width of the progress spinner. */\n    get strokeWidth() {\n        return this._strokeWidth || this.diameter / 10;\n    }\n    set strokeWidth(value) {\n        this._strokeWidth = coerceNumberProperty(value);\n    }\n    /** Value of the progress circle. */\n    get value() {\n        return this.mode === 'determinate' ? this._value : 0;\n    }\n    set value(newValue) {\n        this._value = Math.max(0, Math.min(100, coerceNumberProperty(newValue)));\n    }\n    ngOnInit() {\n        const element = this._elementRef.nativeElement;\n        // Note that we need to look up the root node in ngOnInit, rather than the constructor, because\n        // Angular seems to create the element outside the shadow root and then moves it inside, if the\n        // node is inside an `ngIf` and a ShadowDom-encapsulated component.\n        this._styleRoot = _getShadowRoot(element) || this._document.head;\n        this._attachStyleNode();\n        // On IE and Edge, we can't animate the `stroke-dashoffset`\n        // reliably so we fall back to a non-spec animation.\n        const animationClass = `mat-progress-spinner-indeterminate${this._fallbackAnimation ? '-fallback' : ''}-animation`;\n        element.classList.add(animationClass);\n    }\n    /** The radius of the spinner, adjusted for stroke width. */\n    _getCircleRadius() {\n        return (this.diameter - BASE_STROKE_WIDTH) / 2;\n    }\n    /** The view box of the spinner's svg element. */\n    _getViewBox() {\n        const viewBox = this._getCircleRadius() * 2 + this.strokeWidth;\n        return `0 0 ${viewBox} ${viewBox}`;\n    }\n    /** The stroke circumference of the svg circle. */\n    _getStrokeCircumference() {\n        return 2 * Math.PI * this._getCircleRadius();\n    }\n    /** The dash offset of the svg circle. */\n    _getStrokeDashOffset() {\n        if (this.mode === 'determinate') {\n            return this._getStrokeCircumference() * (100 - this._value) / 100;\n        }\n        // In fallback mode set the circle to 80% and rotate it with CSS.\n        if (this._fallbackAnimation && this.mode === 'indeterminate') {\n            return this._getStrokeCircumference() * 0.2;\n        }\n        return null;\n    }\n    /** Stroke width of the circle in percent. */\n    _getCircleStrokeWidth() {\n        return this.strokeWidth / this.diameter * 100;\n    }\n    /** Dynamically generates a style tag containing the correct animation for this diameter. */\n    _attachStyleNode() {\n        const styleRoot = this._styleRoot;\n        const currentDiameter = this._diameter;\n        const diameters = MatProgressSpinner._diameters;\n        let diametersForElement = diameters.get(styleRoot);\n        if (!diametersForElement || !diametersForElement.has(currentDiameter)) {\n            const styleTag = this._document.createElement('style');\n            styleTag.setAttribute('mat-spinner-animation', this._spinnerAnimationLabel);\n            styleTag.textContent = this._getAnimationText();\n            styleRoot.appendChild(styleTag);\n            if (!diametersForElement) {\n                diametersForElement = new Set();\n                diameters.set(styleRoot, diametersForElement);\n            }\n            diametersForElement.add(currentDiameter);\n        }\n    }\n    /** Generates animation styles adjusted for the spinner's diameter. */\n    _getAnimationText() {\n        const strokeCircumference = this._getStrokeCircumference();\n        return INDETERMINATE_ANIMATION_TEMPLATE\n            // Animation should begin at 5% and end at 80%\n            .replace(/START_VALUE/g, `${0.95 * strokeCircumference}`)\n            .replace(/END_VALUE/g, `${0.2 * strokeCircumference}`)\n            .replace(/DIAMETER/g, `${this._spinnerAnimationLabel}`);\n    }\n    /** Returns the circle diameter formatted for use with the animation-name CSS property. */\n    _getSpinnerAnimationLabel() {\n        // The string of a float point number will include a period ‘.’ character,\n        // which is not valid for a CSS animation-name.\n        return this.diameter.toString().replace('.', '_');\n    }\n}\nMatProgressSpinner.ɵfac = function MatProgressSpinner_Factory(t) { return new (t || MatProgressSpinner)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.Platform), ɵngcc0.ɵɵdirectiveInject(DOCUMENT, 8), ɵngcc0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), ɵngcc0.ɵɵdirectiveInject(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS)); };\nMatProgressSpinner.ɵcmp = /*@__PURE__*/ ɵngcc0.ɵɵdefineComponent({ type: MatProgressSpinner, selectors: [[\"mat-progress-spinner\"]], hostAttrs: [\"role\", \"progressbar\", \"tabindex\", \"-1\", 1, \"mat-progress-spinner\"], hostVars: 10, hostBindings: function MatProgressSpinner_HostBindings(rf, ctx) { if (rf & 2) {\n        ɵngcc0.ɵɵattribute(\"aria-valuemin\", ctx.mode === \"determinate\" ? 0 : null)(\"aria-valuemax\", ctx.mode === \"determinate\" ? 100 : null)(\"aria-valuenow\", ctx.mode === \"determinate\" ? ctx.value : null)(\"mode\", ctx.mode);\n        ɵngcc0.ɵɵstyleProp(\"width\", ctx.diameter, \"px\")(\"height\", ctx.diameter, \"px\");\n        ɵngcc0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._noopAnimations);\n    } }, inputs: { color: \"color\", mode: \"mode\", diameter: \"diameter\", strokeWidth: \"strokeWidth\", value: \"value\" }, exportAs: [\"matProgressSpinner\"], features: [ɵngcc0.ɵɵInheritDefinitionFeature], decls: 3, vars: 8, consts: [[\"preserveAspectRatio\", \"xMidYMid meet\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 3, \"ngSwitch\"], [\"cx\", \"50%\", \"cy\", \"50%\", 3, \"animation-name\", \"stroke-dashoffset\", \"stroke-dasharray\", \"stroke-width\", 4, \"ngSwitchCase\"], [\"cx\", \"50%\", \"cy\", \"50%\", 3, \"stroke-dashoffset\", \"stroke-dasharray\", \"stroke-width\", 4, \"ngSwitchCase\"], [\"cx\", \"50%\", \"cy\", \"50%\"]], template: function MatProgressSpinner_Template(rf, ctx) { if (rf & 1) {\n        ɵngcc0.ɵɵnamespaceSVG();\n        ɵngcc0.ɵɵelementStart(0, \"svg\", 0);\n        ɵngcc0.ɵɵtemplate(1, MatProgressSpinner__svg_circle_1_Template, 1, 9, \"circle\", 1);\n        ɵngcc0.ɵɵtemplate(2, MatProgressSpinner__svg_circle_2_Template, 1, 7, \"circle\", 2);\n        ɵngcc0.ɵɵelementEnd();\n    } if (rf & 2) {\n        ɵngcc0.ɵɵstyleProp(\"width\", ctx.diameter, \"px\")(\"height\", ctx.diameter, \"px\");\n        ɵngcc0.ɵɵproperty(\"ngSwitch\", ctx.mode === \"indeterminate\");\n        ɵngcc0.ɵɵattribute(\"viewBox\", ctx._getViewBox());\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngSwitchCase\", true);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngSwitchCase\", false);\n    } }, dependencies: [ɵngcc2.NgSwitch, ɵngcc2.NgSwitchCase], styles: [_c0], encapsulation: 2, changeDetection: 0 });\n/**\n * Tracks diameters of existing instances to de-dupe generated styles (default d = 100).\n * We need to keep track of which elements the diameters were attached to, because for\n * elements in the Shadow DOM the style tags are attached to the shadow root, rather\n * than the document head.\n */\nMatProgressSpinner._diameters = new WeakMap();\nMatProgressSpinner.ctorParameters = () => [\n    { type: ElementRef },\n    { type: Platform },\n    { type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [DOCUMENT,] }] },\n    { type: String, decorators: [{ type: Optional }, { type: Inject, args: [ANIMATION_MODULE_TYPE,] }] },\n    { type: undefined, decorators: [{ type: Inject, args: [MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS,] }] }\n];\nMatProgressSpinner.propDecorators = {\n    diameter: [{ type: Input }],\n    strokeWidth: [{ type: Input }],\n    mode: [{ type: Input }],\n    value: [{ type: Input }]\n};\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(MatProgressSpinner, [{\n        type: Component,\n        args: [{ selector: 'mat-progress-spinner', exportAs: 'matProgressSpinner', host: {\n                    'role': 'progressbar',\n                    'class': 'mat-progress-spinner',\n                    // set tab index to -1 so screen readers will read the aria-label\n                    // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n                    'tabindex': '-1',\n                    '[class._mat-animation-noopable]': `_noopAnimations`,\n                    '[style.width.px]': 'diameter',\n                    '[style.height.px]': 'diameter',\n                    '[attr.aria-valuemin]': 'mode === \"determinate\" ? 0 : null',\n                    '[attr.aria-valuemax]': 'mode === \"determinate\" ? 100 : null',\n                    '[attr.aria-valuenow]': 'mode === \"determinate\" ? value : null',\n                    '[attr.mode]': 'mode'\n                }, inputs: ['color'], template: \"<!--\\n  preserveAspectRatio of xMidYMid meet as the center of the viewport is the circle's\\n  center. The center of the circle will remain at the center of the mat-progress-spinner\\n  element containing the SVG. `focusable=\\\"false\\\"` prevents IE from allowing the user to\\n  tab into the SVG element.\\n-->\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<svg\\n  [style.width.px]=\\\"diameter\\\"\\n  [style.height.px]=\\\"diameter\\\"\\n  [attr.viewBox]=\\\"_getViewBox()\\\"\\n  preserveAspectRatio=\\\"xMidYMid meet\\\"\\n  focusable=\\\"false\\\"\\n  [ngSwitch]=\\\"mode === 'indeterminate'\\\"\\n  aria-hidden=\\\"true\\\">\\n\\n  <!--\\n    Technically we can reuse the same `circle` element, however Safari has an issue that breaks\\n    the SVG rendering in determinate mode, after switching between indeterminate and determinate.\\n    Using a different element avoids the issue. An alternative to this is adding `display: none`\\n    for a split second and then removing it when switching between modes, but it's hard to know\\n    for how long to hide the element and it can cause the UI to blink.\\n  -->\\n  <circle\\n    *ngSwitchCase=\\\"true\\\"\\n    cx=\\\"50%\\\"\\n    cy=\\\"50%\\\"\\n    [attr.r]=\\\"_getCircleRadius()\\\"\\n    [style.animation-name]=\\\"'mat-progress-spinner-stroke-rotate-' + _spinnerAnimationLabel\\\"\\n    [style.stroke-dashoffset.px]=\\\"_getStrokeDashOffset()\\\"\\n    [style.stroke-dasharray.px]=\\\"_getStrokeCircumference()\\\"\\n    [style.stroke-width.%]=\\\"_getCircleStrokeWidth()\\\"></circle>\\n\\n  <circle\\n    *ngSwitchCase=\\\"false\\\"\\n    cx=\\\"50%\\\"\\n    cy=\\\"50%\\\"\\n    [attr.r]=\\\"_getCircleRadius()\\\"\\n    [style.stroke-dashoffset.px]=\\\"_getStrokeDashOffset()\\\"\\n    [style.stroke-dasharray.px]=\\\"_getStrokeCircumference()\\\"\\n    [style.stroke-width.%]=\\\"_getCircleStrokeWidth()\\\"></circle>\\n</svg>\\n\", changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, styles: [\".mat-progress-spinner{display:block;position:relative;overflow:hidden}.mat-progress-spinner svg{position:absolute;transform:rotate(-90deg);top:0;left:0;transform-origin:center;overflow:visible}.mat-progress-spinner circle{fill:transparent;transform-origin:center;transition:stroke-dashoffset 225ms linear}._mat-animation-noopable.mat-progress-spinner circle{transition:none;animation:none}.cdk-high-contrast-active .mat-progress-spinner circle{stroke:currentColor;stroke:CanvasText}.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] svg{animation:mat-progress-spinner-linear-rotate 2000ms linear infinite}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] svg{transition:none;animation:none}.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] circle{transition-property:stroke;animation-duration:4000ms;animation-timing-function:cubic-bezier(0.35, 0, 0.25, 1);animation-iteration-count:infinite}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] circle{transition:none;animation:none}.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] svg{animation:mat-progress-spinner-stroke-rotate-fallback 10000ms cubic-bezier(0.87, 0.03, 0.33, 1) infinite}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] svg{transition:none;animation:none}.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] circle{transition-property:stroke}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] circle{transition:none;animation:none}@keyframes mat-progress-spinner-linear-rotate{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes mat-progress-spinner-stroke-rotate-100{0%{stroke-dashoffset:268.606171575px;transform:rotate(0)}12.5%{stroke-dashoffset:56.5486677px;transform:rotate(0)}12.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(72.5deg)}25%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(72.5deg)}25.0001%{stroke-dashoffset:268.606171575px;transform:rotate(270deg)}37.5%{stroke-dashoffset:56.5486677px;transform:rotate(270deg)}37.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(161.5deg)}50%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(161.5deg)}50.0001%{stroke-dashoffset:268.606171575px;transform:rotate(180deg)}62.5%{stroke-dashoffset:56.5486677px;transform:rotate(180deg)}62.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(251.5deg)}75%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(251.5deg)}75.0001%{stroke-dashoffset:268.606171575px;transform:rotate(90deg)}87.5%{stroke-dashoffset:56.5486677px;transform:rotate(90deg)}87.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(341.5deg)}100%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(341.5deg)}}@keyframes mat-progress-spinner-stroke-rotate-fallback{0%{transform:rotate(0deg)}25%{transform:rotate(1170deg)}50%{transform:rotate(2340deg)}75%{transform:rotate(3510deg)}100%{transform:rotate(4680deg)}}\\n\"] }]\n    }], function () { return [{ type: ɵngcc0.ElementRef }, { type: ɵngcc1.Platform }, { type: undefined, decorators: [{\n                type: Optional\n            }, {\n                type: Inject,\n                args: [DOCUMENT]\n            }] }, { type: String, decorators: [{\n                type: Optional\n            }, {\n                type: Inject,\n                args: [ANIMATION_MODULE_TYPE]\n            }] }, { type: undefined, decorators: [{\n                type: Inject,\n                args: [MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS]\n            }] }]; }, { mode: [{\n            type: Input\n        }], diameter: [{\n            type: Input\n        }], strokeWidth: [{\n            type: Input\n        }], value: [{\n            type: Input\n        }] }); })();\n/**\n * `<mat-spinner>` component.\n *\n * This is a component definition to be used as a convenience reference to create an\n * indeterminate `<mat-progress-spinner>` instance.\n */\nclass MatSpinner extends MatProgressSpinner {\n    constructor(elementRef, platform, document, animationMode, defaults) {\n        super(elementRef, platform, document, animationMode, defaults);\n        this.mode = 'indeterminate';\n    }\n}\nMatSpinner.ɵfac = function MatSpinner_Factory(t) { return new (t || MatSpinner)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.Platform), ɵngcc0.ɵɵdirectiveInject(DOCUMENT, 8), ɵngcc0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), ɵngcc0.ɵɵdirectiveInject(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS)); };\nMatSpinner.ɵcmp = /*@__PURE__*/ ɵngcc0.ɵɵdefineComponent({ type: MatSpinner, selectors: [[\"mat-spinner\"]], hostAttrs: [\"role\", \"progressbar\", \"mode\", \"indeterminate\", 1, \"mat-spinner\", \"mat-progress-spinner\"], hostVars: 6, hostBindings: function MatSpinner_HostBindings(rf, ctx) { if (rf & 2) {\n        ɵngcc0.ɵɵstyleProp(\"width\", ctx.diameter, \"px\")(\"height\", ctx.diameter, \"px\");\n        ɵngcc0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._noopAnimations);\n    } }, inputs: { color: \"color\" }, features: [ɵngcc0.ɵɵInheritDefinitionFeature], decls: 3, vars: 8, consts: [[\"preserveAspectRatio\", \"xMidYMid meet\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 3, \"ngSwitch\"], [\"cx\", \"50%\", \"cy\", \"50%\", 3, \"animation-name\", \"stroke-dashoffset\", \"stroke-dasharray\", \"stroke-width\", 4, \"ngSwitchCase\"], [\"cx\", \"50%\", \"cy\", \"50%\", 3, \"stroke-dashoffset\", \"stroke-dasharray\", \"stroke-width\", 4, \"ngSwitchCase\"], [\"cx\", \"50%\", \"cy\", \"50%\"]], template: function MatSpinner_Template(rf, ctx) { if (rf & 1) {\n        ɵngcc0.ɵɵnamespaceSVG();\n        ɵngcc0.ɵɵelementStart(0, \"svg\", 0);\n        ɵngcc0.ɵɵtemplate(1, MatSpinner__svg_circle_1_Template, 1, 9, \"circle\", 1);\n        ɵngcc0.ɵɵtemplate(2, MatSpinner__svg_circle_2_Template, 1, 7, \"circle\", 2);\n        ɵngcc0.ɵɵelementEnd();\n    } if (rf & 2) {\n        ɵngcc0.ɵɵstyleProp(\"width\", ctx.diameter, \"px\")(\"height\", ctx.diameter, \"px\");\n        ɵngcc0.ɵɵproperty(\"ngSwitch\", ctx.mode === \"indeterminate\");\n        ɵngcc0.ɵɵattribute(\"viewBox\", ctx._getViewBox());\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngSwitchCase\", true);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngSwitchCase\", false);\n    } }, dependencies: [ɵngcc2.NgSwitch, ɵngcc2.NgSwitchCase], styles: [_c0], encapsulation: 2, changeDetection: 0 });\nMatSpinner.ctorParameters = () => [\n    { type: ElementRef },\n    { type: Platform },\n    { type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [DOCUMENT,] }] },\n    { type: String, decorators: [{ type: Optional }, { type: Inject, args: [ANIMATION_MODULE_TYPE,] }] },\n    { type: undefined, decorators: [{ type: Inject, args: [MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS,] }] }\n];\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(MatSpinner, [{\n        type: Component,\n        args: [{ selector: 'mat-spinner', host: {\n                    'role': 'progressbar',\n                    'mode': 'indeterminate',\n                    'class': 'mat-spinner mat-progress-spinner',\n                    '[class._mat-animation-noopable]': `_noopAnimations`,\n                    '[style.width.px]': 'diameter',\n                    '[style.height.px]': 'diameter'\n                }, inputs: ['color'], template: \"<!--\\n  preserveAspectRatio of xMidYMid meet as the center of the viewport is the circle's\\n  center. The center of the circle will remain at the center of the mat-progress-spinner\\n  element containing the SVG. `focusable=\\\"false\\\"` prevents IE from allowing the user to\\n  tab into the SVG element.\\n-->\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<svg\\n  [style.width.px]=\\\"diameter\\\"\\n  [style.height.px]=\\\"diameter\\\"\\n  [attr.viewBox]=\\\"_getViewBox()\\\"\\n  preserveAspectRatio=\\\"xMidYMid meet\\\"\\n  focusable=\\\"false\\\"\\n  [ngSwitch]=\\\"mode === 'indeterminate'\\\"\\n  aria-hidden=\\\"true\\\">\\n\\n  <!--\\n    Technically we can reuse the same `circle` element, however Safari has an issue that breaks\\n    the SVG rendering in determinate mode, after switching between indeterminate and determinate.\\n    Using a different element avoids the issue. An alternative to this is adding `display: none`\\n    for a split second and then removing it when switching between modes, but it's hard to know\\n    for how long to hide the element and it can cause the UI to blink.\\n  -->\\n  <circle\\n    *ngSwitchCase=\\\"true\\\"\\n    cx=\\\"50%\\\"\\n    cy=\\\"50%\\\"\\n    [attr.r]=\\\"_getCircleRadius()\\\"\\n    [style.animation-name]=\\\"'mat-progress-spinner-stroke-rotate-' + _spinnerAnimationLabel\\\"\\n    [style.stroke-dashoffset.px]=\\\"_getStrokeDashOffset()\\\"\\n    [style.stroke-dasharray.px]=\\\"_getStrokeCircumference()\\\"\\n    [style.stroke-width.%]=\\\"_getCircleStrokeWidth()\\\"></circle>\\n\\n  <circle\\n    *ngSwitchCase=\\\"false\\\"\\n    cx=\\\"50%\\\"\\n    cy=\\\"50%\\\"\\n    [attr.r]=\\\"_getCircleRadius()\\\"\\n    [style.stroke-dashoffset.px]=\\\"_getStrokeDashOffset()\\\"\\n    [style.stroke-dasharray.px]=\\\"_getStrokeCircumference()\\\"\\n    [style.stroke-width.%]=\\\"_getCircleStrokeWidth()\\\"></circle>\\n</svg>\\n\", changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, styles: [\".mat-progress-spinner{display:block;position:relative;overflow:hidden}.mat-progress-spinner svg{position:absolute;transform:rotate(-90deg);top:0;left:0;transform-origin:center;overflow:visible}.mat-progress-spinner circle{fill:transparent;transform-origin:center;transition:stroke-dashoffset 225ms linear}._mat-animation-noopable.mat-progress-spinner circle{transition:none;animation:none}.cdk-high-contrast-active .mat-progress-spinner circle{stroke:currentColor;stroke:CanvasText}.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] svg{animation:mat-progress-spinner-linear-rotate 2000ms linear infinite}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] svg{transition:none;animation:none}.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] circle{transition-property:stroke;animation-duration:4000ms;animation-timing-function:cubic-bezier(0.35, 0, 0.25, 1);animation-iteration-count:infinite}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] circle{transition:none;animation:none}.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] svg{animation:mat-progress-spinner-stroke-rotate-fallback 10000ms cubic-bezier(0.87, 0.03, 0.33, 1) infinite}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] svg{transition:none;animation:none}.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] circle{transition-property:stroke}._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] circle{transition:none;animation:none}@keyframes mat-progress-spinner-linear-rotate{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes mat-progress-spinner-stroke-rotate-100{0%{stroke-dashoffset:268.606171575px;transform:rotate(0)}12.5%{stroke-dashoffset:56.5486677px;transform:rotate(0)}12.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(72.5deg)}25%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(72.5deg)}25.0001%{stroke-dashoffset:268.606171575px;transform:rotate(270deg)}37.5%{stroke-dashoffset:56.5486677px;transform:rotate(270deg)}37.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(161.5deg)}50%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(161.5deg)}50.0001%{stroke-dashoffset:268.606171575px;transform:rotate(180deg)}62.5%{stroke-dashoffset:56.5486677px;transform:rotate(180deg)}62.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(251.5deg)}75%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(251.5deg)}75.0001%{stroke-dashoffset:268.606171575px;transform:rotate(90deg)}87.5%{stroke-dashoffset:56.5486677px;transform:rotate(90deg)}87.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(341.5deg)}100%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(341.5deg)}}@keyframes mat-progress-spinner-stroke-rotate-fallback{0%{transform:rotate(0deg)}25%{transform:rotate(1170deg)}50%{transform:rotate(2340deg)}75%{transform:rotate(3510deg)}100%{transform:rotate(4680deg)}}\\n\"] }]\n    }], function () { return [{ type: ɵngcc0.ElementRef }, { type: ɵngcc1.Platform }, { type: undefined, decorators: [{\n                type: Optional\n            }, {\n                type: Inject,\n                args: [DOCUMENT]\n            }] }, { type: String, decorators: [{\n                type: Optional\n            }, {\n                type: Inject,\n                args: [ANIMATION_MODULE_TYPE]\n            }] }, { type: undefined, decorators: [{\n                type: Inject,\n                args: [MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS]\n            }] }]; }, null); })();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatProgressSpinnerModule {\n}\nMatProgressSpinnerModule.ɵfac = function MatProgressSpinnerModule_Factory(t) { return new (t || MatProgressSpinnerModule)(); };\nMatProgressSpinnerModule.ɵmod = /*@__PURE__*/ ɵngcc0.ɵɵdefineNgModule({ type: MatProgressSpinnerModule });\nMatProgressSpinnerModule.ɵinj = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjector({ imports: [MatCommonModule, CommonModule, MatCommonModule] });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(MatProgressSpinnerModule, [{\n        type: NgModule,\n        args: [{\n                imports: [MatCommonModule, CommonModule],\n                exports: [\n                    MatProgressSpinner,\n                    MatSpinner,\n                    MatCommonModule\n                ],\n                declarations: [\n                    MatProgressSpinner,\n                    MatSpinner\n                ]\n            }]\n    }], null, null); })();\n(function () { (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(MatProgressSpinnerModule, { declarations: function () { return [MatProgressSpinner, MatSpinner]; }, imports: function () { return [MatCommonModule, CommonModule]; }, exports: function () { return [MatProgressSpinner, MatSpinner, MatCommonModule]; } }); })();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS, MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY, MatProgressSpinner, MatProgressSpinnerModule, MatSpinner };\n\n", "import { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport { AriaDescriber, FocusMonitor, A11yModule } from '@angular/cdk/a11y';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { InjectionToken, Directive, ElementRef, ViewContainerRef, NgZone, Inject, Optional, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, ChangeDetectorRef, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport { ScrollDispatcher, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Breakpoints, BreakpointObserver } from '@angular/cdk/layout';\nimport { normalizePassiveListenerOptions, Platform } from '@angular/cdk/platform';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { Subject } from 'rxjs';\nimport { takeUntil, take } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate, keyframes } from '@angular/animations';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by MatTooltip.\n * @docs-private\n */\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/cdk/overlay';\nimport * as ɵngcc2 from '@angular/cdk/scrolling';\nimport * as ɵngcc3 from '@angular/cdk/platform';\nimport * as ɵngcc4 from '@angular/cdk/a11y';\nimport * as ɵngcc5 from '@angular/cdk/bidi';\nimport * as ɵngcc6 from '@angular/cdk/layout';\nimport * as ɵngcc7 from '@angular/common';\nconst matTooltipAnimations = {\n    /** Animation that transitions a tooltip in and out. */\n    tooltipState: trigger('state', [\n        state('initial, void, hidden', style({ opacity: 0, transform: 'scale(0)' })),\n        state('visible', style({ transform: 'scale(1)' })),\n        transition('* => visible', animate('200ms cubic-bezier(0, 0, 0.2, 1)', keyframes([\n            style({ opacity: 0, transform: 'scale(0)', offset: 0 }),\n            style({ opacity: 0.5, transform: 'scale(0.99)', offset: 0.5 }),\n            style({ opacity: 1, transform: 'scale(1)', offset: 1 })\n        ]))),\n        transition('* => hidden', animate('100ms cubic-bezier(0, 0, 0.2, 1)', style({ opacity: 0 }))),\n    ])\n};\n\n/** Time in ms to throttle repositioning after scroll events. */\nconst SCROLL_THROTTLE_MS = 20;\n/** CSS class that will be attached to the overlay panel. */\nconst TOOLTIP_PANEL_CLASS = 'mat-tooltip-panel';\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({ passive: true });\n/**\n * Time between the user putting the pointer on a tooltip\n * trigger and the long press event being fired.\n */\nconst LONGPRESS_DELAY = 500;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nfunction getMatTooltipInvalidPositionError(position) {\n    return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy');\n/** @docs-private */\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition({ scrollThrottle: SCROLL_THROTTLE_MS });\n}\n/** @docs-private */\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY,\n};\n/** Injection token to be used to override the default options for `matTooltip`. */\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n    providedIn: 'root',\n    factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        showDelay: 0,\n        hideDelay: 0,\n        touchendHideDelay: 1500,\n    };\n}\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\nclass MatTooltip {\n    constructor(_overlay, _elementRef, _scrollDispatcher, _viewContainerRef, _ngZone, _platform, _ariaDescriber, _focusMonitor, scrollStrategy, _dir, _defaultOptions, \n    /** @breaking-change 11.0.0 _document argument to become required. */\n    _document) {\n        this._overlay = _overlay;\n        this._elementRef = _elementRef;\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewContainerRef = _viewContainerRef;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._ariaDescriber = _ariaDescriber;\n        this._focusMonitor = _focusMonitor;\n        this._dir = _dir;\n        this._defaultOptions = _defaultOptions;\n        this._position = 'below';\n        this._disabled = false;\n        this._viewInitialized = false;\n        this._pointerExitEventsInitialized = false;\n        /** The default delay in ms before showing the tooltip after show is called */\n        this.showDelay = this._defaultOptions.showDelay;\n        /** The default delay in ms before hiding the tooltip after hide is called */\n        this.hideDelay = this._defaultOptions.hideDelay;\n        /**\n         * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n         * uses a long press gesture to show and hide, however it can conflict with the native browser\n         * gestures. To work around the conflict, Angular Material disables native gestures on the\n         * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n         * elements). The different values for this option configure the touch event handling as follows:\n         * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n         *   browser gestures on particular elements. In particular, it allows text selection on inputs\n         *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n         * - `on` - Enables touch gestures for all elements and disables native\n         *   browser gestures with no exceptions.\n         * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n         *   showing on touch devices.\n         */\n        this.touchGestures = 'auto';\n        this._message = '';\n        /** Manually-bound passive event listeners. */\n        this._passiveListeners = [];\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /**\n         * Handles the keydown events on the host element.\n         * Needs to be an arrow function so that we can use it in addEventListener.\n         */\n        this._handleKeydown = (event) => {\n            if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n                event.preventDefault();\n                event.stopPropagation();\n                this._ngZone.run(() => this.hide(0));\n            }\n        };\n        this._scrollStrategy = scrollStrategy;\n        if (_defaultOptions) {\n            if (_defaultOptions.position) {\n                this.position = _defaultOptions.position;\n            }\n            if (_defaultOptions.touchGestures) {\n                this.touchGestures = _defaultOptions.touchGestures;\n            }\n        }\n        _ngZone.runOutsideAngular(() => {\n            _elementRef.nativeElement.addEventListener('keydown', this._handleKeydown);\n        });\n    }\n    /** Allows the user to define the position of the tooltip relative to the parent element */\n    get position() { return this._position; }\n    set position(value) {\n        if (value !== this._position) {\n            this._position = value;\n            if (this._overlayRef) {\n                this._updatePosition();\n                if (this._tooltipInstance) {\n                    this._tooltipInstance.show(0);\n                }\n                this._overlayRef.updatePosition();\n            }\n        }\n    }\n    /** Disables the display of the tooltip. */\n    get disabled() { return this._disabled; }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        // If tooltip is disabled, hide immediately.\n        if (this._disabled) {\n            this.hide(0);\n        }\n        else {\n            this._setupPointerEnterEventsIfNeeded();\n        }\n    }\n    /** The message to be displayed in the tooltip */\n    get message() { return this._message; }\n    set message(value) {\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this._message, 'tooltip');\n        // If the message is not a string (e.g. number), convert it to a string and trim it.\n        // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n        // away the string-conversion: https://github.com/angular/components/issues/20684\n        this._message = value != null ? String(value).trim() : '';\n        if (!this._message && this._isTooltipVisible()) {\n            this.hide(0);\n        }\n        else {\n            this._setupPointerEnterEventsIfNeeded();\n            this._updateTooltipMessage();\n            this._ngZone.runOutsideAngular(() => {\n                // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n                // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n                // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n                // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n                Promise.resolve().then(() => {\n                    this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n                });\n            });\n        }\n    }\n    /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n    get tooltipClass() { return this._tooltipClass; }\n    set tooltipClass(value) {\n        this._tooltipClass = value;\n        if (this._tooltipInstance) {\n            this._setTooltipClass(this._tooltipClass);\n        }\n    }\n    ngAfterViewInit() {\n        // This needs to happen after view init so the initial values for all inputs have been set.\n        this._viewInitialized = true;\n        this._setupPointerEnterEventsIfNeeded();\n        this._focusMonitor.monitor(this._elementRef)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(origin => {\n            // Note that the focus monitor runs outside the Angular zone.\n            if (!origin) {\n                this._ngZone.run(() => this.hide(0));\n            }\n            else if (origin === 'keyboard') {\n                this._ngZone.run(() => this.show());\n            }\n        });\n    }\n    /**\n     * Dispose the tooltip when destroyed.\n     */\n    ngOnDestroy() {\n        const nativeElement = this._elementRef.nativeElement;\n        clearTimeout(this._touchstartTimeout);\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._tooltipInstance = null;\n        }\n        // Clean up the event listeners set in the constructor\n        nativeElement.removeEventListener('keydown', this._handleKeydown);\n        this._passiveListeners.forEach(([event, listener]) => {\n            nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n        });\n        this._passiveListeners.length = 0;\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n        this._focusMonitor.stopMonitoring(nativeElement);\n    }\n    /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n    show(delay = this.showDelay) {\n        if (this.disabled || !this.message || (this._isTooltipVisible() &&\n            !this._tooltipInstance._showTimeoutId && !this._tooltipInstance._hideTimeoutId)) {\n            return;\n        }\n        const overlayRef = this._createOverlay();\n        this._detach();\n        this._portal = this._portal || new ComponentPortal(TooltipComponent, this._viewContainerRef);\n        this._tooltipInstance = overlayRef.attach(this._portal).instance;\n        this._tooltipInstance.afterHidden()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._setTooltipClass(this._tooltipClass);\n        this._updateTooltipMessage();\n        this._tooltipInstance.show(delay);\n    }\n    /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n    hide(delay = this.hideDelay) {\n        if (this._tooltipInstance) {\n            this._tooltipInstance.hide(delay);\n        }\n    }\n    /** Shows/hides the tooltip */\n    toggle() {\n        this._isTooltipVisible() ? this.hide() : this.show();\n    }\n    /** Returns true if the tooltip is currently visible to the user */\n    _isTooltipVisible() {\n        return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n    }\n    /** Create the overlay config and position strategy */\n    _createOverlay() {\n        if (this._overlayRef) {\n            return this._overlayRef;\n        }\n        const scrollableAncestors = this._scrollDispatcher.getAncestorScrollContainers(this._elementRef);\n        // Create connected position strategy that listens for scroll events to reposition.\n        const strategy = this._overlay.position()\n            .flexibleConnectedTo(this._elementRef)\n            .withTransformOriginOn('.mat-tooltip')\n            .withFlexibleDimensions(false)\n            .withViewportMargin(8)\n            .withScrollableContainers(scrollableAncestors);\n        strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n            if (this._tooltipInstance) {\n                if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n                    // After position changes occur and the overlay is clipped by\n                    // a parent scrollable then close the tooltip.\n                    this._ngZone.run(() => this.hide(0));\n                }\n            }\n        });\n        this._overlayRef = this._overlay.create({\n            direction: this._dir,\n            positionStrategy: strategy,\n            panelClass: TOOLTIP_PANEL_CLASS,\n            scrollStrategy: this._scrollStrategy()\n        });\n        this._updatePosition();\n        this._overlayRef.detachments()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        return this._overlayRef;\n    }\n    /** Detaches the currently-attached tooltip. */\n    _detach() {\n        if (this._overlayRef && this._overlayRef.hasAttached()) {\n            this._overlayRef.detach();\n        }\n        this._tooltipInstance = null;\n    }\n    /** Updates the position of the current tooltip. */\n    _updatePosition() {\n        const position = this._overlayRef.getConfig().positionStrategy;\n        const origin = this._getOrigin();\n        const overlay = this._getOverlayPosition();\n        position.withPositions([\n            Object.assign(Object.assign({}, origin.main), overlay.main),\n            Object.assign(Object.assign({}, origin.fallback), overlay.fallback)\n        ]);\n    }\n    /**\n     * Returns the origin position and a fallback position based on the user's position preference.\n     * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n     */\n    _getOrigin() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let originPosition;\n        if (position == 'above' || position == 'below') {\n            originPosition = { originX: 'center', originY: position == 'above' ? 'top' : 'bottom' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            originPosition = { originX: 'start', originY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            originPosition = { originX: 'end', originY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(originPosition.originX, originPosition.originY);\n        return {\n            main: originPosition,\n            fallback: { originX: x, originY: y }\n        };\n    }\n    /** Returns the overlay position and a fallback position based on the user's preference */\n    _getOverlayPosition() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let overlayPosition;\n        if (position == 'above') {\n            overlayPosition = { overlayX: 'center', overlayY: 'bottom' };\n        }\n        else if (position == 'below') {\n            overlayPosition = { overlayX: 'center', overlayY: 'top' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            overlayPosition = { overlayX: 'end', overlayY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            overlayPosition = { overlayX: 'start', overlayY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n        return {\n            main: overlayPosition,\n            fallback: { overlayX: x, overlayY: y }\n        };\n    }\n    /** Updates the tooltip message and repositions the overlay according to the new message length */\n    _updateTooltipMessage() {\n        // Must wait for the message to be painted to the tooltip so that the overlay can properly\n        // calculate the correct positioning based on the size of the text.\n        if (this._tooltipInstance) {\n            this._tooltipInstance.message = this.message;\n            this._tooltipInstance._markForCheck();\n            this._ngZone.onMicrotaskEmpty.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n                if (this._tooltipInstance) {\n                    this._overlayRef.updatePosition();\n                }\n            });\n        }\n    }\n    /** Updates the tooltip class */\n    _setTooltipClass(tooltipClass) {\n        if (this._tooltipInstance) {\n            this._tooltipInstance.tooltipClass = tooltipClass;\n            this._tooltipInstance._markForCheck();\n        }\n    }\n    /** Inverts an overlay position. */\n    _invertPosition(x, y) {\n        if (this.position === 'above' || this.position === 'below') {\n            if (y === 'top') {\n                y = 'bottom';\n            }\n            else if (y === 'bottom') {\n                y = 'top';\n            }\n        }\n        else {\n            if (x === 'end') {\n                x = 'start';\n            }\n            else if (x === 'start') {\n                x = 'end';\n            }\n        }\n        return { x, y };\n    }\n    /** Binds the pointer events to the tooltip trigger. */\n    _setupPointerEnterEventsIfNeeded() {\n        // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n        if (this._disabled || !this.message || !this._viewInitialized ||\n            this._passiveListeners.length) {\n            return;\n        }\n        // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n        // first tap from firing its click event or can cause the tooltip to open for clicks.\n        if (this._platformSupportsMouseEvents()) {\n            this._passiveListeners\n                .push(['mouseenter', () => {\n                    this._setupPointerExitEventsIfNeeded();\n                    this.show();\n                }]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            this._passiveListeners\n                .push(['touchstart', () => {\n                    // Note that it's important that we don't `preventDefault` here,\n                    // because it can prevent click events from firing on the element.\n                    this._setupPointerExitEventsIfNeeded();\n                    clearTimeout(this._touchstartTimeout);\n                    this._touchstartTimeout = setTimeout(() => this.show(), LONGPRESS_DELAY);\n                }]);\n        }\n        this._addListeners(this._passiveListeners);\n    }\n    _setupPointerExitEventsIfNeeded() {\n        if (this._pointerExitEventsInitialized) {\n            return;\n        }\n        this._pointerExitEventsInitialized = true;\n        const exitListeners = [];\n        if (this._platformSupportsMouseEvents()) {\n            exitListeners.push(['mouseleave', () => this.hide()], ['wheel', event => this._wheelListener(event)]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            const touchendListener = () => {\n                clearTimeout(this._touchstartTimeout);\n                this.hide(this._defaultOptions.touchendHideDelay);\n            };\n            exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n        }\n        this._addListeners(exitListeners);\n        this._passiveListeners.push(...exitListeners);\n    }\n    _addListeners(listeners) {\n        listeners.forEach(([event, listener]) => {\n            this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n        });\n    }\n    _platformSupportsMouseEvents() {\n        return !this._platform.IOS && !this._platform.ANDROID;\n    }\n    /** Listener for the `wheel` event on the element. */\n    _wheelListener(event) {\n        if (this._isTooltipVisible()) {\n            // @breaking-change 11.0.0 Remove `|| document` once the document is a required param.\n            const doc = this._document || document;\n            const elementUnderPointer = doc.elementFromPoint(event.clientX, event.clientY);\n            const element = this._elementRef.nativeElement;\n            // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n            // won't fire if the user scrolls away using the wheel without moving their cursor. We\n            // work around it by finding the element under the user's cursor and closing the tooltip\n            // if it's not the trigger.\n            if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n                this.hide();\n            }\n        }\n    }\n    /** Disables the native browser gestures, based on how the tooltip has been configured. */\n    _disableNativeGesturesIfNecessary() {\n        const gestures = this.touchGestures;\n        if (gestures !== 'off') {\n            const element = this._elementRef.nativeElement;\n            const style = element.style;\n            // If gestures are set to `auto`, we don't disable text selection on inputs and\n            // textareas, because it prevents the user from typing into them on iOS Safari.\n            if (gestures === 'on' || (element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA')) {\n                style.userSelect = style.msUserSelect = style.webkitUserSelect =\n                    style.MozUserSelect = 'none';\n            }\n            // If we have `auto` gestures and the element uses native HTML dragging,\n            // we don't set `-webkit-user-drag` because it prevents the native behavior.\n            if (gestures === 'on' || !element.draggable) {\n                style.webkitUserDrag = 'none';\n            }\n            style.touchAction = 'none';\n            style.webkitTapHighlightColor = 'transparent';\n        }\n    }\n}\nMatTooltip.ɵfac = function MatTooltip_Factory(t) { return new (t || MatTooltip)(ɵngcc0.ɵɵdirectiveInject(ɵngcc1.Overlay), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc2.ScrollDispatcher), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ViewContainerRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.NgZone), ɵngcc0.ɵɵdirectiveInject(ɵngcc3.Platform), ɵngcc0.ɵɵdirectiveInject(ɵngcc4.AriaDescriber), ɵngcc0.ɵɵdirectiveInject(ɵngcc4.FocusMonitor), ɵngcc0.ɵɵdirectiveInject(MAT_TOOLTIP_SCROLL_STRATEGY), ɵngcc0.ɵɵdirectiveInject(ɵngcc5.Directionality, 8), ɵngcc0.ɵɵdirectiveInject(MAT_TOOLTIP_DEFAULT_OPTIONS, 8), ɵngcc0.ɵɵdirectiveInject(DOCUMENT)); };\nMatTooltip.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: MatTooltip, selectors: [[\"\", \"matTooltip\", \"\"]], hostAttrs: [1, \"mat-tooltip-trigger\"], inputs: { showDelay: [\"matTooltipShowDelay\", \"showDelay\"], hideDelay: [\"matTooltipHideDelay\", \"hideDelay\"], touchGestures: [\"matTooltipTouchGestures\", \"touchGestures\"], position: [\"matTooltipPosition\", \"position\"], disabled: [\"matTooltipDisabled\", \"disabled\"], message: [\"matTooltip\", \"message\"], tooltipClass: [\"matTooltipClass\", \"tooltipClass\"] }, exportAs: [\"matTooltip\"] });\nMatTooltip.ctorParameters = () => [\n    { type: Overlay },\n    { type: ElementRef },\n    { type: ScrollDispatcher },\n    { type: ViewContainerRef },\n    { type: NgZone },\n    { type: Platform },\n    { type: AriaDescriber },\n    { type: FocusMonitor },\n    { type: undefined, decorators: [{ type: Inject, args: [MAT_TOOLTIP_SCROLL_STRATEGY,] }] },\n    { type: Directionality, decorators: [{ type: Optional }] },\n    { type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [MAT_TOOLTIP_DEFAULT_OPTIONS,] }] },\n    { type: undefined, decorators: [{ type: Inject, args: [DOCUMENT,] }] }\n];\nMatTooltip.propDecorators = {\n    position: [{ type: Input, args: ['matTooltipPosition',] }],\n    disabled: [{ type: Input, args: ['matTooltipDisabled',] }],\n    showDelay: [{ type: Input, args: ['matTooltipShowDelay',] }],\n    hideDelay: [{ type: Input, args: ['matTooltipHideDelay',] }],\n    touchGestures: [{ type: Input, args: ['matTooltipTouchGestures',] }],\n    message: [{ type: Input, args: ['matTooltip',] }],\n    tooltipClass: [{ type: Input, args: ['matTooltipClass',] }]\n};\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(MatTooltip, [{\n        type: Directive,\n        args: [{\n                selector: '[matTooltip]',\n                exportAs: 'matTooltip',\n                host: {\n                    'class': 'mat-tooltip-trigger'\n                }\n            }]\n    }], function () { return [{ type: ɵngcc1.Overlay }, { type: ɵngcc0.ElementRef }, { type: ɵngcc2.ScrollDispatcher }, { type: ɵngcc0.ViewContainerRef }, { type: ɵngcc0.NgZone }, { type: ɵngcc3.Platform }, { type: ɵngcc4.AriaDescriber }, { type: ɵngcc4.FocusMonitor }, { type: undefined, decorators: [{\n                type: Inject,\n                args: [MAT_TOOLTIP_SCROLL_STRATEGY]\n            }] }, { type: ɵngcc5.Directionality, decorators: [{\n                type: Optional\n            }] }, { type: undefined, decorators: [{\n                type: Optional\n            }, {\n                type: Inject,\n                args: [MAT_TOOLTIP_DEFAULT_OPTIONS]\n            }] }, { type: undefined, decorators: [{\n                type: Inject,\n                args: [DOCUMENT]\n            }] }]; }, { showDelay: [{\n            type: Input,\n            args: ['matTooltipShowDelay']\n        }], hideDelay: [{\n            type: Input,\n            args: ['matTooltipHideDelay']\n        }], touchGestures: [{\n            type: Input,\n            args: ['matTooltipTouchGestures']\n        }], position: [{\n            type: Input,\n            args: ['matTooltipPosition']\n        }], disabled: [{\n            type: Input,\n            args: ['matTooltipDisabled']\n        }], message: [{\n            type: Input,\n            args: ['matTooltip']\n        }], tooltipClass: [{\n            type: Input,\n            args: ['matTooltipClass']\n        }] }); })();\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\nclass TooltipComponent {\n    constructor(_changeDetectorRef, _breakpointObserver) {\n        this._changeDetectorRef = _changeDetectorRef;\n        this._breakpointObserver = _breakpointObserver;\n        /** Property watched by the animation framework to show or hide the tooltip */\n        this._visibility = 'initial';\n        /** Whether interactions on the page should close the tooltip */\n        this._closeOnInteraction = false;\n        /** Subject for notifying that the tooltip has been hidden from the view */\n        this._onHide = new Subject();\n        /** Stream that emits whether the user has a handset-sized display.  */\n        this._isHandset = this._breakpointObserver.observe(Breakpoints.Handset);\n    }\n    /**\n     * Shows the tooltip with an animation originating from the provided origin\n     * @param delay Amount of milliseconds to the delay showing the tooltip.\n     */\n    show(delay) {\n        // Cancel the delayed hide if it is scheduled\n        if (this._hideTimeoutId) {\n            clearTimeout(this._hideTimeoutId);\n            this._hideTimeoutId = null;\n        }\n        // Body interactions should cancel the tooltip if there is a delay in showing.\n        this._closeOnInteraction = true;\n        this._showTimeoutId = setTimeout(() => {\n            this._visibility = 'visible';\n            this._showTimeoutId = null;\n            // Mark for check so if any parent component has set the\n            // ChangeDetectionStrategy to OnPush it will be checked anyways\n            this._markForCheck();\n        }, delay);\n    }\n    /**\n     * Begins the animation to hide the tooltip after the provided delay in ms.\n     * @param delay Amount of milliseconds to delay showing the tooltip.\n     */\n    hide(delay) {\n        // Cancel the delayed show if it is scheduled\n        if (this._showTimeoutId) {\n            clearTimeout(this._showTimeoutId);\n            this._showTimeoutId = null;\n        }\n        this._hideTimeoutId = setTimeout(() => {\n            this._visibility = 'hidden';\n            this._hideTimeoutId = null;\n            // Mark for check so if any parent component has set the\n            // ChangeDetectionStrategy to OnPush it will be checked anyways\n            this._markForCheck();\n        }, delay);\n    }\n    /** Returns an observable that notifies when the tooltip has been hidden from view. */\n    afterHidden() {\n        return this._onHide;\n    }\n    /** Whether the tooltip is being displayed. */\n    isVisible() {\n        return this._visibility === 'visible';\n    }\n    ngOnDestroy() {\n        this._onHide.complete();\n    }\n    _animationStart() {\n        this._closeOnInteraction = false;\n    }\n    _animationDone(event) {\n        const toState = event.toState;\n        if (toState === 'hidden' && !this.isVisible()) {\n            this._onHide.next();\n        }\n        if (toState === 'visible' || toState === 'hidden') {\n            this._closeOnInteraction = true;\n        }\n    }\n    /**\n     * Interactions on the HTML body should close the tooltip immediately as defined in the\n     * material design spec.\n     * https://material.io/design/components/tooltips.html#behavior\n     */\n    _handleBodyInteraction() {\n        if (this._closeOnInteraction) {\n            this.hide(0);\n        }\n    }\n    /**\n     * Marks that the tooltip needs to be checked in the next change detection run.\n     * Mainly used for rendering the initial text before positioning a tooltip, which\n     * can be problematic in components with OnPush change detection.\n     */\n    _markForCheck() {\n        this._changeDetectorRef.markForCheck();\n    }\n}\nTooltipComponent.ɵfac = function TooltipComponent_Factory(t) { return new (t || TooltipComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc6.BreakpointObserver)); };\nTooltipComponent.ɵcmp = /*@__PURE__*/ ɵngcc0.ɵɵdefineComponent({ type: TooltipComponent, selectors: [[\"mat-tooltip-component\"]], hostAttrs: [\"aria-hidden\", \"true\"], hostVars: 2, hostBindings: function TooltipComponent_HostBindings(rf, ctx) { if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function TooltipComponent_click_HostBindingHandler() { return ctx._handleBodyInteraction(); }, false, ɵngcc0.ɵɵresolveBody)(\"auxclick\", function TooltipComponent_auxclick_HostBindingHandler() { return ctx._handleBodyInteraction(); }, false, ɵngcc0.ɵɵresolveBody);\n    } if (rf & 2) {\n        ɵngcc0.ɵɵstyleProp(\"zoom\", ctx._visibility === \"visible\" ? 1 : null);\n    } }, decls: 3, vars: 7, consts: [[1, \"mat-tooltip\", 3, \"ngClass\"]], template: function TooltipComponent_Template(rf, ctx) { if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"div\", 0);\n        ɵngcc0.ɵɵlistener(\"@state.start\", function TooltipComponent_Template_div_animation_state_start_0_listener() { return ctx._animationStart(); })(\"@state.done\", function TooltipComponent_Template_div_animation_state_done_0_listener($event) { return ctx._animationDone($event); });\n        ɵngcc0.ɵɵpipe(1, \"async\");\n        ɵngcc0.ɵɵtext(2);\n        ɵngcc0.ɵɵelementEnd();\n    } if (rf & 2) {\n        let tmp_0_0;\n        ɵngcc0.ɵɵclassProp(\"mat-tooltip-handset\", (tmp_0_0 = ɵngcc0.ɵɵpipeBind1(1, 5, ctx._isHandset)) == null ? null : tmp_0_0.matches);\n        ɵngcc0.ɵɵproperty(\"ngClass\", ctx.tooltipClass)(\"@state\", ctx._visibility);\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵtextInterpolate(ctx.message);\n    } }, dependencies: [ɵngcc7.NgClass, ɵngcc7.AsyncPipe], styles: [\".mat-tooltip-panel{pointer-events:none !important}.mat-tooltip{color:#fff;border-radius:4px;margin:14px;max-width:250px;padding-left:8px;padding-right:8px;overflow:hidden;text-overflow:ellipsis}.cdk-high-contrast-active .mat-tooltip{outline:solid 1px}.mat-tooltip-handset{margin:24px;padding-left:16px;padding-right:16px}\\n\"], encapsulation: 2, data: { animation: [matTooltipAnimations.tooltipState] }, changeDetection: 0 });\nTooltipComponent.ctorParameters = () => [\n    { type: ChangeDetectorRef },\n    { type: BreakpointObserver }\n];\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TooltipComponent, [{\n        type: Component,\n        args: [{ selector: 'mat-tooltip-component', template: \"<div class=\\\"mat-tooltip\\\"\\n     [ngClass]=\\\"tooltipClass\\\"\\n     [class.mat-tooltip-handset]=\\\"(_isHandset | async)?.matches\\\"\\n     [@state]=\\\"_visibility\\\"\\n     (@state.start)=\\\"_animationStart()\\\"\\n     (@state.done)=\\\"_animationDone($event)\\\">{{message}}</div>\\n\", encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, animations: [matTooltipAnimations.tooltipState], host: {\n                    // Forces the element to have a layout in IE and Edge. This fixes issues where the element\n                    // won't be rendered if the animations are disabled or there is no web animations polyfill.\n                    '[style.zoom]': '_visibility === \"visible\" ? 1 : null',\n                    '(body:click)': 'this._handleBodyInteraction()',\n                    '(body:auxclick)': 'this._handleBodyInteraction()',\n                    'aria-hidden': 'true'\n                }, styles: [\".mat-tooltip-panel{pointer-events:none !important}.mat-tooltip{color:#fff;border-radius:4px;margin:14px;max-width:250px;padding-left:8px;padding-right:8px;overflow:hidden;text-overflow:ellipsis}.cdk-high-contrast-active .mat-tooltip{outline:solid 1px}.mat-tooltip-handset{margin:24px;padding-left:16px;padding-right:16px}\\n\"] }]\n    }], function () { return [{ type: ɵngcc0.ChangeDetectorRef }, { type: ɵngcc6.BreakpointObserver }]; }, null); })();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatTooltipModule {\n}\nMatTooltipModule.ɵfac = function MatTooltipModule_Factory(t) { return new (t || MatTooltipModule)(); };\nMatTooltipModule.ɵmod = /*@__PURE__*/ ɵngcc0.ɵɵdefineNgModule({ type: MatTooltipModule });\nMatTooltipModule.ɵinj = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjector({ providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [A11yModule,\n        CommonModule,\n        OverlayModule,\n        MatCommonModule, MatCommonModule, CdkScrollableModule] });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(MatTooltipModule, [{\n        type: NgModule,\n        args: [{\n                imports: [\n                    A11yModule,\n                    CommonModule,\n                    OverlayModule,\n                    MatCommonModule,\n                ],\n                exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n                declarations: [MatTooltip, TooltipComponent],\n                entryComponents: [TooltipComponent],\n                providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER]\n            }]\n    }], null, null); })();\n(function () { (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(MatTooltipModule, { declarations: function () { return [MatTooltip, TooltipComponent]; }, imports: function () { return [A11yModule,\n        CommonModule,\n        OverlayModule,\n        MatCommonModule]; }, exports: function () { return [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule]; } }); })();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TOOLTIP_DEFAULT_OPTIONS, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, MatTooltip, MatTooltipModule, SCROLL_THROTTLE_MS, TOOLTIP_PANEL_CLASS, TooltipComponent, getMatTooltipInvalidPositionError, matTooltipAnimations };\n\n", "import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\n\nexport interface MensagemChat {\n  texto: string;\n  remetente: string;\n  horario: string;\n  tipo?: 'entrada' | 'saida';\n}\n\nexport interface LinkLead {\n  tipo: string;\n  url: string;\n  descricao: string;\n  ordem: number;\n}\n\nexport interface DadosLead {\n  nome?: string;\n  telefone?: string;\n  email?: string;\n  cargo?: string;\n  empresa?: string;\n  segmento?: string;\n  tamanhoEmpresa?: 'Pequena' | 'Média' | 'Grande';\n  localizacao?: string;\n  instagram?: string;\n  linkedin?: string;\n  site?: string;\n  outrasRedes?: string;\n  etapaFunil?: string;\n  dataPrimeiroContato?: string;\n  ultimaInteracao?: string;\n  origemLead?: string;\n  scoreLead?: number;\n  interessesProdutos?: string[];\n  proximoFollowUp?: string;\n  historicoPropostas?: string;\n  observacoes?: string;\n  notas?: string;\n  links?: LinkLead[];\n}\n\nexport interface ContextoConversa {\n  mensagens: MensagemChat[];\n  contatoAtual?: string;\n  telefoneAtual?: string;\n  etapaFunil?: string;\n  tomConversa?: string;\n  dadosLead?: DadosLead;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ConversasService {\n  // BehaviorSubject para manter o estado atual do contexto da conversa\n  private contextoConversaSubject = new BehaviorSubject<ContextoConversa>({\n    mensagens: []\n  });\n\n  // Observable que outros componentes podem assinar\n  public contextoConversa$: Observable<ContextoConversa> = this.contextoConversaSubject.asObservable();\n\n  constructor() {\n    // Ouvir eventos da extensão do Chrome\n    this.configurarEventListener();\n  }\n\n  /**\n   * Configura o listener para receber atualizações da extensão do Chrome\n   */\n  private configurarEventListener(): void {\n    // Adiciona um event listener ao window para receber mensagens da extensão\n    window.addEventListener('message', (event) => {\n      // Verifica se a mensagem é do tipo que esperamos\n      if (event.data && event.data.type === 'crm_conversa_atualizada') {\n        console.log('Recebendo atualização de conversa do WhatsApp:', event.data.payload);\n        this.atualizarContextoConversa(event.data.payload);\n      }\n      \n      // Verifica se é uma atualização de contato selecionado\n      if (event.data && event.data.tipo === 'SELECIONOU_CONTATO') {\n        console.log('Contato selecionado no WhatsApp:', event.data.payload);\n        \n        const contextoAtual = this.contextoConversaSubject.getValue();\n        const novoContexto = {\n          ...contextoAtual,\n          contatoAtual: event.data.payload.nome || contextoAtual.contatoAtual,\n          telefoneAtual: event.data.payload.telefone || contextoAtual.telefoneAtual\n        };\n        \n        this.contextoConversaSubject.next(novoContexto);\n      }\n    });\n  }\n\n  /**\n   * Atualiza o contexto da conversa com novas mensagens\n   */\n  private atualizarContextoConversa(payload: any): void {\n    const contextoAtual = this.contextoConversaSubject.getValue();\n    \n    // Atualiza o contexto com as novas mensagens e informações\n    const novoContexto: ContextoConversa = {\n      mensagens: payload.mensagens || contextoAtual.mensagens,\n      contatoAtual: payload.contatoAtual || contextoAtual.contatoAtual,\n      telefoneAtual: payload.telefoneAtual || contextoAtual.telefoneAtual,\n      etapaFunil: payload.etapaFunil || contextoAtual.etapaFunil\n    };\n\n    // Emite o novo contexto para todos os assinantes\n    this.contextoConversaSubject.next(novoContexto);\n  }\n\n  /**\n   * Permite atualizar manualmente o contexto da conversa (para testes ou simulações)\n   */\n  public setContextoConversa(contexto: ContextoConversa): void {\n    this.contextoConversaSubject.next(contexto);\n  }\n\n  /**\n   * Adiciona uma nova mensagem ao contexto\n   */\n  public adicionarMensagem(mensagem: MensagemChat): void {\n    const contextoAtual = this.contextoConversaSubject.getValue();\n    const novasMensagens = [...contextoAtual.mensagens, mensagem];\n    \n    this.contextoConversaSubject.next({\n      ...contextoAtual,\n      mensagens: novasMensagens\n    });\n  }\n\n  /**\n   * Limpa todas as mensagens do contexto atual\n   */\n  public limparMensagens(): void {\n    const contextoAtual = this.contextoConversaSubject.getValue();\n    \n    this.contextoConversaSubject.next({\n      ...contextoAtual,\n      mensagens: []\n    });\n  }\n\n  /**\n   * Retorna o contexto atual da conversa\n   */\n  public getContextoAtual(): ContextoConversa {\n    return this.contextoConversaSubject.getValue();\n  }\n}", "import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { ServerService } from '../../services/ServerService';\n\n@Injectable()\nexport class LeadService extends ServerService {\n  constructor(protected http: HttpClient) {\n    super(http);\n  }\n\n  private endpoint = '/crm/leads';\n\n  liste(params: any = {}): Promise<any> {\n    return this.obtenha(this.endpoint, params);\n  }\n\n  selecione(id: number): Promise<any> {\n    return this.obtenha(`${this.endpoint}/${id}`, {});\n  }\n\n  salveLead(lead: any): Promise<any> {\n    if (lead.id) {\n      // Atualizar lead existente com PUT\n      return this.facaPut(`${this.endpoint}/${lead.id}`, lead);\n    } else {\n      // Criar novo lead com POST\n      return this.facaPost(this.endpoint, lead);\n    }\n  }\n\n  removaLead(id: number): Promise<any> {\n    return this.remova(`${this.endpoint}/${id}`, {});\n  }\n\n  buscarDadosInstagram(username: string): Promise<any> {\n    const params: any = { username };\n    return this.obtenha(`${this.endpoint}/dadosig`, params);\n  }\n\n  enviarDadosInstagram(texto: any, crmEmpresaId?: number, username?: string): Promise<any> {\n    const payload = {\n      texto,\n      username,\n      crmEmpresaId: crmEmpresaId || null\n    };\n    return this.facaPost(`${this.endpoint}/dadosig2`, payload);\n  }\n\n  analisarWebsite(url: string): Promise<any> {\n    const params = { url };\n    return this.obtenha(`${this.endpoint}/analisar-website`, params);\n  }\n\n  categorizarLinks(links: string[]): Promise<any> {\n    const payload = { links };\n    return this.facaPost(`${this.endpoint}/categorizar-links`, payload);\n  }\n\n  descobrirCnpj(nomeEmpresa: string, cidade?: string): Promise<any> {\n    const params = { nomeEmpresa, cidade };\n    return this.obtenha(`${this.endpoint}/descobrir-cnpj`, params);\n  }\n\n  buscarDetalhesSocios(cnpj: string): Promise<any> {\n    const payload = { cnpj };\n    return this.facaPost(`${this.endpoint}/buscar-detalhes-socios`, payload);\n  }\n\n  // ===== MÉTODOS PARA GERENCIAR LINKS =====\n\n  listarLinks(leadId: number): Promise<any> {\n    return this.obtenha(`${this.endpoint}/${leadId}/links`, {});\n  }\n\n  adicionarLink(leadId: number, link: any): Promise<any> {\n    return this.facaPost(`${this.endpoint}/${leadId}/links`, link);\n  }\n\n  atualizarLink(leadId: number, linkId: number, link: any): Promise<any> {\n    return this.facaPut(`${this.endpoint}/${leadId}/links/${linkId}`, link);\n  }\n\n  removerLink(leadId: number, linkId: number): Promise<any> {\n    return this.remova(`${this.endpoint}/${leadId}/links/${linkId}`, {});\n  }\n\n  removerLinkPorTipo(leadId: number, tipo: string): Promise<any> {\n    return this.remova(`${this.endpoint}/${leadId}/links/tipo/${tipo}`, {});\n  }\n}\n", "<div class=\"crm-home-container\">\n  <!-- Modern Header -->\n  <div class=\"modern-header\">\n    <div class=\"header-brand\">\n      <div class=\"brand-icon\">\n        <i class=\"fa fa-users\"></i>\n      </div>\n      <div class=\"brand-text\">\n        <h1 class=\"brand-title\">Assistente de Vendas</h1>\n        <span class=\"brand-subtitle\">CardápioTech CRM</span>\n      </div>\n    </div>\n    <div class=\"header-actions\">\n      <div class=\"status-indicator\">\n        <span class=\"status-dot active\"></span>\n        <span class=\"status-text\">Online</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- Loading State -->\n  <div class=\"loading-container\" *ngIf=\"carregandoLead\">\n    <div class=\"loading-card\">\n      <div class=\"loading-animation\">\n        <div class=\"spinner-modern\"></div>\n      </div>\n      <h3 class=\"loading-title\">Carregando dados do lead</h3>\n      <p class=\"loading-text\">Buscando informações para @{{ username }}...</p>\n    </div>\n  </div>\n\n  <!-- Lead Not Found -->\n  <div class=\"loading-container\" *ngIf=\"!carregandoLead && !leadEncontrado && username\">\n    <div class=\"loading-card warning\">\n      <div class=\"warning-icon\">\n        <i class=\"fa fa-exclamation-triangle\"></i>\n      </div>\n      <h3 class=\"loading-title\">Lead não encontrado</h3>\n      <p class=\"loading-text\">Não foi possível encontrar dados para @{{ username }}. Redirecionando...</p>\n    </div>\n  </div>\n\n  <!-- Modern Lead Detail Layout -->\n  <div class=\"lead-detail-layout\" *ngIf=\"!carregandoLead && leadEncontrado && dadosLeadAtual\">\n\n    <!-- Hero Header Card -->\n    <div class=\"hero-card\">\n      <div class=\"hero-content\">\n        <!-- Company & Contact Info -->\n        <div class=\"hero-main\">\n          <div class=\"company-info\">\n            <h1 class=\"company-name\">{{ dadosLeadAtual?.empresa || 'Empresa não informada' }}</h1>\n            <div class=\"contact-person\">\n              <i class=\"fa fa-user\"></i>\n              <span>{{ dadosLeadAtual?.nome || 'Contato não informado' }}</span>\n            </div>\n          </div>\n          \n          <!-- Score & Stage Badges -->\n          <div class=\"status-badges\">\n            <div class=\"score-badge\" [ngStyle]=\"{'background-color': getCorDoScore(dadosLeadAtual?.scoreLead)}\">\n              <i class=\"fa fa-star\"></i>\n              <span>{{ formatarScore(dadosLeadAtual?.scoreLead) }}</span>\n            </div>\n            <div class=\"stage-badge\" [ngClass]=\"'stage-' + (dadosLeadAtual?.etapaFunil?.toLowerCase() || 'indefinida')\">\n              <i class=\"fa fa-flag\"></i>\n              <span>{{ dadosLeadAtual?.etapaFunil || 'Indefinida' }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Primary Action Buttons -->\n        <div class=\"hero-actions\">\n          <a class=\"action-btn primary\" \n             *ngIf=\"dadosLeadAtual?.telefone\"\n             [href]=\"getWhatsAppUrl(dadosLeadAtual?.telefone)\"\n             target=\"_blank\"\n             title=\"Enviar WhatsApp\">\n            <i class=\"fab fa-whatsapp\"></i>\n            <span>WhatsApp</span>\n          </a>\n          <a class=\"action-btn secondary\" \n             *ngIf=\"dadosLeadAtual?.telefone\"\n             [href]=\"'tel:' + dadosLeadAtual?.telefone\"\n             title=\"Ligar agora\">\n            <i class=\"fa fa-phone\"></i>\n            <span>Ligar</span>\n          </a>\n          <a class=\"action-btn secondary\" \n             *ngIf=\"dadosLeadAtual?.email\"\n             [href]=\"'mailto:' + dadosLeadAtual?.email\"\n             title=\"Enviar email\">\n            <i class=\"fa fa-envelope\"></i>\n            <span>Email</span>\n          </a>\n        </div>\n      </div>\n    </div>\n\n    <!-- Quick Info Grid -->\n    <div class=\"info-grid\">\n      <!-- Contact Information -->\n      <div class=\"info-card\">\n        <div class=\"card-header\">\n          <i class=\"fa fa-address-book\"></i>\n          <h3>Contato</h3>\n        </div>\n        <div class=\"card-content\">\n          <div class=\"info-item\" *ngIf=\"dadosLeadAtual?.telefone\">\n            <div class=\"info-icon\">\n              <i class=\"fa fa-phone\"></i>\n            </div>\n            <div class=\"info-details\">\n              <span class=\"info-label\">Telefone</span>\n              <span class=\"info-value\">{{ dadosLeadAtual?.telefone }}</span>\n            </div>\n          </div>\n          \n          <div class=\"info-item\" *ngIf=\"dadosLeadAtual?.email\">\n            <div class=\"info-icon\">\n              <i class=\"fa fa-envelope\"></i>\n            </div>\n            <div class=\"info-details\">\n              <span class=\"info-label\">Email</span>\n              <span class=\"info-value\">{{ dadosLeadAtual?.email }}</span>\n            </div>\n          </div>\n\n          <div class=\"info-item\" *ngIf=\"dadosLeadAtual?.instagram\">\n            <div class=\"info-icon instagram\">\n              <i class=\"fab fa-instagram\"></i>\n            </div>\n            <div class=\"info-details\">\n              <span class=\"info-label\">Instagram</span>\n              <a [href]=\"getInstagramUrl(dadosLeadAtual?.instagram)\" \n                 target=\"_blank\" \n                 class=\"info-value link\">{{ dadosLeadAtual?.instagram }}</a>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Business Information -->\n      <div class=\"info-card\">\n        <div class=\"card-header\">\n          <i class=\"fa fa-building\"></i>\n          <h3>Negócio</h3>\n        </div>\n        <div class=\"card-content\">\n          <div class=\"info-item\" *ngIf=\"dadosLeadAtual?.segmento\">\n            <div class=\"info-icon\">\n              <i class=\"fa fa-tag\"></i>\n            </div>\n            <div class=\"info-details\">\n              <span class=\"info-label\">Segmento</span>\n              <span class=\"info-value\">{{ dadosLeadAtual?.segmento }}</span>\n            </div>\n          </div>\n\n          <div class=\"info-item\" *ngIf=\"dadosLeadAtual?.origemLead\">\n            <div class=\"info-icon\">\n              <i class=\"fa fa-external-link\"></i>\n            </div>\n            <div class=\"info-details\">\n              <span class=\"info-label\">Origem</span>\n              <span class=\"info-value\">{{ dadosLeadAtual?.origemLead }}</span>\n            </div>\n          </div>\n\n          <div class=\"info-item\" *ngIf=\"dadosLeadAtual?.tamanhoEmpresa\">\n            <div class=\"info-icon\">\n              <i class=\"fa fa-users\"></i>\n            </div>\n            <div class=\"info-details\">\n              <span class=\"info-label\">Porte</span>\n              <span class=\"info-value\">{{ dadosLeadAtual?.tamanhoEmpresa }}</span>\n            </div>\n          </div>\n\n          <div class=\"info-item\" *ngIf=\"dadosLeadAtual?.localizacao\">\n            <div class=\"info-icon\">\n              <i class=\"fa fa-map-marker\"></i>\n            </div>\n            <div class=\"info-details\">\n              <span class=\"info-label\">Localização</span>\n              <span class=\"info-value\">{{ dadosLeadAtual?.localizacao }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Timeline Information -->\n    <div class=\"timeline-card\" *ngIf=\"dadosLeadAtual?.dataPrimeiroContato || dadosLeadAtual?.ultimaInteracao || dadosLeadAtual?.proximoFollowUp\">\n      <div class=\"card-header\">\n        <i class=\"fa fa-clock-o\"></i>\n        <h3>Timeline</h3>\n      </div>\n      <div class=\"timeline-content\">\n        <div class=\"timeline-item\" *ngIf=\"dadosLeadAtual?.dataPrimeiroContato\">\n          <div class=\"timeline-icon\">\n            <i class=\"fa fa-handshake-o\"></i>\n          </div>\n          <div class=\"timeline-details\">\n            <span class=\"timeline-label\">Primeiro Contato</span>\n            <span class=\"timeline-value\">{{ dadosLeadAtual?.dataPrimeiroContato }}</span>\n          </div>\n        </div>\n\n        <div class=\"timeline-item\" *ngIf=\"dadosLeadAtual?.ultimaInteracao\">\n          <div class=\"timeline-icon\">\n            <i class=\"fa fa-comments\"></i>\n          </div>\n          <div class=\"timeline-details\">\n            <span class=\"timeline-label\">Última Interação</span>\n            <span class=\"timeline-value\">{{ dadosLeadAtual?.ultimaInteracao }}</span>\n          </div>\n        </div>\n\n        <div class=\"timeline-item priority\" *ngIf=\"dadosLeadAtual?.proximoFollowUp\">\n          <div class=\"timeline-icon\">\n            <i class=\"fa fa-calendar\"></i>\n          </div>\n          <div class=\"timeline-details\">\n            <span class=\"timeline-label\">Próximo Follow-up</span>\n            <span class=\"timeline-value highlight\">{{ dadosLeadAtual?.proximoFollowUp }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Business Links -->\n    <div class=\"links-card\" *ngIf=\"dadosLeadAtual?.links && dadosLeadAtual?.links.length > 0\">\n      <div class=\"card-header\">\n        <i class=\"fa fa-link\"></i>\n        <h3>Links Importantes</h3>\n      </div>\n      <div class=\"links-grid\">\n        <a *ngFor=\"let link of dadosLeadAtual?.links\" \n           [href]=\"getLinkUrl(link)\"\n           target=\"_blank\"\n           class=\"link-item\"\n           [title]=\"link.descricao || 'Abrir ' + link.tipo\">\n          <div class=\"link-icon\" [style.color]=\"getLinkColor(link.tipo)\">\n            <i [ngClass]=\"getLinkIcon(link.tipo)\"></i>\n          </div>\n          <div class=\"link-content\">\n            <span class=\"link-type\">{{ link.tipo }}</span>\n            <span class=\"link-description\">{{ link.descricao || link.url }}</span>\n          </div>\n          <div class=\"link-action\">\n            <i class=\"fa fa-external-link\"></i>\n          </div>\n        </a>\n      </div>\n    </div>\n\n    <!-- Website Link -->\n    <div class=\"website-card\" *ngIf=\"dadosLeadAtual?.site\">\n      <a [href]=\"getWebsiteUrl(dadosLeadAtual?.site)\" \n         target=\"_blank\"\n         class=\"website-link\">\n        <div class=\"website-icon\">\n          <i class=\"fa fa-globe\"></i>\n        </div>\n        <div class=\"website-content\">\n          <span class=\"website-label\">Website</span>\n          <span class=\"website-url\">{{ dadosLeadAtual?.site }}</span>\n        </div>\n        <div class=\"website-action\">\n          <i class=\"fa fa-external-link\"></i>\n        </div>\n      </a>\n    </div>\n\n    <!-- Notes & Observations -->\n    <div class=\"notes-card\" *ngIf=\"dadosLeadAtual?.observacoes || dadosLeadAtual?.historicoPropostas\">\n      <div class=\"card-header\">\n        <i class=\"fa fa-sticky-note\"></i>\n        <h3>Observações</h3>\n      </div>\n      <div class=\"notes-content\">\n        <div class=\"note-section\" *ngIf=\"dadosLeadAtual?.observacoes\">\n          <div class=\"note-header\">\n            <i class=\"fa fa-edit\"></i>\n            <span>Observações de Vendas</span>\n          </div>\n          <p class=\"note-text\">{{ dadosLeadAtual?.observacoes }}</p>\n        </div>\n\n        <div class=\"note-section\" *ngIf=\"dadosLeadAtual?.historicoPropostas\">\n          <div class=\"note-header\">\n            <i class=\"fa fa-file-text\"></i>\n            <span>Histórico de Propostas</span>\n          </div>\n          <p class=\"note-text\">{{ dadosLeadAtual?.historicoPropostas }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Interests Tags -->\n    <div class=\"interests-card\" *ngIf=\"dadosLeadAtual?.interessesProdutos && dadosLeadAtual?.interessesProdutos.length > 0\">\n      <div class=\"card-header\">\n        <i class=\"fa fa-star\"></i>\n        <h3>Interesses</h3>\n      </div>\n      <div class=\"interests-content\">\n        <span class=\"interest-tag\" *ngFor=\"let interesse of dadosLeadAtual?.interessesProdutos\">\n          <i class=\"fa fa-tag\"></i>\n          {{ interesse }}\n        </span>\n      </div>\n    </div>\n\n  </div>\n</div>", "import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ConversasService, MensagemChat, DadosLead, LinkLead } from '../services/conversas.service';\nimport { LeadService } from '../services/lead.service';\n\n@Component({\n  selector: 'app-crm-home',\n  templateUrl: './crm-home.component.html',\n  styleUrls: ['./crm-home.component.scss']\n})\nexport class CrmHomeComponent implements OnInit {\n  // Etapa do funil atual para teste\n  etapaAtual = 'Conectado';\n\n  // Dados do lead atual\n  dadosLeadAtual: DadosLead | undefined;\n\n  // Username do Instagram\n  username: string | null = null;\n\n  // Estado da busca\n  carregandoLead = false;\n  leadEncontrado = false;\n\n  constructor(\n    private conversasService: ConversasService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private leadService: LeadService\n  ) {}\n\n  ngOnInit(): void {\n    // Captura o parâmetro username da rota\n    this.route.paramMap.subscribe(params => {\n      this.username = params.get('username');\n      console.log('Username capturado da rota:', this.username);\n\n      if (this.username) {\n        this.buscarLeadPorUsername(this.username);\n      } else {\n        this.inicializarModoDemonstracao();\n      }\n\n      // Debug do estado após inicialização\n      setTimeout(() => this.debugEstado(), 100);\n    });\n\n    // Subscreve para atualizações no contexto da conversa\n    this.conversasService.contextoConversa$.subscribe(contexto => {\n      if (contexto && contexto.dadosLead) {\n        this.dadosLeadAtual = contexto.dadosLead;\n      }\n    });\n\n  }\n\n  /**\n   * Simula o recebimento de novas mensagens para testar o componente de sugestões\n   */\n  simularNovasMensagens(): void {\n    const horaAtual = new Date().toLocaleTimeString();\n\n    // Alterna entre diferentes conjuntos de simulações de mensagens para testar\n    // diferentes cenários e sugestões\n    const cenarios = [\n      // Cenário 1: Cliente interessado no preço\n      {\n        mensagens: [\n          {\n            texto: 'Olá, estou interessado no seu sistema de gestão. Quanto custa?',\n            remetente: 'Cliente',\n            horario: horaAtual,\n            tipo: 'entrada' as const\n          },\n          {\n            texto: 'Olá! Obrigado pelo interesse. Nossos planos começam a partir de R$99/mês.',\n            remetente: 'Eu',\n            horario: horaAtual,\n            tipo: 'saida' as const\n          },\n          {\n            texto: 'Isso parece interessante. Vocês oferecem alguma demonstração?',\n            remetente: 'Cliente',\n            horario: horaAtual,\n            tipo: 'entrada' as const\n          }\n        ],\n        contato: 'João Silva',\n        telefone: '+5511987654321',\n        etapa: 'Qualificação'\n      },\n\n      // Cenário 2: Cliente com objeção de preço\n      {\n        mensagens: [\n          {\n            texto: 'Obrigado pelas informações, mas achei um pouco caro para o meu negócio atual.',\n            remetente: 'Cliente',\n            horario: horaAtual,\n            tipo: 'entrada' as const\n          },\n          {\n            texto: 'Entendo sua preocupação com o investimento. Você já avaliou o retorno que pode ter?',\n            remetente: 'Eu',\n            horario: horaAtual,\n            tipo: 'saida' as const\n          },\n          {\n            texto: 'Ainda não calculei. Teria como me passar mais detalhes sobre isso?',\n            remetente: 'Cliente',\n            horario: horaAtual,\n            tipo: 'entrada' as const\n          }\n        ],\n        contato: 'Maria Oliveira',\n        telefone: '+5511976543210',\n        etapa: 'Objeção'\n      },\n\n      // Cenário 3: Cliente pronto para fechar\n      {\n        mensagens: [\n          {\n            texto: 'Fiquei muito satisfeito com a demonstração. Como fazemos para avançar?',\n            remetente: 'Cliente',\n            horario: horaAtual,\n            tipo: 'entrada' as const\n          },\n          {\n            texto: 'Ótimo! Posso enviar a proposta comercial e os próximos passos por e-mail.',\n            remetente: 'Eu',\n            horario: horaAtual,\n            tipo: 'saida' as const\n          },\n          {\n            texto: 'Perfeito. E quanto tempo leva para implementar o sistema?',\n            remetente: 'Cliente',\n            horario: horaAtual,\n            tipo: 'entrada' as const\n          }\n        ],\n        contato: 'Ricardo Mendes',\n        telefone: '+5511955556666',\n        etapa: 'Fechamento'\n      }\n    ];\n\n    // Seleciona um cenário aleatoriamente\n    const cenarioAtual = cenarios[Math.floor(Math.random() * cenarios.length)];\n\n    // Cria dados de lead simulados para teste\n    const dadosLeadSimulados = this.gerarDadosLeadSimulados(cenarioAtual.contato, cenarioAtual.etapa);\n\n    // Atualiza o contexto da conversa com as mensagens simuladas e dados do lead\n    this.conversasService.setContextoConversa({\n      mensagens: cenarioAtual.mensagens,\n      contatoAtual: cenarioAtual.contato,\n      telefoneAtual: cenarioAtual.telefone,\n      etapaFunil: cenarioAtual.etapa,\n      dadosLead: dadosLeadSimulados\n    });\n\n    // Força a atualização local para garantir que os dados sejam exibidos\n    this.dadosLeadAtual = dadosLeadSimulados;\n\n    // Atualiza a etapa atual para o componente saber\n    this.etapaAtual = cenarioAtual.etapa;\n  }\n\n  /**\n   * Gera dados simulados de lead para testes\n   */\n  private gerarDadosLeadSimulados(nome: string, etapa: string): DadosLead {\n    // Define diferentes perfis de lead baseados no nome\n    const perfis = {\n      'João Silva': {\n        email: '<EMAIL>',\n        cargo: 'Gerente de Operações',\n        empresa: 'Pizzaria Bella Napoli',\n        segmento: 'Alimentação',\n        tamanhoEmpresa: 'Pequena' as const,\n        localizacao: 'São Paulo, SP',\n        instagram: '@bellanapoli_pizzaria',\n        site: 'www.bellanapoli.com.br',\n        dataPrimeiroContato: '10/05/2023',\n        ultimaInteracao: new Date().toLocaleDateString(),\n        origemLead: 'Site',\n        scoreLead: 75,\n        interessesProdutos: ['Cardápio Digital', 'Gestão de Pedidos'],\n        proximoFollowUp: this.gerarDataFutura(3),\n        historicoPropostas: 'Enviada proposta inicial em 15/05/2023',\n        observacoes: 'Cliente demonstra interesse em automatizar atendimento',\n        links: [\n          { tipo: 'Ifood', url: 'https://www.ifood.com.br/delivery/sao-paulo-sp/pizzaria-bella-napoli', descricao: 'Cardápio no iFood', ordem: 1 },\n          { tipo: 'Instagram', url: 'https://instagram.com/bellanapoli_pizzaria', descricao: 'Perfil no Instagram', ordem: 2 },\n          { tipo: 'Localização', url: 'Rua das Flores, 123 - São Paulo, SP', descricao: 'Endereço da pizzaria', ordem: 3 }\n        ]\n      },\n      'Maria Oliveira': {\n        email: '<EMAIL>',\n        cargo: 'Proprietária',\n        empresa: 'Confeitaria Doce Sabor',\n        segmento: 'Alimentação',\n        tamanhoEmpresa: 'Pequena' as const,\n        localizacao: 'Rio de Janeiro, RJ',\n        instagram: '@docesabor_confeitaria',\n        linkedin: 'linkedin.com/in/mariaoliveira',\n        site: 'www.docesabor.com.br',\n        dataPrimeiroContato: '22/04/2023',\n        ultimaInteracao: new Date().toLocaleDateString(),\n        origemLead: 'Instagram',\n        scoreLead: 60,\n        interessesProdutos: ['Sistema de Delivery', 'Fidelização'],\n        proximoFollowUp: this.gerarDataFutura(2),\n        observacoes: 'Preocupada com custo-benefício',\n        links: [\n          { tipo: 'Site', url: 'https://www.docesabor.com.br', descricao: 'Website oficial', ordem: 1 },\n          { tipo: 'WhatsApp', url: '21987654321', descricao: 'WhatsApp para pedidos', ordem: 2 },\n          { tipo: 'Instagram', url: 'https://instagram.com/docesabor_confeitaria', descricao: 'Instagram da confeitaria', ordem: 3 }\n        ]\n      },\n      'Ricardo Mendes': {\n        email: '<EMAIL>',\n        cargo: 'Diretor',\n        empresa: 'Restaurante Fusion',\n        segmento: 'Alimentação',\n        tamanhoEmpresa: 'Média' as const,\n        localizacao: 'Belo Horizonte, MG',\n        instagram: '@restaurantefusion',\n        linkedin: 'linkedin.com/in/ricardomendes',\n        site: 'www.restaurantefusion.com.br',\n        dataPrimeiroContato: '03/06/2023',\n        ultimaInteracao: new Date().toLocaleDateString(),\n        origemLead: 'Indicação',\n        scoreLead: 90,\n        interessesProdutos: ['Sistema Completo', 'Integração PDV'],\n        proximoFollowUp: this.gerarDataFutura(1),\n        historicoPropostas: 'Apresentação realizada em 10/06/2023. Proposta enviada em 12/06/2023',\n        observacoes: 'Cliente com alto potencial, já testou a solução concorrente',\n        links: [\n          { tipo: 'Site', url: 'https://www.restaurantefusion.com.br', descricao: 'Website do restaurante', ordem: 1 },\n          { tipo: 'Ifood', url: 'https://www.ifood.com.br/delivery/belo-horizonte-mg/restaurante-fusion', descricao: 'Delivery no iFood', ordem: 2 },\n          { tipo: 'Reservas', url: 'https://www.opentable.com.br/restaurante-fusion', descricao: 'Sistema de reservas', ordem: 3 },\n          { tipo: 'Concorrente', url: 'https://sistema-concorrente.com.br', descricao: 'Sistema atual em uso', ordem: 4 }\n        ]\n      }\n    };\n\n    // Seleciona o perfil correspondente ou cria um genérico\n    const perfil = perfis[nome] || {\n      email: `${nome.toLowerCase().replace(' ', '.')}@email.com`,\n      cargo: 'Proprietário',\n      empresa: 'Empresa Exemplo',\n      segmento: 'Alimentação',\n      tamanhoEmpresa: 'Pequena' as const,\n      dataPrimeiroContato: new Date().toLocaleDateString(),\n      ultimaInteracao: new Date().toLocaleDateString(),\n      origemLead: 'WhatsApp',\n      scoreLead: 50\n    };\n\n    // Adiciona nome, telefone e etapa ao perfil\n    return {\n      ...perfil,\n      nome: nome,\n      telefone: this.gerarTelefoneAleatorio(),\n      etapaFunil: etapa\n    };\n  }\n\n  /**\n   * Gera uma data futura para simulação\n   */\n  private gerarDataFutura(diasAFrente: number): string {\n    const data = new Date();\n    data.setDate(data.getDate() + diasAFrente);\n    return data.toLocaleDateString();\n  }\n\n  /**\n   * Gera um número de telefone aleatório para simulação\n   */\n  private gerarTelefoneAleatorio(): string {\n    const ddd = Math.floor(Math.random() * 89) + 11; // DDD entre 11 e 99\n    const parte1 = Math.floor(Math.random() * 9000) + 1000; // 4 dígitos\n    const parte2 = Math.floor(Math.random() * 9000) + 1000; // 4 dígitos\n    return `+55${ddd}9${parte1}${parte2}`;\n  }\n\n\n  /**\n   * Retorna uma cor baseada no score do lead\n   */\n  getCorDoScore(score: number | undefined): string {\n    if (!score) return '#999'; // Cinza para score indefinido\n\n    if (score >= 80) return '#2ecc71'; // Verde para score alto\n    if (score >= 50) return '#f39c12'; // Amarelo para score médio\n    return '#e74c3c'; // Vermelho para score baixo\n  }\n\n  /**\n   * Formata o score para exibição\n   */\n  formatarScore(score: number | undefined): string {\n    if (!score && score !== 0) return 'N/A';\n    return `${score}%`;\n  }\n\n  /**\n   * Gera URL do WhatsApp\n   */\n  getWhatsAppUrl(telefone: string): string {\n    if (!telefone) return '#';\n    const numeroLimpo = telefone.replace(/\\D/g, '');\n    return `https://wa.me/55${numeroLimpo}`;\n  }\n\n  /**\n   * Gera URL do Instagram\n   */\n  getInstagramUrl(instagram: string): string {\n    if (!instagram) return '#';\n    const username = instagram.replace('@', '');\n    return `https://instagram.com/${username}`;\n  }\n\n  /**\n   * Gera URL do website\n   */\n  getWebsiteUrl(site: string): string {\n    if (!site) return '#';\n    return site.startsWith('http') ? site : `https://${site}`;\n  }\n\n  /**\n   * Obtém o ícone apropriado para cada tipo de link\n   */\n  getLinkIcon(tipo: string): string {\n    const icones: { [key: string]: string } = {\n      'Ifood': 'fa fa-utensils',\n      'Site do Cardápio': 'fa fa-list-alt',\n      'Concorrente': 'fa fa-exclamation-triangle',\n      'Reservas': 'fa fa-calendar-check',\n      'WhatsApp': 'fab fa-whatsapp',\n      'Localização': 'fa fa-map-marker-alt',\n      'Site': 'fa fa-globe',\n      'Instagram': 'fa fa-instagram'\n    };\n    return icones[tipo] || 'fa fa-link';\n  }\n\n  /**\n   * Obtém a cor apropriada para cada tipo de link\n   */\n  getLinkColor(tipo: string): string {\n    const cores: { [key: string]: string } = {\n      'Ifood': '#ea1d2c',\n      'Site do Cardápio': '#007bff',\n      'Concorrente': '#ff6b35',\n      'Reservas': '#28a745',\n      'WhatsApp': '#25d366',\n      'Localização': '#dc3545',\n      'Site': '#6c757d',\n      'Instagram': '#e4405f'\n    };\n    return cores[tipo] || '#6c757d';\n  }\n\n\n  /**\n   * Formata a URL do link baseado no tipo\n   */\n  getLinkUrl(link: LinkLead): string {\n    if (!link.url) return '#';\n\n    switch (link.tipo) {\n      case 'WhatsApp':\n        return this.formatarWhatsAppLink(link.url);\n      case 'Localização':\n        return this.formatarLocalizacaoLink(link.url);\n      default:\n        return link.url.startsWith('http') ? link.url : `https://${link.url}`;\n    }\n  }\n\n  /**\n   * Obtém o texto de exibição para o link\n   */\n  getLinkDisplayText(link: LinkLead): string {\n    if (link.descricao) return link.descricao;\n\n    switch (link.tipo) {\n      case 'WhatsApp':\n        return 'WhatsApp';\n      case 'Localização':\n        return 'Ver no Mapa';\n      case 'Ifood':\n        return 'Cardápio iFood';\n      case 'Site do Cardápio':\n        return 'Cardápio Online';\n      case 'Concorrente':\n        return 'Sistema Concorrente';\n      case 'Reservas':\n        return 'Fazer Reserva';\n      default:\n        return link.url;\n    }\n  }\n\n  /**\n   * Formata link do WhatsApp\n   */\n  private formatarWhatsAppLink(url: string): string {\n    // Se já é um link do WhatsApp, retorna como está\n    if (url.includes('wa.me') || url.includes('whatsapp.com')) {\n      return url;\n    }\n\n    // Se é apenas um número, formata para link do WhatsApp\n    const numeroLimpo = url.replace(/\\D/g, '');\n    if (numeroLimpo.length >= 10) {\n      return `https://wa.me/55${numeroLimpo}`;\n    }\n\n    return url;\n  }\n\n  /**\n   * Formata link de localização\n   */\n  private formatarLocalizacaoLink(url: string): string {\n    // Se já é um link do Google Maps, retorna como está\n    if (url.includes('maps.google.com') || url.includes('goo.gl/maps')) {\n      return url;\n    }\n\n    // Se parece ser coordenadas ou endereço, formata para Google Maps\n    if (url.includes(',') || url.includes('rua') || url.includes('av')) {\n      return `https://maps.google.com/maps?q=${encodeURIComponent(url)}`;\n    }\n\n    return url;\n  }\n\n  /**\n   * Busca lead na base de dados pelo username do Instagram\n   */\n  async buscarLeadPorUsername(username: string): Promise<void> {\n    console.log('Iniciando busca para username:', username);\n    this.carregandoLead = true;\n\n    try {\n      // Usa o LeadService para buscar por todos os leads com filtro por username\n      const response = await this.leadService.liste({ \n        texto: username\n      });\n      \n      console.log('Resposta da API via LeadService:', response);\n\n      if (response && response.data && response.data.length > 0) {\n        // Procura o lead exato pelo username do Instagram\n        const lead = response.data.find((l: any) => \n          l.instagramHandle === username || \n          l.instagramHandle === username.replace('@', '')\n        );\n\n        if (lead) {\n          // Lead encontrado - converter para formato DadosLead\n          this.dadosLeadAtual = this.converterLeadParaDadosLead(lead);\n          this.leadEncontrado = true;\n          console.log('Lead encontrado e convertido:', this.dadosLeadAtual);\n\n          // Atualizar contexto da conversa\n          this.conversasService.setContextoConversa({\n            mensagens: [],\n            contatoAtual: lead.nomeResponsavel,\n            telefoneAtual: lead.telefone,\n            etapaFunil: lead.etapa,\n            dadosLead: this.dadosLeadAtual\n          });\n        } else {\n          // Username não encontrado na lista\n          console.log('Username específico não encontrado na lista de leads');\n          this.leadEncontrado = false;\n          this.dadosLeadAtual = undefined;\n        }\n      } else {\n        // Nenhum lead encontrado\n        console.log('Nenhum lead encontrado na resposta da API');\n        this.leadEncontrado = false;\n        this.dadosLeadAtual = undefined;\n      }\n    } catch (error) {\n      console.error('Erro ao buscar lead via LeadService:', error);\n      this.leadEncontrado = false;\n      this.dadosLeadAtual = undefined;\n    } finally {\n      this.carregandoLead = false;\n      console.log('Estado final da busca - leadEncontrado:', this.leadEncontrado, 'carregandoLead:', this.carregandoLead);\n      \n      // Passo 1: Redirecionamento automático se lead não foi encontrado\n      if (!this.leadEncontrado && this.username) {\n        console.log('Lead não encontrado, redirecionando para novo lead');\n        this.router.navigate(['/crm/novo-lead'], {\n          queryParams: { username: this.username }\n        });\n      }\n    }\n  }\n\n  /**\n   * Converte objeto Lead do backend para DadosLead do frontend\n   */\n  private converterLeadParaDadosLead(lead: any): DadosLead {\n    // Debug: Log da estrutura completa do lead para entender os dados\n    console.log('CRM-HOME: Dados completos do lead recebido:', lead);\n    console.log('CRM-HOME: Campo empresa:', lead.empresa);\n    console.log('CRM-HOME: Campo crmEmpresa:', lead.crmEmpresa);\n    console.log('CRM-HOME: Campos disponíveis:', Object.keys(lead));\n\n    // Converter links se existirem\n    let linksConvertidos: LinkLead[] = [];\n    if (lead.links && Array.isArray(lead.links)) {\n      linksConvertidos = lead.links.map((link: any) => ({\n        tipo: link.tipo,\n        url: link.url,\n        descricao: link.descricao,\n        ordem: link.ordem\n      })).sort((a: LinkLead, b: LinkLead) => a.ordem - b.ordem);\n    }\n\n    // Determinar nome da empresa com fallbacks\n    let nomeEmpresa = '';\n    \n    if (lead.empresa && lead.empresa.trim()) {\n      nomeEmpresa = lead.empresa.trim();\n      console.log('CRM-HOME: Usando lead.empresa:', nomeEmpresa);\n    } else if (lead.crmEmpresa?.nome && lead.crmEmpresa.nome.trim()) {\n      nomeEmpresa = lead.crmEmpresa.nome.trim();\n      console.log('CRM-HOME: Usando lead.crmEmpresa.nome:', nomeEmpresa);\n    } else if (lead.instagramHandle) {\n      // Fallback: usar o Instagram handle formatado como empresa\n      nomeEmpresa = lead.instagramHandle.replace('@', '').replace(/[_.-]/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n      console.log('CRM-HOME: Usando Instagram handle formatado como empresa:', nomeEmpresa);\n    } else {\n      nomeEmpresa = 'Empresa não informada';\n      console.log('CRM-HOME: Nenhum nome de empresa encontrado, usando fallback');\n    }\n\n    const dadosConvertidos = {\n      nome: lead.nomeResponsavel,\n      telefone: lead.telefone,\n      email: lead.crmEmpresa?.email || '',\n      cargo: 'Proprietário',\n      empresa: nomeEmpresa,\n      segmento: lead.segmento || 'Alimentação',\n      tamanhoEmpresa: 'Pequena' as const,\n      localizacao: lead.instagramData?.location || '',\n      instagram: lead.instagramHandle ? `@${lead.instagramHandle}` : '',\n      site: lead.linkInsta || lead.instagramData?.website || '',\n      dataPrimeiroContato: lead.dataCriacao ? new Date(lead.dataCriacao).toLocaleDateString() : '',\n      ultimaInteracao: lead.dataUltimaInteracao ? new Date(lead.dataUltimaInteracao).toLocaleDateString() : new Date().toLocaleDateString(),\n      origemLead: lead.origem,\n      scoreLead: lead.score,\n      etapaFunil: lead.etapa,\n      interessesProdutos: [],\n      proximoFollowUp: lead.dataProximoFollowup ? new Date(lead.dataProximoFollowup).toLocaleDateString() : '',\n      observacoes: lead.observacoes || '',\n      notas: lead.notas || '',\n      links: linksConvertidos\n    };\n\n    console.log('CRM-HOME: Dados convertidos para exibição:', dadosConvertidos);\n    return dadosConvertidos;\n  }\n\n  /**\n   * Inicializa o modo demonstração quando não há username\n   */\n  private inicializarModoDemonstracao(): void {\n    this.leadEncontrado = true;\n    this.simularNovasMensagens();\n  }\n\n\n  /**\n   * Método para debug - mostra o estado atual\n   */\n  debugEstado(): void {\n    console.log('=== Estado atual do componente ===');\n    console.log('username:', this.username);\n    console.log('carregandoLead:', this.carregandoLead);\n    console.log('leadEncontrado:', this.leadEncontrado);\n    console.log('dadosLeadAtual:', this.dadosLeadAtual);\n  }\n}\n", "import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { ServerService } from '../../services/ServerService';\n\n@Injectable()\nexport class CrmEmpresaService extends ServerService {\n  constructor(protected http: HttpClient) {\n    super(http);\n  }\n\n  private endpoint = '/crm/empresas';\n\n  liste(params: any = {}): Promise<any> {\n    return this.obtenha(this.endpoint, params);\n  }\n\n  selecione(id: number): Promise<any> {\n    return this.obtenha(`${this.endpoint}/${id}`, {});\n  }\n\n  salveEmpresa(empresa: any): Promise<any> {\n    return this.salve(this.endpoint, empresa);\n  }\n\n  removaEmpresa(id: number): Promise<any> {\n    return this.remova(`${this.endpoint}/${id}`, {});\n  }\n} ", "<div class=\"container-fluid\">\n  <div class=\"d-flex justify-content-between align-items-center mb-4\">\n    <h3><i class=\"fa fa-users\"></i> Gerenciamento de Leads</h3>\n    <div class=\"d-flex\">\n      <button class=\"btn btn-outline-secondary mr-2\" (click)=\"toggleFiltros()\">\n        <i class=\"fa fa-filter\"></i> Filtros\n      </button>\n      <button class=\"btn btn-primary\" (click)=\"novo()\" *ngIf=\"!modoEdicao\">\n        <i class=\"fa fa-plus\"></i> Novo Lead\n      </button>\n    </div>\n  </div>\n\n  <!-- Filtros Rápidos -->\n  <div class=\"card mb-4\" *ngIf=\"mostrarFiltros\">\n    <div class=\"card-body\">\n      <div class=\"row\">\n        <div class=\"col-md-3\">\n          <label><strong>Etapa</strong></label>\n          <select class=\"form-control\" [(ngModel)]=\"filtroEtapa\" (ngModelChange)=\"aplicarFiltros()\">\n            <option value=\"\">Todas as Etapas</option>\n            <option *ngFor=\"let etapa of etapas\" [value]=\"etapa.valor\">{{ etapa.texto }}</option>\n          </select>\n        </div>\n        <div class=\"col-md-3\">\n          <label><strong>Origem</strong></label>\n          <select class=\"form-control\" [(ngModel)]=\"filtroOrigem\" (ngModelChange)=\"aplicarFiltros()\">\n            <option value=\"\">Todas as Origens</option>\n            <option *ngFor=\"let origem of origens\" [value]=\"origem.valor\">{{ origem.texto }}</option>\n          </select>\n        </div>\n        <div class=\"col-md-4\">\n          <label><strong>Buscar</strong></label>\n          <input type=\"text\" class=\"form-control\" [(ngModel)]=\"filtroTexto\" \n                 (ngModelChange)=\"aplicarFiltros()\" placeholder=\"Nome, empresa ou telefone...\">\n        </div>\n        <div class=\"col-md-2\">\n          <label><strong>Pendências</strong></label>\n          <div class=\"form-check\">\n            <input type=\"checkbox\" class=\"form-check-input\" [(ngModel)]=\"filtroPendencias\" \n                   (ngModelChange)=\"aplicarFiltros()\">\n            <label class=\"form-check-label\">Só pendentes</label>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Formulário de Cadastro/Edição -->\n  <div class=\"card mb-4\" *ngIf=\"modoEdicao\">\n    <div class=\"card-header\">\n      <h5 class=\"mb-0\">\n        <i class=\"fa fa-edit\"></i> \n        {{ leadSelecionado.id ? 'Editar Lead' : 'Novo Lead' }}\n      </h5>\n    </div>\n    <div class=\"card-body\">\n      <form #leadForm=\"ngForm\" (ngSubmit)=\"salvar()\">\n        \n        <!-- Busca Instagram -->\n        <div class=\"card mb-4 bg-light\" *ngIf=\"!leadSelecionado.id\">\n          <div class=\"card-header py-2\">\n            <h6 class=\"mb-0\">\n              <i class=\"fab fa-instagram text-danger mr-2\"></i>\n              Buscar Dados do Instagram\n            </h6>\n          </div>\n          <div class=\"card-body py-3\">\n            <div class=\"row\">\n              <div class=\"col-md-8\">\n                <div class=\"form-group mb-2\">\n                  <label for=\"instagramUsername\"><strong>Username do Instagram</strong></label>\n                  <div class=\"input-group\">\n                    <div class=\"input-group-prepend\">\n                      <span class=\"input-group-text\">@</span>\n                    </div>\n                    <input type=\"text\" \n                           id=\"instagramUsername\"\n                           class=\"form-control\" \n                           [(ngModel)]=\"instagramUsername\"\n                           name=\"instagramUsername\"\n                           placeholder=\"usuario_instagram\"\n                           [disabled]=\"buscandoInstagram\">\n                  </div>\n                </div>\n              </div>\n              <div class=\"col-md-4\">\n                <label>&nbsp;</label>\n                <div class=\"form-group\">\n                  <button type=\"button\" \n                          class=\"btn btn-info btn-block\"\n                          (click)=\"buscarDadosInstagram()\"\n                          [disabled]=\"buscandoInstagram || !instagramUsername\">\n                    <i class=\"fa fa-search mr-2\" *ngIf=\"!buscandoInstagram\"></i>\n                    <i class=\"fa fa-spinner fa-spin mr-2\" *ngIf=\"buscandoInstagram\"></i>\n                    {{ buscandoInstagram ? 'Buscando...' : 'Buscar Dados' }}\n                  </button>\n                </div>\n              </div>\n            </div>\n            <small class=\"text-muted\">\n              <i class=\"fa fa-info-circle mr-1\"></i>\n              Digite o username (sem @) para buscar automaticamente os dados do perfil e criar/vincular a empresa.\n            </small>\n          </div>\n        </div>\n\n        <!-- Dados Básicos -->\n        <div class=\"row\">\n          <div class=\"col-md-4\">\n            <div class=\"form-group\">\n              <label for=\"nomeResponsavel\"><strong>Nome do Responsável *</strong></label>\n              <input type=\"text\" \n                     id=\"nomeResponsavel\"\n                     name=\"nomeResponsavel\" \n                     class=\"form-control\" \n                     [(ngModel)]=\"leadSelecionado.nomeResponsavel\" \n                     required\n                     placeholder=\"Ex: João Silva\">\n            </div>\n          </div>\n          \n          <div class=\"col-md-4\">\n            <div class=\"form-group\">\n              <label for=\"empresa\"><strong>Empresa/Estabelecimento *</strong></label>\n              <input type=\"text\" \n                     id=\"empresa\"\n                     name=\"empresa\" \n                     class=\"form-control\" \n                     [(ngModel)]=\"leadSelecionado.empresa\" \n                     required\n                     placeholder=\"Ex: Pizzaria do João\">\n            </div>\n          </div>\n          \n          <div class=\"col-md-4\">\n            <div class=\"form-group\">\n              <label for=\"telefone\"><strong>Telefone/WhatsApp *</strong></label>\n              <input type=\"text\" \n                     id=\"telefone\"\n                     name=\"telefone\" \n                     class=\"form-control\" \n                     [(ngModel)]=\"leadSelecionado.telefone\" \n                     (ngModelChange)=\"formatarTelefone()\"\n                     required\n                     placeholder=\"(11) 99999-9999\">\n            </div>\n          </div>\n        </div>\n\n        <!-- Empresa CRM -->\n        <div class=\"row\">\n          <div class=\"col-md-12\">\n            <div class=\"form-group\">\n              <label for=\"crmEmpresa\"><strong>Empresa CRM</strong></label>\n              <select id=\"crmEmpresa\" \n                      name=\"crmEmpresa\" \n                      class=\"form-control\" \n                      [(ngModel)]=\"leadSelecionado.crmEmpresa\">\n                <option [ngValue]=\"null\">Selecione uma empresa CRM...</option>\n                <option *ngFor=\"let empresa of crmEmpresas\" [ngValue]=\"empresa\">\n                  {{ empresa.nome }}\n                </option>\n              </select>\n              <small class=\"text-muted\">Empresa no sistema CRM que este lead será vinculado</small>\n            </div>\n          </div>\n        </div>\n\n        <!-- Informações de Processo -->\n        <div class=\"row\">\n          <div class=\"col-md-3\">\n            <div class=\"form-group\">\n              <label for=\"etapa\"><strong>Etapa do Funil</strong></label>\n              <select id=\"etapa\" \n                      name=\"etapa\" \n                      class=\"form-control\" \n                      [(ngModel)]=\"leadSelecionado.etapa\">\n                <option *ngFor=\"let etapa of etapas\" [value]=\"etapa.valor\">\n                  {{ etapa.texto }}\n                </option>\n              </select>\n            </div>\n          </div>\n          \n          <div class=\"col-md-3\">\n            <div class=\"form-group\">\n              <label for=\"origem\"><strong>Origem</strong></label>\n              <select id=\"origem\" \n                      name=\"origem\" \n                      class=\"form-control\" \n                      [(ngModel)]=\"leadSelecionado.origem\">\n                <option *ngFor=\"let origem of origens\" [value]=\"origem.valor\">\n                  {{ origem.texto }}\n                </option>\n              </select>\n            </div>\n          </div>\n          \n          <div class=\"col-md-3\">\n            <div class=\"form-group\">\n              <label for=\"segmento\"><strong>Segmento</strong></label>\n              <select id=\"segmento\" \n                      name=\"segmento\" \n                      class=\"form-control\" \n                      [(ngModel)]=\"leadSelecionado.segmento\">\n                <option value=\"\">Selecione...</option>\n                <option *ngFor=\"let segmento of segmentos\" [value]=\"segmento.valor\">\n                  {{ segmento.texto }}\n                </option>\n              </select>\n            </div>\n          </div>\n          \n          <div class=\"col-md-3\">\n            <div class=\"form-group\">\n              <label for=\"score\"><strong>Score (0-100)</strong></label>\n              <input type=\"number\" \n                     id=\"score\"\n                     name=\"score\" \n                     class=\"form-control\" \n                     [(ngModel)]=\"leadSelecionado.score\" \n                     min=\"0\" \n                     max=\"100\"\n                     placeholder=\"0\">\n            </div>\n          </div>\n        </div>\n\n        <!-- Instagram e Link -->\n        <div class=\"row\">\n          <div class=\"col-md-4\">\n            <div class=\"form-group\">\n              <label for=\"instagram\"><strong>Instagram</strong></label>\n              <div class=\"input-group\">\n                <div class=\"input-group-prepend\">\n                  <span class=\"input-group-text\">@</span>\n                </div>\n                <input type=\"text\" \n                       id=\"instagram\"\n                       name=\"instagramHandle\" \n                       class=\"form-control\" \n                       [(ngModel)]=\"leadSelecionado.instagramHandle\" \n                       (ngModelChange)=\"formatarInstagram()\"\n                       placeholder=\"usuario_instagram\">\n              </div>\n            </div>\n          </div>\n          \n          <div class=\"col-md-4\">\n            <div class=\"form-group\">\n              <label for=\"linkInsta\"><strong>Link da Bio</strong></label>\n              <input type=\"url\" \n                     id=\"linkInsta\"\n                     name=\"linkInsta\" \n                     class=\"form-control\" \n                     [(ngModel)]=\"leadSelecionado.linkInsta\" \n                     placeholder=\"https://site.com.br\">\n              <small class=\"text-muted\">Link externo da biografia do Instagram</small>\n            </div>\n          </div>\n          \n          <div class=\"col-md-4\">\n            <div class=\"form-group\">\n              <label for=\"valorPotencial\"><strong>Valor Potencial (R$)</strong></label>\n              <input type=\"number\" \n                     id=\"valorPotencial\"\n                     name=\"valorPotencial\" \n                     class=\"form-control\" \n                     [(ngModel)]=\"leadSelecionado.valorPotencial\" \n                     step=\"0.01\"\n                     placeholder=\"0,00\">\n            </div>\n          </div>\n        </div>\n\n        <!-- Bio Instagram -->\n        <div class=\"row\" *ngIf=\"leadSelecionado.bioInsta\">\n          <div class=\"col-12\">\n            <div class=\"form-group\">\n              <label for=\"bioInsta\"><strong>Bio do Instagram</strong></label>\n              <textarea id=\"bioInsta\"\n                        name=\"bioInsta\" \n                        class=\"form-control\" \n                        [(ngModel)]=\"leadSelecionado.bioInsta\" \n                        rows=\"2\"\n                        readonly\n                        placeholder=\"Bio extraída do Instagram...\"></textarea>\n              <small class=\"text-muted\">\n                <i class=\"fa fa-info-circle mr-1\"></i>\n                Bio extraída automaticamente do perfil do Instagram\n              </small>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"row\">\n          <div class=\"col-12\">\n            <div class=\"form-group\">\n              <label for=\"notas\"><strong>Observações</strong></label>\n              <textarea id=\"notas\"\n                        name=\"notas\"\n                        class=\"form-control\"\n                        [(ngModel)]=\"leadSelecionado.notas\"\n                        rows=\"3\"\n                        placeholder=\"Observações sobre o lead...\"></textarea>\n            </div>\n          </div>\n        </div>\n\n        <!-- Seção de Links -->\n        <div class=\"row\">\n          <div class=\"col-12\">\n            <div class=\"card\">\n              <div class=\"card-header d-flex justify-content-between align-items-center\">\n                <h6 class=\"mb-0\">\n                  <i class=\"fa fa-link mr-2\"></i>Links do Lead\n                </h6>\n                <button type=\"button\"\n                        class=\"btn btn-sm btn-outline-primary\"\n                        (click)=\"toggleSecaoLinks()\">\n                  <i class=\"fa\" [class.fa-chevron-down]=\"!mostrarSecaoLinks\"\n                                [class.fa-chevron-up]=\"mostrarSecaoLinks\"></i>\n                  {{ mostrarSecaoLinks ? 'Ocultar' : 'Mostrar' }}\n                </button>\n              </div>\n\n              <div class=\"card-body\" *ngIf=\"mostrarSecaoLinks\">\n                <!-- Links existentes -->\n                <div class=\"mb-3\" *ngIf=\"leadSelecionado.links && leadSelecionado.links.length > 0\">\n                  <h6>Links Cadastrados:</h6>\n                  <div class=\"row\">\n                    <div class=\"col-md-6 mb-2\" *ngFor=\"let link of leadSelecionado.links; let i = index\">\n                      <div class=\"card border-left-primary\">\n                        <div class=\"card-body p-2\">\n                          <div class=\"d-flex justify-content-between align-items-center\">\n                            <div class=\"d-flex align-items-center\">\n                              <i class=\"fa mr-2\"\n                                 [ngClass]=\"getTipoLinkInfo(link.tipo).icone\"\n                                 [style.color]=\"getTipoLinkInfo(link.tipo).cor\"></i>\n                              <div>\n                                <strong>{{ getTipoLinkInfo(link.tipo).texto }}</strong>\n                                <br>\n                                <small class=\"text-muted\">{{ link.url }}</small>\n                                <div *ngIf=\"link.descricao\" class=\"text-muted\" style=\"font-size: 0.8rem;\">\n                                  {{ link.descricao }}\n                                </div>\n                              </div>\n                            </div>\n                            <div class=\"btn-group\">\n                              <button type=\"button\"\n                                      class=\"btn btn-sm btn-outline-info\"\n                                      (click)=\"abrirLink(link.url, link.tipo)\"\n                                      title=\"Abrir link\">\n                                <i class=\"fa fa-external-link-alt\"></i>\n                              </button>\n                              <button type=\"button\"\n                                      class=\"btn btn-sm btn-outline-danger\"\n                                      (click)=\"removerLink(i)\"\n                                      title=\"Remover link\">\n                                <i class=\"fa fa-trash\"></i>\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Formulário para adicionar novo link -->\n                <div class=\"border-top pt-3\">\n                  <h6>Adicionar Novo Link:</h6>\n                  <div class=\"row\">\n                    <div class=\"col-md-3\">\n                      <div class=\"form-group\">\n                        <label>Tipo</label>\n                        <select class=\"form-control form-control-sm\"\n                                [(ngModel)]=\"novoLink.tipo\"\n                                name=\"novoLinkTipo\">\n                          <option value=\"\">Selecione...</option>\n                          <option *ngFor=\"let tipo of tiposLink\" [value]=\"tipo.valor\">\n                            {{ tipo.texto }}\n                          </option>\n                        </select>\n                      </div>\n                    </div>\n                    <div class=\"col-md-4\">\n                      <div class=\"form-group\">\n                        <label>URL</label>\n                        <input type=\"text\"\n                               class=\"form-control form-control-sm\"\n                               [(ngModel)]=\"novoLink.url\"\n                               name=\"novoLinkUrl\"\n                               placeholder=\"https://exemplo.com\">\n                      </div>\n                    </div>\n                    <div class=\"col-md-3\">\n                      <div class=\"form-group\">\n                        <label>Descrição (opcional)</label>\n                        <input type=\"text\"\n                               class=\"form-control form-control-sm\"\n                               [(ngModel)]=\"novoLink.descricao\"\n                               name=\"novoLinkDescricao\"\n                               placeholder=\"Descrição do link\">\n                      </div>\n                    </div>\n                    <div class=\"col-md-2\">\n                      <div class=\"form-group\">\n                        <label>&nbsp;</label>\n                        <button type=\"button\"\n                                class=\"btn btn-success btn-sm btn-block\"\n                                (click)=\"adicionarLink()\"\n                                [disabled]=\"!novoLink.tipo || !novoLink.url\">\n                          <i class=\"fa fa-plus\"></i> Adicionar\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Botões -->\n        <div class=\"d-flex justify-content-end\">\n          <button type=\"button\" class=\"btn btn-secondary mr-2\" (click)=\"cancelar()\">\n            <i class=\"fa fa-times\"></i> Cancelar\n          </button>\n          <button type=\"submit\" \n                  class=\"btn btn-success\" \n                  [disabled]=\"leadForm.invalid\">\n            <i class=\"fa fa-save\"></i> Salvar\n          </button>\n        </div>\n      </form>\n    </div>\n  </div>\n\n  <!-- Loading -->\n  <div class=\"text-center py-4\" *ngIf=\"carregando\">\n    <i class=\"fa fa-spinner fa-spin fa-2x\"></i>\n    <p class=\"mt-2\">Carregando leads...</p>\n  </div>\n\n  <!-- Lista de Cards dos Leads -->\n  <div class=\"row\" *ngIf=\"!modoEdicao && !carregando\">\n    <div class=\"col-12 mb-3\" *ngIf=\"leadsFiltrados.length === 0\">\n      <div class=\"card\">\n        <div class=\"card-body text-center py-5\">\n          <i class=\"fa fa-users fa-3x text-muted mb-3\"></i>\n          <h5 class=\"text-muted\">Nenhum lead encontrado</h5>\n          <p class=\"text-muted\">Adicione um novo lead ou ajuste os filtros</p>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"col-lg-6 col-xl-4 mb-3\" *ngFor=\"let lead of leadsFiltrados\">\n      <div class=\"card lead-card h-100 shadow-sm\" \n           [class.border-warning]=\"isAtrasado(lead)\"\n           [class.border-success]=\"lead.etapa === 'Ganho'\"\n           [class.border-danger]=\"lead.etapa === 'Perdido'\"\n           [class.border-info]=\"lead.etapa === 'Fechamento'\"\n           [class.border-primary]=\"lead.etapa === 'Qualificação'\">\n        \n        <!-- Header Compacto -->\n                 <div class=\"card-header py-2 px-3\"\n              [class.bg-gradient-success]=\"lead.etapa === 'Ganho'\"\n              [class.bg-gradient-danger]=\"lead.etapa === 'Perdido'\"\n              [class.bg-gradient-warning]=\"isAtrasado(lead)\"\n              [class.bg-gradient-info]=\"lead.etapa === 'Fechamento'\"\n              [class.bg-gradient-primary]=\"lead.etapa === 'Qualificação'\"\n              [class.bg-gradient-secondary]=\"lead.etapa === 'Prospecção'\">\n          \n          <div class=\"d-flex justify-content-between align-items-center\">\n            <div class=\"d-flex align-items-center\">\n              <i class=\"fa {{ getIconeEtapa(lead.etapa) }} mr-2\"></i>\n                             <span class=\"font-weight-light\" style=\"font-size: 0.85rem;\">{{ lead.etapa }}</span>\n            </div>\n            \n            <div class=\"d-flex align-items-center\">\n              <span class=\"badge badge-light badge-pill mr-2 px-2\"\n                    [style.background-color]=\"getCorScore(lead.score)\"\n                    [style.color]=\"'white'\"\n                    style=\"font-size: 0.7rem;\">\n                {{ lead.score || 0 }}%\n              </span>\n              \n              <div class=\"dropdown\">\n                                 <button class=\"btn btn-sm btn-link p-0\" data-toggle=\"dropdown\">\n                  <i class=\"fa fa-ellipsis-v\"></i>\n                </button>\n                <div class=\"dropdown-menu dropdown-menu-right\">\n                  <a class=\"dropdown-item\" href=\"#\" (click)=\"editar(lead)\">\n                    <i class=\"fa fa-edit mr-2 text-primary\"></i> Editar\n                  </a>\n                  <a class=\"dropdown-item text-danger\" href=\"#\" (click)=\"remover(lead.id)\">\n                    <i class=\"fa fa-trash mr-2\"></i> Remover\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Corpo Compacto -->\n        <div class=\"card-body p-3\">\n          <!-- Nome do Responsável -->\n          <div class=\"mb-2\">\n            <h6 class=\"card-title mb-2 text-dark font-weight-light\" style=\"font-size: 1rem;\">\n              <i class=\"fa fa-user text-primary mr-2\"></i>\n              {{ lead.nomeResponsavel }}\n            </h6>\n          </div>\n\n          <!-- Origem e Segmento em linha -->\n          <div class=\"d-flex flex-wrap mb-2\">\n            <span class=\"badge mr-1 mb-1 px-2 py-1\" \n                  [class.badge-instagram]=\"lead.origem === 'Instagram'\"\n                  [class.badge-whatsapp]=\"lead.origem === 'WhatsApp Direto'\"\n                  [class.badge-site]=\"lead.origem === 'Site/Landing Page'\"\n                  [class.badge-indicacao]=\"lead.origem === 'Indicação'\"\n                  [class.badge-secondary]=\"!['Instagram', 'WhatsApp Direto', 'Site/Landing Page', 'Indicação'].includes(lead.origem)\"\n                  style=\"font-size: 0.7rem;\">\n              {{ lead.origem }}\n            </span>\n            <span class=\"badge badge-outline-info mb-1 px-2 py-1\" \n                  *ngIf=\"lead.segmento\"\n                  style=\"font-size: 0.7rem;\">\n              {{ lead.segmento }}\n            </span>\n          </div>\n\n          <!-- Contatos em linha compacta -->\n          <div class=\"contact-info mb-2\">\n            <div class=\"d-flex align-items-center justify-content-between mb-1\" *ngIf=\"lead.telefone\">\n              <div class=\"d-flex align-items-center\">\n                <i class=\"fa fa-phone text-success mr-2\" style=\"font-size: 0.8rem;\"></i>\n                <span style=\"font-size: 0.8rem;\">{{ lead.telefone }}</span>\n              </div>\n              <div class=\"d-flex\">\n                <button class=\"btn btn-open-lead btn-xs mr-1\" \n                        (click)=\"abrirLead(lead)\"\n                        title=\"Abrir Lead\">\n                  <i class=\"fa fa-external-link-alt mr-1\"></i>\n                  <span>Lead</span>\n                </button>\n                <a [href]=\"getWhatsappUrl(lead.telefone)\" \n                   target=\"_blank\" class=\"btn btn-whatsapp btn-xs\">\n                  <i class=\"fa fa-whatsapp\"></i>\n                </a>\n              </div>\n            </div>\n            \n            <div class=\"d-flex align-items-center justify-content-between mb-1\" *ngIf=\"lead.instagramHandle\">\n              <div class=\"d-flex align-items-center\">\n                <i class=\"fab fa-instagram text-danger mr-2\" style=\"font-size: 0.8rem;\"></i>\n                <span style=\"font-size: 0.8rem;\">@{{ lead.instagramHandle }}</span>\n              </div>\n              <div class=\"d-flex gap-1\">\n                <a [href]=\"getInstagramUrl(lead.instagramHandle)\" \n                   target=\"_blank\" class=\"btn btn-instagram btn-xs\" title=\"Ver perfil\">\n                  <i class=\"fab fa-instagram\"></i>\n                </a>\n                <a *ngIf=\"lead.linkInsta\" \n                   [href]=\"lead.linkInsta\" \n                   target=\"_blank\" class=\"btn btn-link btn-xs\" title=\"Link da bio\">\n                  <i class=\"fa fa-external-link-alt\"></i>\n                </a>\n              </div>\n            </div>\n          </div>\n\n          <!-- Valor Potencial -->\n          <div class=\"mb-2\" *ngIf=\"lead.valorPotencial\">\n            <i class=\"fa fa-dollar-sign text-success mr-1\"></i>\n            <strong class=\"text-success\" style=\"font-size: 0.8rem;\">{{ formatarValor(lead.valorPotencial) }}</strong>\n          </div>\n\n          <!-- CRM Empresa como tag -->\n          <div class=\"mb-2\">\n            <span class=\"badge badge-crm-empresa px-2 py-1\" style=\"font-size: 0.7rem;\" *ngIf=\"lead.empresa\">\n              <i class=\"fa fa-building mr-1\"></i>\n              {{ lead.empresa }}\n            </span>\n          </div>\n\n          <!-- Links do Lead -->\n          <div class=\"mb-2\" *ngIf=\"lead.links && lead.links.length > 0\">\n            <div class=\"d-flex flex-wrap\">\n              <a *ngFor=\"let link of lead.links\"\n                 [href]=\"formatarUrlLink(link.url, link.tipo)\"\n                 target=\"_blank\"\n                 class=\"btn btn-xs mr-1 mb-1\"\n                 [style.background-color]=\"getTipoLinkInfo(link.tipo).cor\"\n                 [style.color]=\"'white'\"\n                 [title]=\"link.descricao || getTipoLinkInfo(link.tipo).texto\">\n                <i class=\"fa {{ getTipoLinkInfo(link.tipo).icone }} mr-1\"></i>\n                <span style=\"font-size: 0.7rem;\">{{ getTipoLinkInfo(link.tipo).texto }}</span>\n              </a>\n            </div>\n          </div>\n\n          <!-- Alerta de Pendência Compacto -->\n          <div class=\"alert alert-warning py-1 px-2 mb-2\" \n               *ngIf=\"isAtrasado(lead)\"\n               style=\"font-size: 0.7rem;\">\n            <i class=\"fa fa-exclamation-triangle mr-1\"></i>\n            <strong>Follow-up atrasado!</strong>\n          </div>\n\n          <!-- Data compacta -->\n          <div class=\"text-muted text-right\" style=\"font-size: 0.7rem;\">\n            <i class=\"fa fa-calendar mr-1\"></i>\n            {{ formatarData(lead.dataCriacao) }}\n          </div>\n        </div>\n\n        <!-- Footer compacto -->\n        <div class=\"card-footer p-2 bg-light\">\n          <div class=\"row no-gutters\">\n            <div class=\"col-6 pr-1\">\n              <button class=\"btn btn-outline-primary btn-sm btn-block py-1\" \n                      (click)=\"editar(lead)\"\n                      style=\"font-size: 0.75rem;\">\n                <i class=\"fa fa-edit mr-1\"></i> Editar\n              </button>\n            </div>\n            <div class=\"col-6 pl-1\">\n              <button class=\"btn btn-outline-info btn-sm btn-block py-1\"\n                      (click)=\"abrirDetalhesLead(lead)\"\n                      style=\"font-size: 0.75rem;\">\n                <i class=\"fa fa-info-circle mr-1\"></i> Detalhes\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div> ", "import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { LeadService } from '../services/lead.service';\nimport { CrmEmpresaService } from '../services/crm-empresa.service';\n\n@Component({\n  selector: 'app-lead-crud',\n  templateUrl: './lead-crud.component.html',\n  styleUrls: ['./lead-crud.component.scss']\n})\nexport class LeadCrudComponent implements OnInit {\n  leads: any[] = [];\n  leadsFiltrados: any[] = [];\n  leadSelecionado: any = {};\n  carregando = false;\n  modoEdicao = false;\n  \n  // Lista de empresas CRM para seleção\n  crmEmpresas: any[] = [];\n\n  // Controle de filtros\n  mostrarFiltros = false;\n  filtroEtapa = '';\n  filtroOrigem = '';\n  filtroTexto = '';\n  filtroPendencias = false;\n  // Controle de busca Instagram\n  buscandoInstagram = false;\n  instagramUsername = '';\n\n  // Controle de links\n  mostrarSecaoLinks = false;\n  novoLink = { tipo: '', url: '', descricao: '' };\n  tiposLink = [\n    { valor: 'Ifood', texto: 'iFood', icone: 'fa-utensils', cor: '#ea1d2c' },\n    { valor: 'Site do Cardápio', texto: 'Site do Cardápio', icone: 'fa-list-alt', cor: '#007bff' },\n    { valor: 'Concorrente', texto: 'Concorrente', icone: 'fa-exclamation-triangle', cor: '#ff6b35' },\n    { valor: 'Reservas', texto: 'Reservas', icone: 'fa-calendar-check', cor: '#28a745' },\n    { valor: 'WhatsApp', texto: 'WhatsApp', icone: 'fa-whatsapp', cor: '#25d366' },\n    { valor: 'Localização', texto: 'Localização', icone: 'fa-map-marker-alt', cor: '#dc3545' },\n    { valor: 'Site', texto: 'Site', icone: 'fa-globe', cor: '#6c757d' },\n    { valor: 'Instagram', texto: 'Instagram', icone: 'fa-instagram', cor: '#e4405f' }\n  ];\n\n  // Opções para dropdowns baseadas nos enums do Lead.ts\n  etapas = [\n    { valor: 'Prospecção', texto: 'Prospecção' },\n    { valor: 'Qualificação', texto: 'Qualificação' }, \n    { valor: 'Objeção', texto: 'Objeção' },\n    { valor: 'Fechamento', texto: 'Fechamento' },\n    { valor: 'Ganho', texto: 'Ganho' },\n    { valor: 'Perdido', texto: 'Perdido' }\n  ];\n\n  origens = [\n    { valor: 'Instagram', texto: 'Instagram' },\n    { valor: 'Site/Landing Page', texto: 'Site/Landing Page' },\n    { valor: 'WhatsApp Direto', texto: 'WhatsApp Direto' },\n    { valor: 'Indicação', texto: 'Indicação' },\n    { valor: 'Evento/Feira', texto: 'Evento/Feira' },\n    { valor: 'Outros', texto: 'Outros' }\n  ];\n\n  segmentos = [\n    { valor: 'Restaurante', texto: 'Restaurante' },\n    { valor: 'Pizzaria', texto: 'Pizzaria' },\n    { valor: 'Lanchonete', texto: 'Lanchonete' },\n    { valor: 'Hamburgueria', texto: 'Hamburgueria' },\n    { valor: 'Confeitaria', texto: 'Confeitaria/Doceria' },\n    { valor: 'Bar', texto: 'Bar/Boteco' },\n    { valor: 'Food Truck', texto: 'Food Truck' },\n    { valor: 'Outros', texto: 'Outros' }\n  ];\n\n  constructor(\n    private leadService: LeadService,\n    private crmEmpresaService: CrmEmpresaService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Primeiro carrega as empresas, depois os leads\n    this.carregarEmpresas().then(() => {\n      this.listar();\n    });\n  }\n\n  carregarEmpresas(): Promise<void> {\n    return this.crmEmpresaService.liste({ ativa: true }).then((resp) => {\n      this.crmEmpresas = resp.data?.data || resp.data || [];\n      console.log('Empresas CRM carregadas:', this.crmEmpresas);\n    }).catch((erro) => {\n      console.error('Erro ao carregar empresas CRM:', erro);\n      alert('Erro ao carregar empresas CRM. Verifique se há empresas cadastradas em /crm/empresas');\n    });\n  }\n\n  listar(): void {\n    this.carregando = true;\n    this.leadService.liste({ inicio: 0, total: 100 }).then((resp) => {\n      this.leads = resp.data?.data || resp.data || [];\n      console.log('Leads carregados:', this.leads);\n      console.log('CRM Empresas disponíveis:', this.crmEmpresas);\n      this.aplicarFiltros();\n      this.carregando = false;\n    }).catch(() => this.carregando = false);\n  }\n\n  // Controle de filtros\n  toggleFiltros(): void {\n    this.mostrarFiltros = !this.mostrarFiltros;\n  }\n\n  aplicarFiltros(): void {\n    this.leadsFiltrados = this.leads.filter(lead => {\n      // Filtro por etapa\n      if (this.filtroEtapa && lead.etapa !== this.filtroEtapa) {\n        return false;\n      }\n      \n      // Filtro por origem\n      if (this.filtroOrigem && lead.origem !== this.filtroOrigem) {\n        return false;\n      }\n      \n      // Filtro por texto\n      if (this.filtroTexto) {\n        const texto = this.filtroTexto.toLowerCase();\n        const contemTexto = \n          lead.nomeResponsavel?.toLowerCase().includes(texto) ||\n          lead.telefone?.toLowerCase().includes(texto) ||\n          lead.instagramHandle?.toLowerCase().includes(texto);\n        \n        if (!contemTexto) {\n          return false;\n        }\n      }\n      \n      // Filtro por pendências\n      if (this.filtroPendencias && !this.isAtrasado(lead)) {\n        return false;\n      }\n      \n      return true;\n    });\n  }\n\n  novo(): void {\n    this.leadSelecionado = {\n      id: null,\n      crmEmpresa: null,\n      nomeResponsavel: '',\n      empresa: '',\n      telefone: '',\n      instagramHandle: '',\n      linkInsta: '',\n      bioInsta: '',\n      etapa: 'Prospecção',\n      origem: 'Instagram',\n      score: 0,\n      valorPotencial: 0,\n      segmento: '',\n      notas: '',\n      links: []\n    };\n    this.instagramUsername = '';\n    this.modoEdicao = true;\n    this.mostrarSecaoLinks = false;\n    this.novoLink = { tipo: '', url: '', descricao: '' };\n  }\n\n  editar(lead: any): void {\n    console.log('Editando lead:', lead);\n    this.leadSelecionado = { ...lead };\n\n    // Garantir que os campos numéricos estejam corretos para a edição\n    if (this.leadSelecionado.score) {\n      this.leadSelecionado.score = parseInt(this.leadSelecionado.score);\n    }\n    if (this.leadSelecionado.valorPotencial) {\n      this.leadSelecionado.valorPotencial = parseFloat(this.leadSelecionado.valorPotencial);\n    }\n\n    // Se não tem o objeto crmEmpresa mas tem crmEmpresaId, buscar na lista\n    if (!this.leadSelecionado.crmEmpresa && this.leadSelecionado.crmEmpresaId) {\n      this.leadSelecionado.crmEmpresa = this.crmEmpresas.find(e => e.id == this.leadSelecionado.crmEmpresaId) || null;\n    }\n\n    // Garantir que links existe como array\n    if (!this.leadSelecionado.links) {\n      this.leadSelecionado.links = [];\n    }\n\n    // Carregar links do lead se tiver ID\n    if (this.leadSelecionado.id) {\n      this.leadService.listarLinks(this.leadSelecionado.id).then((resposta) => {\n        this.leadSelecionado.links = resposta || [];\n        console.log('Links carregados:', this.leadSelecionado.links);\n      }).catch((erro) => {\n        console.error('Erro ao carregar links:', erro);\n        this.leadSelecionado.links = [];\n      });\n    }\n\n    console.log('Lead selecionado para edição:', this.leadSelecionado);\n    this.modoEdicao = true;\n    this.mostrarSecaoLinks = false;\n    this.novoLink = { tipo: '', url: '', descricao: '' };\n  }\n\n  cancelar(): void {\n    this.leadSelecionado = {};\n    this.modoEdicao = false;\n  }\n\n  salvar(): void {\n    // Garantir que score seja número se preenchido\n    if (this.leadSelecionado.score) {\n      this.leadSelecionado.score = parseInt(this.leadSelecionado.score);\n    }\n    \n    // Garantir que valorPotencial seja número se preenchido\n    if (this.leadSelecionado.valorPotencial) {\n      this.leadSelecionado.valorPotencial = parseFloat(this.leadSelecionado.valorPotencial);\n    }\n\n    // Preparar dados para envio - converter crmEmpresa para crmEmpresaId\n    const dadosParaEnvio = {\n      ...this.leadSelecionado,\n      crmEmpresaId: this.leadSelecionado.crmEmpresa?.id || null\n    };\n    \n    // Remover o objeto crmEmpresa completo para não enviar dados desnecessários\n    delete dadosParaEnvio.crmEmpresa;\n    \n    console.log('Dados sendo enviados:', dadosParaEnvio);\n    console.log('Modo edição - ID presente?', !!dadosParaEnvio.id);\n    \n    this.leadService.salveLead(dadosParaEnvio).then((resposta) => {\n      console.log('Resposta do servidor:', resposta);\n      alert('Lead salvo com sucesso!');\n      this.cancelar();\n      this.listar();\n    }).catch((erro) => {\n      console.error('Erro completo:', erro);\n      console.error('Erro detalhado:', JSON.stringify(erro, null, 2));\n      let mensagemErro = 'Erro desconhecido';\n      \n      if (erro.error && erro.error.mensagem) {\n        mensagemErro = erro.error.mensagem;\n      } else if (erro.message) {\n        mensagemErro = erro.message;\n      } else if (typeof erro === 'string') {\n        mensagemErro = erro;\n      }\n      \n      alert('Erro ao salvar lead: ' + mensagemErro);\n    });\n  }\n\n  remover(id: number): void {\n    if (!confirm('Deseja realmente remover este lead?')) return;\n    this.leadService.removaLead(id).then(() => this.listar());\n  }\n\n  // Ação para avançar etapa\n  avancarEtapa(lead: any): void {\n    const etapasOrdem = ['Prospecção', 'Qualificação', 'Objeção', 'Fechamento', 'Ganho'];\n    const indexAtual = etapasOrdem.indexOf(lead.etapa);\n    \n    if (indexAtual >= 0 && indexAtual < etapasOrdem.length - 1) {\n      lead.etapa = etapasOrdem[indexAtual + 1];\n      this.leadService.salveLead(lead).then(() => {\n        this.listar();\n      }).catch((erro) => {\n        console.error('Erro ao avançar etapa:', erro);\n        alert('Erro ao avançar etapa');\n      });\n    }\n  }\n\n  // Formatar telefone durante digitação\n  formatarTelefone(): void {\n    if (this.leadSelecionado.telefone) {\n      let telefone = this.leadSelecionado.telefone.replace(/\\D/g, '');\n      if (telefone.length <= 11) {\n        telefone = telefone.replace(/^(\\d{2})(\\d{5})(\\d{4})/, '($1) $2-$3');\n        telefone = telefone.replace(/^(\\d{2})(\\d{4})(\\d{4})/, '($1) $2-$3');\n      }\n      this.leadSelecionado.telefone = telefone;\n    }\n  }\n\n  // Formatar Instagram handle\n  formatarInstagram(): void {\n    if (this.leadSelecionado.instagramHandle) {\n      let handle = this.leadSelecionado.instagramHandle.replace('@', '').trim();\n      this.leadSelecionado.instagramHandle = handle;\n    }\n  }\n\n  // Formatação de dados para exibição\n  formatarValor(valor: number): string {\n    if (!valor) return 'N/A';\n    return valor.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });\n  }\n\n  formatarData(data: any): string {\n    if (!data) return 'N/A';\n    const dataObj = typeof data === 'string' ? new Date(data) : data;\n    return dataObj.toLocaleDateString('pt-BR');\n  }\n\n  // Verificar se o lead está atrasado\n  isAtrasado(lead: any): boolean {\n    if (!lead.dataProximoFollowup) return false;\n    const hoje = new Date();\n    const followup = new Date(lead.dataProximoFollowup);\n    return hoje > followup;\n  }\n\n  // Cor baseada no score\n  getCorScore(score: number): string {\n    if (!score) return '#6c757d';\n    if (score >= 80) return '#28a745';\n    if (score >= 50) return '#ffc107';\n    return '#dc3545';\n  }\n\n  // Ícone baseado na etapa\n  getIconeEtapa(etapa: string): string {\n    const icones = {\n      'Prospecção': 'fa-eye',\n      'Qualificação': 'fa-search',\n      'Objeção': 'fa-exclamation-triangle',\n      'Fechamento': 'fa-handshake',\n      'Ganho': 'fa-check-circle',\n      'Perdido': 'fa-times-circle'\n    };\n    return icones[etapa] || 'fa-circle';\n  }\n\n  // Obter nome da empresa CRM pelo objeto\n  getNomeCrmEmpresa(lead: any): string {\n    if (lead.crmEmpresa && lead.crmEmpresa.nome) {\n      return lead.crmEmpresa.nome;\n    }\n    if (lead.empresa) {\n      return lead.empresa;\n    }\n    return 'N/A';\n  }\n\n\n  // Gerar URL do WhatsApp\n  getWhatsappUrl(telefone: string): string {\n    if (!telefone) return '#';\n    const numeroLimpo = telefone.replace(/\\D/g, '');\n    return `https://wa.me/55${numeroLimpo}`;\n  }\n\n  // Gerar URL do Instagram\n  getInstagramUrl(handle: string): string {\n    if (!handle) return '#';\n    return `https://instagram.com/${handle}`;\n  }\n\n  // Abrir lead em modal ou tela detalhada\n  abrirLead(lead: any): void {\n    // Por enquanto, abre o lead para edição\n    // Futuramente pode abrir um modal de detalhes ou navegar para tela específica\n    this.editar(lead);\n  }\n\n  // Abrir detalhes do lead navegando para /crm/home\n  abrirDetalhesLead(lead: any): void {\n    console.log('Abrindo detalhes do lead:', lead);\n    // Navegar para /crm/home passando o lead como parâmetro\n    this.router.navigate(['/crm/home'], { \n      queryParams: { \n        leadId: lead.id,\n        nomeResponsavel: lead.nomeResponsavel,\n        empresa: this.getNomeCrmEmpresa(lead),\n        telefone: lead.telefone,\n        etapa: lead.etapa,\n        origem: lead.origem\n      }\n    });\n  }\n\n  buscarDadosInstagram(): void {\n    if (!this.instagramUsername || !this.instagramUsername.trim()) {\n      alert('Digite um username do Instagram');\n      return;\n    }\n\n    const username = this.instagramUsername.replace('@', '').trim();\n    this.buscandoInstagram = true;\n\n    // Garantir que leadSelecionado existe e tem uma estrutura mínima\n    if (!this.leadSelecionado) {\n      this.leadSelecionado = {};\n    }\n\n    console.log('Chamando buscarDadosInstagram com:', { username });\n    \n    this.leadService.buscarDadosInstagram(username)\n      .then((resposta) => {\n        console.log('Resposta completa do serviço:', resposta);\n        \n        // O ServerService já extrai o campo 'data' da resposta\n        const leadInstagram = resposta;\n        console.log('Lead Instagram extraído:', leadInstagram);\n        \n        if (!leadInstagram) {\n          throw new Error('Resposta do servidor não contém dados válidos');\n        }\n        \n        // Preencher formulário com dados do Instagram\n        this.leadSelecionado = {\n          ...this.leadSelecionado,\n          nomeResponsavel: leadInstagram.nomeResponsavel || leadInstagram.nome || '',\n          empresa: leadInstagram.empresa || '',\n          telefone: leadInstagram.telefone || '',\n          instagramHandle: leadInstagram.instagramHandle || username,\n          linkInsta: leadInstagram.linkInsta || '',\n          bioInsta: leadInstagram.bioInsta || leadInstagram.bio || '',\n          notas: leadInstagram.notas || '',\n          origem: 'Instagram'\n        };\n\n        // Buscar e definir a empresa CRM correspondente\n        if (leadInstagram.crmEmpresa) {\n          // Buscar na lista de empresas carregadas\n          const empresaEncontrada = this.crmEmpresas.find(e => e.id === leadInstagram.crmEmpresa.id);\n          this.leadSelecionado.crmEmpresa = empresaEncontrada || leadInstagram.crmEmpresa;\n        }\n\n        console.log('Lead selecionado após preenchimento:', this.leadSelecionado);\n\n        // Tentar detectar segmento baseado na bio\n        if (this.leadSelecionado.bioInsta) {\n          this.detectarSegmento(this.leadSelecionado.bioInsta);\n        }\n\n        alert('Dados do Instagram carregados com sucesso!');\n        this.buscandoInstagram = false;\n      })\n      .catch((erro) => {\n        console.error('Erro ao buscar dados do Instagram:', erro);\n        alert('Erro ao buscar dados do Instagram: ' + (erro.message || 'Erro desconhecido'));\n        this.buscandoInstagram = false;\n      });\n  }\n\n  detectarSegmento(bio: string): void {\n    if (!bio) return;\n\n    const bioLower = bio.toLowerCase();\n\n    if (bioLower.includes('pizza') || bioLower.includes('pizzaria')) {\n      this.leadSelecionado.segmento = 'Pizzaria';\n    } else if (bioLower.includes('hambur') || bioLower.includes('burger')) {\n      this.leadSelecionado.segmento = 'Hamburgueria';\n    } else if (bioLower.includes('lanche') || bioLower.includes('sanduí')) {\n      this.leadSelecionado.segmento = 'Lanchonete';\n    } else if (bioLower.includes('doce') || bioLower.includes('bolo') || bioLower.includes('confeit')) {\n      this.leadSelecionado.segmento = 'Confeitaria';\n    } else if (bioLower.includes('bar') || bioLower.includes('boteco') || bioLower.includes('cervej')) {\n      this.leadSelecionado.segmento = 'Bar';\n    } else if (bioLower.includes('food truck') || bioLower.includes('foodtruck')) {\n      this.leadSelecionado.segmento = 'Food Truck';\n    } else if (bioLower.includes('restaurante') || bioLower.includes('comida') || bioLower.includes('culinária')) {\n      this.leadSelecionado.segmento = 'Restaurante';\n    }\n  }\n\n  // ===== MÉTODOS PARA GERENCIAR LINKS =====\n\n  toggleSecaoLinks(): void {\n    this.mostrarSecaoLinks = !this.mostrarSecaoLinks;\n  }\n\n  adicionarLink(): void {\n    if (!this.novoLink.tipo || !this.novoLink.url) {\n      alert('Tipo e URL são obrigatórios');\n      return;\n    }\n\n    if (!this.leadSelecionado.links) {\n      this.leadSelecionado.links = [];\n    }\n\n    // Verificar se já existe um link do mesmo tipo\n    const linkExistente = this.leadSelecionado.links.find(link => link.tipo === this.novoLink.tipo);\n    if (linkExistente) {\n      if (confirm('Já existe um link deste tipo. Deseja substituir?')) {\n        linkExistente.url = this.novoLink.url;\n        linkExistente.descricao = this.novoLink.descricao;\n      }\n    } else {\n      this.leadSelecionado.links.push({\n        tipo: this.novoLink.tipo,\n        url: this.novoLink.url,\n        descricao: this.novoLink.descricao,\n        ativo: true,\n        ordem: this.leadSelecionado.links.length\n      });\n    }\n\n    // Limpar formulário\n    this.novoLink = { tipo: '', url: '', descricao: '' };\n  }\n\n  removerLink(index: number): void {\n    if (confirm('Deseja remover este link?')) {\n      this.leadSelecionado.links.splice(index, 1);\n    }\n  }\n\n  getTipoLinkInfo(tipo: string): any {\n    return this.tiposLink.find(t => t.valor === tipo) || {\n      texto: tipo,\n      icone: 'fa-link',\n      cor: '#6c757d'\n    };\n  }\n\n  formatarUrlLink(url: string, tipo: string): string {\n    if (!url) return '';\n\n    // Formatações específicas por tipo\n    switch (tipo) {\n      case 'WhatsApp':\n        if (url.includes('wa.me') || url.includes('whatsapp.com')) {\n          return url;\n        }\n        const numeroLimpo = url.replace(/\\D/g, '');\n        return numeroLimpo.length >= 10 ? `https://wa.me/55${numeroLimpo}` : url;\n\n      case 'Localização':\n        if (url.includes('maps.google.com') || url.includes('goo.gl/maps')) {\n          return url;\n        }\n        return `https://maps.google.com/maps?q=${encodeURIComponent(url)}`;\n\n      default:\n        return url.startsWith('http') ? url : `https://${url}`;\n    }\n  }\n\n  abrirLink(url: string, tipo: string): void {\n    const urlFormatada = this.formatarUrlLink(url, tipo);\n    window.open(urlFormatada, '_blank');\n  }\n}", "<div class=\"container-fluid\">\n  <div class=\"d-flex justify-content-between align-items-center mb-3\">\n    <h3><i class=\"fa fa-building\"></i> Empresas CRM</h3>\n    <button class=\"btn btn-primary\" (click)=\"nova()\" *ngIf=\"!modoEdicao\">\n      <i class=\"fa fa-plus\"></i> Nova Empresa\n    </button>\n  </div>\n\n  <!-- Formulário -->\n  <div class=\"card mb-4\" *ngIf=\"modoEdicao\">\n    <div class=\"card-header\">\n      <h5 class=\"mb-0\">\n        <i class=\"fa fa-edit\"></i>\n        {{ empresaSelecionada.id ? 'Editar Empresa' : 'Nova Empresa' }}\n      </h5>\n    </div>\n    <div class=\"card-body\">\n      <form #empresaForm=\"ngForm\" (ngSubmit)=\"salvar()\">\n        <div class=\"row\">\n          <div class=\"col-md-6\">\n            <div class=\"form-group\">\n              <label for=\"nome\"><strong>Nome da Empresa *</strong></label>\n              <input type=\"text\"\n                     id=\"nome\" name=\"nome\"\n                     class=\"form-control\"\n                     [(ngModel)]=\"empresaSelecionada.nome\"\n                     required\n                     placeholder=\"Ex: MeuCardápio LTDA\">\n            </div>\n          </div>\n\n          <div class=\"col-md-3\">\n            <div class=\"form-group\">\n              <label for=\"cnpj\"><strong>CNPJ</strong></label>\n              <input type=\"text\"\n                     id=\"cnpj\" name=\"cnpj\"\n                     class=\"form-control\"\n                     [(ngModel)]=\"empresaSelecionada.cnpj\"\n                     (ngModelChange)=\"formatarCnpj()\"\n                     placeholder=\"00.000.000/0001-00\">\n            </div>\n          </div>\n\n          <div class=\"col-md-3\">\n            <div class=\"form-group\">\n              <label for=\"ativa\"><strong>Status</strong></label>\n              <select id=\"ativa\" name=\"ativa\"\n                      class=\"form-control\"\n                      [(ngModel)]=\"empresaSelecionada.ativa\">\n                <option [value]=\"true\">Ativa</option>\n                <option [value]=\"false\">Inativa</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"row\">\n          <div class=\"col-md-6\">\n            <div class=\"form-group\">\n              <label for=\"telefone\"><strong>Telefone</strong></label>\n              <input type=\"text\"\n                     id=\"telefone\" name=\"telefone\"\n                     class=\"form-control\"\n                     [(ngModel)]=\"empresaSelecionada.telefone\"\n                     placeholder=\"(11) 99999-9999\">\n            </div>\n          </div>\n\n          <div class=\"col-md-6\">\n            <div class=\"form-group\">\n              <label for=\"email\"><strong>E-mail</strong></label>\n              <input type=\"email\"\n                     id=\"email\" name=\"email\"\n                     class=\"form-control\"\n                     [(ngModel)]=\"empresaSelecionada.email\"\n                     placeholder=\"<EMAIL>\">\n            </div>\n          </div>\n        </div>\n\n        <div class=\"row\">\n          <div class=\"col-12\">\n            <div class=\"form-group\">\n              <label for=\"endereco\"><strong>Endereço</strong></label>\n              <textarea id=\"endereco\" name=\"endereco\"\n                        class=\"form-control\"\n                        [(ngModel)]=\"empresaSelecionada.endereco\"\n                        rows=\"2\"\n                        placeholder=\"Endereço completo...\"></textarea>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"d-flex justify-content-end\">\n          <button type=\"button\" class=\"btn btn-secondary mr-2\" (click)=\"cancelar()\">\n            <i class=\"fa fa-times\"></i> Cancelar\n          </button>\n          <button type=\"submit\" class=\"btn btn-success\" [disabled]=\"empresaForm.invalid\">\n            <i class=\"fa fa-save\"></i> Salvar\n          </button>\n        </div>\n      </form>\n    </div>\n  </div>\n\n  <!-- Grid de Listagem -->\n  <div class=\"card\" *ngIf=\"!modoEdicao\">\n    <div class=\"card-header\">\n      <h5 class=\"mb-0\"><i class=\"fa fa-list\"></i> Lista de Empresas</h5>\n    </div>\n    <div class=\"card-body\">\n      <kendo-grid [data]=\"empresas\"\n                  [loading]=\"carregando\"\n                  [height]=\"500\"\n                  [pageable]=\"true\"\n                  [sortable]=\"true\"\n                  [filterable]=\"true\">\n\n        <kendo-grid-column field=\"nome\" title=\"Nome\" [width]=\"250\">\n          <ng-template kendoGridCellTemplate let-dataItem>\n            <strong>{{ dataItem.nome }}</strong>\n          </ng-template>\n        </kendo-grid-column>\n\n        <kendo-grid-column field=\"cnpj\" title=\"CNPJ\" [width]=\"150\">\n        </kendo-grid-column>\n\n        <kendo-grid-column field=\"telefone\" title=\"Telefone\" [width]=\"130\">\n        </kendo-grid-column>\n\n        <kendo-grid-column field=\"email\" title=\"E-mail\" [width]=\"200\">\n        </kendo-grid-column>\n\n        <kendo-grid-column field=\"ativa\" title=\"Status\" [width]=\"80\">\n          <ng-template kendoGridCellTemplate let-dataItem>\n            <span class=\"badge\"\n                  [class]=\"dataItem.ativa ? 'badge-success' : 'badge-secondary'\">\n              {{ dataItem.ativa ? 'Ativa' : 'Inativa' }}\n            </span>\n          </ng-template>\n        </kendo-grid-column>\n\n        <kendo-grid-column title=\"Ações\" [width]=\"120\">\n          <ng-template kendoGridCellTemplate let-dataItem>\n            <button class=\"btn btn-sm btn-outline-primary mr-1\"\n                    (click)=\"editar(dataItem)\"\n                    title=\"Editar\">\n              <i class=\"fa fa-edit\"></i>\n            </button>\n            <button class=\"btn btn-sm btn-outline-danger\"\n                    (click)=\"remover(dataItem.id)\"\n                    title=\"Remover\">\n              <i class=\"fa fa-trash\"></i>\n            </button>\n          </ng-template>\n        </kendo-grid-column>\n      </kendo-grid>\n    </div>\n  </div>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { CrmEmpresaService } from '../services/crm-empresa.service';\n\n@Component({\n  selector: 'app-crm-empresa-crud',\n  templateUrl: './crm-empresa-crud.component.html',\n  styleUrls: ['./crm-empresa-crud.component.scss']\n})\nexport class CrmEmpresaCrudComponent implements OnInit {\n  empresas: any[] = [];\n  empresaSelecionada: any = {};\n  carregando = false;\n  modoEdicao = false;\n\n  constructor(private crmEmpresaService: CrmEmpresaService) {}\n\n  ngOnInit(): void {\n    this.listar();\n  }\n\n  listar(): void {\n    this.carregando = true;\n    this.crmEmpresaService.liste({ inicio: 0, total: 100 }).then((resp) => {\n      this.empresas = resp.data || resp;\n      this.carregando = false;\n    }).catch(() => this.carregando = false);\n  }\n\n  nova(): void {\n    this.empresaSelecionada = {\n      ativa: true\n    };\n    this.modoEdicao = true;\n  }\n\n  editar(empresa: any): void {\n    this.empresaSelecionada = { ...empresa };\n    this.modoEdicao = true;\n  }\n\n  cancelar(): void {\n    this.empresaSelecionada = {};\n    this.modoEdicao = false;\n  }\n\n  salvar(): void {\n    this.crmEmpresaService.salveEmpresa(this.empresaSelecionada).then(() => {\n      this.cancelar();\n      this.listar();\n    }).catch((erro) => {\n      alert('Erro ao salvar: ' + erro);\n    });\n  }\n\n  remover(id: number): void {\n    if (!confirm('Deseja realmente remover esta empresa?')) return;\n    this.crmEmpresaService.removaEmpresa(id).then(() => this.listar());\n  }\n\n  // Formatação de CNPJ\n  formatarCnpj(): void {\n    if (this.empresaSelecionada.cnpj) {\n      let cnpj = this.empresaSelecionada.cnpj.replace(/\\D/g, '');\n      cnpj = cnpj.replace(/^(\\d{2})(\\d{3})(\\d{3})(\\d{4})(\\d{2})/, '$1.$2.$3/$4-$5');\n      this.empresaSelecionada.cnpj = cnpj;\n    }\n  }\n} ", "import { Injectable } from '@angular/core';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class InstagramDataService {\n  private dadosInstagram: any = null;\n  private username: string = '';\n\n  /**\n   * Armazena os dados do Instagram temporariamente\n   */\n  setDados(dados: any, username: string): void {\n    this.dadosInstagram = dados;\n    this.username = username;\n    console.log('Dados do Instagram salvos no service:', { username, dados });\n  }\n\n  /**\n   * Recupera os dados do Instagram armazenados\n   */\n  getDados(): { dados: any, username: string } | null {\n    if (this.dadosInstagram) {\n      return {\n        dados: this.dadosInstagram,\n        username: this.username\n      };\n    }\n    return null;\n  }\n\n  /**\n   * Limpa os dados armazenados\n   */\n  clearDados(): void {\n    this.dadosInstagram = null;\n    this.username = '';\n    console.log('Dados do Instagram limpos do service');\n  }\n\n  /**\n   * Verifica se há dados armazenados\n   */\n  hasDados(): boolean {\n    return this.dadosInstagram !== null;\n  }\n}\n", "<div class=\"wizard-container\">\n  <!-- Header com Progress Bar -->\n  <div class=\"wizard-header\">\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <div class=\"col-12\">\n          <div class=\"wizard-progress\">\n            <div class=\"step-progress\">\n              <div\n                *ngFor=\"let i of [1,2,3,4,5]\"\n                class=\"step\"\n                [class]=\"getStepClass(i)\"\n                (click)=\"goToStep(i)\">\n                <div class=\"step-number\">{{i}}</div>\n                <div class=\"step-label\">\n                  <span *ngIf=\"i === 1\">Extrair</span>\n                  <span *ngIf=\"i === 2\">Links</span>\n                  <span *ngIf=\"i === 3\">CNPJ</span>\n                  <span *ngIf=\"i === 4\">Sócios</span>\n                  <span *ngIf=\"i === 5\">Finalizar</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- <PERSON><PERSON><PERSON><PERSON> do Passo Atual -->\n  <div class=\"wizard-title\">\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <div class=\"col-12\">\n          <h3 class=\"step-title\">\n            <i class=\"fas fa-user-plus\" *ngIf=\"currentStep === 1\"></i>\n            <i class=\"fas fa-link\" *ngIf=\"currentStep === 2\"></i>\n            <i class=\"fas fa-id-card\" *ngIf=\"currentStep === 3\"></i>\n            <i class=\"fas fa-users\" *ngIf=\"currentStep === 4\"></i>\n            <i class=\"fas fa-check-circle\" *ngIf=\"currentStep === 5\"></i>\n            {{getCurrentStepTitle()}}\n          </h3>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Barra de Busca Google Global -->\n  <div class=\"google-search-bar\" *ngIf=\"lead.empresa?.trim()\">\n    <div class=\"container-fluid\">\n      <div class=\"search-compact\">\n        <i class=\"fab fa-google\"></i>\n        <input\n          type=\"text\"\n          class=\"form-control\"\n          [(ngModel)]=\"searchTerm\"\n          name=\"globalSearchTerm\"\n          placeholder=\"Empresa - Cidade\">\n        <button\n          type=\"button\"\n          class=\"btn\"\n          (click)=\"buscarEmpresaNoGoogle()\"\n          [disabled]=\"!lead.empresa?.trim()\"\n          title=\"Ver empresa no Google\">\n          Ver no Google\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Conteúdo dos Passos -->\n  <div class=\"wizard-content\">\n    <div class=\"container-fluid\">\n\n      <!-- ===== PASSO 1: EXTRAIR DADOS ===== -->\n      <div *ngIf=\"currentStep === 1\" class=\"step-content step-1\">\n\n        <!-- Card Principal: Extração de Dados -->\n        <div class=\"row\">\n          <div class=\"col-12\">\n            <div class=\"card extraction-main-card\">\n              <div class=\"card-body\">\n                <h5 class=\"card-title\">\n                  <i class=\"fab fa-instagram text-primary\"></i>\n                  Extrair Dados do Instagram\n                </h5>\n                <p class=\"card-text text-muted\">\n                  Informe o username do Instagram da empresa para extrair dados automaticamente\n                </p>\n\n                <!-- Input Instagram -->\n                <div class=\"form-group\">\n                  <label for=\"instagramUsername\">Username do Instagram</label>\n                  <div class=\"input-group\">\n                    <div class=\"input-group-prepend\">\n                      <span class=\"input-group-text\">@</span>\n                    </div>\n                    <input\n                      type=\"text\"\n                      id=\"instagramUsername\"\n                      class=\"form-control\"\n                      [(ngModel)]=\"username\"\n                      name=\"instagramUsername\"\n                      placeholder=\"restaurante_exemplo\"\n                      (keyup.enter)=\"solicitarDadosInstagram()\">\n                    <div class=\"input-group-append\">\n                      <button\n                        type=\"button\"\n                        class=\"btn btn-primary\"\n                        (click)=\"solicitarDadosInstagram()\"\n                        [disabled]=\"carregando || !username\">\n                        <i class=\"fas fa-download\" *ngIf=\"!carregando\"></i>\n                        <i class=\"fas fa-spinner fa-spin\" *ngIf=\"carregando\"></i>\n                        <span *ngIf=\"!carregando\">Extrair Dados</span>\n                        <span *ngIf=\"carregando\">Extraindo...</span>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- OU Divisor -->\n                <div class=\"divider-or\">\n                  <span>OU</span>\n                </div>\n\n                <!-- Botão Criar Manual -->\n                <div class=\"text-center\">\n                  <button\n                    type=\"button\"\n                    class=\"btn btn-outline-secondary\"\n                    (click)=\"mostrarFormularioManual()\">\n                    <i class=\"fas fa-edit\"></i>\n                    Criar Lead Manualmente\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Card: Dados do Instagram (se extraídos) -->\n        <div class=\"row\" *ngIf=\"dadosInstagram && dadosInstagram.user\">\n          <div class=\"col-12\">\n            <div class=\"card data-card\">\n              <div class=\"card-body\">\n                <h6 class=\"card-title\">\n                  <i class=\"fas fa-check text-success\"></i>\n                  Dados Extraídos do Instagram\n                </h6>\n\n                <div class=\"instagram-profile\">\n                  <div class=\"profile-header-simple\">\n                    <div class=\"profile-details\">\n                      <h6 class=\"username\"><i class=\"fab fa-instagram\"></i> @{{dadosInstagram.user.username}}</h6>\n                      <p class=\"full-name\" *ngIf=\"dadosInstagram.user.full_name\">{{dadosInstagram.user.full_name}}</p>\n                      <div class=\"stats\">\n                        <span class=\"stat\">\n                          <i class=\"fas fa-users\"></i>\n                          <strong>{{getSeguidoresFormatado()}}</strong> seguidores\n                        </span>\n                        <span class=\"stat\">\n                          <i class=\"fas fa-user-plus\"></i>\n                          <strong>{{dadosInstagram.user.edge_follow?.count || 0}}</strong> seguindo\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Biografia destacada -->\n                  <div class=\"bio-section\" *ngIf=\"dadosInstagram.user.biography\">\n                    <h6 class=\"bio-title\">\n                      <i class=\"fas fa-quote-left\"></i>\n                      Biografia do Perfil\n                    </h6>\n                    <div class=\"bio-content\">\n                      {{dadosInstagram.user.biography}}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Card: Formulário de Dados Básicos -->\n        <div class=\"row\" *ngIf=\"mostrarFormulario || (dadosInstagram && dadosInstagram.user)\">\n          <div class=\"col-12\">\n            <div class=\"card form-card\">\n              <div class=\"card-body\">\n                <h6 class=\"card-title\">\n                  <i class=\"fas fa-edit\"></i>\n                  Dados Básicos do Lead\n                </h6>\n\n                  <div class=\"row\">\n                    <!-- Nome do Responsável -->\n                  <div class=\"col-md-6\">\n                    <div class=\"form-group\">\n                      <label for=\"nomeResponsavel\">Nome do Responsável *</label>\n                      <input\n                        type=\"text\"\n                        id=\"nomeResponsavel\"\n                        class=\"form-control\"\n                        [(ngModel)]=\"lead.nomeResponsavel\"\n                        name=\"nomeResponsavel\"\n                        placeholder=\"João Silva\"\n                        required>\n                    </div>\n                  </div>\n\n                  <!-- Nome da Empresa -->\n                  <div class=\"col-md-6\">\n                    <div class=\"form-group\">\n                      <label for=\"empresa\">Nome da Empresa *</label>\n                      <input\n                        type=\"text\"\n                        id=\"empresa\"\n                        class=\"form-control\"\n                        [(ngModel)]=\"lead.empresa\"\n                        name=\"empresa\"\n                        placeholder=\"Restaurante Exemplo\"\n                        required>\n                      <small class=\"form-text text-muted\">\n                        <i class=\"fas fa-info-circle\"></i>\n                        Use o cartão \"Buscar Empresa no Google\" abaixo para encontrar mais informações\n                      </small>\n                    </div>\n                  </div>\n\n                  <!-- Cidade -->\n                  <div class=\"col-md-6\">\n                    <div class=\"form-group\">\n                      <label for=\"cidade\">Cidade *</label>\n                      <input\n                        type=\"text\"\n                        id=\"cidade\"\n                        class=\"form-control\"\n                        [(ngModel)]=\"lead.cidade\"\n                        name=\"cidade\"\n                        placeholder=\"São Paulo\"\n                        required>\n                    </div>\n                  </div>\n\n                  <!-- Telefone -->\n                  <div class=\"col-md-6\">\n                    <div class=\"form-group\">\n                      <label for=\"telefone\">Telefone *</label>\n                      <kendo-maskedtextbox\n                        id=\"telefone\"\n                        class=\"form-control\"\n                        [(ngModel)]=\"telefoneFormatado\"\n                        name=\"telefone\"\n                        [mask]=\"'(00) 00000-0000'\"\n                        placeholder=\"(62) 99999-9999\"\n                        (blur)=\"onTelefoneBlur()\"\n                        (valueChange)=\"onTelefoneChange($event)\"\n                        (paste)=\"onTelefonePaste($event)\"\n                        required\n                        campoTelefone>\n                      </kendo-maskedtextbox>\n                      <small class=\"form-text text-muted text-danger\">\n                        <i class=\"fas fa-exclamation-circle\"></i>\n                        Campo obrigatório para criar contato no Bitrix\n                      </small>\n                    </div>\n                  </div>\n\n                  <!-- Instagram Handle -->\n                  <div class=\"col-md-6\">\n                    <div class=\"form-group\">\n                      <label for=\"instagramHandle\">Username Instagram *</label>\n                      <div class=\"input-group\">\n                        <div class=\"input-group-prepend\">\n                          <span class=\"input-group-text\">@</span>\n                        </div>\n                        <input\n                          type=\"text\"\n                          id=\"instagramHandle\"\n                          class=\"form-control\"\n                          [(ngModel)]=\"lead.instagramHandle\"\n                          name=\"instagramHandle\"\n                          placeholder=\"restaurante_exemplo\"\n                          required>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Website -->\n                  <div class=\"col-md-6\">\n                    <div class=\"form-group\">\n                      <label for=\"website\">Website</label>\n                      <input\n                        type=\"url\"\n                        id=\"website\"\n                        class=\"form-control\"\n                        [(ngModel)]=\"lead.website\"\n                        name=\"website\"\n                        placeholder=\"https://exemplo.com.br\"\n                        (blur)=\"onWebsiteBlur()\">\n                    </div>\n                  </div>\n\n                  <!-- Biografia Instagram -->\n                  <div class=\"col-md-6\" *ngIf=\"dadosInstagram && dadosInstagram.user && dadosInstagram.user.biography\">\n                    <div class=\"form-group\">\n                      <label for=\"bioInsta\">Biografia do Instagram</label>\n                      <textarea\n                        id=\"bioInsta\"\n                        class=\"form-control\"\n                        [(ngModel)]=\"lead.bioInsta\"\n                        name=\"bioInsta\"\n                        rows=\"3\"\n                        placeholder=\"Biografia extraída do Instagram...\">{{dadosInstagram.user.biography}}</textarea>\n                    </div>\n                  </div>\n\n                  <!-- Observações -->\n                  <div class=\"col-md-6\" *ngIf=\"lead.observacoes\">\n                    <div class=\"form-group\">\n                      <label for=\"observacoesStep1\">Observações</label>\n                      <textarea\n                        id=\"observacoesStep1\"\n                        class=\"form-control\"\n                        [(ngModel)]=\"lead.observacoes\"\n                        name=\"observacoesStep1\"\n                        rows=\"3\"\n                        placeholder=\"Observações sobre o lead...\"></textarea>\n                    </div>\n                    </div>\n                  </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n      </div>\n\n      <!-- ===== PASSO 2: BUSCAR LINKS ===== -->\n      <div *ngIf=\"currentStep === 2\" class=\"step-content step-2\">\n\n        <!-- Resumo do Passo Anterior -->\n        <div class=\"row\">\n          <div class=\"col-12\">\n            <div class=\"card summary-card\">\n              <div class=\"card-body\">\n                <h6 class=\"card-title\">\n                  <i class=\"fas fa-check text-success\"></i>\n                  Dados Básicos (Passo 1)\n                </h6>\n                <div class=\"summary-content d-flex justify-content-between align-items-center\">\n                  <div>\n                    <span class=\"badge badge-primary\">@{{lead.instagramHandle}}</span>\n                    <strong>{{lead.nomeResponsavel}}</strong> - {{lead.empresa}} - {{lead.cidade}}\n                  </div>\n                  <button class=\"btn btn-sm btn-outline-secondary ml-3\" (click)=\"goToStep(1)\">\n                    <i class=\"fas fa-edit\"></i> Editar\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n        <!-- Card Principal: Análise de Website -->\n        <div class=\"row\">\n          <div class=\"col-12\">\n            <div class=\"card main-card\">\n              <div class=\"card-body\">\n                <h5 class=\"card-title\">\n                  <i class=\"fas fa-link text-primary\"></i>\n                  Analisar Website da Empresa\n                </h5>\n                <p class=\"card-text text-muted\">\n                  Informe o website da empresa para descobrir links relevantes automaticamente\n                </p>\n\n                <!-- Website Input -->\n                <div class=\"form-group\">\n                  <label for=\"websiteAnalysis\">Website da Empresa</label>\n                  <div class=\"input-group\">\n                    <input\n                      type=\"url\"\n                      id=\"websiteAnalysis\"\n                      class=\"form-control\"\n                      [(ngModel)]=\"lead.website\"\n                      name=\"websiteAnalysis\"\n                      placeholder=\"https://restaurante-exemplo.com.br\"\n                      (blur)=\"onWebsiteBlur()\">\n                    <div class=\"input-group-append\">\n                      <button\n                        type=\"button\"\n                        class=\"btn btn-primary\"\n                        (click)=\"carregarDadosDoLink()\"\n                        [disabled]=\"carregandoWebsite || !lead.website\">\n                        <i class=\"fas fa-search\" *ngIf=\"!carregandoWebsite\"></i>\n                        <i class=\"fas fa-spinner fa-spin\" *ngIf=\"carregandoWebsite\"></i>\n                        <span *ngIf=\"!carregandoWebsite\">Analisar Website</span>\n                        <span *ngIf=\"carregandoWebsite\">Analisando...</span>\n                      </button>\n                    </div>\n                  </div>\n                  <small class=\"form-text text-muted\">\n                    A análise irá descobrir links para WhatsApp, Instagram, iFood, cardápios e muito mais\n                  </small>\n                </div>\n\n                <!-- Aviso se não tiver website -->\n                <div class=\"alert alert-info\" *ngIf=\"!lead.website\">\n                  <i class=\"fas fa-info-circle\"></i>\n                  <strong>Etapa Opcional:</strong> Se a empresa não possui website, você pode pular esta etapa.\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Links Encontrados -->\n        <div class=\"row\" *ngIf=\"linksEncontrados && linksEncontrados.length > 0\">\n          <div class=\"col-12\">\n            <div class=\"card results-card\">\n              <div class=\"card-body\">\n                <h6 class=\"card-title\">\n                  <i class=\"fas fa-check text-success\"></i>\n                  Links Encontrados ({{linksEncontrados.length}})\n                </h6>\n\n                <div class=\"links-grid\">\n                  <div\n                    *ngFor=\"let link of linksEncontrados\"\n                    class=\"link-item\"\n                    [class.link-whatsapp]=\"link.tipo === 'WhatsApp'\"\n                    [class.link-instagram]=\"link.tipo === 'Instagram'\"\n                    [class.link-ifood]=\"link.tipo === 'Ifood'\"\n                    [class.link-cardapio]=\"link.tipo === 'Site do Cardápio'\"\n                    [class.link-concorrente]=\"link.tipo === 'Concorrente'\"\n                    [class.link-localizacao]=\"link.tipo === 'Localização'\"\n                    [class.link-site]=\"link.tipo === 'Site'\">\n                    <div class=\"link-icon\">\n                      <i class=\"fab fa-whatsapp\" *ngIf=\"link.tipo === 'WhatsApp'\"></i>\n                      <i class=\"fab fa-instagram\" *ngIf=\"link.tipo === 'Instagram'\"></i>\n                      <i class=\"fas fa-utensils\" *ngIf=\"link.tipo === 'Ifood'\"></i>\n                      <i class=\"fas fa-list-alt\" *ngIf=\"link.tipo === 'Site do Cardápio'\"></i>\n                      <i class=\"fas fa-exclamation-triangle\" *ngIf=\"link.tipo === 'Concorrente'\"></i>\n                      <i class=\"fas fa-map-marker-alt\" *ngIf=\"link.tipo === 'Localização'\"></i>\n                      <i class=\"fas fa-globe\" *ngIf=\"link.tipo === 'Site'\"></i>\n                      <i class=\"fas fa-link\" *ngIf=\"!['WhatsApp', 'Instagram', 'Ifood', 'Site do Cardápio', 'Concorrente', 'Localização', 'Site'].includes(link.tipo)\"></i>\n                    </div>\n                    <div class=\"link-content\">\n                      <div class=\"link-tipo\">{{link.tipo}}</div>\n                      <div class=\"link-descricao\">{{link.descricao}}</div>\n                      <div class=\"link-url\">{{link.url | slice:0:40}}{{link.url.length > 40 ? '...' : ''}}</div>\n                    </div>\n                    <div class=\"link-actions\">\n                      <button\n                        type=\"button\"\n                        class=\"btn btn-sm btn-outline-primary\"\n                        (click)=\"abrirLink(link.url)\"\n                        title=\"Abrir link\">\n                        <i class=\"fas fa-external-link-alt\"></i>\n                      </button>\n                      <button\n                        type=\"button\"\n                        class=\"btn btn-sm btn-outline-secondary\"\n                        (click)=\"copiarLink(link.url)\"\n                        title=\"Copiar link\">\n                        <i class=\"fas fa-copy\"></i>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Sistema Concorrente -->\n                <div class=\"mt-4\">\n                  <h6 class=\"text-muted mb-3\">\n                    <i class=\"fas fa-exclamation-triangle text-warning\"></i>\n                    Sistema Concorrente\n                  </h6>\n                  <div class=\"form-group\">\n                    <label for=\"concorrente\">\n                      Sistema utilizado atualmente\n                      <small class=\"text-muted\">(detectado automaticamente dos links ou selecione manualmente)</small>\n                    </label>\n                    <select\n                      id=\"concorrente\"\n                      class=\"form-control\"\n                      [(ngModel)]=\"lead.concorrente\"\n                      name=\"concorrente\">\n                      <option value=\"\">-- Selecione um concorrente --</option>\n                      <option *ngFor=\"let concorrente of concorrentes\" [value]=\"concorrente.valor\">\n                        {{concorrente.texto}}\n                      </option>\n                    </select>\n                    <small class=\"form-text text-muted\">\n                      <i class=\"fas fa-info-circle\"></i>\n                      Se você identificou um sistema concorrente nos links, selecione-o aqui\n                    </small>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- ===== PASSO 3: DESCOBRIR CNPJ ===== -->\n      <div *ngIf=\"currentStep === 3\" class=\"step-content step-3\">\n\n        <!-- Resumo dos Passos Anteriores -->\n        <div class=\"row\">\n          <div class=\"col-12\">\n            <div class=\"card summary-card\">\n              <div class=\"card-body\">\n                <h6 class=\"card-title\">\n                  <i class=\"fas fa-check text-success\"></i>\n                  Progresso Anterior\n                </h6>\n                <div class=\"summary-content\">\n                  <div class=\"summary-item\">\n                    <strong>Dados:</strong> {{lead.empresa}} - {{lead.cidade}} (@{{lead.instagramHandle}})\n                  </div>\n                  <div class=\"d-flex justify-content-between align-items-center\">\n                  <div>\n                    <div class=\"summary-item\" *ngIf=\"linksEncontrados.length > 0\">\n                      <strong>Links:</strong> {{linksEncontrados.length}} encontrados\n                    </div>\n                  </div>\n                  <button class=\"btn btn-sm btn-outline-secondary ml-3\" (click)=\"goToStep(1)\">\n                    <i class=\"fas fa-edit\"></i> Editar\n                  </button>\n                </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n        <!-- Card Principal: Descoberta de CNPJ -->\n        <div class=\"row\">\n          <div class=\"col-12\">\n            <div class=\"card main-card cnpj-discovery-card\">\n              <div class=\"card-body\">\n                <h5 class=\"card-title\">\n                  <i class=\"fas fa-id-card text-info\"></i>\n                  Descobrir CNPJ da Empresa\n                </h5>\n                <p class=\"card-text text-muted\">\n                  Encontre o CNPJ oficial da empresa através de busca inteligente\n                </p>\n\n                <!-- Inputs de Busca -->\n                <div class=\"row\">\n                  <div class=\"col-md-5\">\n                    <div class=\"form-group\">\n                      <label for=\"empresaBusca\">Nome da Empresa</label>\n                      <input\n                        type=\"text\"\n                        id=\"empresaBusca\"\n                        class=\"form-control\"\n                        [(ngModel)]=\"lead.empresa\"\n                        name=\"empresaBusca\"\n                        placeholder=\"Nome exato da empresa\">\n                    </div>\n                  </div>\n                  <div class=\"col-md-4\">\n                    <div class=\"form-group\">\n                      <label for=\"cidadeBusca\">Cidade</label>\n                      <input\n                        type=\"text\"\n                        id=\"cidadeBusca\"\n                        class=\"form-control\"\n                        [(ngModel)]=\"lead.cidade\"\n                        name=\"cidadeBusca\"\n                        placeholder=\"Cidade da empresa\">\n                    </div>\n                  </div>\n                  <div class=\"col-md-3\">\n                    <div class=\"form-group\">\n                      <label>&nbsp;</label>\n                      <button\n                        type=\"button\"\n                        class=\"btn btn-primary btn-block\"\n                        (click)=\"descobrirCnpj()\"\n                        [disabled]=\"carregandoCnpj || !lead.empresa || !lead.cidade\">\n                        <i class=\"fas fa-search\" *ngIf=\"!carregandoCnpj\"></i>\n                        <i class=\"fas fa-spinner fa-spin\" *ngIf=\"carregandoCnpj\"></i>\n                        <span *ngIf=\"!carregandoCnpj\">Buscar CNPJs</span>\n                        <span *ngIf=\"carregandoCnpj\">Buscando...</span>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Separador OU -->\n                <div class=\"divider-or mt-3\">\n                  <span>OU</span>\n                </div>\n\n                <!-- Busca Manual Integrada -->\n                <div class=\"manual-search-section mt-3\">\n                  <h6 class=\"text-center mb-2 text-muted\">\n                    <i class=\"fas fa-search\"></i>\n                    Buscar CNPJ Manualmente\n                  </h6>\n\n                  <!-- Botão de Busca no Google -->\n                  <div class=\"text-center mb-2\" *ngIf=\"lead.empresa && lead.cidade\">\n                    <button\n                      type=\"button\"\n                      class=\"btn btn-outline-info\"\n                      (click)=\"abrirGoogleEmpresaCompleta()\">\n                      <i class=\"fab fa-google\"></i>\n                      Buscar \"{{lead.empresa}} {{lead.cidade}}\" no Google\n                    </button>\n                  </div>\n\n                  <!-- Campo para informar CNPJ -->\n                  <div class=\"row\">\n                    <div class=\"col-md-8 mx-auto\">\n                      <label class=\"text-muted mb-2\">Encontrou o CNPJ? Informe abaixo:</label>\n                      <div class=\"input-group\">\n                        <input\n                          type=\"text\"\n                          class=\"form-control\"\n                          [(ngModel)]=\"cnpjManual\"\n                          name=\"cnpjManual\"\n                          placeholder=\"00.000.000/0000-00\"\n                          cnpjValido\n                          #cnpjManualField=\"ngModel\"\n                          (input)=\"formatarCnpjInput($event)\"\n                          maxlength=\"18\">\n                        <button\n                          type=\"button\"\n                          class=\"btn btn-warning\"\n                          (click)=\"buscarCnpjManual()\"\n                          [disabled]=\"carregandoCnpjManual || cnpjManualField.invalid || !cnpjManual\">\n                          <i class=\"fas fa-search\" *ngIf=\"!carregandoCnpjManual\"></i>\n                          <i class=\"fas fa-spinner fa-spin\" *ngIf=\"carregandoCnpjManual\"></i>\n                          <span *ngIf=\"!carregandoCnpjManual\">Buscar Dados</span>\n                          <span *ngIf=\"carregandoCnpjManual\">Buscando...</span>\n                        </button>\n                      </div>\n                      <div class=\"invalid-feedback\" *ngIf=\"cnpjManualField.invalid && cnpjManualField.touched\">\n                        <small *ngIf=\"cnpjManualField.errors?.['cnpjInvalido']\">\n                          <i class=\"fas fa-exclamation-triangle\"></i>\n                          CNPJ inválido. Verifique o número digitado.\n                        </small>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Aviso se campos não preenchidos -->\n                <div class=\"alert alert-info\" *ngIf=\"!lead.empresa || !lead.cidade\">\n                  <i class=\"fas fa-info-circle\"></i>\n                  <strong>Etapa Opcional:</strong> Se não souber o CNPJ da empresa, você pode pular esta etapa.\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n        <!-- CNPJs Encontrados -->\n        <div class=\"row\" *ngIf=\"cnpjsEncontrados && cnpjsEncontrados.length > 0\">\n          <div class=\"col-12\">\n            <div class=\"card results-card\">\n              <div class=\"card-body\">\n                <h6 class=\"card-title\">\n                  <i class=\"fas fa-check text-success\"></i>\n                  CNPJs Encontrados ({{cnpjsEncontrados.length}})\n                  <button\n                    type=\"button\"\n                    class=\"btn btn-sm btn-outline-secondary float-right ml-1\"\n                    (click)=\"removerSelecaoCnpj()\"\n                    *ngIf=\"lead.cnpj\">\n                    <i class=\"fas fa-times\"></i> Limpar Seleção\n                  </button>\n                </h6>\n\n                <!-- Confirmação de que o usuário não quer nenhum CNPJ -->\n                <div class=\"alert alert-info d-flex align-items-center\"\n                     *ngIf=\"cnpjRecusadoExplicitamente\">\n                  <i class=\"fas fa-info-circle mr-2\"></i>\n                  <div class=\"flex-grow-1\">\n                    <strong>Confirmado:</strong> Nenhum dos CNPJs encontrados corresponde ao lead.\n                  </div>\n                  <button\n                    type=\"button\"\n                    class=\"btn btn-sm btn-outline-info ml-2\"\n                    (click)=\"cnpjRecusadoExplicitamente = false\">\n                    <i class=\"fas fa-undo\"></i> Desfazer\n                  </button>\n                </div>\n\n                <!-- Grid de CNPJs (3 colunas) -->\n                <div class=\"cnpjs-grid\">\n                  <div\n                    *ngFor=\"let cnpj of cnpjsEncontrados\"\n                    class=\"cnpj-card\"\n                    [class.selected]=\"cnpj.selecionado\">\n                    <div class=\"cnpj-header\">\n                      <span class=\"cnpj-badge\" [class]=\"'badge-' + getCorConfianca(cnpj.confianca)\">\n                        {{cnpj.confianca | titlecase}}\n                      </span>\n                      <div class=\"cnpj-number\">{{cnpj.cnpj}}</div>\n                    </div>\n\n                    <div class=\"cnpj-info\">\n                      <!-- Nome Fantasia -->\n                      <div class=\"cnpj-name\" *ngIf=\"cnpj.nomeFantasia\">\n                        <i class=\"fas fa-store\"></i>\n                        {{cnpj.nomeFantasia}}\n                      </div>\n\n                      <!-- Razão Social -->\n                      <div class=\"cnpj-razao-social\" *ngIf=\"cnpj.razaoSocial\">\n                        <i class=\"fas fa-building\"></i>\n                        <small>{{cnpj.razaoSocial}}</small>\n                      </div>\n\n                      <!-- Endereço -->\n                      <div class=\"cnpj-endereco\" *ngIf=\"cnpj.endereco\">\n                        <i class=\"fas fa-map-marker-alt\"></i>\n                        <small>{{cnpj.endereco}}</small>\n                      </div>\n\n                      <!-- Capital Social -->\n                      <div class=\"cnpj-capital\" *ngIf=\"cnpj.capitalSocial\">\n                        <i class=\"fas fa-dollar-sign\"></i>\n                        <small><strong>Capital:</strong> {{cnpj.capitalSocial}}</small>\n                      </div>\n\n                      <!-- Atividade Principal -->\n                      <div class=\"cnpj-atividade\" *ngIf=\"cnpj.atividadePrincipal\">\n                        <i class=\"fas fa-briefcase\"></i>\n                        <small><strong>Atividade:</strong> {{cnpj.atividadePrincipal}}</small>\n                      </div>\n\n                      <!-- Sócios -->\n                      <div class=\"cnpj-socios\" *ngIf=\"cnpj.socios && cnpj.socios.length > 0\">\n                        <i class=\"fas fa-users\"></i>\n                        <small><strong>Sócios:</strong> {{cnpj.socios.join(', ')}}</small>\n                      </div>\n\n                      <!-- Status e Fonte -->\n                      <div class=\"cnpj-footer-info\">\n                        <div class=\"cnpj-status\" *ngIf=\"cnpj.situacao\">\n                          <span class=\"status-badge\" [class.active]=\"cnpj.situacao === 'ATIVA'\">\n                            {{cnpj.situacao}}\n                          </span>\n                        </div>\n                        <div class=\"cnpj-fonte\" *ngIf=\"cnpj.fonte\">\n                          <small class=\"text-muted\"><i class=\"fas fa-info-circle\"></i> {{cnpj.fonte}}</small>\n                        </div>\n                      </div>\n                    </div>\n                    <div class=\"cnpj-actions\">\n                      <button\n                        type=\"button\"\n                        class=\"btn btn-select\"\n                        [class.btn-success]=\"cnpj.selecionado\"\n                        [class.btn-primary]=\"!cnpj.selecionado\"\n                        (click)=\"selecionarCnpj(cnpj)\">\n                        <i class=\"fas fa-check\" *ngIf=\"cnpj.selecionado\"></i>\n                        <i class=\"fas fa-plus\" *ngIf=\"!cnpj.selecionado\"></i>\n                        {{cnpj.selecionado ? 'Selecionado' : 'Selecionar'}}\n                      </button>\n                      <button\n                        type=\"button\"\n                        class=\"btn btn-outline-info btn-details\"\n                        (click)=\"abrirCnpjBiz(cnpj.cnpj)\"\n                        title=\"Ver detalhes\">\n                        <i class=\"fas fa-external-link-alt\"></i>\n                        Ver Detalhes\n                      </button>\n                      <button\n                        type=\"button\"\n                        class=\"btn btn-outline-secondary btn-google\"\n                        (click)=\"abrirGoogleVerificarCnpj(cnpj)\"\n                        title=\"Verificar empresa no Google\">\n                        <i class=\"fab fa-google\"></i>\n                        Verificar\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- ===== PASSO 4: BUSCAR SÓCIOS ===== -->\n      <div *ngIf=\"currentStep === 4\" class=\"step-content step-4\">\n\n        <!-- Resumo dos Passos Anteriores -->\n        <div class=\"row\">\n          <div class=\"col-12\">\n            <div class=\"card summary-card\">\n              <div class=\"card-body\">\n                <h6 class=\"card-title\">\n                  <i class=\"fas fa-check text-success\"></i>\n                  CNPJ Selecionado\n                </h6>\n                <div class=\"summary-content\" *ngIf=\"lead.cnpj\">\n                <div class=\"d-flex justify-content-between align-items-center\">\n                  <div class=\"summary-item\">\n                    <strong>CNPJ:</strong> {{lead.cnpj}} - {{lead.empresa}}\n                    <div *ngIf=\"razaoSocialSelecionada\" class=\"text-muted small mt-1\">\n                      <strong>Razão Social:</strong> {{razaoSocialSelecionada}}\n                    </div>\n                    <span *ngIf=\"lead.endereco\" class=\"text-muted ml-2\">{{lead.endereco}}</span>\n                  </div>\n                  <button class=\"btn btn-sm btn-outline-secondary ml-3\" (click)=\"goToStep(3)\">\n                    <i class=\"fas fa-edit\"></i> Editar\n                  </button>\n                </div>\n                </div>\n                <div class=\"alert alert-warning\" *ngIf=\"!lead.cnpj\">\n                  <i class=\"fas fa-exclamation-triangle\"></i>\n                  Nenhum CNPJ foi selecionado. Volte ao passo anterior para selecionar um CNPJ.\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n        <!-- Card Principal: Buscar Sócios -->\n        <div class=\"row\" *ngIf=\"lead.cnpj\">\n          <div class=\"col-12\">\n            <div class=\"card main-card\">\n              <div class=\"card-body\">\n                <h5 class=\"card-title\">\n                  <i class=\"fas fa-users text-primary\"></i>\n                  Identificar Sócios e Responsáveis\n                </h5>\n                <p class=\"card-text text-muted\">\n                  Descubra quem são os sócios e decisores da empresa para melhor qualificar o lead\n                </p>\n\n                <!-- Botão Buscar Sócios -->\n                <div class=\"text-center\" *ngIf=\"!sociosBuscados || carregandoSocios\">\n                  <button\n                    type=\"button\"\n                    class=\"btn btn-primary\"\n                    (click)=\"buscarDetalhesSocios()\"\n                    [disabled]=\"carregandoSocios || !lead.cnpj\">\n                    <i class=\"fas fa-search\" *ngIf=\"!carregandoSocios\"></i>\n                    <i class=\"fas fa-spinner fa-spin\" *ngIf=\"carregandoSocios\"></i>\n                    <span *ngIf=\"!carregandoSocios\">Buscar Sócios</span>\n                    <span *ngIf=\"carregandoSocios\">Buscando...</span>\n                  </button>\n                  <div class=\"mt-3\">\n                    <small class=\"text-muted\">\n                      A busca irá procurar informações públicas sobre os sócios da empresa\n                    </small>\n                  </div>\n                </div>\n\n                <!-- Botão para buscar novamente (após primeira busca) -->\n                <div class=\"text-center mt-3\" *ngIf=\"sociosBuscados && sociosDetalhados.length > 0 && !carregandoSocios\">\n                  <button\n                    type=\"button\"\n                    class=\"btn btn-outline-primary btn-sm\"\n                    (click)=\"buscarDetalhesSocios()\"\n                    [disabled]=\"carregandoSocios\">\n                    <i class=\"fas fa-redo\"></i>\n                    Buscar Novamente\n                  </button>\n                </div>\n\n                <!-- Aviso se etapa é opcional -->\n                <div class=\"alert alert-info mt-3\" *ngIf=\"!sociosBuscados\">\n                  <i class=\"fas fa-info-circle\"></i>\n                  <strong>Etapa Opcional:</strong> Você pode pular esta etapa se não precisar dos dados dos sócios.\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Sócios Encontrados -->\n        <div class=\"row\" *ngIf=\"sociosDetalhados && sociosDetalhados.length > 0\">\n          <div class=\"col-12\">\n            <div class=\"card results-card\">\n              <div class=\"card-body\">\n                <h6 class=\"card-title\">\n                  <i class=\"fas fa-check text-success\"></i>\n                  Sócios Encontrados ({{sociosDetalhados.length}})\n                  <span *ngIf=\"getSocioPrincipal()\" class=\"badge badge-info ml-2\">\n                    <i class=\"fas fa-robot\"></i> Seleção Automática\n                  </span>\n                </h6>\n\n                <!-- Aviso sobre seleção automática -->\n                <div class=\"alert alert-info\" *ngIf=\"getSocioPrincipal()\">\n                  <i class=\"fas fa-magic\"></i>\n                  <strong>Seleção Inteligente:</strong>\n                  Identificamos automaticamente <strong>{{getSocioPrincipal().nome}}</strong> como sócio principal.\n                  <br>\n                  <small>\n                    <strong>Motivo:</strong> {{formatarMotivoSelecao(getSocioPrincipal().motivoSelecao)}}\n                    <span class=\"badge ml-1\" [ngClass]=\"getScoreClasse(getSocioPrincipal().scoreAnalise)\">\n                      {{getSocioPrincipal().scoreAnalise}}/100\n                    </span>\n                  </small>\n                  <br>\n                  <small class=\"text-muted\">\n                    Você pode alterar esta seleção clicando em outro sócio abaixo.\n                  </small>\n                </div>\n\n                <div class=\"socios-grid\">\n                  <div\n                    *ngFor=\"let socio of sociosDetalhados; let i = index\"\n                    class=\"socio-card\"\n                    [class.principal]=\"socio.principal\"\n                    (click)=\"marcarComoPrincipal(socio)\"\n                    style=\"cursor: pointer\">\n                    <div class=\"socio-header\">\n                      <div class=\"socio-number\">#{{i + 1}}</div>\n                      <div class=\"socio-nome\">\n                        <i class=\"fas fa-user-tie\"></i>\n                        {{socio.nome}}\n                        <!-- Badge de sócio principal -->\n                        <span *ngIf=\"socio.principal\" class=\"badge badge-warning ml-1\">\n                          <i class=\"fas fa-star\"></i> Principal\n                        </span>\n                        <!-- Score da análise -->\n                        <span *ngIf=\"socio.scoreAnalise\"\n                              class=\"badge ml-1\"\n                              [ngClass]=\"getScoreClasse(socio.scoreAnalise)\"\n                              [title]=\"'Score: ' + socio.scoreAnalise + '/100 - ' + formatarMotivoSelecao(socio.motivoSelecao)\">\n                          {{socio.scoreAnalise}}\n                        </span>\n                      </div>\n                      <div class=\"socio-actions\">\n                        <button\n                          type=\"button\"\n                          class=\"btn btn-sm\"\n                          [class.btn-warning]=\"socio.principal\"\n                          [class.btn-outline-warning]=\"!socio.principal\"\n                          (click)=\"marcarComoPrincipal(socio)\"\n                          title=\"Marcar como contato principal\">\n                          <i class=\"fas fa-star\"></i>\n                        </button>\n                      </div>\n                    </div>\n\n                    <div class=\"socio-info\">\n                      <!-- CPF -->\n                      <div class=\"info-item\" *ngIf=\"socio.cpf\">\n                        <i class=\"fas fa-id-badge\"></i>\n                        <span>CPF: {{socio.cpf}}</span>\n                      </div>\n\n                      <!-- Participação -->\n                      <div class=\"info-item\" *ngIf=\"socio.participacao\">\n                        <i class=\"fas fa-percentage\"></i>\n                        <span>Participação: {{socio.participacao}}</span>\n                      </div>\n\n                      <!-- Cargo -->\n                      <div class=\"info-item\" *ngIf=\"socio.cargo\">\n                        <i class=\"fas fa-briefcase\"></i>\n                        <span>{{socio.cargo}}</span>\n                      </div>\n\n                      <!-- Data de Entrada -->\n                      <div class=\"info-item\" *ngIf=\"socio.dataEntrada\">\n                        <i class=\"fas fa-calendar-alt\"></i>\n                        <span>Desde: {{socio.dataEntrada}}</span>\n                      </div>\n\n                      <!-- Qualificação -->\n                      <div class=\"info-item\" *ngIf=\"socio.qualificacao\">\n                        <i class=\"fas fa-award\"></i>\n                        <span>{{socio.qualificacao}}</span>\n                      </div>\n                    </div>\n\n                    <!-- Observações do Sócio -->\n                    <div class=\"socio-observacoes\">\n                      <label>Observações sobre {{socio.nome}}:</label>\n                      <textarea\n                        class=\"form-control form-control-sm\"\n                        [(ngModel)]=\"socio.observacoes\"\n                        [name]=\"'obs_socio_' + i\"\n                        rows=\"2\"\n                        placeholder=\"Ex: Responsável pelas decisões de marketing...\"></textarea>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Campo Responsável -->\n                <div class=\"mt-3\">\n                  <label for=\"responsavelLead\">\n                    <i class=\"fas fa-user-check text-primary\"></i>\n                    Responsável pelo Lead:\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"responsavelLead\"\n                    class=\"form-control\"\n                    [(ngModel)]=\"lead.nomeResponsavel\"\n                    name=\"responsavelLead\"\n                    placeholder=\"Nome do responsável principal da empresa\"\n                    title=\"Este será o contato principal do lead\">\n                  <small class=\"form-text text-muted\">\n                    <i class=\"fas fa-info-circle\"></i>\n                    Marque um sócio como principal acima ou digite o nome manualmente\n                  </small>\n                </div>\n\n                <!-- Observações Gerais sobre os Sócios -->\n                <div class=\"mt-3\">\n                  <label for=\"observacoesSocios\">Observações Gerais sobre os Sócios:</label>\n                  <textarea\n                    id=\"observacoesSocios\"\n                    class=\"form-control\"\n                    [(ngModel)]=\"lead.observacoesSocios\"\n                    name=\"observacoesSocios\"\n                    rows=\"3\"\n                    placeholder=\"Informações adicionais sobre a estrutura societária ou decisores...\"></textarea>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Mensagem quando não encontrar sócios -->\n        <div class=\"row\" *ngIf=\"sociosBuscados && sociosDetalhados.length === 0 && !carregandoSocios\">\n          <div class=\"col-12\">\n            <div class=\"card\">\n              <div class=\"card-body text-center\">\n                <i class=\"fas fa-user-slash fa-3x text-muted mb-3\"></i>\n                <h6>Nenhum sócio encontrado</h6>\n                <p class=\"text-muted\">Não foi possível encontrar informações públicas sobre os sócios desta empresa.</p>\n\n                <div class=\"d-flex justify-content-center mb-4\">\n                  <button\n                    type=\"button\"\n                    class=\"btn btn-outline-primary mx-2\"\n                    (click)=\"buscarDetalhesSocios()\">\n                    <i class=\"fas fa-redo\"></i>\n                    Tentar Novamente\n                  </button>\n                  <button\n                    type=\"button\"\n                    class=\"btn btn-outline-info mx-2\"\n                    (click)=\"abrirConsultaCNPJReceita()\"\n                    title=\"Consultar CNPJ no site da Receita Federal\">\n                    <i class=\"fas fa-external-link-alt\"></i>\n                    Consultar na Receita Federal\n                  </button>\n                </div>\n\n                <div class=\"divider-or\">\n                  <span>OU</span>\n                </div>\n\n                <div class=\"manual-socio-input mt-3\">\n                  <p class=\"text-muted mb-2\">Informe o nome do sócio manualmente:</p>\n                  <div class=\"input-group mx-auto\" style=\"max-width: 400px;\">\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      [(ngModel)]=\"nomeSocioManual\"\n                      name=\"nomeSocioManual\"\n                      placeholder=\"Nome do sócio principal\"\n                      (keyup.enter)=\"adicionarSocioManual()\">\n                    <button\n                      type=\"button\"\n                      class=\"btn btn-success\"\n                      (click)=\"adicionarSocioManual()\"\n                      [disabled]=\"!nomeSocioManual?.trim()\">\n                      <i class=\"fas fa-plus\"></i>\n                      Adicionar\n                    </button>\n                  </div>\n                  <small class=\"form-text text-muted mt-1\">\n                    <i class=\"fas fa-info-circle\"></i>\n                    Digite o nome do responsável principal da empresa\n                  </small>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- ===== PASSO 5: FINALIZAR ===== -->\n      <div *ngIf=\"currentStep === 5\" class=\"step-content step-5\">\n\n\n        <!-- Resumo Completo -->\n        <div class=\"row\">\n          <div class=\"col-md-8\">\n            <div class=\"card summary-card\">\n              <div class=\"card-body\">\n                <h6 class=\"card-title\">\n                  <i class=\"fas fa-clipboard-list\"></i>\n                  Resumo do Lead\n                </h6>\n\n                <!-- Dados Básicos -->\n                <div class=\"summary-section\">\n                  <h6><i class=\"fas fa-user\"></i> Dados Básicos</h6>\n                  <div class=\"summary-grid\">\n                    <div><strong>Responsável:</strong> {{lead.nomeResponsavel}}</div>\n                    <div><strong>Empresa:</strong> {{lead.empresa}}</div>\n                    <div><strong>Cidade:</strong> {{lead.cidade}}</div>\n                    <div><strong>Telefone:</strong> {{lead.telefone || 'Não informado'}}</div>\n                    <div><strong>Instagram:</strong> @{{lead.instagramHandle}}</div>\n                    <div><strong>Website:</strong> {{lead.website || 'Não informado'}}</div>\n                  </div>\n                </div>\n\n                <!-- Links Encontrados -->\n                <div class=\"summary-section\" *ngIf=\"linksEncontrados.length > 0\">\n                  <h6><i class=\"fas fa-link\"></i> Links Encontrados ({{linksEncontrados.length}})</h6>\n                  <div class=\"links-summary-grid\">\n                    <div\n                      *ngFor=\"let link of linksEncontrados\"\n                      class=\"link-summary-item\"\n                      [class.link-whatsapp]=\"link.tipo === 'WhatsApp'\"\n                      [class.link-instagram]=\"link.tipo === 'Instagram'\"\n                      [class.link-ifood]=\"link.tipo === 'Ifood'\"\n                      [class.link-cardapio]=\"link.tipo === 'Site do Cardápio'\"\n                      [class.link-concorrente]=\"link.tipo === 'Concorrente'\"\n                      [class.link-localizacao]=\"link.tipo === 'Localização'\"\n                      [class.link-site]=\"link.tipo === 'Site'\">\n                      <div class=\"link-icon\">\n                        <i class=\"fab fa-whatsapp\" *ngIf=\"link.tipo === 'WhatsApp'\"></i>\n                        <i class=\"fab fa-instagram\" *ngIf=\"link.tipo === 'Instagram'\"></i>\n                        <i class=\"fas fa-utensils\" *ngIf=\"link.tipo === 'Ifood'\"></i>\n                        <i class=\"fas fa-list-alt\" *ngIf=\"link.tipo === 'Site do Cardápio'\"></i>\n                        <i class=\"fas fa-exclamation-triangle\" *ngIf=\"link.tipo === 'Concorrente'\"></i>\n                        <i class=\"fas fa-map-marker-alt\" *ngIf=\"link.tipo === 'Localização'\"></i>\n                        <i class=\"fas fa-globe\" *ngIf=\"link.tipo === 'Site'\"></i>\n                        <i class=\"fas fa-link\" *ngIf=\"!['WhatsApp', 'Instagram', 'Ifood', 'Site do Cardápio', 'Concorrente', 'Localização', 'Site'].includes(link.tipo)\"></i>\n                      </div>\n                      <div class=\"link-content\">\n                        <div class=\"link-tipo\">{{link.tipo}}</div>\n                        <div class=\"link-descricao\" *ngIf=\"link.descricao\">{{link.descricao}}</div>\n                        <div class=\"link-url\">{{link.url | slice:0:35}}{{link.url.length > 35 ? '...' : ''}}</div>\n                      </div>\n                      <div class=\"link-actions\">\n                        <button\n                          type=\"button\"\n                          class=\"btn btn-sm btn-outline-primary\"\n                          (click)=\"abrirLink(link.url)\"\n                          title=\"Abrir link\">\n                          <i class=\"fas fa-external-link-alt\"></i>\n                        </button>\n                        <button\n                          type=\"button\"\n                          class=\"btn btn-sm btn-outline-secondary\"\n                          (click)=\"copiarLink(link.url)\"\n                          title=\"Copiar link\">\n                          <i class=\"fas fa-copy\"></i>\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Sócios Encontrados -->\n                <div class=\"summary-section\" *ngIf=\"sociosDetalhados && sociosDetalhados.length > 0\">\n                  <h6><i class=\"fas fa-users\"></i> Sócios Encontrados ({{sociosDetalhados.length}})</h6>\n                  <div class=\"socios-summary-grid\">\n                    <div *ngFor=\"let socio of sociosDetalhados\" class=\"socio-summary-item\">\n                      <div class=\"socio-info-summary\">\n                        <strong>{{socio.nome}}</strong>\n                        <span *ngIf=\"socio.principal\" class=\"badge badge-warning ml-2\">Principal</span>\n                        <div class=\"socio-details\">\n                          <span *ngIf=\"socio.cpf\">CPF: {{socio.cpf}}</span>\n                          <span *ngIf=\"socio.cargo\"> • {{socio.cargo}}</span>\n                          <span *ngIf=\"socio.dataEntrada\"> • Desde: {{socio.dataEntrada}}</span>\n                          <span *ngIf=\"socio.faixaEtaria\"> • {{socio.faixaEtaria}}</span>\n                        </div>\n                        <div *ngIf=\"socio.observacoes\" class=\"socio-obs\">\n                          <small class=\"text-muted\">{{socio.observacoes}}</small>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div *ngIf=\"lead.observacoesSocios\" class=\"mt-3\">\n                    <strong>Observações Gerais:</strong>\n                    <p class=\"text-muted mb-0\">{{lead.observacoesSocios}}</p>\n                  </div>\n                </div>\n\n                <!-- CNPJ Selecionado -->\n                <div class=\"summary-section\" *ngIf=\"lead.cnpj\">\n                  <h6><i class=\"fas fa-id-card\"></i> CNPJ Selecionado</h6>\n                  <div class=\"cnpj-summary\">\n                    <div><strong>CNPJ:</strong> {{lead.cnpj}}</div>\n                    <div><strong>Empresa:</strong> {{lead.empresa}}</div>\n                    <div *ngIf=\"lead.endereco\"><strong>Endereço:</strong> {{lead.endereco}}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Configurações Finais -->\n          <div class=\"col-md-4\">\n            <div class=\"card form-card\">\n              <div class=\"card-body\">\n                <h6 class=\"card-title\">\n                  <i class=\"fas fa-cog\"></i>\n                  Configurações do Lead\n                </h6>\n\n                <!-- Responsável pelo Lead -->\n                <div class=\"form-group\">\n                  <label for=\"nomeResponsavelFinal\">\n                    <i class=\"fas fa-user-check text-primary\"></i>\n                    Responsável\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"nomeResponsavelFinal\"\n                    class=\"form-control\"\n                    [(ngModel)]=\"lead.nomeResponsavel\"\n                    name=\"nomeResponsavelFinal\"\n                    placeholder=\"Nome do responsável\">\n                  <small class=\"form-text text-muted\">\n                    Contato principal da empresa\n                  </small>\n                </div>\n\n                <!-- Etapa do Funil -->\n                <div class=\"form-group\">\n                  <label for=\"etapa\">Etapa do Funil</label>\n                  <select\n                    id=\"etapa\"\n                    class=\"form-control\"\n                    [(ngModel)]=\"lead.etapa\"\n                    name=\"etapa\">\n                    <option *ngFor=\"let etapa of etapas\" [value]=\"etapa.valor\">\n                      {{etapa.texto}}\n                    </option>\n                  </select>\n                </div>\n\n                <!-- Origem -->\n                <div class=\"form-group\">\n                  <label for=\"origem\">Origem do Lead</label>\n                  <select\n                    id=\"origem\"\n                    class=\"form-control\"\n                    [(ngModel)]=\"lead.origem\"\n                    name=\"origem\">\n                    <option *ngFor=\"let origem of origens\" [value]=\"origem.valor\">\n                      {{origem.texto}}\n                    </option>\n                  </select>\n                </div>\n\n                <!-- Segmento -->\n                <div class=\"form-group\">\n                  <label for=\"segmento\">Segmento</label>\n                  <select\n                    id=\"segmento\"\n                    class=\"form-control\"\n                    [(ngModel)]=\"lead.segmento\"\n                    name=\"segmento\">\n                    <option *ngFor=\"let segmento of segmentos\" [value]=\"segmento.valor\">\n                      {{segmento.texto}}\n                    </option>\n                  </select>\n                </div>\n\n                <!-- Observações -->\n                <div class=\"form-group\">\n                  <label for=\"observacoes\">Observações</label>\n                  <textarea\n                    id=\"observacoes\"\n                    class=\"form-control\"\n                    [(ngModel)]=\"lead.observacoes\"\n                    name=\"observacoes\"\n                    rows=\"3\"\n                    placeholder=\"Observações sobre o lead...\"></textarea>\n                </div>\n\n                <!-- Sincronizar com Bitrix -->\n                <div class=\"form-check\">\n                  <input\n                    type=\"checkbox\"\n                    class=\"form-check-input\"\n                    id=\"sincronizarBitrix\"\n                    [(ngModel)]=\"sincronizarBitrix\"\n                    name=\"sincronizarBitrix\">\n                  <label class=\"form-check-label\" for=\"sincronizarBitrix\">\n                    Sincronizar com Bitrix24\n                  </label>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n    </div>\n  </div>\n\n  <!-- Footer de Navegação -->\n  <div class=\"wizard-footer\">\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <div class=\"col-12\">\n          <div class=\"navigation-buttons\">\n            <!-- Botão Voltar -->\n            <button\n              type=\"button\"\n              class=\"btn btn-outline-secondary\"\n              (click)=\"prevStep()\"\n              *ngIf=\"currentStep > 1\">\n              <i class=\"fas fa-arrow-left\"></i>\n              Voltar\n            </button>\n\n            <!-- Espaçador -->\n            <div class=\"flex-grow-1\"></div>\n\n            <!-- Mensagem de erro -->\n            <div class=\"alert alert-danger mb-0 mr-3\" *ngIf=\"erro\">\n              <i class=\"fas fa-exclamation-triangle\"></i>\n              {{erro}}\n            </div>\n\n            <!-- Botão Pular (apenas passos 2 e 3) -->\n            <button\n              type=\"button\"\n              class=\"btn btn-outline-warning mr-3\"\n              (click)=\"skipStep()\"\n              *ngIf=\"canSkipCurrentStep()\">\n              <i class=\"fas fa-forward\"></i>\n              Pular Etapa\n            </button>\n\n            <!-- Botão Próximo -->\n            <button\n              type=\"button\"\n              class=\"btn btn-primary\"\n              (click)=\"nextStep()\"\n              *ngIf=\"currentStep < totalSteps\">\n              Próximo\n              <i class=\"fas fa-arrow-right\"></i>\n            </button>\n\n            <!-- Botão Salvar Lead -->\n            <button\n              type=\"button\"\n              class=\"btn btn-success\"\n              (click)=\"salvarLead()\"\n              [disabled]=\"carregando\"\n              *ngIf=\"currentStep === totalSteps\">\n              <i class=\"fas fa-save\" *ngIf=\"!carregando\"></i>\n              <i class=\"fas fa-spinner fa-spin\" *ngIf=\"carregando\"></i>\n              {{carregando ? 'Salvando...' : 'Salvar Lead'}}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\n\n// componentes\nimport { CrmHomeComponent } from './crm-home/crm-home.component';\nimport { TelaCrmLeadsComponent } from '../tela-crm-leads/tela-crm-leads.component';\nimport { LeadCrudComponent } from './lead-crud/lead-crud.component';\nimport { CrmEmpresaCrudComponent } from './crm-empresa-crud/crm-empresa-crud.component';\nimport { NovoLeadComponent } from './novo-lead/novo-lead.component';\n\nconst routes: Routes = [\n  { path: 'home', component: CrmHomeComponent },\n  { path: 'home/:username', component: CrmHomeComponent },\n  { path: 'novo-lead', component: NovoLeadComponent },\n  { path: 'index', component: LeadCrudComponent },\n  { path: 'empresas', component: CrmEmpresaCrudComponent },\n  { path: 'leads', component: TelaCrmLeadsComponent },\n  { path: '', redirectTo: 'leads', pathMatch: 'full' }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class CrmRoutingModule { }\n", "import { Component, OnInit, ChangeDetectorRef } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { InstagramDataService } from '../services/instagram-data.service';\nimport { LeadService } from '../services/lead.service';\nimport { CrmEmpresaService } from '../services/crm-empresa.service';\n// import Lead, { ConcorrenteLead } from '../../../server/domain/crm/Lead'; // Removido - frontend não deve referenciar objetos de domínio\n\n@Component({\n  selector: 'app-novo-lead',\n  templateUrl: './novo-lead.component.html',\n  styleUrls: ['./novo-lead.component.scss'],\n  animations: [\n    trigger('slideDown', [\n      state('closed', style({\n        height: '0px',\n        opacity: 0,\n        overflow: 'hidden'\n      })),\n      state('open', style({\n        height: '*',\n        opacity: 1,\n        overflow: 'visible'\n      })),\n      transition('closed => open', [\n        animate('300ms ease-in-out')\n      ]),\n      transition('open => closed', [\n        animate('300ms ease-in-out')\n      ])\n    ])\n  ]\n})\nexport class NovoLeadComponent implements OnInit {\n\n  // Dados do Instagram\n  dadosInstagram: any = null;\n  username: string = '';\n\n  // Lead processado pela API dadosig2\n  leadProcessadoAPI: any = null;\n\n  // Estado do componente\n  carregando = false;\n  carregandoWebsite = false;\n  carregandoCnpj = false;\n  erro = '';\n  mostrarFormulario = false;\n  sincronizarBitrix = false;\n\n\n  // Links encontrados do website\n  linksEncontrados: any[] = [];\n\n  // Telefones múltiplos\n  telefonesEncontrados: any[] = [];\n\n  // CNPJs encontrados\n  cnpjsEncontrados: any[] = [];\n\n  // Sócios detalhados\n  sociosDetalhados: any[] = [];\n  carregandoSocios = false;\n  sociosBuscados = false;\n\n  // CNPJ manual\n  cnpjManual = '';\n  carregandoCnpjManual = false;\n\n  // Wizard state\n  currentStep = 1;\n  totalSteps = 5;\n  wizardData = {\n    dadosBasicos: {},\n    linksEncontrados: [],\n    cnpjSelecionado: null,\n    sociosEncontrados: [],\n    configuracoes: {}\n  };\n\n  // Controle de validação das etapas\n  websiteAnalisado = false;\n  cnpjBuscado = false;\n  etapaFoiPulada = false;\n  cnpjRecusadoExplicitamente = false; // Usuário confirmou que não quer escolher nenhum CNPJ\n  parceirSelecionado = false; // Usuário selecionou um sócio como contato principal\n\n  // Dados do formulário\n  lead: any = {\n    nomeResponsavel: '',\n    empresa: '',\n    cidade: '',\n    endereco: '',\n    cnpj: '',\n    telefone: '',\n    instagramHandle: '',\n    website: '',\n    linkCardapio: '',\n    bioInsta: '',\n    biografia: '',\n    observacoes: '',\n    observacoesSocios: '',\n    origem: 'Instagram',\n    etapa: 'Prospecção',\n    segmento: 'Alimentação',\n    concorrente: '',\n    crmEmpresaId: null\n  };\n\n  // Lista de empresas CRM para seleção\n  crmEmpresas: any[] = [];\n\n  // Campo formatado do telefone (para exibição)\n  telefoneFormatado: string = '';\n\n  // Razão social do CNPJ selecionado (para exibição no resumo)\n  razaoSocialSelecionada: string = '';\n\n  // Nome do sócio manual\n  nomeSocioManual: string = '';\n\n  // Propriedade computed para busca no Google\n  get searchTerm(): string {\n    const empresa = this.lead?.empresa?.trim() || '';\n    const cidade = this.lead?.cidade?.trim() || '';\n\n    if (empresa && cidade) {\n      return `${empresa} - ${cidade}`;\n    } else if (empresa) {\n      return empresa;\n    } else {\n      return '';\n    }\n  }\n\n  set searchTerm(value: string) {\n    if (!value) {\n      this.lead.empresa = '';\n      this.lead.cidade = '';\n      return;\n    }\n\n    // Se contém \" - \", divide em empresa e cidade\n    if (value.includes(' - ')) {\n      const parts = value.split(' - ');\n      this.lead.empresa = parts[0].trim();\n      this.lead.cidade = parts.slice(1).join(' - ').trim(); // Em caso de múltiplos \" - \"\n    } else {\n      // Se não contém \" - \", considera tudo como empresa\n      this.lead.empresa = value.trim();\n      // Mantém a cidade atual se já existir\n    }\n  }\n\n  // Opções para dropdowns\n  etapas = [\n    { valor: 'Prospecção', texto: 'Prospecção' },\n    { valor: 'Qualificação', texto: 'Qualificação' },\n    { valor: 'Objeção', texto: 'Objeção' },\n    { valor: 'Fechamento', texto: 'Fechamento' },\n    { valor: 'Ganho', texto: 'Ganho' },\n    { valor: 'Perdido', texto: 'Perdido' }\n  ];\n\n  origens = [\n    { valor: 'Instagram', texto: 'Instagram' },\n    { valor: 'Site/Landing Page', texto: 'Site/Landing Page' },\n    { valor: 'WhatsApp Direto', texto: 'WhatsApp Direto' },\n    { valor: 'Indicação', texto: 'Indicação' },\n    { valor: 'Evento/Feira', texto: 'Evento/Feira' },\n    { valor: 'Outros', texto: 'Outros' }\n  ];\n\n  segmentos = [\n    { valor: 'Alimentação', texto: 'Alimentação' },\n    { valor: 'Varejo', texto: 'Varejo' },\n    { valor: 'Serviços', texto: 'Serviços' },\n    { valor: 'Saúde', texto: 'Saúde' },\n    { valor: 'Educação', texto: 'Educação' },\n    { valor: 'Outros', texto: 'Outros' }\n  ];\n\n  tiposTelefone = [\n    { valor: 'WhatsApp', texto: 'WhatsApp', icone: 'fa-whatsapp', cor: '#25d366' },\n    { valor: 'Telefone Fixo', texto: 'Telefone Fixo', icone: 'fa-phone', cor: '#6c757d' },\n    { valor: 'Celular', texto: 'Celular', icone: 'fa-mobile-alt', cor: '#007bff' },\n    { valor: 'Comercial', texto: 'Comercial', icone: 'fa-briefcase', cor: '#28a745' },\n    { valor: 'Emergência', texto: 'Emergência', icone: 'fa-exclamation-triangle', cor: '#dc3545' }\n  ];\n\n  concorrentes = [\n    { valor: 'Não descobri', texto: 'Não descobri' },\n    { valor: 'Não tem sistema', texto: 'Não tem sistema' },\n    { valor: 'Accon', texto: 'Accon' },\n    { valor: 'Amo Delivery', texto: 'Amo Delivery' },\n    { valor: 'Anota Ai', texto: 'Anota Ai' },\n    { valor: 'App Para Delivery', texto: 'App Para Delivery' },\n    { valor: 'Beetech', texto: 'Beetech' },\n    { valor: 'Bigdim', texto: 'Bigdim' },\n    { valor: 'By App Food', texto: 'By App Food' },\n    { valor: 'By Food', texto: 'By Food' },\n    { valor: 'Cardapio.co', texto: 'Cardapio.co' },\n    { valor: 'Cardápio Fácil', texto: 'Cardápio Fácil' },\n    { valor: 'Cardápio Pronto', texto: 'Cardápio Pronto' },\n    { valor: 'Cardapioweb', texto: 'Cardapioweb' },\n    { valor: 'Cardapius', texto: 'Cardapius' },\n    { valor: 'CCMPedidoOnline', texto: 'CCMPedidoOnline' },\n    { valor: 'Cinndi', texto: 'Cinndi' },\n    { valor: 'Delivery Direto', texto: 'Delivery Direto' },\n    { valor: 'Delivery Seguro', texto: 'Delivery Seguro' },\n    { valor: 'Delyver', texto: 'Delyver' },\n    { valor: 'Ecta', texto: 'Ecta' },\n    { valor: 'Eita.delivery', texto: 'Eita.delivery' },\n    { valor: 'Expresso Delivery', texto: 'Expresso Delivery' },\n    { valor: 'Expresso Menu', texto: 'Expresso Menu' },\n    { valor: 'Glow Delivery', texto: 'Glow Delivery' },\n    { valor: 'Go2Go Solutions', texto: 'Go2Go Solutions' },\n    { valor: 'GoEntrega', texto: 'GoEntrega' },\n    { valor: 'Goomer Gratuito', texto: 'Goomer Gratuito' },\n    { valor: 'Goomer Pago', texto: 'Goomer Pago' },\n    { valor: 'Grand Chef', texto: 'Grand Chef' },\n    { valor: 'Instabuy', texto: 'Instabuy' },\n    { valor: 'Instadelivery', texto: 'Instadelivery' },\n    { valor: 'Jotaja', texto: 'Jotaja' },\n    { valor: 'Kyte', texto: 'Kyte' },\n    { valor: 'Kuppi', texto: 'Kuppi' },\n    { valor: 'LadDelivery', texto: 'LadDelivery' },\n    { valor: 'Magnata App', texto: 'Magnata App' },\n    { valor: 'MenuIntegrado', texto: 'MenuIntegrado' },\n    { valor: 'Menuvem', texto: 'Menuvem' },\n    { valor: 'Meu Cardápio Digital', texto: 'Meu Cardápio Digital' },\n    { valor: 'MeuPedido', texto: 'MeuPedido' },\n    { valor: 'Menap', texto: 'Menap' },\n    { valor: 'Menu Dino', texto: 'Menu Dino' },\n    { valor: 'Menu Ifood', texto: 'Menu Ifood' },\n    { valor: 'Miller Delivery', texto: 'Miller Delivery' },\n    { valor: 'Neemo', texto: 'Neemo' },\n    { valor: 'Ola Click', texto: 'Ola Click' },\n    { valor: 'Pedefacil UOL', texto: 'Pedefacil UOL' },\n    { valor: 'Pediaki', texto: 'Pediaki' },\n    { valor: 'Pedir Delivery', texto: 'Pedir Delivery' },\n    { valor: 'Pedir Online', texto: 'Pedir Online' },\n    { valor: 'Pedyun', texto: 'Pedyun' },\n    { valor: 'PedZap', texto: 'PedZap' },\n    { valor: 'PodePedir', texto: 'PodePedir' },\n    { valor: 'PopSales', texto: 'PopSales' },\n    { valor: 'Prefiro Delivery', texto: 'Prefiro Delivery' },\n    { valor: 'Rvpedidos', texto: 'Rvpedidos' },\n    { valor: 'Saipos Cardápio', texto: 'Saipos Cardápio' },\n    { valor: 'StayApp', texto: 'StayApp' },\n    { valor: 'StarFood', texto: 'StarFood' },\n    { valor: 'Vtto', texto: 'Vtto' },\n    { valor: 'Zappedis', texto: 'Zappedis' },\n    { valor: 'Wabiz', texto: 'Wabiz' },\n    { valor: 'Webcardapio', texto: 'Webcardapio' },\n    { valor: 'Whats Menu', texto: 'Whats Menu' },\n    { valor: 'Alloy', texto: 'Alloy' },\n    { valor: 'Cardápio Digital Totvs', texto: 'Cardápio Digital Totvs' },\n    { valor: 'Pedir Agora', texto: 'Pedir Agora' },\n    { valor: 'StiloWeb Delivery', texto: 'StiloWeb Delivery' },\n    { valor: 'Tuigo Eats', texto: 'Tuigo Eats' },\n    { valor: 'Hubt', texto: 'Hubt' }\n  ];\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private instagramDataService: InstagramDataService,\n    private leadService: LeadService,\n    private crmEmpresaService: CrmEmpresaService,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  ngOnInit(): void {\n    // DEBUG: Log initial state\n    console.log('🐛 [ngOnInit] Starting - lead.empresa:', this.lead.empresa, 'Type:', typeof this.lead.empresa);\n\n    // Pega o username da URL\n    this.route.queryParams.subscribe(params => {\n      this.username = params['username'] || '';\n      console.log('Username da URL:', this.username);\n    });\n\n    // Recupera dados do Instagram do service\n    const dadosService = this.instagramDataService.getDados();\n    if (dadosService) {\n      this.dadosInstagram = dadosService.dados;\n      this.username = dadosService.username;\n      this.preencherFormularioComDadosInstagram();\n    } else {\n      console.warn('Nenhum dado do Instagram encontrado no service');\n\n      // Se tem username, configura listener mas NÃO busca automaticamente\n      if (this.username) {\n        console.log('Username presente, aguardando ação do usuário para buscar dados');\n        this.setupInstagramDataListener();\n      } else {\n        // Se não tem username, redireciona para home\n        this.router.navigate(['/crm/home']);\n      }\n    }\n\n    // Carrega lista de empresas CRM\n    this.carregarCrmEmpresas();\n\n    // Inicializa campo de telefone formatado se existir\n    if (this.lead.telefone) {\n      this.telefoneFormatado = this.formatarTelefone(this.lead.telefone);\n    }\n\n    // DEBUG: Log final state\n    console.log('🐛 [ngOnInit] Finished - lead.empresa:', this.lead.empresa, 'Type:', typeof this.lead.empresa);\n  }\n\n  /**\n   * Preenche o formulário com dados do Instagram\n   */\n  private preencherFormularioComDadosInstagram(): void {\n    if (!this.dadosInstagram || !this.dadosInstagram.user) {\n      console.warn('Dados do Instagram não encontrados ou estrutura incorreta:', this.dadosInstagram);\n      return;\n    }\n\n    const user = this.dadosInstagram.user;\n\n    this.lead.nomeResponsavel = user.full_name || user.username || '';\n    this.lead.empresa = user.full_name || user.username || '';\n    this.lead.instagramHandle = user.username || '';\n    this.lead.website = user.external_url || '';\n    this.lead.bioInsta = user.biography || '';\n    this.lead.biografia = user.biography || '';\n    this.lead.telefone = user.business_phone_number || '';\n    \n    // Atualiza telefone formatado\n    if (this.lead.telefone) {\n      this.telefoneFormatado = this.formatarTelefone(this.lead.telefone);\n    }\n\n    console.log('Formulário preenchido com dados do Instagram:', this.lead);\n\n    // DEBUG: Log empresa change\n    console.log('🐛 [preencherFormularioComDadosInstagram] lead.empresa set to:', this.lead.empresa, 'Type:', typeof this.lead.empresa);\n  }\n\n  /**\n   * Preenche o formulário com dados do lead processado pela API\n   * Agora recebe um objeto Lead completo com CrmEmpresa\n   */\n  private preencherFormularioComLeadProcessado(leadProcessado: any): void {\n    if (!leadProcessado) {\n      console.warn('Lead processado não encontrado:', leadProcessado);\n      return;\n    }\n\n    console.log('Preenchendo formulário com lead processado (objeto Lead completo):', leadProcessado);\n\n    // Armazena o lead processado para uso posterior\n    this.leadProcessadoAPI = leadProcessado;\n\n    // Preenche campos do formulário com dados do Lead\n    this.lead.nomeResponsavel = leadProcessado.nomeResponsavel || '';\n    this.lead.empresa = leadProcessado.empresa || '';\n    this.lead.cidade = leadProcessado.cidade || '';\n    this.lead.endereco = leadProcessado.endereco || '';\n    this.lead.telefone = leadProcessado.telefone || '';\n    \n    // Atualiza telefone formatado\n    if (this.lead.telefone) {\n      this.telefoneFormatado = this.formatarTelefone(this.lead.telefone);\n    }\n\n    // DEBUG: Log empresa change\n    console.log('🐛 [preencherFormularioComLeadProcessado] lead.empresa set to:', this.lead.empresa, 'Type:', typeof this.lead.empresa);\n\n    // Log de endereço extraído\n    if (leadProcessado.endereco) {\n      console.log('Endereço extraído e preenchido no formulário:', leadProcessado.endereco);\n    }\n    this.lead.instagramHandle = leadProcessado.instagramHandle || '';\n    this.lead.website = leadProcessado.website || (leadProcessado.instagramData?.website || '');\n    this.lead.bioInsta = leadProcessado.bioInsta || '';\n    this.lead.biografia = leadProcessado.bioInsta || '';\n    this.lead.origem = leadProcessado.origem || 'Instagram';\n    this.lead.etapa = leadProcessado.etapa || 'Prospecção';\n    this.lead.score = leadProcessado.score || 0;\n\n    // Mapear segmento baseado na categoria do negócio\n    if (leadProcessado.instagramData?.businessCategory) {\n      this.lead.segmento = this.mapearSegmento(leadProcessado.instagramData.businessCategory);\n    } else {\n      this.lead.segmento = 'Alimentação'; // padrão\n    }\n\n    // Define empresa CRM se disponível\n    if (leadProcessado.crmEmpresaId) {\n      this.lead.crmEmpresaId = leadProcessado.crmEmpresaId;\n    }\n\n    // Processa telefones múltiplos se disponíveis\n    if (leadProcessado.telefones && Array.isArray(leadProcessado.telefones)) {\n      this.telefonesEncontrados = leadProcessado.telefones.map((telefone: any, index: number) => ({\n        id: `temp_${index}`,\n        tipo: telefone.tipo,\n        numero: telefone.numero,\n        descricao: telefone.descricao || '',\n        numeroFormatado: this.formatarTelefone(telefone.numero),\n        icone: this.getIconeTelefone(telefone.tipo),\n        cor: this.getCorTelefone(telefone.tipo)\n      }));\n      console.log('Telefones processados no frontend:', this.telefonesEncontrados);\n    }\n\n    // Processa links múltiplos se disponíveis\n    if (leadProcessado.links && Array.isArray(leadProcessado.links)) {\n      this.linksEncontrados = leadProcessado.links.map((link: any, index: number) => ({\n        id: link.id || `temp_${index}`,\n        tipo: link.tipo,\n        url: link.url,\n        descricao: link.descricao || '',\n        ordem: link.ordem || index + 1,\n        ativo: link.ativo !== false\n      }));\n      console.log('Links processados no frontend:', this.linksEncontrados);\n      \n      // Atualizar wizard data para manter consistência\n      this.wizardData.linksEncontrados = [...this.linksEncontrados];\n      \n      // Marcar que já tem links analisados para pular análise automática\n      this.websiteAnalisado = true;\n      \n      console.log('Total de links carregados do backend:', this.linksEncontrados.length);\n    }\n\n    // Adiciona informações extras do Instagram nas notas se não estiverem presentes\n    if (leadProcessado.notas) {\n      this.lead.observacoes = leadProcessado.notas;\n    }\n\n    // Simula dados do Instagram para o template (se necessário)\n    if (leadProcessado.instagramData) {\n      this.dadosInstagram = {\n        user: {\n          username: leadProcessado.instagramHandle,\n          full_name: leadProcessado.empresa,\n          biography: leadProcessado.bioInsta,\n          business_phone_number: leadProcessado.telefone,\n          edge_followed_by: { count: leadProcessado.instagramData.followers },\n          edge_follow: { count: leadProcessado.instagramData.following },\n          is_business_account: leadProcessado.instagramData.accountType === 'Business',\n          business_category_name: leadProcessado.instagramData.businessCategory,\n          external_url: leadProcessado.instagramData.website,\n          profile_pic_url: leadProcessado.avatarUrl\n        }\n      };\n    }\n\n    console.log('Formulário preenchido com lead processado:', this.lead);\n    console.log('Dados do Instagram simulados para template:', this.dadosInstagram);\n  }\n\n  /**\n   * Mapeia categoria de negócio para segmento\n   */\n  private mapearSegmento(categoria: string): string {\n    if (!categoria) return 'Outros';\n\n    const categoriaLower = categoria.toLowerCase();\n\n    if (categoriaLower.includes('restaurante') ||\n        categoriaLower.includes('comida') ||\n        categoriaLower.includes('food') ||\n        categoriaLower.includes('pizza') ||\n        categoriaLower.includes('lanche') ||\n        categoriaLower.includes('café') ||\n        categoriaLower.includes('bar') ||\n        categoriaLower.includes('japonês') ||\n        categoriaLower.includes('delivery')) {\n      return 'Alimentação';\n    }\n\n    if (categoriaLower.includes('loja') ||\n        categoriaLower.includes('varejo') ||\n        categoriaLower.includes('shop')) {\n      return 'Varejo';\n    }\n\n    if (categoriaLower.includes('serviço') ||\n        categoriaLower.includes('service')) {\n      return 'Serviços';\n    }\n\n    if (categoriaLower.includes('saúde') ||\n        categoriaLower.includes('health') ||\n        categoriaLower.includes('médico') ||\n        categoriaLower.includes('clínica')) {\n      return 'Saúde';\n    }\n\n    if (categoriaLower.includes('educação') ||\n        categoriaLower.includes('education') ||\n        categoriaLower.includes('escola') ||\n        categoriaLower.includes('curso')) {\n      return 'Educação';\n    }\n\n    return 'Outros';\n  }\n\n  /**\n   * Carrega lista de empresas CRM\n   */\n  private async carregarCrmEmpresas(): Promise<void> {\n    try {\n      const response = await this.crmEmpresaService.liste();\n      if (response.sucesso) {\n        this.crmEmpresas = response.dados || [];\n      }\n    } catch (error) {\n      console.error('Erro ao carregar empresas CRM:', error);\n    }\n  }\n\n  /**\n   * Salva o novo lead\n   */\n  async salvarLead(): Promise<void> {\n    if (!this.validarFormulario()) {\n      return;\n    }\n\n    this.carregando = true;\n    this.erro = '';\n\n    // Garante que o telefone está limpo antes de salvar\n    if (this.telefoneFormatado) {\n      this.lead.telefone = this.limparTelefone(this.telefoneFormatado);\n    }\n\n    try {\n      // Se temos um lead processado pela API dadosig2, usa ele como base\n      let leadParaSalvar: any;\n\n      if (this.leadProcessadoAPI) {\n        // Usa o lead processado pela API como base e aplica modificações do formulário\n        leadParaSalvar = { ...this.leadProcessadoAPI };\n\n        // Atualiza com dados modificados no formulário\n        leadParaSalvar.nomeResponsavel = this.lead.nomeResponsavel;\n        leadParaSalvar.empresa = this.lead.empresa;\n        leadParaSalvar.cidade = this.lead.cidade;\n        leadParaSalvar.endereco = this.lead.endereco;\n        leadParaSalvar.telefone = this.lead.telefone;\n        leadParaSalvar.instagramHandle = this.lead.instagramHandle;\n        leadParaSalvar.website = this.lead.website;\n        leadParaSalvar.linkCardapio = this.lead.linkCardapio;\n        leadParaSalvar.bioInsta = this.lead.bioInsta;\n        leadParaSalvar.origem = this.lead.origem;\n        leadParaSalvar.etapa = this.lead.etapa;\n        leadParaSalvar.segmento = this.lead.segmento;\n        leadParaSalvar.crmEmpresaId = this.lead.crmEmpresaId;\n        leadParaSalvar.observacoes = this.lead.observacoes;\n        leadParaSalvar.observacoesSocios = this.lead.observacoesSocios;\n        leadParaSalvar.concorrente = this.lead.concorrente;\n\n        // Remove campos que não devem ser enviados na criação\n        delete leadParaSalvar.id;\n        delete leadParaSalvar.dataCriacao;\n        delete leadParaSalvar.createdAt;\n        delete leadParaSalvar.updatedAt;\n\n        // Adiciona links categorizados se existirem\n        if (this.linksEncontrados && this.linksEncontrados.length > 0) {\n          leadParaSalvar.links = this.linksEncontrados;\n          console.log('Adicionando links categorizados ao lead processado:', this.linksEncontrados);\n        }\n\n        // Adiciona telefones múltiplos se existirem\n        if (this.telefonesEncontrados && this.telefonesEncontrados.length > 0) {\n          leadParaSalvar.telefones = this.telefonesEncontrados.map(tel => ({\n            tipo: tel.tipo,\n            numero: tel.numero,\n            descricao: tel.descricao\n          }));\n          console.log('Adicionando telefones ao lead processado:', leadParaSalvar.telefones);\n        }\n\n        // Adiciona sócios detalhados se existirem\n        if (this.sociosDetalhados && this.sociosDetalhados.length > 0) {\n          leadParaSalvar.sociosDetalhados = this.sociosDetalhados;\n          console.log('Adicionando sócios detalhados ao lead:', this.sociosDetalhados.length);\n        }\n\n      } else {\n        // Fallback: cria lead do zero (modo manual)\n        leadParaSalvar = { ...this.lead };\n\n        // Adiciona dados do Instagram de forma estruturada (se disponível)\n        if (this.dadosInstagram && this.dadosInstagram.user) {\n          const user = this.dadosInstagram.user;\n\n          // Dados básicos\n          leadParaSalvar.avatarUrl = user.profile_pic_url;\n\n          // Estrutura instagramData\n          leadParaSalvar.instagramData = {\n            bio: user.biography,\n            followers: user.edge_followed_by?.count,\n            following: user.edge_follow?.count,\n            accountType: user.is_business_account ? 'Business' : 'Pessoal',\n            businessCategory: user.business_category_name || user.category_name,\n            location: user.business_address_json ? JSON.stringify(user.business_address_json) : undefined,\n            website: user.external_url\n          };\n        }\n\n        // Adiciona links categorizados se existirem (modo manual)\n        if (this.linksEncontrados && this.linksEncontrados.length > 0) {\n          leadParaSalvar.links = this.linksEncontrados;\n          console.log('Adicionando links categorizados ao lead manual:', this.linksEncontrados);\n        }\n\n        // Adiciona telefones múltiplos se existirem (modo manual)\n        if (this.telefonesEncontrados && this.telefonesEncontrados.length > 0) {\n          leadParaSalvar.telefones = this.telefonesEncontrados.map(tel => ({\n            tipo: tel.tipo,\n            numero: tel.numero,\n            descricao: tel.descricao\n          }));\n          console.log('Adicionando telefones ao lead manual:', leadParaSalvar.telefones);\n        }\n\n        // Adiciona sócios detalhados se existirem (modo manual)\n        if (this.sociosDetalhados && this.sociosDetalhados.length > 0) {\n          leadParaSalvar.sociosDetalhados = this.sociosDetalhados;\n          console.log('Adicionando sócios detalhados ao lead manual:', this.sociosDetalhados.length);\n        }\n      }\n\n      console.log('Salvando lead:', leadParaSalvar);\n      console.log('Sincronizar com Bitrix:', this.sincronizarBitrix);\n      console.log('Total de links categorizados a serem enviados:', leadParaSalvar.links?.length || 0);\n\n      if (leadParaSalvar.links && leadParaSalvar.links.length > 0) {\n        console.log('Links categorizados detalhados:');\n        leadParaSalvar.links.forEach((link: any, index: number) => {\n          console.log(`  ${index + 1}. ${link.tipo}: ${link.url} (${link.descricao})`);\n        });\n      }\n\n      // Adicionar flag de sincronização no objeto a ser salvo\n      if (this.sincronizarBitrix) {\n        leadParaSalvar.sincronizarBitrix = true;\n      }\n\n      const leadCriado = await this.leadService.salveLead(leadParaSalvar);\n\n      console.log('Lead criado com sucesso:', leadCriado);\n\n      // Limpa dados do service\n      this.instagramDataService.clearDados();\n\n      // Redireciona para a home do lead criado\n      this.router.navigate(['/crm/home', this.username]);\n    } catch (error) {\n      console.error('Erro ao salvar lead:', error);\n      this.erro = 'Erro ao criar lead. Tente novamente.';\n    } finally {\n      this.carregando = false;\n    }\n  }\n\n  /**\n   * Valida o formulário\n   */\n  private validarFormulario(): boolean {\n    if (!this.lead.nomeResponsavel) {\n      this.erro = 'Nome do responsável é obrigatório';\n      return false;\n    }\n    if (!this.lead.empresa) {\n      this.erro = 'Nome da empresa é obrigatório';\n      return false;\n    }\n    if (!this.lead.cidade) {\n      this.erro = 'Cidade da empresa é obrigatória';\n      return false;\n    }\n    if (!this.lead.instagramHandle) {\n      this.erro = 'Username do Instagram é obrigatório';\n      return false;\n    }\n    return true;\n  }\n\n  /**\n   * Cancela e volta para a home\n   */\n  cancelar(): void {\n    this.instagramDataService.clearDados();\n    this.router.navigate(['/crm/home', this.username]);\n  }\n\n  /**\n   * Retorna a URL da foto do perfil do Instagram\n   */\n  getFotoPerfilInstagram(): string {\n    if (this.dadosInstagram?.user?.profile_pic_url) {\n      return this.dadosInstagram.user.profile_pic_url;\n    }\n    return '/assets/images/default-avatar.png'; // fallback\n  }\n\n  /**\n   * Retorna o número de seguidores formatado\n   */\n  getSeguidoresFormatado(): string {\n    const count = this.dadosInstagram?.user?.edge_followed_by?.count;\n    if (!count) return '0';\n\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    } else if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  }\n\n  /**\n   * Solicita dados do Instagram via content script\n   */\n  solicitarDadosInstagram(): void {\n    // Enviar mensagem para content script via iframe\n    const message = {\n      tipo: 'REQUEST_INSTAGRAM_DATA',\n      username: this.username\n    };\n\n    // Comunicação via postMessage para parent window (content script)\n    if (window.parent && window.parent !== window) {\n      window.parent.postMessage({tipo: \"NOVA_MENSAGEM\", text: message}, \"*\");\n    }\n\n    console.log('Solicitação de dados enviada para content script:', message);\n  }\n\n  /**\n   * Configura listener para eventos do Instagram\n   */\n  private setupInstagramDataListener(): void {\n    window.addEventListener('message', (event) => {\n      // Verifica se é um evento de resposta do Instagram\n      if (event.data && event.data.tipo === 'INSTAGRAM_DATA_RESPONSE') {\n        console.log('Dados do Instagram recebidos:', event.data);\n        this.processarDadosInstagram(event.data);\n      }\n\n      // Verifica se houve erro na busca\n      if (event.data && event.data.tipo === 'INSTAGRAM_DATA_ERROR') {\n        console.error('Erro ao buscar dados do Instagram:', event.data);\n        this.processarErroInstagram(event.data);\n      }\n    });\n  }\n\n  /**\n   * Processa os dados recebidos do Instagram\n   */\n  private async processarDadosInstagram(dadosInstagram: any): Promise<void> {\n    console.log('Processando dados do Instagram para username:', dadosInstagram.username);\n\n    try {\n      // Primeiro, envia texto do Instagram para a API dadosig2 para processamento\n      console.log('Enviando texto do Instagram para API dadosig2...');\n      this.carregando = true;\n\n      const leadProcessado = await this.leadService.enviarDadosInstagram(dadosInstagram.textoInsta, null, dadosInstagram.username);\n\n      console.log('Lead processado pela API dadosig2:', leadProcessado);\n\n      if (leadProcessado) {\n        // ServerService já extraiu o 'dados', então leadProcessado é o objeto Lead completo\n        console.log('Objeto Lead processado com sucesso:', leadProcessado);\n\n        // Define username\n        this.username = dadosInstagram.username;\n\n        // Preenche formulário com dados do lead processado pela API\n        // A função preencherFormularioComLeadProcessado agora simula dadosInstagram para o template\n        this.preencherFormularioComLeadProcessado(leadProcessado);\n\n        // Salva dados do Instagram no service (se necessário para outras funcionalidades)\n        this.instagramDataService.setDados(this.dadosInstagram, this.username);\n\n        console.log('Lead processado e formulário preenchido:', this.lead);\n        console.log('Dados simulados para template:', this.dadosInstagram);\n      } else {\n        console.error('Erro: resposta vazia da API dadosig2');\n        this.erro = 'Erro ao processar dados do Instagram na API.';\n      }\n    } catch (error) {\n      console.error('Erro ao enviar dados para API dadosig2:', error);\n      this.erro = 'Erro ao processar dados do Instagram. Tente novamente.';\n    } finally {\n      this.carregando = false;\n    }\n  }\n\n  /**\n   * Envia dados do Instagram para a API (caso necessário futuramente)\n   */\n  private async enviarDadosInstagramParaAPI(dadosInstagram: any): Promise<void> {\n    try {\n      console.log('Enviando dados do Instagram para API:', dadosInstagram.data);\n\n      const response = await this.leadService.enviarDadosInstagram(dadosInstagram.data, null, dadosInstagram.username);\n\n      if (response.sucesso) {\n        console.log('Lead criado/atualizado com sucesso:', response.dados);\n      } else {\n        console.error('Erro na resposta da API:', response.erro);\n      }\n    } catch (error) {\n      console.error('Erro ao enviar dados do Instagram para API:', error);\n    }\n  }\n\n  /**\n   * Processa erros na busca de dados do Instagram\n   */\n  private processarErroInstagram(errorData: any): void {\n    console.error('Erro ao buscar dados do Instagram:', errorData.error);\n    this.erro = 'Erro ao buscar dados do Instagram. Tente novamente.';\n  }\n\n  /**\n   * Exibe o formulário para criação manual de lead\n   */\n  mostrarFormularioManual(): void {\n    this.mostrarFormulario = true;\n    console.log('Formulário manual ativado');\n  }\n\n  /**\n   * Normaliza URL do website quando o usuário sai do campo\n   */\n  onWebsiteBlur(): void {\n    if (this.lead.website) {\n      const urlOriginal = this.lead.website;\n      this.lead.website = this.normalizarUrl(this.lead.website);\n\n      if (urlOriginal !== this.lead.website) {\n        console.log('URL normalizada:', urlOriginal, '->', this.lead.website);\n      }\n    }\n  }\n\n  /**\n   * Processa o telefone formatado quando o usuário sai do campo\n   */\n  onTelefoneBlur(): void {\n    // Remove a formatação e armazena apenas os números no objeto lead\n    this.lead.telefone = this.limparTelefone(this.telefoneFormatado);\n    console.log('Telefone formatado:', this.telefoneFormatado, '-> Limpo:', this.lead.telefone);\n  }\n\n  /**\n   * Processa mudanças no campo de telefone em tempo real\n   */\n  onTelefoneChange(value: string): void {\n    // Atualiza o valor limpo do telefone enquanto o usuário digita\n    this.lead.telefone = this.limparTelefone(value || '');\n  }\n\n  /**\n   * Processa quando o usuário cola um telefone\n   */\n  onTelefonePaste(event: ClipboardEvent): void {\n    event.preventDefault();\n    \n    const clipboardData = event.clipboardData?.getData('text') || '';\n    console.log('Telefone colado:', clipboardData);\n    \n    // Processa o número removendo código do país se necessário\n    const numeroProcessado = this.processarTelefoneColado(clipboardData);\n    \n    // Define o valor limpo e formatado\n    this.lead.telefone = numeroProcessado;\n    this.telefoneFormatado = this.formatarTelefone(numeroProcessado);\n    \n    console.log('Telefone processado:', this.telefoneFormatado);\n    \n    // Força atualização\n    this.cdr.detectChanges();\n  }\n\n  /**\n   * Remove formatação do telefone mantendo apenas números\n   */\n  private limparTelefone(telefone: string): string {\n    if (!telefone) return '';\n    return telefone.replace(/\\D/g, '');\n  }\n\n  /**\n   * Processa telefone colado removendo código do país se necessário\n   */\n  private processarTelefoneColado(valor: string): string {\n    // Remove tudo que não é número\n    let numeroLimpo = valor.replace(/\\D/g, '');\n    \n    // Se tem código do país do Brasil (55) no início, remove\n    if (numeroLimpo.startsWith('55') && numeroLimpo.length > 11) {\n      numeroLimpo = numeroLimpo.substring(2);\n      console.log('Removido código do país 55, telefone:', numeroLimpo);\n    }\n    \n    // Se ainda tem mais de 11 dígitos, pega apenas os 11 últimos\n    if (numeroLimpo.length > 11) {\n      numeroLimpo = numeroLimpo.substring(numeroLimpo.length - 11);\n      console.log('Telefone tinha mais de 11 dígitos, pegando últimos 11:', numeroLimpo);\n    }\n    \n    return numeroLimpo;\n  }\n\n  /**\n   * Carrega dados extras do website informado\n   */\n  async carregarDadosDoLink(): Promise<void> {\n    if (!this.lead.website) {\n      this.erro = 'Informe o website para carregar os dados';\n      return;\n    }\n\n    // Normalizar URL antes da validação\n    this.lead.website = this.normalizarUrl(this.lead.website);\n\n    // Validar se é uma URL válida\n    try {\n      new URL(this.lead.website);\n    } catch {\n      this.erro = 'Informe uma URL válida (ex: https://exemplo.com.br)';\n      return;\n    }\n\n    this.carregandoWebsite = true;\n    this.erro = '';\n    this.marcarWebsiteAnalisado(); // Marcar como tentativa executada\n\n    try {\n      console.log('Carregando dados do website:', this.lead.website);\n\n      // Enviar URL para a API para análise\n      const dadosWebsite = await this.leadService.analisarWebsite(this.lead.website);\n\n      if (dadosWebsite) {\n        console.log('Dados do website recebidos:', dadosWebsite);\n\n        // Armazenar links categorizados\n        if (dadosWebsite.linksCategorized && Array.isArray(dadosWebsite.linksCategorized)) {\n          this.linksEncontrados = dadosWebsite.linksCategorized.sort((a: any, b: any) => a.ordem - b.ordem);\n          console.log('Links categorizados recebidos da API:', this.linksEncontrados);\n          console.log('Total de links categorizados:', this.linksEncontrados.length);\n\n          // Log detalhado de cada link\n          this.linksEncontrados.forEach((link: any, index: number) => {\n            console.log(`Link ${index + 1}: ${link.tipo} = ${link.url} (${link.descricao})`);\n          });\n        } else {\n          console.warn('Nenhum link categorizado recebido da API ou formato inválido:', dadosWebsite.linksCategorized);\n          this.linksEncontrados = [];\n        }\n\n        // Preencher automaticamente telefone do primeiro WhatsApp encontrado\n        const whatsappLink = this.linksEncontrados.find((link: any) => link.tipo === 'WhatsApp');\n        if (whatsappLink && !this.lead.telefone) {\n          // Extrair número do WhatsApp\n          const numeroMatch = whatsappLink.url.match(/(\\d{10,15})/);\n          if (numeroMatch) {\n            this.lead.telefone = numeroMatch[1];\n            // Atualiza o campo formatado também\n            this.telefoneFormatado = this.formatarTelefone(this.lead.telefone);\n          }\n        }\n\n        // Preencher automaticamente Instagram handle\n        const instagramLink = this.linksEncontrados.find((link: any) => link.tipo === 'Instagram');\n        if (instagramLink && !this.lead.instagramHandle) {\n          // Extrair username do Instagram\n          const usernameMatch = instagramLink.url.match(/instagram\\.com\\/([^\\/\\?]+)/);\n          if (usernameMatch) {\n            this.lead.instagramHandle = usernameMatch[1];\n          }\n        }\n\n        // Preencher automaticamente link do cardápio (Site do Cardápio ou Concorrente)\n        const cardapioLink = this.linksEncontrados.find((link: any) =>\n          link.tipo === 'Site do Cardápio' || link.tipo === 'Concorrente'\n        );\n        if (cardapioLink) {\n          this.lead.linkCardapio = cardapioLink.url;\n          console.log(`Link de cardápio/concorrente encontrado: ${cardapioLink.tipo} = ${cardapioLink.url}`);\n        }\n\n        console.log('Formulário atualizado com dados dos links:', this.lead);\n      } else {\n        this.erro = 'Não foi possível extrair dados do website informado';\n      }\n    } catch (error) {\n      console.error('Erro ao carregar dados do website:', error);\n      this.erro = 'Erro ao analisar o website. Verifique a URL e tente novamente.';\n    } finally {\n      this.carregandoWebsite = false;\n    }\n  }\n\n  /**\n   * Normaliza URL adicionando https:// se necessário\n   */\n  private normalizarUrl(url: string): string {\n    if (!url) return url;\n\n    // Remove espaços extras\n    url = url.trim();\n\n    // Se já tem protocolo, retorna como está\n    if (url.match(/^https?:\\/\\//i)) {\n      return url;\n    }\n\n    // Se não tem protocolo, adiciona https://\n    return `https://${url}`;\n  }\n\n  /**\n   * Abre um link em nova aba (sempre com https://)\n   */\n  abrirLink(url: string): void {\n    const urlNormalizada = this.normalizarUrl(url);\n    window.open(urlNormalizada, '_blank');\n    console.log('Abrindo link:', url, '-> normalizado:', urlNormalizada);\n  }\n\n  /**\n   * Descobre múltiplos CNPJs da empresa usando busca no Google + IA\n   */\n  async descobrirCnpj(): Promise<void> {\n    if (!this.lead.empresa) {\n      this.erro = 'Informe o nome da empresa para descobrir o CNPJ';\n      return;\n    }\n\n    if (!this.lead.cidade) {\n      this.erro = 'Informe a cidade da empresa para descobrir o CNPJ';\n      return;\n    }\n\n    this.carregandoCnpj = true;\n    this.erro = '';\n    this.cnpjsEncontrados = []; // Limpar resultados anteriores\n    this.cnpjRecusadoExplicitamente = false; // Reset da flag ao buscar novos CNPJs\n    this.marcarCnpjBuscado(); // Marcar como tentativa executada\n\n    try {\n      console.log('Descobrindo CNPJs para empresa:', this.lead.empresa, 'em', this.lead.cidade);\n\n      // ServerService retorna diretamente os dados (response.data) quando sucesso\n      const response = await this.leadService.descobrirCnpj(this.lead.empresa, this.lead.cidade);\n\n      console.log('Resposta completa do servidor:', response);\n      console.log('Tipo da resposta:', typeof response);\n      console.log('Estrutura da resposta:', Object.keys(response || {}));\n\n      // ServerService já extraiu .data, então response É os dados\n      const dados = response;\n\n      console.log('Dados extraídos:', dados);\n      console.log('CNPJs encontrados:', dados?.cnpjsEncontrados);\n      console.log('Total encontrados:', dados?.totalEncontrados);\n\n      // Debug detalhado\n      console.log('dados existe?', !!dados);\n      console.log('dados.cnpjsEncontrados existe?', !!dados?.cnpjsEncontrados);\n      console.log('dados.cnpjsEncontrados é array?', Array.isArray(dados?.cnpjsEncontrados));\n      console.log('length do array:', dados?.cnpjsEncontrados?.length);\n\n      if (dados && dados.cnpjsEncontrados && dados.cnpjsEncontrados.length > 0) {\n        this.cnpjsEncontrados = dados.cnpjsEncontrados.map((cnpj: any, index: number) => ({\n          ...cnpj,\n          id: `cnpj_${index}`,\n          selecionado: false\n        }));\n\n        console.log('CNPJs encontrados:', dados.totalEncontrados);\n        console.log('Lista de CNPJs processada:', this.cnpjsEncontrados);\n        console.log('Propriedade cnpjsEncontrados.length:', this.cnpjsEncontrados.length);\n        console.log('Primeiro CNPJ:', this.cnpjsEncontrados[0]);\n\n        // Forçar detecção de mudanças\n        this.cdr.detectChanges();\n\n        setTimeout(() => {\n          console.log('Após timeout - cnpjsEncontrados.length:', this.cnpjsEncontrados.length);\n          this.cdr.detectChanges();\n        }, 100);\n\n        // Se encontrou apenas 1 CNPJ com alta confiança, pode selecionar automaticamente\n        if (dados.totalEncontrados === 1 && dados.cnpjsEncontrados[0].confianca === 'alta') {\n          this.selecionarCnpj(this.cnpjsEncontrados[0]);\n          console.log('CNPJ único com alta confiança selecionado automaticamente');\n        }\n      } else {\n        console.warn('Condição de CNPJs falhou:', {\n          dados: !!dados,\n          cnpjsEncontrados: !!dados?.cnpjsEncontrados,\n          isArray: Array.isArray(dados?.cnpjsEncontrados),\n          length: dados?.cnpjsEncontrados?.length,\n          dadosCompletos: dados\n        });\n\n        this.erro = `Nenhum CNPJ encontrado para \"${this.lead.empresa}\" em ${this.lead.cidade}. Tente com um nome mais específico ou verifique se a empresa possui CNPJ.`;\n        console.warn('Nenhum CNPJ encontrado para a busca realizada');\n      }\n    } catch (error) {\n      console.error('Erro ao descobrir CNPJs:', error);\n      this.erro = `Erro ao buscar CNPJs: ${error}`;\n    } finally {\n      this.carregandoCnpj = false;\n    }\n  }\n\n  /**\n   * Seleciona um CNPJ da lista de opções encontradas\n   */\n  selecionarCnpj(cnpjSelecionado: any): void {\n    // Marcar apenas o selecionado\n    this.cnpjsEncontrados.forEach(cnpj => {\n      cnpj.selecionado = cnpj.id === cnpjSelecionado.id;\n    });\n\n    // Preencher dados no formulário\n    this.lead.cnpj = cnpjSelecionado.cnpj;\n\n    // Armazenar razão social para exibição no resumo\n    this.razaoSocialSelecionada = cnpjSelecionado.razaoSocial || '';\n\n    // Reset da flag de recusa já que selecionou um CNPJ\n    this.cnpjRecusadoExplicitamente = false;\n    this.erro = ''; // Limpa qualquer erro\n\n    // NÃO alteramos o nome da empresa - mantemos o que veio do Instagram\n    // O nome do Instagram é mais relevante (como o mercado conhece a empresa)\n    console.log('Mantendo nome da empresa do Instagram:', this.lead.empresa);\n    if (cnpjSelecionado.nomeFantasia && cnpjSelecionado.nomeFantasia !== this.lead.empresa) {\n      console.log('Nome fantasia do CNPJ é diferente:', cnpjSelecionado.nomeFantasia);\n      console.log('Mas mantemos o nome original:', this.lead.empresa);\n    }\n\n    // Adicionar razão social nas observações se disponível\n    if (cnpjSelecionado.razaoSocial) {\n      const observacaoExtra = `Razão Social: ${cnpjSelecionado.razaoSocial}`;\n      if (!this.lead.observacoes) {\n        this.lead.observacoes = observacaoExtra;\n      } else if (!this.lead.observacoes.includes('Razão Social:')) {\n        this.lead.observacoes = `${observacaoExtra}\\n\\n${this.lead.observacoes}`;\n      }\n    }\n\n    // Adicionar endereço se disponível e campo estiver vazio\n    if (cnpjSelecionado.endereco && !this.lead.endereco) {\n      this.lead.endereco = cnpjSelecionado.endereco;\n      console.log('Endereço preenchido automaticamente:', cnpjSelecionado.endereco);\n    }\n\n    console.log('CNPJ selecionado:', cnpjSelecionado.cnpj);\n    console.log('Dados atualizados no formulário:', {\n      cnpj: this.lead.cnpj,\n      empresa: this.lead.empresa + ' (mantido do Instagram)',\n      endereco: this.lead.endereco\n    });\n  }\n\n  /**\n   * Remove a seleção de CNPJ\n   */\n  removerSelecaoCnpj(): void {\n    this.cnpjsEncontrados.forEach(cnpj => {\n      cnpj.selecionado = false;\n    });\n    this.lead.cnpj = '';\n    this.razaoSocialSelecionada = ''; // Limpar razão social\n    this.cnpjRecusadoExplicitamente = false; // Reset da confirmação\n    console.log('Seleção de CNPJ removida');\n  }\n\n  /**\n   * Formatar input de CNPJ em tempo real\n   */\n  formatarCnpjInput(event: any): void {\n    let valor = event.target.value.replace(/\\D/g, ''); // Remove tudo que não é número\n\n    // Aplica a máscara XX.XXX.XXX/XXXX-XX\n    if (valor.length <= 14) {\n      valor = valor.replace(/(\\d{2})(\\d)/, '$1.$2');\n      valor = valor.replace(/(\\d{2})\\.(\\d{3})(\\d)/, '$1.$2.$3');\n      valor = valor.replace(/\\.(\\d{3})(\\d)/, '.$1/$2');\n      valor = valor.replace(/(\\d{4})(\\d)/, '$1-$2');\n    }\n\n    // Atualiza o valor do input\n    event.target.value = valor;\n    this.cnpjManual = valor;\n  }\n\n  /**\n   * Buscar dados da empresa pelo CNPJ informado manualmente\n   */\n  async buscarCnpjManual(): Promise<void> {\n    if (!this.cnpjManual) {\n      this.erro = 'Informe o CNPJ para buscar os dados da empresa';\n      return;\n    }\n\n    // Remove formatação para validação\n    const cnpjLimpo = this.cnpjManual.replace(/\\D/g, '');\n    if (cnpjLimpo.length !== 14) {\n      this.erro = 'CNPJ deve ter 14 dígitos';\n      return;\n    }\n\n    this.carregandoCnpjManual = true;\n    this.erro = '';\n\n    try {\n      console.log('Buscando dados para CNPJ manual:', this.cnpjManual);\n\n      // Usar a mesma API que busca detalhes dos sócios, mas só para obter dados da empresa\n      const response = await this.leadService.buscarDetalhesSocios(cnpjLimpo);\n\n      if (response && response.empresa) {\n        // Criar um objeto CNPJ simulado para adicionar à lista\n        const cnpjEncontrado = {\n          id: 'cnpj_manual',\n          cnpj: cnpjLimpo,\n          cnpjFormatado: this.cnpjManual,\n          nomeFantasia: response.empresa.nomeFantasia || response.empresa.razaoSocial || 'Nome não informado',\n          razaoSocial: response.empresa.razaoSocial || '',\n          endereco: response.empresa.endereco || '',\n          capitalSocial: response.empresa.capitalSocial || '',\n          porte: response.empresa.porte || '',\n          situacao: response.empresa.situacao || 'ATIVA',\n          atividadePrincipal: response.empresa.naturezaJuridica || response.empresa.atividadePrincipal || '',\n          socios: response.socios ? response.socios.map((s: any) => s.nome).slice(0, 3) : [],\n          confianca: 'manual',\n          fonte: 'Informado manualmente',\n          selecionado: false\n        };\n\n        // Limpar resultados anteriores e adicionar o CNPJ manual\n        this.cnpjsEncontrados = [cnpjEncontrado];\n\n        // Marcar automaticamente como selecionado\n        this.selecionarCnpj(cnpjEncontrado);\n\n        console.log('Dados do CNPJ manual encontrados:', cnpjEncontrado);\n\n        // Limpar o campo manual após sucesso\n        this.cnpjManual = '';\n\n        // Forçar detecção de mudanças\n        this.cdr.detectChanges();\n\n      } else {\n        this.erro = 'CNPJ não encontrado ou dados indisponíveis. Verifique o número informado.';\n        console.warn('Nenhum dado encontrado para o CNPJ manual:', cnpjLimpo);\n      }\n    } catch (error) {\n      console.error('Erro ao buscar CNPJ manual:', error);\n      this.erro = 'Erro ao buscar dados do CNPJ. Verifique o número e tente novamente.';\n    } finally {\n      this.carregandoCnpjManual = false;\n    }\n  }\n\n  /**\n   * Abrir Google com busca específica para o CNPJ informado\n   */\n  abrirGoogleCnpj(): void {\n    if (!this.cnpjManual) {\n      console.warn('CNPJ manual não informado para busca no Google');\n      return;\n    }\n\n    const cnpjLimpo = this.cnpjManual.replace(/\\D/g, '');\n    const termoBusca = `CNPJ ${this.cnpjManual}`;\n    const urlGoogle = `https://www.google.com/search?q=${encodeURIComponent(termoBusca)}`;\n\n    console.log('Abrindo busca Google para CNPJ:', this.cnpjManual);\n    window.open(urlGoogle, '_blank');\n  }\n\n  /**\n   * Abrir Google com busca para CNPJ da empresa\n   */\n  abrirGoogleEmpresaCnpj(): void {\n    if (!this.lead.empresa) {\n      console.warn('Nome da empresa não informado para busca no Google');\n      return;\n    }\n\n    const termoBusca = `${this.lead.empresa} CNPJ`;\n    const urlGoogle = `https://www.google.com/search?q=${encodeURIComponent(termoBusca)}`;\n\n    console.log('Abrindo busca Google para empresa:', termoBusca);\n    window.open(urlGoogle, '_blank');\n  }\n\n  /**\n   * Abrir Google com busca mais específica (empresa + cidade + CNPJ)\n   */\n  abrirGoogleEmpresaCompleta(): void {\n    if (!this.lead.empresa) {\n      console.warn('Nome da empresa não informado para busca no Google');\n      return;\n    }\n\n    let termoBusca = `${this.lead.empresa}`;\n\n    // Adicionar cidade se disponível\n    if (this.lead.cidade) {\n      termoBusca += ` ${this.lead.cidade}`;\n    }\n\n    termoBusca += ' CNPJ';\n\n    const urlGoogle = `https://www.google.com/search?q=${encodeURIComponent(termoBusca)}`;\n\n    console.log('Abrindo busca Google completa:', termoBusca);\n    window.open(urlGoogle, '_blank');\n  }\n\n  /**\n   * Abrir consulta direta da Receita Federal\n   */\n  abrirGoogleReceitaFederal(): void {\n    const urlReceita = 'https://solucoes.receita.fazenda.gov.br/servicos/cnpjreva/cnpjreva_solicitacao.asp';\n\n    console.log('Abrindo consulta Receita Federal');\n    window.open(urlReceita, '_blank');\n  }\n\n  /**\n   * Abrir consulta de CNPJ na Receita Federal (usado quando não encontra sócios)\n   */\n  abrirConsultaCNPJReceita(): void {\n    const urlReceita = 'https://solucoes.receita.fazenda.gov.br/servicos/cnpjreva/cnpjreva_solicitacao.asp';\n    \n    // Se tiver CNPJ, copia para área de transferência para facilitar\n    if (this.lead.cnpj) {\n      navigator.clipboard.writeText(this.lead.cnpj)\n        .then(() => {\n          console.log('CNPJ copiado para área de transferência:', this.lead.cnpj);\n          // Poderia mostrar um toast/notificação aqui\n        })\n        .catch(err => {\n          console.error('Erro ao copiar CNPJ:', err);\n        });\n    }\n    \n    console.log('Abrindo consulta Receita Federal para buscar sócios');\n    window.open(urlReceita, '_blank');\n  }\n\n  /**\n   * Adicionar sócio manualmente quando não encontrar na busca\n   */\n  adicionarSocioManual(): void {\n    if (!this.nomeSocioManual?.trim()) {\n      return;\n    }\n    \n    const socioManual = {\n      nome: this.nomeSocioManual.trim(),\n      cpf: null,\n      participacao: null,\n      cargo: 'Sócio',\n      dataEntrada: null,\n      qualificacao: null,\n      principal: true,\n      observacoes: 'Informado manualmente',\n      scoreAnalise: 50,\n      motivoSelecao: 'Informado manualmente pelo usuário',\n      manual: true\n    };\n    \n    // Limpa lista anterior e adiciona o sócio manual\n    this.sociosDetalhados = [socioManual];\n    \n    // Define como responsável do lead\n    this.lead.nomeResponsavel = socioManual.nome;\n    this.parceirSelecionado = true;\n    \n    // Limpa o campo de input\n    this.nomeSocioManual = '';\n    \n    // Força detecção de mudanças\n    this.cdr.detectChanges();\n    \n    console.log('Sócio adicionado manualmente:', socioManual.nome);\n    console.log('Nome responsável atualizado para:', this.lead.nomeResponsavel);\n  }\n\n  /**\n   * Abrir Google para verificar um CNPJ específico\n   */\n  abrirGoogleVerificarCnpj(cnpj: any): void {\n    const nomeEmpresa = cnpj.nomeFantasia || cnpj.razaoSocial || 'empresa';\n    const termoBusca = `${nomeEmpresa} CNPJ ${cnpj.cnpj}`;\n    const urlGoogle = `https://www.google.com/search?q=${encodeURIComponent(termoBusca)}`;\n\n    console.log('Abrindo busca Google para verificar CNPJ:', termoBusca);\n    window.open(urlGoogle, '_blank');\n  }\n\n\n  /**\n   * Confirma que o usuário não quer escolher nenhum dos CNPJs encontrados\n   */\n  confirmarNenhumCnpj(): void {\n    this.cnpjRecusadoExplicitamente = true;\n    this.removerSelecaoCnpj();\n    this.erro = ''; // Limpa qualquer erro\n    console.log('Usuário confirmou que não quer escolher nenhum CNPJ');\n  }\n\n  /**\n   * Copia CNPJ para a área de transferência\n   */\n  async copiarCnpj(cnpj: string): Promise<void> {\n    try {\n      await navigator.clipboard.writeText(cnpj);\n      console.log('CNPJ copiado:', cnpj);\n    } catch (error) {\n      console.error('Erro ao copiar CNPJ:', error);\n    }\n  }\n\n  /**\n   * Abre detalhes do CNPJ no site CNPJ.biz\n   */\n  abrirCnpjBiz(cnpj: string): void {\n    // Remove formatação do CNPJ para usar na URL\n    const cnpjLimpo = cnpj.replace(/[^\\d]/g, '');\n    const url = `https://cnpj.biz/${cnpjLimpo}`;\n    window.open(url, '_blank');\n    console.log('Abrindo CNPJ.biz para:', cnpj, '-> URL:', url);\n  }\n\n  /**\n   * Obter cor do badge de confiança\n   */\n  getCorConfianca(confianca: string): string {\n    switch (confianca) {\n      case 'alta': return 'success';\n      case 'media': return 'warning';\n      case 'baixa': return 'danger';\n      default: return 'secondary';\n    }\n  }\n\n  /**\n   * Obter ícone de confiança\n   */\n  getIconeConfianca(confianca: string): string {\n    switch (confianca) {\n      case 'alta': return 'fa-check-circle';\n      case 'media': return 'fa-exclamation-circle';\n      case 'baixa': return 'fa-times-circle';\n      default: return 'fa-question-circle';\n    }\n  }\n\n  /**\n   * Copia link para a área de transferência (sempre com https://)\n   */\n  async copiarLink(url: string): Promise<void> {\n    try {\n      const urlNormalizada = this.normalizarUrl(url);\n      await navigator.clipboard.writeText(urlNormalizada);\n      console.log('Link copiado:', url, '-> normalizado:', urlNormalizada);\n      // Opcional: mostrar notificação de sucesso\n    } catch (error) {\n      console.error('Erro ao copiar link:', error);\n      // Fallback para navegadores antigos\n      const urlNormalizada = this.normalizarUrl(url);\n      const textArea = document.createElement('textarea');\n      textArea.value = urlNormalizada;\n      document.body.appendChild(textArea);\n      textArea.select();\n      document.execCommand('copy');\n      document.body.removeChild(textArea);\n    }\n  }\n\n  /**\n   * Formatar número de telefone\n   */\n  formatarTelefone(numero: string): string {\n    if (!numero) return '';\n\n    const numeroLimpo = numero.replace(/\\D/g, '');\n\n    if (numeroLimpo.length === 11) {\n      // Celular: (XX) 9XXXX-XXXX\n      return numeroLimpo.replace(/^(\\d{2})(\\d{5})(\\d{4})/, '($1) $2-$3');\n    } else if (numeroLimpo.length === 10) {\n      // Fixo: (XX) XXXX-XXXX\n      return numeroLimpo.replace(/^(\\d{2})(\\d{4})(\\d{4})/, '($1) $2-$3');\n    }\n\n    return numero;\n  }\n\n  /**\n   * Obter ícone por tipo de telefone\n   */\n  getIconeTelefone(tipo: string): string {\n    const tipoInfo = this.tiposTelefone.find(t => t.valor === tipo);\n    return tipoInfo ? tipoInfo.icone : 'fa-phone';\n  }\n\n  /**\n   * Obter cor por tipo de telefone\n   */\n  getCorTelefone(tipo: string): string {\n    const tipoInfo = this.tiposTelefone.find(t => t.valor === tipo);\n    return tipoInfo ? tipoInfo.cor : '#6c757d';\n  }\n\n  /**\n   * Abrir telefone (WhatsApp ou discador)\n   */\n  abrirTelefone(telefone: any): void {\n    if (telefone.tipo === 'WhatsApp' || telefone.tipo === 'Celular') {\n      const numeroLimpo = telefone.numero.replace(/\\D/g, '');\n      const whatsappUrl = `https://wa.me/55${numeroLimpo}`;\n      window.open(whatsappUrl, '_blank');\n    } else {\n      // Para telefones fixos, apenas copiar o número\n      this.copiarTelefone(telefone.numero);\n    }\n  }\n\n  /**\n   * Copiar telefone para a área de transferência\n   */\n  async copiarTelefone(numero: string): Promise<void> {\n    try {\n      const numeroFormatado = this.formatarTelefone(numero);\n      await navigator.clipboard.writeText(numeroFormatado);\n      console.log('Telefone copiado:', numeroFormatado);\n      // Opcional: mostrar notificação de sucesso\n    } catch (error) {\n      console.error('Erro ao copiar telefone:', error);\n    }\n  }\n\n  /**\n   * Buscar detalhes dos sócios da empresa\n   */\n  async buscarDetalhesSocios(): Promise<void> {\n    if (!this.lead.cnpj) {\n      this.erro = 'CNPJ não selecionado';\n      return;\n    }\n\n    this.carregandoSocios = true;\n    this.erro = '';\n    this.sociosBuscados = true;\n    this.parceirSelecionado = false; // Reset da seleção anterior\n\n    try {\n      console.log('Buscando sócios para CNPJ:', this.lead.cnpj);\n\n      // Chamar API para buscar sócios\n      // IMPORTANTE: ServerService já extrai .data quando sucesso=true\n      const response = await this.leadService.buscarDetalhesSocios(this.lead.cnpj);\n\n      console.log('Resposta do leadService (já extraída):', response);\n\n      // Como ServerService já extraiu .data, response É os dados\n      if (response && response.socios) {\n        console.log('Dados recebidos:', response);\n        console.log('Array de sócios:', response.socios);\n\n        // Garantir que é um array\n        if (Array.isArray(response.socios)) {\n          this.sociosDetalhados = response.socios;\n        } else {\n          console.error('response.socios não é um array:', response.socios);\n          this.sociosDetalhados = [];\n        }\n\n        console.log('Sócios atribuídos a this.sociosDetalhados:', this.sociosDetalhados);\n        console.log('Quantidade de sócios:', this.sociosDetalhados.length);\n        console.log('sociosBuscados:', this.sociosBuscados);\n\n        // Verificar cada sócio\n        this.sociosDetalhados.forEach((socio, index) => {\n          console.log(`Sócio ${index}:`, socio);\n        });\n\n        // Aplicar seleção inteligente do sócio principal (vem do backend)\n        if (this.sociosDetalhados.length > 0) {\n          const socioPrincipal = this.sociosDetalhados.find(socio => socio.principal);\n\n          if (socioPrincipal) {\n            // Atualizar responsável com base na análise inteligente\n            this.lead.nomeResponsavel = socioPrincipal.nome;\n            this.parceirSelecionado = true;\n\n            console.log('🎯 Sócio principal identificado automaticamente:', socioPrincipal.nome);\n            console.log(`   Score: ${socioPrincipal.scoreAnalise}/100`);\n            console.log(`   Motivo: ${socioPrincipal.motivoSelecao}`);\n            console.log('   Nome responsável atualizado para:', this.lead.nomeResponsavel);\n          } else {\n            // Fallback: se por algum motivo não tiver principal, define o primeiro\n            this.sociosDetalhados[0].principal = true;\n            this.lead.nomeResponsavel = this.sociosDetalhados[0].nome;\n            this.parceirSelecionado = true;\n            console.log('⚠️ Fallback: primeiro sócio definido como principal:', this.sociosDetalhados[0].nome);\n          }\n\n          // Forçar detecção de mudanças imediatamente após definir\n          this.cdr.detectChanges();\n        }\n\n        // Forçar detecção de mudanças\n        this.cdr.detectChanges();\n\n        // Timeout para garantir renderização\n        setTimeout(() => {\n          console.log('Após timeout - sociosDetalhados:', this.sociosDetalhados);\n          console.log('Após timeout - length:', this.sociosDetalhados.length);\n          console.log('Após timeout - lead.nomeResponsavel:', this.lead.nomeResponsavel);\n          this.cdr.detectChanges();\n        }, 100);\n\n        // Armazenar informações extras da empresa se disponíveis\n        if (response.empresa) {\n          console.log('Informações extras da empresa:', response.empresa);\n\n          // Adicionar informações da empresa nas observações se não existirem\n          const infoEmpresa = response.empresa;\n          let observacoesExtras = '';\n\n          if (infoEmpresa.razaoSocial && infoEmpresa.razaoSocial !== this.lead.empresa) {\n            observacoesExtras += `Razão Social: ${infoEmpresa.razaoSocial}\\n`;\n          }\n          if (infoEmpresa.capitalSocial) {\n            observacoesExtras += `Capital Social: ${infoEmpresa.capitalSocial}\\n`;\n          }\n          if (infoEmpresa.porte) {\n            observacoesExtras += `Porte: ${infoEmpresa.porte}\\n`;\n          }\n          if (infoEmpresa.naturezaJuridica) {\n            observacoesExtras += `Natureza Jurídica: ${infoEmpresa.naturezaJuridica}\\n`;\n          }\n          if (infoEmpresa.mei === 'Sim') {\n            observacoesExtras += `MEI: Sim\\n`;\n          }\n\n          if (observacoesExtras && !this.lead.observacoes?.includes('Razão Social:')) {\n            this.lead.observacoes = observacoesExtras + (this.lead.observacoes || '');\n          }\n        }\n\n        // Se não encontrou detalhes dos sócios mas tem sócios no CNPJ selecionado\n        if (this.sociosDetalhados.length === 0) {\n          // Verificar se tem sócios básicos do passo anterior\n          const cnpjSelecionado = this.cnpjsEncontrados.find(cnpj => cnpj.cnpj === this.lead.cnpj);\n          if (cnpjSelecionado && cnpjSelecionado.socios && cnpjSelecionado.socios.length > 0) {\n            // Converter lista de nomes para formato detalhado\n            const sociosBasicos = cnpjSelecionado.socios;\n            this.sociosDetalhados = sociosBasicos.map((nome: string, index: number) => ({\n              nome: nome,\n              cpf: null,\n              participacao: null,\n              cargo: 'Sócio',\n              dataEntrada: null,\n              qualificacao: null,\n              principal: index === 0,\n              observacoes: '',\n              scoreAnalise: index === 0 ? 60 : 30, // Score simples baseado na posição\n              motivoSelecao: index === 0 ? (sociosBasicos.length === 1 ? 'Sócio único da empresa' : 'Primeiro na lista') : 'Sócio secundário'\n            }));\n\n            // Definir o primeiro sócio como responsável\n            if (this.sociosDetalhados.length > 0) {\n              this.lead.nomeResponsavel = this.sociosDetalhados[0].nome;\n              this.parceirSelecionado = true; // Marcar como selecionado automaticamente\n\n              if (this.sociosDetalhados.length === 1) {\n                console.log('🎯 Sócio único (do CNPJ) definido automaticamente como responsável:', this.sociosDetalhados[0].nome);\n                console.log('   Score: 60/100 (sócio único)');\n              } else {\n                console.log('🎯 Primeiro sócio (do CNPJ) definido como responsável:', this.sociosDetalhados[0].nome);\n                console.log('   Score: 60/100 (primeiro na lista)');\n              }\n              console.log('   Nome responsável atualizado para:', this.lead.nomeResponsavel);\n\n              // Forçar detecção de mudanças imediatamente\n              this.cdr.detectChanges();\n            }\n\n            console.log('Usando sócios básicos do CNPJ selecionado:', this.sociosDetalhados);\n          }\n        }\n      } else {\n        console.warn('Nenhum sócio encontrado na busca detalhada. Response:', response);\n        this.sociosDetalhados = [];\n        this.parceirSelecionado = false; // Não há parceiro para selecionar\n\n        // Se a resposta for um erro (string), mostrar mensagem\n        if (typeof response === 'string') {\n          this.erro = response;\n        }\n      }\n    } catch (error) {\n      console.error('Erro ao buscar sócios:', error);\n      this.erro = 'Erro ao buscar informações dos sócios. Tente novamente.';\n      this.sociosDetalhados = [];\n      this.parceirSelecionado = false; // Reset em caso de erro\n    } finally {\n      this.carregandoSocios = false;\n    }\n  }\n\n  /**\n   * Extrair porcentagem numérica de uma string de participação\n   */\n  private extrairPorcentagem(participacao: string): number {\n    if (!participacao) return 0;\n    const match = participacao.match(/(\\d+(?:\\.\\d+)?)/);\n    return match ? parseFloat(match[1]) : 0;\n  }\n\n  /**\n   * Identificar automaticamente o sócio principal baseado em critérios\n   */\n  identificarSocioPrincipalAutomaticamente(): void {\n    if (!this.sociosDetalhados || this.sociosDetalhados.length === 0) {\n      console.log('Nenhum sócio encontrado para identificação automática');\n      return;\n    }\n\n    let socioPrincipal = null;\n    let criterioUsado = '';\n\n    // 1. Buscar por cargo (prioridade alta)\n    const cargosPrioritarios = [\n      'presidente',\n      'diretor presidente',\n      'ceo',\n      'diretor',\n      'administrador',\n      'sócio administrador',\n      'gerente',\n      'representante legal'\n    ];\n\n    for (const cargo of cargosPrioritarios) {\n      socioPrincipal = this.sociosDetalhados.find(s =>\n        s.cargo?.toLowerCase().includes(cargo)\n      );\n      if (socioPrincipal) {\n        criterioUsado = `cargo: ${socioPrincipal.cargo}`;\n        break;\n      }\n    }\n\n    // 2. Se não encontrou por cargo, buscar por maior participação\n    if (!socioPrincipal) {\n      const socioComMaiorParticipacao = this.sociosDetalhados.reduce((prev, current) => {\n        const prevParticipacao = this.extrairPorcentagem(prev.participacao) || 0;\n        const currentParticipacao = this.extrairPorcentagem(current.participacao) || 0;\n        return currentParticipacao > prevParticipacao ? current : prev;\n      });\n\n      const maiorParticipacao = this.extrairPorcentagem(socioComMaiorParticipacao.participacao);\n      if (maiorParticipacao > 0) {\n        socioPrincipal = socioComMaiorParticipacao;\n        criterioUsado = `maior participação: ${socioComMaiorParticipacao.participacao}`;\n      }\n    }\n\n    // 3. Se ainda não encontrou, buscar por qualificação\n    if (!socioPrincipal) {\n      const qualificacoesPrioritarias = ['administrador', 'diretor', 'representante'];\n      for (const qualificacao of qualificacoesPrioritarias) {\n        socioPrincipal = this.sociosDetalhados.find(s =>\n          s.qualificacao?.toLowerCase().includes(qualificacao)\n        );\n        if (socioPrincipal) {\n          criterioUsado = `qualificação: ${socioPrincipal.qualificacao}`;\n          break;\n        }\n      }\n    }\n\n    // 4. Se ainda não encontrou, pegar o primeiro da lista\n    if (!socioPrincipal) {\n      socioPrincipal = this.sociosDetalhados[0];\n      criterioUsado = 'primeiro da lista';\n    }\n\n    // Marcar automaticamente como principal\n    if (socioPrincipal) {\n      console.log(`Sócio principal identificado automaticamente: ${socioPrincipal.nome} (critério: ${criterioUsado})`);\n      this.marcarComoPrincipal(socioPrincipal);\n\n      // Adicionar propriedade para mostrar que foi selecionado automaticamente\n      socioPrincipal.selecionadoAutomaticamente = true;\n      socioPrincipal.criterioSelecao = criterioUsado;\n    }\n  }\n\n  /**\n   * Marcar sócio como contato principal\n   */\n  marcarComoPrincipal(socioSelecionado: any): void {\n    // Desmarcar todos os outros e limpar flags de seleção automática\n    this.sociosDetalhados.forEach(socio => {\n      socio.principal = false;\n      if (socio !== socioSelecionado) {\n        socio.selecionadoAutomaticamente = false;\n        socio.criterioSelecao = null;\n      }\n    });\n\n    // Marcar o selecionado\n    socioSelecionado.principal = true;\n\n    // Se não foi seleção automática, marcar como seleção manual\n    if (!socioSelecionado.selecionadoAutomaticamente) {\n      socioSelecionado.selecionadoManualmente = true;\n    }\n\n    // Atualizar o nome do responsável do lead\n    this.lead.nomeResponsavel = socioSelecionado.nome;\n\n    // Marcar que um parceiro foi selecionado\n    this.parceirSelecionado = true;\n    this.erro = ''; // Limpa qualquer erro de validação\n\n    console.log('Sócio marcado como principal:', socioSelecionado.nome);\n    console.log('Nome responsável atualizado para:', this.lead.nomeResponsavel);\n\n    // Log da seleção inteligente\n    if (socioSelecionado.scoreAnalise) {\n      console.log(`Score do sócio selecionado: ${socioSelecionado.scoreAnalise}/100`);\n      console.log(`Motivo da seleção: ${socioSelecionado.motivoSelecao}`);\n    }\n  }\n\n  /**\n   * Retorna o sócio marcado como principal\n   */\n  getSocioPrincipal(): any {\n    return this.sociosDetalhados?.find(socio => socio.principal);\n  }\n\n  /**\n   * Formatar score de confiança para exibição\n   */\n  getScoreClasse(score: number): string {\n    if (score >= 80) return 'badge-success';\n    if (score >= 60) return 'badge-warning';\n    if (score >= 40) return 'badge-info';\n    return 'badge-secondary';\n  }\n\n  /**\n   * Formatar motivo de seleção para exibição mais amigável\n   */\n  formatarMotivoSelecao(motivo: string): string {\n    if (!motivo) return '';\n\n    // Capitalizar primeira letra e melhorar formatação\n    return motivo.charAt(0).toUpperCase() + motivo.slice(1);\n  }\n\n\n  /**\n   * Buscar empresa no Google para encontrar informações adicionais\n   */\n  buscarEmpresaNoGoogle(): void {\n    console.log('🔍 buscarEmpresaNoGoogle called - lead.empresa:', this.lead.empresa);\n\n    if (!this.lead.empresa?.trim()) {\n      console.warn('Nome da empresa não informado para busca no Google');\n      return;\n    }\n\n    let termoBusca = this.lead.empresa.trim();\n\n    // Adicionar cidade se disponível para busca mais precisa\n    if (this.lead.cidade?.trim()) {\n      termoBusca += ` ${this.lead.cidade.trim()}`;\n    }\n\n    // URL otimizada para Google Business (mostra telefone, endereço, etc.)\n    const termoCodificado = encodeURIComponent(termoBusca);\n    const urlGoogle = `https://www.google.com/search?q=${termoCodificado}`;\n\n    console.log('Abrindo busca Google para:', termoBusca);\n    console.log('URL:', urlGoogle);\n\n    // Abrir em nova aba para não perder dados do formulário\n    window.open(urlGoogle, '_blank');\n  }\n\n  // ===== MÉTODOS DO WIZARD =====\n\n  /**\n   * Navegar para próximo passo\n   */\n  nextStep(): void {\n    // Sempre valida antes de tentar avançar\n    if (!this.canAdvance()) {\n      this.mostrarMensagemValidacao();\n      return;\n    }\n    \n    // Se passou na validação, avança\n    this.saveCurrentStepData();\n    this.currentStep++;\n    this.etapaFoiPulada = false; // Reset flag de pular\n    this.erro = ''; // Limpar erro ao avançar com sucesso\n    console.log('Avançando para passo:', this.currentStep);\n  }\n\n  /**\n   * Voltar para passo anterior\n   */\n  prevStep(): void {\n    if (this.currentStep > 1) {\n      this.saveCurrentStepData();\n      this.currentStep--;\n      console.log('Voltando para passo:', this.currentStep);\n    }\n  }\n\n  /**\n   * Ir para passo específico\n   */\n  goToStep(step: number): void {\n    if (step >= 1 && step <= this.totalSteps) {\n      this.saveCurrentStepData();\n      this.currentStep = step;\n      console.log('Indo para passo:', this.currentStep);\n    }\n  }\n\n  /**\n   * Obter lista de campos obrigatórios faltantes\n   */\n  getCamposFaltantes(): string[] {\n    const faltantes = [];\n    \n    if (!this.lead.nomeResponsavel?.trim()) {\n      faltantes.push('Nome do Responsável');\n    }\n    if (!this.lead.empresa?.trim()) {\n      faltantes.push('Nome da Empresa');\n    }\n    if (!this.lead.cidade?.trim()) {\n      faltantes.push('Cidade');\n    }\n    if (!this.lead.instagramHandle?.trim()) {\n      faltantes.push('Instagram');\n    }\n    \n    // Para o telefone, verifica tanto o campo formatado quanto o limpo\n    if (this.telefoneFormatado) {\n      this.lead.telefone = this.limparTelefone(this.telefoneFormatado);\n    }\n    if (!this.lead.telefone?.trim()) {\n      faltantes.push('Telefone');\n    }\n    \n    return faltantes;\n  }\n\n  /**\n   * Verificar se pode avançar para próximo passo\n   */\n  canAdvance(): boolean {\n    switch (this.currentStep) {\n      case 1: // Extrair Dados\n        // Garante que o telefone está atualizado antes de validar\n        if (this.telefoneFormatado) {\n          this.lead.telefone = this.limparTelefone(this.telefoneFormatado);\n        }\n        return !!(this.lead.nomeResponsavel && this.lead.empresa && this.lead.cidade && this.lead.instagramHandle && this.lead.telefone?.trim());\n      case 2: // Buscar Links\n        // Deve ter analisado website OU não ter website OU ter pulado a etapa\n        return this.websiteAnalisado || !this.lead.website || this.etapaFoiPulada;\n      case 3: // Descobrir CNPJ\n        // Se encontrou CNPJs, deve ter selecionado um OU confirmado explicitamente que não quer nenhum\n        if (this.cnpjsEncontrados.length > 0) {\n          return !!this.lead.cnpj || this.cnpjRecusadoExplicitamente;\n        }\n        // Se não encontrou CNPJs ou não buscou, pode avançar normalmente\n        return this.cnpjBuscado || !this.lead.empresa || !this.lead.cidade || this.etapaFoiPulada;\n      case 4: // Buscar Sócios\n        // Pode avançar se selecionou um sócio OU se não tem CNPJ OU se pulou a etapa\n        return this.parceirSelecionado || !this.lead.cnpj || this.etapaFoiPulada;\n      case 5: // Finalizar\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  /**\n   * Verificar se passo foi completado\n   */\n  isStepCompleted(step: number): boolean {\n    switch (step) {\n      case 1:\n        return !!(this.lead.nomeResponsavel && this.lead.empresa && this.lead.cidade);\n      case 2:\n        return this.linksEncontrados.length > 0;\n      case 3:\n        return !!this.lead.cnpj;\n      case 4:\n        return this.parceirSelecionado; // Completo se um parceiro foi selecionado\n      default:\n        return false;\n    }\n  }\n\n  /**\n   * Obter classe CSS do passo\n   */\n  getStepClass(step: number): string {\n    if (step === this.currentStep) return 'active';\n    if (step < this.currentStep || this.isStepCompleted(step)) return 'completed';\n    return 'pending';\n  }\n\n  /**\n   * Salvar dados do passo atual\n   */\n  saveCurrentStepData(): void {\n    switch (this.currentStep) {\n      case 1:\n        this.wizardData.dadosBasicos = {\n          nomeResponsavel: this.lead.nomeResponsavel,\n          empresa: this.lead.empresa,\n          cidade: this.lead.cidade,\n          telefone: this.lead.telefone,\n          instagramHandle: this.lead.instagramHandle,\n          website: this.lead.website,\n          bioInsta: this.lead.bioInsta\n        };\n        break;\n      case 2:\n        this.wizardData.linksEncontrados = [...this.linksEncontrados];\n        break;\n      case 3:\n        this.wizardData.cnpjSelecionado = this.lead.cnpj ? {\n          cnpj: this.lead.cnpj,\n          empresa: this.lead.empresa,\n          endereco: this.lead.endereco\n        } : null;\n        break;\n      case 4:\n        this.wizardData.sociosEncontrados = [...this.sociosDetalhados];\n        break;\n      case 5:\n        this.wizardData.configuracoes = {\n          etapa: this.lead.etapa,\n          origem: this.lead.origem,\n          segmento: this.lead.segmento,\n          observacoes: this.lead.observacoes,\n          observacoesSocios: this.lead.observacoesSocios,\n          sincronizarBitrix: this.sincronizarBitrix\n        };\n        break;\n    }\n    console.log('Dados salvos do passo', this.currentStep, ':', this.wizardData);\n  }\n\n  /**\n   * Pular etapa atual\n   */\n  skipStep(): void {\n    if (this.currentStep < this.totalSteps && this.canSkipCurrentStep()) {\n      console.log('Pulando passo:', this.currentStep);\n      this.etapaFoiPulada = true;\n      this.nextStep();\n    }\n  }\n\n  /**\n   * Obter título do passo atual\n   */\n  getCurrentStepTitle(): string {\n    switch (this.currentStep) {\n      case 1: return 'Extrair Dados do Instagram';\n      case 2: return 'Buscar Links do Website';\n      case 3: return 'Descobrir CNPJ da Empresa';\n      case 4: return 'Buscar Sócios da Empresa';\n      case 5: return 'Finalizar Lead';\n      default: return 'Passo Desconhecido';\n    }\n  }\n\n  /**\n   * Verificar se pode pular passo atual\n   */\n  canSkipCurrentStep(): boolean {\n    return this.currentStep === 2 || this.currentStep === 3 || this.currentStep === 4; // Links, CNPJ e Sócios são opcionais\n  }\n\n  /**\n   * Mostrar mensagem de validação específica\n   */\n  mostrarMensagemValidacao(): void {\n    switch (this.currentStep) {\n      case 1: // Extrair Dados\n        const camposFaltantes = this.getCamposFaltantes();\n        if (camposFaltantes.length > 0) {\n          this.erro = `Preencha os campos obrigatórios: ${camposFaltantes.join(', ')}`;\n        }\n        break;\n      case 2:\n        if (this.lead.website && !this.websiteAnalisado) {\n          this.erro = 'Você deve analisar o website antes de continuar ou pular esta etapa.';\n        }\n        break;\n      case 3:\n        if (this.cnpjsEncontrados.length > 0 && !this.lead.cnpj && !this.cnpjRecusadoExplicitamente) {\n          this.erro = 'Foram encontrados CNPJs para esta empresa. Selecione um ou confirme que nenhum corresponde ao lead.';\n        } else if (this.lead.empresa && this.lead.cidade && !this.cnpjBuscado) {\n          this.erro = 'Você deve buscar o CNPJ da empresa antes de continuar ou pular esta etapa.';\n        }\n        break;\n      case 4:\n        if (this.lead.cnpj && this.sociosBuscados && !this.parceirSelecionado) {\n          this.erro = 'Você deve selecionar um sócio como contato principal antes de continuar ou pular esta etapa.';\n        } else if (this.lead.cnpj && !this.sociosBuscados) {\n          this.erro = 'Você deve buscar os sócios da empresa antes de continuar ou pular esta etapa.';\n        }\n        break;\n      default:\n        this.erro = 'Complete os dados obrigatórios para continuar.';\n    }\n  }\n\n  /**\n   * Marcar website como analisado\n   */\n  marcarWebsiteAnalisado(): void {\n    this.websiteAnalisado = true;\n    this.erro = ''; // Limpa erro se houver\n  }\n\n  /**\n   * Marcar CNPJ como buscado\n   */\n  marcarCnpjBuscado(): void {\n    this.cnpjBuscado = true;\n    this.erro = ''; // Limpa erro se houver\n  }\n\n}\n", "import { Injectable } from '@angular/core';\nimport { Observable, of } from 'rxjs';\nimport { ContextoConversa } from './conversas.service';\n\nexport interface SugestaoMensagem {\n  texto: string;\n  palavraChave?: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SugestoesService {\n  // Mapa de palavras-chave para sugestões de mensagens\n  private mapaPalavrasChave: Record<string, string[]> = {\n    'preço': [\n      'Nossos planos começam a partir de R$99/mês. Posso explicar os detalhes de cada um?',\n      'Temos condições especiais neste mês. Posso enviar uma proposta personalizada?'\n    ],\n    'demo': [\n      'Ótimo! Posso agendar uma demonstração para amanhã às 14h?',\n      'Podemos fazer uma demonstração hoje mesmo. Qual horário seria melhor para você?'\n    ],\n    'dúvida': [\n      'Entendo sua dúvida. Nosso sistema resolve isso de forma simples, permitindo que você...',\n      'É uma dúvida comum. Vou te explicar como funciona:'\n    ],\n    'concorrente': [\n      'Diferente da concorrência, nosso sistema oferece integração completa com WhatsApp e mais recursos de automação.',\n      'Nosso diferencial é a facilidade de uso e suporte 24/7, algo que a concorrência não oferece.'\n    ],\n    'interessado': [\n      'Que ótimo! Posso lhe mostrar uma demonstração agora mesmo?',\n      'Excelente! Vamos agendar uma reunião para mostrar como podemos atender suas necessidades?'\n    ],\n    'valor': [\n      'O investimento é bem acessível comparado ao retorno que você terá. Posso detalhar melhor?',\n      'Nosso sistema se paga em poucos meses pelo aumento de conversão que proporciona.'\n    ],\n    'problema': [\n      'Entendo o desafio que você está enfrentando. Nossa solução foi desenvolvida especificamente para resolver isso.',\n      'Muitos clientes tinham esse mesmo problema e conseguiram resultados incríveis com nossa plataforma.'\n    ],\n    'prazo': [\n      'A implementação é rápida, em média 3 dias úteis para ter tudo funcionando.',\n      'Podemos iniciar imediatamente após a contratação. Todo o processo leva menos de uma semana.'\n    ]\n  };\n\n  // Sugestões por etapa do funil\n  private sugestoesPorEtapa: Record<string, Record<string, string[]>> = {\n    'Prospecção': {\n      'Consultivo': [\n        'Oi Ricardo! Que legal conhecer você! Vi que tem uma pizzaria. Quantas mesas vocês atendem por dia? Nosso cardápio digital pode multiplicar isso!',\n        'Olá! Adoro pizzarias familiares. Vocês já pensaram em como seria se cada mesa pudesse fazer pedidos sem chamar o garçom? Parece ficção científica, né?',\n        'Ricardo, parabéns pela pizzaria! Uma pergunta: seus clientes reclamam da demora no atendimento nos fins de semana?'\n      ],\n      'Empático': [\n        'Olá Ricardo! Como está o movimento na pizzaria? Sei que gerenciar o fluxo de clientes pode ser desafiador, especialmente em horários de pico!',\n        'Oi! Adoro pizza! Imagino que o maior desafio seja manter a qualidade do atendimento quando a casa está cheia, né?',\n        'Olá! Sou apaixonado por negócios familiares como pizzarias. Conte um pouco sobre como começou sua jornada!'\n      ]\n    },\n    'Qualificação': {\n      'Consultivo': [\n        'Entendi suas necessidades! Nossa solução permite que os clientes façam pedidos diretamente pelo celular escaneando um QR code na mesa. Isso reduz erros em 70% e aumenta a rotatividade das mesas.',\n        'Baseado no seu volume de 30 mesas, nossa plataforma pode aumentar sua eficiência em até 40%. O melhor é que você paga apenas uma pequena taxa mensal, sem investimento inicial.',\n        'Nossos clientes relatam um aumento médio de 22% no valor do ticket quando usam nosso cardápio digital. Os adicionais são muito mais fáceis de vender!'\n      ],\n      'Técnico': [\n        'Nossa plataforma integra com 95% dos sistemas de PDV do mercado, incluindo o que você usa. A implementação leva apenas 24 horas e não precisa de nenhum hardware adicional.',\n        'A sincronização com seu estoque é em tempo real. Quando um item acaba, ele é automaticamente removido do cardápio, evitando frustração dos clientes.',\n        'Nossas APIs são abertas e documentadas, permitindo integração com qualquer sistema de gestão ou contabilidade que você já use.'\n      ]\n    },\n    'Objeção': {\n      'Consultivo': [\n        'Entendo sua preocupação com o custo. A maioria dos nossos clientes recupera o investimento em menos de 45 dias. Uma pizzaria do seu porte economiza em média R$3.200/mês só com redução de erros e otimização da equipe.',\n        'O plano mais básico custa menos que um delivery por dia. E você pode fazer um teste gratuito por 30 dias para comprovar o retorno antes de decidir.',\n        'Comparado ao custo de contratar mais um garçom (salário + encargos + benefícios), nossa solução é 80% mais econômica e funciona 24/7 sem reclamar!'\n      ],\n      'Técnico': [\n        'Nossa plataforma não exige compra de tablets ou hardware. Os clientes usam o próprio celular, e oferecemos opções flexíveis de pagamento, incluindo mensalidade sem contrato de fidelidade.',\n        'Você sabia que restaurantes com sistemas de autoatendimento reduzem o custo operacional em até 30%? Nossos relatórios detalhados permitem identificar todas as economias geradas.',\n        'Nossa API processa mais de 500 mil pedidos por dia com 99.99% de uptime garantido em contrato. O suporte técnico está disponível 24/7 sem custo adicional.'\n      ]\n    },\n    'Fechamento': {\n      'Consultivo': [\n        'Excelente! Vamos formalizar? Posso enviar o contrato hoje mesmo e agendar a implementação para a próxima semana. Assim você já começa a ver resultados antes do fim do mês.',\n        'Com base na nossa conversa, recomendo o plano Profissional. Ele inclui todas as funcionalidades que você precisa agora, e você pode fazer upgrade quando sua operação crescer.',\n        'Para começarmos, preciso apenas que você preencha este formulário com os dados básicos do restaurante. Nossa equipe fará todo o resto!'\n      ],\n      'Urgente': [\n        'Temos uma promoção especial que termina hoje: 30% de desconto nos 3 primeiros meses. Posso garantir esse valor se fecharmos agora.',\n        'Consigo antecipar sua implementação para esta semana se confirmarmos hoje. Assim você já aproveita o movimento do final de semana com o sistema rodando!',\n        'Acabei de confirmar com nosso time de implementação que podemos fazer a configuração expressa em 24h se assinarmos hoje. O que acha?'\n      ]\n    }\n  };\n\n  // Sugestões genéricas para quando não há contexto específico\n  private sugestoesGenericas: string[] = [\n    'Olá! Como posso ajudar você hoje?',\n    'Temos várias soluções que podem atender suas necessidades. Podemos conversar mais sobre seu negócio?',\n    'Gostaria de agendar uma demonstração para conhecer melhor nossa plataforma?',\n    'Estou à disposição para esclarecer qualquer dúvida sobre nossos serviços.',\n    'Nossos clientes têm obtido resultados excelentes com nossa solução. Posso compartilhar alguns casos de sucesso?'\n  ];\n\n  constructor() { }\n\n  /**\n   * Gera sugestões com base no contexto da conversa e parâmetros de configuração\n   */\n  gerarSugestoes(contexto: ContextoConversa): Observable<SugestaoMensagem[]> {\n    console.log('SugestoesService - gerarSugestoes, contexto:', contexto);\n    \n    try {\n      const sugestoes: SugestaoMensagem[] = [];\n      const etapa = contexto.etapaFunil || 'Prospecção';\n      const tom = contexto.tomConversa || 'Consultivo';\n      \n      // Verifica se temos sugestões específicas para esta combinação de etapa e tom\n      if (this.sugestoesPorEtapa[etapa] && this.sugestoesPorEtapa[etapa][tom]) {\n        this.sugestoesPorEtapa[etapa][tom].forEach(texto => {\n          sugestoes.push({ texto });\n        });\n      }\n      \n      // Se não encontrou sugestões específicas para o tom, tenta usar qualquer tom disponível\n      if (sugestoes.length === 0 && this.sugestoesPorEtapa[etapa]) {\n        const tomsDisponiveis = Object.keys(this.sugestoesPorEtapa[etapa]);\n        if (tomsDisponiveis.length > 0) {\n          const tomAlternativo = tomsDisponiveis[0];\n          this.sugestoesPorEtapa[etapa][tomAlternativo].forEach(texto => {\n            sugestoes.push({ texto });\n          });\n        }\n      }\n      \n      // Se ainda não tem sugestões, verifica palavras-chave nas mensagens\n      if (sugestoes.length === 0 && contexto.mensagens && contexto.mensagens.length > 0) {\n        const ultimasMensagens = contexto.mensagens\n          .slice(-3)\n          .map(msg => msg.texto.toLowerCase());\n        \n        for (const [palavraChave, respostas] of Object.entries(this.mapaPalavrasChave)) {\n          for (const mensagem of ultimasMensagens) {\n            if (mensagem.includes(palavraChave)) {\n              respostas.forEach(resposta => {\n                sugestoes.push({\n                  texto: resposta,\n                  palavraChave\n                });\n              });\n              break;\n            }\n          }\n        }\n      }\n      \n      // Se ainda não tem sugestões, usa genéricas\n      if (sugestoes.length === 0) {\n        this.sugestoesGenericas.forEach(texto => {\n          sugestoes.push({ texto });\n        });\n      }\n      \n      // Limita a 3 sugestões e remove duplicatas\n      const sugestoesFiltradas = this.removerDuplicatas(sugestoes).slice(0, 3);\n      console.log('Sugestões geradas:', sugestoesFiltradas);\n      \n      return of(sugestoesFiltradas);\n    } catch (error) {\n      console.error('Erro ao gerar sugestões:', error);\n      // Em caso de erro, retorna algumas sugestões padrão\n      return of([\n        { texto: 'Olá! Como posso ajudar?' },\n        { texto: 'Gostaria de saber mais sobre nossos produtos?' },\n        { texto: 'Podemos agendar uma demonstração se preferir.' }\n      ]);\n    }\n  }\n\n  /**\n   * Remove sugestões duplicadas com base no texto\n   */\n  private removerDuplicatas(sugestoes: SugestaoMensagem[]): SugestaoMensagem[] {\n    const textosUnicos = new Set<string>();\n    return sugestoes.filter(sugestao => {\n      if (textosUnicos.has(sugestao.texto)) {\n        return false;\n      }\n      textosUnicos.add(sugestao.texto);\n      return true;\n    });\n  }\n\n  /**\n   * Método para implementar a versão com IA (a ser desenvolvido)\n   */\n  gerarSugestoesComIA(contexto: ContextoConversa): Observable<SugestaoMensagem[]> {\n    // Placeholder para futura implementação com IA\n    // Por enquanto, usa a versão offline\n    return this.gerarSugestoes(contexto);\n  }\n}", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatRippleModule } from '@angular/material/core';\nimport { GridModule } from '@progress/kendo-angular-grid';\nimport { MaskedTextBoxModule } from '@progress/kendo-angular-inputs';\n\nimport { CrmRoutingModule } from './crm-routing.module';\nimport { CrmHomeComponent } from './crm-home/crm-home.component';\nimport { TelaCrmLeadsComponent } from '../tela-crm-leads/tela-crm-leads.component';\nimport { PromptSugestoesComponent } from './prompt-sugestoes/prompt-sugestoes.component';\nimport { LeadInfoComponent } from './lead-info/lead-info.component';\nimport { ConversasService } from './services/conversas.service';\nimport { SugestoesService } from './services/sugestoes.service';\nimport { LeadService } from './services/lead.service';\nimport { CrmEmpresaService } from './services/crm-empresa.service';\nimport { LeadCrudComponent } from './lead-crud/lead-crud.component';\nimport { CrmEmpresaCrudComponent } from './crm-empresa-crud/crm-empresa-crud.component';\nimport { NovoLeadComponent } from './novo-lead/novo-lead.component';\nimport { InstagramDataService } from './services/instagram-data.service';\n\n@NgModule({\n  declarations: [\n    CrmHomeComponent,\n    PromptSugestoesComponent,\n    LeadInfoComponent,\n    LeadCrudComponent,\n    CrmEmpresaCrudComponent,\n    NovoLeadComponent\n  ],\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatButtonModule,\n    MatCardModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule,\n    MatRippleModule,\n    CrmRoutingModule,\n    GridModule,\n    MaskedTextBoxModule\n  ],\n  providers: [\n    ConversasService,\n    SugestoesService,\n    LeadService,\n    CrmEmpresaService,\n    InstagramDataService\n  ]\n})\nexport class CrmModule { }\n"], "x_google_ignoreList": [0, 1, 2, 3]}