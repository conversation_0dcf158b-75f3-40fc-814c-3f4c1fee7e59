{"version": 3, "file": "978.790fba0ea34261e5.js", "mappings": "+QAuGgBA,oCACEA,uDACFA,kDACAA,oCACCA,+EACDA,qDANFA,uCACEA,yCAGAA,yCAGFA,kGANQA,0EAGAA,6HAQVA,qCAA8C,OAAGA,sBAAQA,uEAARA,sFAEjDA,qCAAyD,OACnDA,uGAA2EA,uDAEjFA,0CAAyHA,0CAAiBA,qEAA9DA,qHC9GjF,IAAMC,EAAoB,MAA3B,MAAOA,EAOXC,YAAoBC,qBAHpBC,aAAe,EAG+B,CAE9CC,WACA,CAEAC,mBACEC,OAAOC,SAAS,EAAGC,SAASC,KAAKC,aAAe,KAChDP,KAAKQ,KAAKC,cAAcC,OAC1B,CAEAC,kBACQX,KAAKY,KACTZ,KAAKa,IAAIC,OACTd,KAAKe,UAAW,EAChBf,KAAKD,WAAWiB,KAAK,oBAAqBhB,KAAKiB,SAASC,YAAYC,KAAOC,IACzEpB,KAAKe,UAAW,EACbK,EAASC,QACVrB,KAAKiB,QAAQK,OAAQ,EAErBtB,KAAKY,KAAOQ,EAASG,WAEtBC,MAAOZ,IACRZ,KAAKe,UAAW,EACff,KAAKY,KAAO,+BAGhBZ,KAAKY,KAAO,qCAEf,+CAnCWf,GAAoBD,sFAApBC,EAAoB4B,ypEDTjC7B,sCAA+B,UAA/BA,CAA+B,UAA/BA,CAA+B,WAIvBA,iCACFA,2BAEAA,sCAAiB,SAEbA,iCACAA,sCAAwB,QAClBA,mGAAmEA,2BACvEA,mCAAGA,8IACoBA,2BAEvBA,0CAA2DA,iDAAS8B,oBAAkB,GAAE9B,sCAAYA,iCAI1GA,uCAAiB,UAEbA,mCACAA,uCAAwB,SAClBA,0DAA6BA,2BACjCA,mCAAGA,gNACkGA,2BACrGA,0CAA0DA,iDAAS8B,oBAAkB,GAAE9B,sCAAYA,iCAIzGA,wCAAiB,UAEbA,mCACAA,uCAAwB,SAClBA,uEAA6CA,2BACjDA,oCAAI,QAAJA,CAAI,aAC4BA,iCAAqBA,2BAAOA,gDAAkBA,2BAC5EA,oCAAI,aAAyBA,iCAAqBA,2BAAQA,qDAAuBA,2BACjFA,oCAAI,aAAyBA,iCAAqBA,2BAAQA,4DAAwBA,2BAClFA,oCAAI,aAAyBA,iCAAqBA,2BAAQA,yDAA2BA,2BACrFA,oCAAI,aAAyBA,iCAAqBA,2BAAQA,uDAA4BA,2BACtFA,oCAAI,aAAyBA,iCAAqBA,2BAAQA,4DAAiCA,6BAG7FA,0CAA0DA,iDAAS8B,oBAAkB,GAAE9B,sCAAYA,iCAKzGA,wCAAiB,UAEbA,mCACAA,uCAAwB,SAClBA,mFAAyDA,2BAC7DA,mCAAGA,2JACqBA,2BACxBA,0CAA0DA,iDAAS8B,oBAAkB,GAAE9B,sCAAYA,iCAOzGA,wCAAiB,UAEbA,mCACAA,uCAAwB,SAClBA,qEAAwCA,2BAC5CA,mCAAIA,kKAA8HA,2BAElIA,0CAA0DA,iDAAS8B,oBAAkB,GAAE9B,sCAAYA,iCAMzGA,2CAA4B,SAA5BA,CAA4B,iBAESA,oDAAY8B,YAAU,GACrD9B,uCAAwBA,6GAAgFA,2BACxGA,+BACAA,wCAAwB,kBACTA,6EAAbA,2BACAA,wCAA8B,QACvBA,kDAAkBA,+BAG3BA,wCAAwB,kBACNA,gFAAhBA,2BACAA,wCAA8B,QACvBA,qDAAqBA,+BAG9BA,wCAAwB,kBACRA,8EAAdA,2BACAA,yCAA8B,SACvBA,qDAAoBA,+BAG7BA,yCAAwB,mBACOA,iFAA7BA,2BAEAA,2CASFA,2BACAA,gCAEAA,yCAEAA,yCAGAA,8CAKFA,8GA9CyDA,sHAIxCA,0EAMGA,6EAMFA,2EAMeA,8EAEEA,iEAY7BA,+DAEAA,wEAG+FA,s5LC9GlGC,CAAoB,sECsGjBD,oCACEA,uDACFA,kDACAA,oCACEA,+EACFA,qDANFA,uCACEA,yCAGAA,yCAGFA,kGANQA,0EAGAA,6HAQVA,qCAA8C,OAAGA,sBAAQA,uEAARA,sFAEjDA,qCAAyD,OACpDA,uGAA2EA,uDAEhFA,0CAAyHA,0CAAiBA,qEAA9DA,qHC1HlF+B,EAAkB,CACtB,CAACC,KAAM,aAAcC,UCGiB,MAAlC,MAAOC,EAQXhC,YAAoBC,qBAJpBC,aAAe,EAI+B,CAE9CC,WACA,CAEAC,mBACEC,OAAOC,SAAS,EAAGC,SAASC,KAAKC,aAAe,KAChDP,KAAKQ,KAAKC,cAAcC,OAC1B,CAGAC,kBACSX,KAAKY,KACTZ,KAAKa,IAAIC,OACVd,KAAKe,UAAW,EAChBf,KAAKD,WAAWiB,KAAK,oBAAqBhB,KAAKiB,SAASC,YAAYC,KAAOC,IACzEpB,KAAKe,UAAW,EACbK,EAASC,QACVrB,KAAKiB,QAAQK,OAAQ,EAErBtB,KAAKY,KAAOQ,EAASG,WAEtBC,MAAOZ,IACRZ,KAAKe,UAAW,EAChBf,KAAKY,KAAO,+BAGdZ,KAAKY,KAAO,qCAEhB,+CArCWkB,GAA2BlC,sFAA3BkC,EAA2BL,grEFTxC7B,sCAA+B,UAA/BA,CAA+B,UAA/BA,CAA+B,WAIvBA,iCACFA,2BAEAA,sCAAiB,WAITA,iCACAA,sCAAwB,QAElBA,0EAA+CA,2BACnDA,mCAAGA,2LAAkJA,2BAErJA,0CAA2DA,iDAAS8B,oBAAkB,GAAE9B,sCAAYA,iCAW9GA,wCAAiB,UAEbA,mCACAA,uCAAwB,SAClBA,4DAAkCA,2BACtCA,mCAAGA,2NAC8GA,2BACjHA,0CAA0DA,iDAAS8B,oBAAkB,GAAE9B,sCAAYA,iCAIzGA,wCAAiB,UAEbA,mCACAA,uCAAwB,SAClBA,uEAA6CA,2BACjDA,oCAAI,QAAJA,CAAI,aAC4BA,iCAAqBA,2BAAOA,0DAA4BA,2BACtFA,oCAAI,aAAyBA,iCAAqBA,2BAAQA,qDAAuBA,2BACjFA,oCAAI,aAAyBA,iCAAqBA,2BAAQA,4DAAwBA,2BAClFA,oCAAI,aAAyBA,iCAAqBA,2BAAQA,yDAA2BA,2BACrFA,oCAAI,aAAyBA,iCAAqBA,2BAAQA,uDAA4BA,2BACtFA,oCAAI,aAAyBA,iCAAqBA,2BAAQA,mDAAwBA,6BAGpFA,0CAA0DA,iDAAS8B,oBAAkB,GAAE9B,sCAAYA,iCAKzGA,wCAAiB,UAEbA,mCACAA,uCAAwB,SAClBA,mFAAyDA,2BAC7DA,mCAAGA,uLAAuJA,2BAC1JA,0CAA0DA,iDAAS8B,oBAAkB,GAAE9B,sCAAYA,iCAOzGA,wCAAiB,UAEbA,mCACAA,uCAAwB,SAClBA,qEAAwCA,2BAC5CA,mCAAIA,mKAAkIA,2BAEtIA,0CAA0DA,iDAAS8B,oBAAkB,GAAE9B,sCAAYA,iCAMzGA,2CAA4B,SAA5BA,CAA4B,iBAESA,oDAAY8B,YAAU,GACrD9B,uCAAwBA,6GAAgFA,2BACxGA,+BACAA,wCAAwB,kBACTA,6EAAbA,2BACAA,wCAA8B,QACvBA,kDAAkBA,+BAG3BA,wCAAwB,kBACNA,gFAAhBA,2BACAA,wCAA8B,QACvBA,qDAAqBA,+BAG9BA,wCAAwB,kBACRA,8EAAdA,2BACAA,yCAA8B,SACvBA,qDAAoBA,+BAG7BA,yCAAwB,mBACOA,iFAA7BA,2BAEAA,2CASFA,2BACAA,gCAEAA,yCAEAA,yCAGAA,8CAKFA,8GA9CyDA,sHAIxCA,0EAMGA,6EAMFA,2EAMeA,8EAEEA,iEAY7BA,+DAEAA,wEAG+FA,siMEtHlGkC,CAA2B,MDFtC,CACEF,KAAM,GAAIC,UAAWhC,EACrBkC,SAAU,CACR,CAACH,KAAM,QAASC,UAAWhC,MAW1B,IAAMmC,EAAwB,MAA/B,MAAOA,kDAAwB,iDAAxBA,uDAHDC,cAAsBN,GACtBM,QAECD,CAAwB,KEFxBE,EAAiB,MAAxB,MAAOA,kDAAiB,iDAAjBA,uDALTC,eACAH,EACAI,QAGSF,CAAiB", "names": ["i0", "LandingpageComponent", "constructor", "httpClient", "this", "ngOnInit", "solicitarContato", "window", "scrollTo", "document", "body", "scrollHeight", "nome", "nativeElement", "focus", "onSubmit", "erro", "frm", "valid", "salvando", "post", "contato", "to<PERSON>romise", "then", "resposta", "sucesso", "salvo", "mensagem", "catch", "selectors", "ctx", "routes", "path", "component", "LandingPagePedidosComponent", "children", "LandingpageRoutingModule", "RouterModule", "LandingpageModule", "CommonModule", "FormsModule"], "sourceRoot": "webpack:///", "sources": ["./src/app/landingpage/landingpage.component.html", "./src/app/landingpage/landingpage.component.ts", "./src/app/landing-page-pedidos/landing-page-pedidos.component.html", "./src/app/landingpage/landingpage.routing.ts", "./src/app/landing-page-pedidos/landing-page-pedidos.component.ts", "./src/app/landingpage/landingpage.module.ts"], "sourcesContent": ["<div class=\"container-fluid  \">\n  <div class=\"row\">\n    <div class=\"col\">\n      <div  class=\"base-logo\">\n        <img  src=\"/assets/fidelidade/ladingpage/logo-promokit.svg\">\n      </div>\n\n      <div class=\"bg1\">\n        <div>\n          <img src=\"/assets/fidelidade/ladingpage/img-01.png\" align=\"right\">\n          <div class=\"promo-info\">\n            <h1>Programa de fidelidade digital, focado na sua relação com o cliente</h1>\n            <p>De um jeito simples e moderno, você fideliza seus clientes e ainda utiliza as features para\n              alavancar suas vendas</p>\n\n            <button class=\"btn btn-blue btn-rounded btn-lg shadow-lg \" (click)=\"solicitarContato()\">EXPERIMENTAR</button>\n          </div>\n        </div>\n      </div>\n      <div class=\"bg2\">\n        <div>\n          <img src=\"/assets/fidelidade/ladingpage/img-02.png\" align=\"left\">\n          <div class=\"promo-info\">\n            <h1>Diga adeus ao cartão de papel</h1>\n            <p>Implantar um programa de fidelidade agora é simples e barato. Além disso,\n              você pode utilizar diversos recursos exclusivos para envolver seus clientes e aumentar suas vendas.</p>\n            <button class=\"btn btn-blue btn-rounded btn-lg shadow-lg\" (click)=\"solicitarContato()\">EXPERIMENTAR</button>\n          </div>\n        </div>\n      </div>\n      <div class=\"bg3\">\n        <div>\n          <img src=\"/assets/fidelidade/ladingpage/img-03.png\" align=\"right\">\n          <div class=\"promo-info\">\n            <h1>Utilize nossas features e aumente suas vendas</h1>\n            <ul>\n              <li> <div class=\"check-group\"><i class=\"check\"></i></div> Gestão de contatos</li>\n              <li><div class=\"check-group\"><i class=\"check\"></i></div>  Relatórios inteligentes</li>\n              <li><div class=\"check-group\"><i class=\"check\"></i></div>  Notificações Automáticas</li>\n              <li><div class=\"check-group\"><i class=\"check\"></i></div>  Página personalizada mobile</li>\n              <li><div class=\"check-group\"><i class=\"check\"></i></div>  Campanhas via SMS e WhatsApp</li>\n              <li><div class=\"check-group\"><i class=\"check\"></i></div>  Link da usa empresa para WhatsApp</li>\n            </ul>\n\n            <button class=\"btn btn-blue btn-rounded btn-lg shadow-lg\" (click)=\"solicitarContato()\">EXPERIMENTAR</button>\n          </div>\n\n        </div>\n      </div>\n      <div class=\"bg4\">\n        <div>\n          <img src=\"/assets/fidelidade/ladingpage/img-04.png\" align=\"left\">\n          <div class=\"promo-info\">\n            <h1>Campanhas via SMS e Whatsapp conforme o perfil do cliente</h1>\n            <p>Com os filtros, é possível enviar mensagens para clientes de um determinado perfil, com avisos\n              ou promoções especiais</p>\n            <button class=\"btn btn-blue btn-rounded btn-lg shadow-lg\" (click)=\"solicitarContato()\">EXPERIMENTAR</button>\n          </div>\n\n        </div>\n\n\n      </div>\n      <div class=\"bg5\">\n        <div>\n          <img src=\"/assets/fidelidade/ladingpage/img-05.png\" align=\"right\">\n          <div class=\"promo-info\">\n            <h1>Planos flexíveis que cabem no seu bolso.</h1>\n            <p> A partir de R$ 99,00 você pode ter uma solução completa para fidelizar seus clientes e gerar mais resultados para sua empresa.</p>\n\n            <button class=\"btn btn-blue btn-rounded btn-lg shadow-lg\" (click)=\"solicitarContato()\">EXPERIMENTAR</button>\n          </div>\n\n        </div>\n\n      </div>\n      <div class=\"bg6\" #bgContato>\n        <div>\n          <form   novalidate #frm=\"ngForm\" (ngSubmit)=\"onSubmit()\" [ngClass]=\"{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}\">\n            <h1 class=\"text-center\">Solicite um representante e começe agora a fidelizar seus clientes e vender mais</h1>\n            <br>\n            <div class=\"form-group\">\n              <input #nome [(ngModel)]=\"contato.nome\"  name='nome' type=\"text\" class=\"form-control\" placeholder=\"Informe seu nome\" required>\n              <div class=\"invalid-feedback\">\n                <p  >Nome é obrigatório</p>\n              </div>\n            </div>\n            <div class=\"form-group\">\n              <input #empresa [(ngModel)]=\"contato.empresa\" name='empresa'  type=\"text\" class=\"form-control\" placeholder=\"Informe o nome da Empresa\" required>\n              <div class=\"invalid-feedback\">\n                <p  >Empresa é obrigatório</p>\n              </div>\n            </div>\n            <div class=\"form-group\">\n              <input #email [(ngModel)]=\"contato.email\"  name='email' type=\"email\" class=\"form-control\" placeholder=\"Informe seu email\" required>\n              <div class=\"invalid-feedback\">\n                <p  >E-mail é obrigatório</p>\n              </div>\n            </div>\n            <div class=\"form-group\">\n              <input  #telefone=\"ngModel\"  [(ngModel)]=\"contato.telefone\"  campoTelefone  name='telefone' minlength=\"10\"\n                     type=\"tel\" class=\"form-control\" placeholder=\"Informe seu telefone\" required>\n              <div class=\"invalid-feedback\" *ngIf=\"telefone.errors\">\n                <div *ngIf=\"telefone.errors.required\">\n                  Telefone é obrigatório\n                </div>\n                <div *ngIf=\"telefone.errors.campoTelefone && !telefone.errors.required\">\n                 Telefone somente números com DD, Ex.: 62981000001\n                </div>\n              </div>\n\n            </div>\n            <br>\n\n            <p *ngIf=\"erro\" class=\"text-danger pb-2 pb-2\"><b>{{erro}}</b></p>\n\n            <p *ngIf=\"contato.salvo\" class=\"text-success  pb-2 pb-2\">\n               <b>Contato salvo com sucesso! Aguarde que um representante entrará em contato.</b>\n            </p>\n            <button type=\"submit\" class=\"btn btn-success btn-lg btn-rounded shadow-lg \" [disabled]=\"salvando\" *ngIf=\"!contato.salvo\">Solicitar contato</button>\n\n\n\n\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';\nimport {NgForm} from \"@angular/forms\";\nimport {HttpClient} from \"@angular/common/http\";\n\n@Component({\n  selector: 'app-landingpage',\n  templateUrl: './landingpage.component.html',\n  styleUrls: ['./landingpage.component.scss']\n})\nexport class LandingpageComponent implements OnInit {\n  @ViewChild('bgContato', { static: true}) private bgContato: ElementRef;\n  @ViewChild('nome', {static: true}) nome: ElementRef;\n  @ViewChild('frm', {static: true}) frm: NgForm;\n  contato: any = {};\n  erro: string;\n  salvando: boolean;\n  constructor(private httpClient: HttpClient) { }\n\n  ngOnInit() {\n  }\n\n  solicitarContato() {\n    window.scrollTo(0, document.body.scrollHeight - 700);\n    this.nome.nativeElement.focus();\n  }\n\n  onSubmit() {\n   delete this.erro;\n   if(this.frm.valid){\n      this.salvando = true;\n      this.httpClient.post('/empresas/contato', this.contato).toPromise().then( (resposta: any ) => {\n        this.salvando = false;\n        if(resposta.sucesso){\n          this.contato.salvo = true;\n        }else{\n          this.erro = resposta.mensagem;\n        }\n      }).catch( erro => {\n        this.salvando = false;\n         this.erro = 'Falha ao salvar contato! '\n      })\n   } else {\n     this.erro = 'Verifique os campos obrigatórios'\n   }\n  }\n}\n", "<div class=\"container-fluid  \">\n  <div class=\"row\">\n    <div class=\"col\">\n      <div  class=\"base-logo\">\n        <img  src=\"/assets/fidelidade/ladingpage/logo-promokit.svg\">\n      </div>\n\n      <div class=\"bg1\">\n        <!--<div class=\"row mt-2 justify-content-between\">\n          <div class=\"col-12 col-md-3 \">-->\n            <div class=\"mt-md-5\">\n              <img src=\"/assets/fidelidade/ladingpage/Mockup.png\" align=\"right\">\n              <div class=\"promo-info\">\n\n                <h1>Aceite e registre pedidos sem sair do Whatsapp\t</h1>\n                <p>De um jeito simples e integrado ao WhatsApp Web, você recebe os pedidos, acompanha a realização e ainda utiliza funções para alavancar suas vendas</p>\n\n                <button class=\"btn btn-blue btn-rounded btn-lg shadow-lg \" (click)=\"solicitarContato()\">EXPERIMENTAR</button>\n              </div>\n              <!--<div class=\"col-12 col-md-9 mt-md-5 \">\n\n              </div>-->\n            </div>\n\n<!--          </div>\n\n        </div>-->\n      </div>\n      <div class=\"bg2\">\n        <div>\n          <img src=\"/assets/fidelidade/ladingpage/Mobile.png\" align=\"left\">\n          <div class=\"promo-info\">\n            <h1>Pare de acompanhar vendas no papel</h1>\n            <p>Implantar uma plataforma de pedidos e delivery agora é simples e barato.\n              Além disso, você pode utilizar diversos recursos exclusivos para envolver seus clientes e aumentar suas vendas.</p>\n            <button class=\"btn btn-blue btn-rounded btn-lg shadow-lg\" (click)=\"solicitarContato()\">EXPERIMENTAR</button>\n          </div>\n        </div>\n      </div>\n      <div class=\"bg3\">\n        <div>\n          <img src=\"/assets/fidelidade/ladingpage/img-03.png\" align=\"right\">\n          <div class=\"promo-info\">\n            <h1>Utilize nossas features e aumente suas vendas</h1>\n            <ul>\n              <li> <div class=\"check-group\"><i class=\"check\"></i></div> Gestão de pedidos e contatos</li>\n              <li><div class=\"check-group\"><i class=\"check\"></i></div>  Relatórios inteligentes</li>\n              <li><div class=\"check-group\"><i class=\"check\"></i></div>  Notificações Automáticas</li>\n              <li><div class=\"check-group\"><i class=\"check\"></i></div>  Página personalizada mobile</li>\n              <li><div class=\"check-group\"><i class=\"check\"></i></div>  Campanhas via SMS e WhatsApp</li>\n              <li><div class=\"check-group\"><i class=\"check\"></i></div>  Mensagens personalizadas</li>\n            </ul>\n\n            <button class=\"btn btn-blue btn-rounded btn-lg shadow-lg\" (click)=\"solicitarContato()\">EXPERIMENTAR</button>\n          </div>\n\n        </div>\n      </div>\n      <div class=\"bg4\">\n        <div>\n          <img src=\"/assets/fidelidade/ladingpage/img-04.png\" align=\"left\">\n          <div class=\"promo-info\">\n            <h1>Campanhas via SMS e Whatsapp conforme o perfil do cliente</h1>\n            <p>Registre o contato dos seus clientes de delivery e alcance-os novamente com mensagens personalizadas para cada perfil, com avisos e promoções especiais</p>\n            <button class=\"btn btn-blue btn-rounded btn-lg shadow-lg\" (click)=\"solicitarContato()\">EXPERIMENTAR</button>\n          </div>\n\n        </div>\n\n\n      </div>\n      <div class=\"bg5\">\n        <div>\n          <img src=\"/assets/fidelidade/ladingpage/img-05.png\" align=\"right\">\n          <div class=\"promo-info\">\n            <h1>Planos flexíveis que cabem no seu bolso.</h1>\n            <p> Nossos consultores analisam sua empresa e oferecem uma solução personalizada para fidelizar seus clientes e gerar mais resultados.</p>\n\n            <button class=\"btn btn-blue btn-rounded btn-lg shadow-lg\" (click)=\"solicitarContato()\">EXPERIMENTAR</button>\n          </div>\n\n        </div>\n\n      </div>\n      <div class=\"bg6\" #bgContato>\n        <div>\n          <form   novalidate #frm=\"ngForm\" (ngSubmit)=\"onSubmit()\" [ngClass]=\"{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}\">\n            <h1 class=\"text-center\">Solicite um representante e começe agora a fidelizar seus clientes e vender mais</h1>\n            <br>\n            <div class=\"form-group\">\n              <input #nome [(ngModel)]=\"contato.nome\"  name='nome' type=\"text\" class=\"form-control\" placeholder=\"Informe seu nome\" required>\n              <div class=\"invalid-feedback\">\n                <p  >Nome é obrigatório</p>\n              </div>\n            </div>\n            <div class=\"form-group\">\n              <input #empresa [(ngModel)]=\"contato.empresa\" name='empresa'  type=\"text\" class=\"form-control\" placeholder=\"Informe o nome da Empresa\" required>\n              <div class=\"invalid-feedback\">\n                <p  >Empresa é obrigatório</p>\n              </div>\n            </div>\n            <div class=\"form-group\">\n              <input #email [(ngModel)]=\"contato.email\"  name='email' type=\"email\" class=\"form-control\" placeholder=\"Informe seu email\" required>\n              <div class=\"invalid-feedback\">\n                <p  >E-mail é obrigatório</p>\n              </div>\n            </div>\n            <div class=\"form-group\">\n              <input  #telefone=\"ngModel\"  [(ngModel)]=\"contato.telefone\"  campoTelefone  name='telefone' minlength=\"10\"\n                      type=\"tel\" class=\"form-control\" placeholder=\"Informe seu telefone\" required>\n              <div class=\"invalid-feedback\" *ngIf=\"telefone.errors\">\n                <div *ngIf=\"telefone.errors.required\">\n                  Telefone é obrigatório\n                </div>\n                <div *ngIf=\"telefone.errors.campoTelefone && !telefone.errors.required\">\n                  Telefone somente números com DD, Ex.: 62981000001\n                </div>\n              </div>\n\n            </div>\n            <br>\n\n            <p *ngIf=\"erro\" class=\"text-danger pb-2 pb-2\"><b>{{erro}}</b></p>\n\n            <p *ngIf=\"contato.salvo\" class=\"text-success  pb-2 pb-2\">\n              <b>Contato salvo com sucesso! Aguarde que um representante entrará em contato.</b>\n            </p>\n            <button type=\"submit\" class=\"btn btn-success btn-lg btn-rounded shadow-lg \" [disabled]=\"salvando\" *ngIf=\"!contato.salvo\">Solicitar contato</button>\n\n\n\n\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "import {Route, RouterModule} from \"@angular/router\";\nimport {NgModule} from \"@angular/core\";\nimport {LandingpageComponent} from \"./landingpage.component\";\nimport {LandingPagePedidosComponent} from \"../landing-page-pedidos/landing-page-pedidos.component\";\n\nconst routes: Route[] = [\n  {path: 'lp/pedidos', component: LandingPagePedidosComponent},\n  {\n    path: '', component: LandingpageComponent,\n    children: [\n      {path: 'index', component: LandingpageComponent}\n    ]\n  }\n\n];\n\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class LandingpageRoutingModule { }\n", "import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';\nimport {NgForm} from \"@angular/forms\";\nimport {HttpClient} from \"@angular/common/http\";\n\n@Component({\n  selector: 'app-landing-page-pedidos',\n  templateUrl: './landing-page-pedidos.component.html',\n  styleUrls: ['./landing-page-pedidos.component.scss']\n})\nexport class LandingPagePedidosComponent implements OnInit {\n  @ViewChild('bgContato', { static: true}) private bgContato: ElementRef;\n  @ViewChild('nome', {static: true}) nome: ElementRef;\n  @ViewChild('frm', {static: true}) frm: NgForm;\n  contato: any = {};\n  erro: string;\n  salvando: boolean;\n\n  constructor(private httpClient: HttpClient) { }\n\n  ngOnInit() {\n  }\n\n  solicitarContato() {\n    window.scrollTo(0, document.body.scrollHeight - 700);\n    this.nome.nativeElement.focus();\n  }\n\n\n  onSubmit() {\n    delete this.erro;\n    if(this.frm.valid){\n      this.salvando = true;\n      this.httpClient.post('/empresas/contato', this.contato).toPromise().then( (resposta: any ) => {\n        this.salvando = false;\n        if(resposta.sucesso){\n          this.contato.salvo = true;\n        }else{\n          this.erro = resposta.mensagem;\n        }\n      }).catch( erro => {\n        this.salvando = false;\n        this.erro = 'Falha ao salvar contato! '\n      })\n    } else {\n      this.erro = 'Verifique os campos obrigatórios'\n    }\n  }\n}\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport {LandingpageRoutingModule} from \"./landingpage.routing\";\nimport {LandingpageComponent} from \"./landingpage.component\";\nimport {FormsModule} from \"@angular/forms\";\nimport {LandingPagePedidosComponent} from \"../landing-page-pedidos/landing-page-pedidos.component\";\n\n\n\n@NgModule({\n  declarations: [LandingpageComponent,\n    LandingPagePedidosComponent\n  ],\n  imports: [\n    CommonModule,\n    LandingpageRoutingModule,\n    FormsModule\n  ]\n})\nexport class LandingpageModule { }\n"], "x_google_ignoreList": []}