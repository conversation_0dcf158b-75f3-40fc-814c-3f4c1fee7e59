"use strict";(self.webpackChunksorteieme_js=self.webpackChunksorteieme_js||[]).push([[555],{31555:(ve,C,d)=>{d.r(C),d.d(C,{PainelGrupoModule:()=>he});var p=d(94666),c=d(72433),e=d(22560),b=d(62570),S=d(28163),_=d(15077),s=d(2508),l=d(27209),E=d(202),y=d(64615);function M(o,a){1&o&&e.\u0275\u0275element(0,"i",65)}function G(o,a){1&o&&e.\u0275\u0275element(0,"i",66)}function k(o,a){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"span",67),e.\u0275\u0275listener("click",function(i){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(r.clicouAlerta(i))}),e.\u0275\u0275elementStart(1,"kendo-switch",68),e.\u0275\u0275listener("ngModelChange",function(i){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(r.monitoradorPedidos.tocarAlerta=i)})("ngModelChange",function(i){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(r.alterouStatusAlerta(i))}),e.\u0275\u0275elementEnd()()}if(2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngModel",t.monitoradorPedidos.tocarAlerta)("onLabel","Sim")("offLabel","N\xe3o")}}function w(o,a){1&o&&(e.\u0275\u0275elementStart(0,"a",69),e.\u0275\u0275text(1," Nenhum pedido recente "),e.\u0275\u0275elementEnd())}function I(o,a){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"a",70),e.\u0275\u0275listener("click",function(){const r=e.\u0275\u0275restoreView(t).$implicit,g=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(g.verPedido(r))}),e.\u0275\u0275elementStart(1,"div",71)(2,"small",72),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"p",73),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"p",74)(7,"small"),e.\u0275\u0275text(8),e.\u0275\u0275pipe(9,"tel"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"p",75)(11,"small"),e.\u0275\u0275text(12),e.\u0275\u0275pipe(13,"currency"),e.\u0275\u0275elementEnd()()()}if(2&o){const t=a.$implicit;e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("#",t.codigo,""),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.cliente.nome," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(9,4,t.cliente.telefone)),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind2(13,6,t.total,"BRL"))}}function T(o,a){1&o&&(e.\u0275\u0275elementStart(0,"span",78),e.\u0275\u0275text(1," *"),e.\u0275\u0275elementStart(2,"b"),e.\u0275\u0275text(3,"Aguardando pagamento online"),e.\u0275\u0275elementEnd()())}function F(o,a){if(1&o&&(e.\u0275\u0275elementStart(0,"div"),e.\u0275\u0275template(1,T,4,0,"span",76),e.\u0275\u0275elementStart(2,"h4",77),e.\u0275\u0275text(3," Voce tem um novo pedido: "),e.\u0275\u0275elementStart(4,"b"),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.monitoradorPedidos.novosPedidos[0].aguardandoPagamentoOnline),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1("#",t.monitoradorPedidos.novosPedidos[0].codigo,"")}}function j(o,a){if(1&o&&(e.\u0275\u0275elementStart(0,"div")(1,"h4",77),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" Voce tem ",t.monitoradorPedidos.novosPedidos.length," novos pedidos ")}}function V(o,a){if(1&o&&(e.\u0275\u0275elementStart(0,"h4",77),e.\u0275\u0275text(1," O Pagamento do pedido "),e.\u0275\u0275elementStart(2,"b"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(4," foi confirmado "),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",t.monitoradorPedidos.pedidosConfirmados[0].codigo,"")}}function O(o,a){1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1,", "),e.\u0275\u0275elementEnd())}function B(o,a){if(1&o&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1),e.\u0275\u0275template(2,O,2,0,"span",0),e.\u0275\u0275elementEnd()),2&o){const t=a.$implicit,n=a.last;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.codigo," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!n)}}function A(o,a){if(1&o&&(e.\u0275\u0275elementStart(0,"h4",77),e.\u0275\u0275text(1," Os Pagamentos dos pedidos foram confirmados: "),e.\u0275\u0275elementStart(2,"b"),e.\u0275\u0275template(3,B,3,2,"span",80),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",t.monitoradorPedidos.pedidosConfirmados)}}function D(o,a){if(1&o&&(e.\u0275\u0275elementStart(0,"div"),e.\u0275\u0275template(1,V,5,1,"h4",79),e.\u0275\u0275template(2,A,4,1,"h4",79),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",1===t.monitoradorPedidos.pedidosConfirmados.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.monitoradorPedidos.pedidosConfirmados.length>1)}}function z(o,a){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",81),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.verPedido(i.monitoradorPedidos.novosPedidos[0]))}),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()}if(2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.monitoradorPedidos.novosPedidos[0].aceito?"Ver":"Aprovar"," Pedido ")}}function R(o,a){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",81),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.verPedido(i.monitoradorPedidos.pedidosConfirmados[0]))}),e.\u0275\u0275text(1," Ver Pedido "),e.\u0275\u0275elementEnd()}}function L(o,a){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",81),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.verPedidos())}),e.\u0275\u0275text(1,"Ver Pedidos"),e.\u0275\u0275elementEnd()}}function Y(o,a){1&o&&(e.\u0275\u0275element(0,"i",82),e.\u0275\u0275text(1," Conectando com a impressora... "),e.\u0275\u0275element(2,"i",83))}function Q(o,a){1&o&&e.\u0275\u0275element(0,"div",84)}const N=function(o,a){return{"badge-danger":o,"badge-light":a}};function J(o,a){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",1)(2,"div",2)(3,"ul",3)(4,"li",4)(5,"a",5),e.\u0275\u0275template(6,M,1,0,"i",6),e.\u0275\u0275template(7,G,1,0,"i",7),e.\u0275\u0275elementStart(8,"span",8),e.\u0275\u0275text(9),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",9)(11,"div",10)(12,"h5",11),e.\u0275\u0275text(13," Alerta de novos pedidos "),e.\u0275\u0275template(14,k,2,3,"span",12),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(15,"div",13),e.\u0275\u0275template(16,w,2,0,"a",14),e.\u0275\u0275template(17,I,14,9,"a",15),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(18,"li",16)(19,"a",17),e.\u0275\u0275element(20,"img",18),e.\u0275\u0275elementStart(21,"span",19),e.\u0275\u0275text(22),e.\u0275\u0275element(23,"i",20),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(24,"div",21)(25,"div",22)(26,"h6",23),e.\u0275\u0275text(27,"Bem vindo!"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(28,"a",24),e.\u0275\u0275element(29,"i",25),e.\u0275\u0275elementStart(30,"span"),e.\u0275\u0275text(31,"Minha Conta"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(32,"div",26),e.\u0275\u0275elementStart(33,"a",27),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.logout())}),e.\u0275\u0275element(34,"i",28),e.\u0275\u0275elementStart(35,"span"),e.\u0275\u0275text(36,"Logout"),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(37,"ul",29)(38,"li",30)(39,"form",31)(40,"div",32)(41,"div",33),e.\u0275\u0275element(42,"input",34),e.\u0275\u0275elementStart(43,"div",35)(44,"button",36),e.\u0275\u0275element(45,"i",37),e.\u0275\u0275elementEnd()()()()()()(),e.\u0275\u0275elementStart(46,"div",38)(47,"a",39)(48,"span",40),e.\u0275\u0275element(49,"img",41),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(50,"span",42),e.\u0275\u0275element(51,"img",43),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275element(52,"ul",29),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(53,"div",44)(54,"div",45)(55,"div",46)(56,"div",47,48)(58,"div",49)(59,"div",50)(60,"div",51)(61,"div",52)(62,"div",53)(63,"div",54)(64,"div",55),e.\u0275\u0275element(65,"i",56),e.\u0275\u0275elementStart(66,"h2",57),e.\u0275\u0275text(67,"Alerta Pedidos"),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(68,F,6,2,"div",0),e.\u0275\u0275template(69,j,3,1,"div",0),e.\u0275\u0275template(70,D,3,2,"div",0),e.\u0275\u0275elementStart(71,"button",58),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.monitoradorPedidos.fecheEZerePedidosNovos())}),e.\u0275\u0275text(72,"Ok"),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(73,z,2,1,"button",59),e.\u0275\u0275template(74,R,2,0,"button",59),e.\u0275\u0275template(75,L,2,0,"button",59),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275template(76,Y,3,0,"ng-template",null,60,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275element(78,"router-outlet"),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275element(79,"app-footer"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(80,Q,1,0,"div",61),e.\u0275\u0275elementStart(81,"audio",62),e.\u0275\u0275element(82,"source",63),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(83,"div",64),e.\u0275\u0275elementContainerEnd()}if(2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(6),e.\u0275\u0275property("ngIf",t.monitoradorPedidos.tocarAlerta),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.monitoradorPedidos.tocarAlerta),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction2(15,N,t.monitoradorPedidos.totalPedidosMonitorados()>0,!t.monitoradorPedidos.totalPedidosMonitorados())),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.monitoradorPedidos.totalPedidosMonitorados(),""),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngIf",t.monitoradorPedidos.inIframe()),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!t.monitoradorPedidos.pedidos.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t.monitoradorPedidos.pedidos),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate1(" ",t.usuario.nome," "),e.\u0275\u0275advance(46),e.\u0275\u0275property("ngIf",1===t.monitoradorPedidos.novosPedidos.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.monitoradorPedidos.novosPedidos.length>1),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.monitoradorPedidos.pedidosConfirmados.length>0),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",1===t.monitoradorPedidos.novosPedidos.length&&!t.monitoradorPedidos.pedidosConfirmados.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.monitoradorPedidos.novosPedidos.length&&1===t.monitoradorPedidos.pedidosConfirmados.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.monitoradorPedidos.totalPedidosMonitorados()>1),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngIf",t.bloqueado)}}let q=(()=>{class o{constructor(t,n,i,r){this.router=t,this.autorizacaoService=n,this.monitoradorPedidos=i,this.grupolojasService=r,this.autorizacaoService.usuarioLogado$.subscribe(g=>{!g||(this.usuario=g)}),this.grupolojasService.logado().then(g=>{this.grupoLojas=g}),this.monitoradorPedidos.inicieMonitoramentoGrupoLojas()}ngOnInit(){}logout(){null!==this.autorizacaoService.logout()&&this.router.navigate(["/admin-rede/login"],{}).then(()=>{})}alterouStatusAlerta(t){this.monitoradorPedidos.toogleTocarIframe()}clicouAlerta(t){t.stopPropagation(),t.preventDefault()}verPedido(t){this.monitoradorPedidos.fecheEZerePedidosNovos(),this.router.navigateByUrl(String(`/admin-grupo/pedidos/detalhes/${t.empresa.id}/${t.guid}`),{state:t})}verPedidos(){this.monitoradorPedidos.fecheEZerePedidosNovos(),this.router.navigateByUrl("/admin-grupo/index")}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275directiveInject(c.F0),e.\u0275\u0275directiveInject(b.u),e.\u0275\u0275directiveInject(S.q),e.\u0275\u0275directiveInject(_.R))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["app-painel-grupo"]],decls:1,vars:1,consts:[[4,"ngIf"],["id","wrapper"],[1,"navbar-custom"],[1,"list-unstyled","topnav-menu","float-right","mb-0"],[1,"dropdown","notification-list","topbar-dropdown"],["data-toggle","dropdown","href","#","role","button","aria-haspopup","false","aria-expanded","false",1,"nav-link","dropdown-toggle","waves-effect","waves-light"],["class","fe-bell noti-icon",4,"ngIf"],["class","fe-bell-off noti-icon",4,"ngIf"],[1,"badge","rounded-circle","noti-icon-badge",3,"ngClass"],[1,"dropdown-menu","dropdown-menu-right","dropdown-lg"],[1,"dropdown-item","noti-title"],[1,"m-0"],["class","ml-1",3,"click",4,"ngIf"],[1,"slimscroll","noti-scroll"],["class","dropdown-item notify-item text-muted text-center ",4,"ngIf"],["class","dropdown-item notify-item cpointer ","style","position: relative",3,"click",4,"ngFor","ngForOf"],[1,"dropdown","notification-list"],["data-toggle","dropdown","href","#","role","button","aria-haspopup","false","aria-expanded","false",1,"nav-link","dropdown-toggle","nav-user","mr-0","waves-effect","waves-light"],["src","/assets/fidelidade/icones/user-icon.png","alt","user-image",2,"width","32px","height","32px"],[1,"pro-user-name","ml-1"],[1,"mdi","mdi-chevron-down"],[1,"dropdown-menu","dropdown-menu-right","profile-dropdown"],[1,"dropdown-header","noti-title"],[1,"text-overflow","m-0"],["href","javascript:void(0);",1,"dropdown-item","notify-item"],[1,"fe-user"],[1,"dropdown-divider"],["href","javascript:void(0);",1,"dropdown-item","notify-item",3,"click"],[1,"fe-log-out"],[1,"list-unstyled","topnav-menu","topnav-menu-left","m-0"],[1,"d-none"],[1,"app-search"],[1,"app-search-box"],[1,"input-group"],["type","text","placeholder","Buscar...",1,"form-control"],[1,"input-group-append"],["type","submit",1,"btn"],[1,"fe-search"],[1,"logo-box"],["routerLink","/admin-grupo/index",1,"logo","text-center"],[1,"logo-lg"],["src","/assets/fidelidade/promokit-horizontal-branco.png","alt","","height","36"],[1,"logo-sm"],["src","/assets/fidelidade/zapkit-logo.png","alt","","height","36"],[1,"content-page",2,"margin-left","0px"],[1,"content"],[1,"container-fluid"],[1,"row"],["appendTo",""],[1,"col-12","conteudo-componente"],[1,"page-title-box"],["id","alertaNovo","tabindex","-1","role","dialog","aria-modal","true","data-keyboard","false",1,"modal","fade"],[1,"modal-dialog"],[1,"modal-content"],[1,"modal-body","p-4"],[1,"text-center"],[1,"dripicons-information","h1","text-info"],[1,"mt-2"],["type","button",1,"btn","btn-light",3,"click"],["type","button","class","btn btn-info ml-2  ",3,"click",4,"ngIf"],["template",""],["class","bloqueio",4,"ngIf"],["id","beep"],["src","/Servidor/src/assets/audio/beet-pedido.mp3"],["kendoWindowContainer",""],[1,"fe-bell","noti-icon"],[1,"fe-bell-off","noti-icon"],[1,"ml-1",3,"click"],[3,"ngModel","onLabel","offLabel","ngModelChange"],[1,"dropdown-item","notify-item","text-muted","text-center"],[1,"dropdown-item","notify-item","cpointer",2,"position","relative",3,"click"],[1,"notify-icon"],[1,"text-blue","d-block"],[1,"notify-details"],[1,"text-muted","mb-0","user-msg"],[1,"mb-0","user-msg","valor"],["class","text-danger",4,"ngIf"],[1,"mt-3"],[1,"text-danger"],["class","mt-3",4,"ngIf"],[4,"ngFor","ngForOf"],["type","button",1,"btn","btn-info","ml-2",3,"click"],[1,"fe-printer","fa-lg","mr-1"],[1,"k-icon","k-i-loading"],[1,"bloqueio"]],template:function(t,n){1&t&&e.\u0275\u0275template(0,J,84,18,"ng-container",0),2&t&&e.\u0275\u0275property("ngIf",null!=n.usuario)},dependencies:[p.NgClass,p.NgForOf,p.NgIf,s._Y,s.JJ,s.JL,s.On,s.F,l.qU,E.c,c.lC,c.yS,p.CurrencyPipe,y.t]}),o})();var h=d(89904),u=d(14362),f=d(30620),v=d(73185),P=d(33162);const $=["gridPedidos"];function U(o,a){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",19),e.\u0275\u0275element(1,"i",20),e.\u0275\u0275text(2),e.\u0275\u0275elementStart(3,"button",21),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.fecheMensagemSucesso())}),e.\u0275\u0275elementStart(4,"span",22),e.\u0275\u0275text(5,"\xd7"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(6,"i",23),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.imprimaPedido(i.novoPedido))}),e.\u0275\u0275elementEnd()()}if(2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.mensagemSucesso," ")}}function Z(o,a){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",24),e.\u0275\u0275listener("click",function(i){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.onFilter(i))}),e.\u0275\u0275elementEnd()}2&o&&e.\u0275\u0275property("look","clear")("icon","search")}let K=(()=>{class o{constructor(t,n,i,r){this.grupolojasService=t,this.router=n,this.route=i,this.autorizacaoService=r,this.pedidos=[],this.novosPedidos=[],this.paginacao={total:1e3},this.carregando=!1,this.resumoPedidos={total:0,emPreparacao:0,emEntrega:0,novos:0},this.page=0,this.usuario=null,this.objFiltro={q:""}}ngOnInit(){this.usuario=this.autorizacaoService.getUser(),this.carreguePedidos()}carreguePedidos(){if(this.carregando)return;this.carregando=!0;let t=u.d.pedidos;this.mesa&&(t=u.d.comanda),this.grupolojasService.listePedidos(0,this.paginacao.total,{q:this.objFiltro.q,emAberto:!0},t).then(n=>{this.pedidos=n.pedidos||[],this.ultimaAtualizacao=n.ultimaAtualizacao,this.setResumoPedidos(),this.gridPedidos.atualizeGridPedidos(this.pedidos,"em-aberto"),this.carregando=!1}).catch(()=>{this.carregando=!1})}setResumoPedidos(){this.resumoPedidos.total=this.pedidos.length,this.resumoPedidos.emPreparacao=this.pedidos.filter(t=>"Em prepara\xe7\xe3o"===t.status).length,this.resumoPedidos.emEntrega=this.pedidos.filter(t=>"Saiu para entrega"===t.status).length,this.resumoPedidos.novos=this.pedidos.filter(t=>"Novo"===t.status).length}onFilter(t){this.carreguePedidos()}imprimaPedido(t){window.open("/imprimir/pedido/"+t.guid)}fecheMensagemSucesso(){delete this.mensagemSucesso}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275directiveInject(_.R),e.\u0275\u0275directiveInject(c.F0),e.\u0275\u0275directiveInject(c.gz),e.\u0275\u0275directiveInject(b.u))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["app-acompanhar-pedidos-grupo"]],viewQuery:function(t,n){if(1&t&&e.\u0275\u0275viewQuery($,7),2&t){let i;e.\u0275\u0275queryRefresh(i=e.\u0275\u0275loadQuery())&&(n.gridPedidos=i.first)}},inputs:{mesa:"mesa"},decls:53,vars:19,consts:[[1,"row"],[1,"col-6","col-lg-3"],[1,"card-box","ribbon-box","com-borda"],[1,"ribbon","ribbon-dark","float-left"],[1,"text-info","float-right","mt-0","total"],[1,"ribbon-content"],[1,"card-box","ribbon-box"],[1,"ribbon","ribbon-warning","float-left"],[1,"ribbon","ribbon-success","float-left"],[1,"ribbon","ribbon-info","float-left"],["class","alert alert-success alert-dismissible fade show mb-2","role","alert",4,"ngIf"],[1,"mb-2"],[1,"col-auto"],["placeholder","Busque por nome ou telefone do cliente ou por c\xf3digo","name","txtFiltro","appAutoFocus","",2,"width","500px",3,"ngModel","clearButton","ngModelChange","valueChange"],["kendoTextBoxSuffixTemplate",""],[1,"clearfix","mt-2"],[3,"pedidos","carregando","mesa","modoGrupoLojas"],["gridPedidos",""],[1,"clearfix","mt-4"],["role","alert",1,"alert","alert-success","alert-dismissible","fade","show","mb-2"],[1,"mdi","mdi-check-all","mr-2"],["type","button","data-dismiss","alert","aria-label","Fechar",1,"close",3,"click"],["aria-hidden","true"],[1,"fa","fa-print","fa-lg","ml-1","cpointer",3,"click"],["kendoButton","",3,"look","icon","click"]],template:function(t,n){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"span"),e.\u0275\u0275text(5,"Total"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(6,"h5",4),e.\u0275\u0275text(7),e.\u0275\u0275pipe(8,"number"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div",5),e.\u0275\u0275element(10,"p"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(11,"div",1)(12,"div",6)(13,"div",7)(14,"span"),e.\u0275\u0275text(15,"Em Prepara\xe7\xe3o"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"div",4),e.\u0275\u0275text(17),e.\u0275\u0275pipe(18,"number"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(19,"div",5),e.\u0275\u0275element(20,"p"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(21,"div",1)(22,"div",6)(23,"div",8)(24,"span"),e.\u0275\u0275text(25,"Em Entrega"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(26,"div",4),e.\u0275\u0275text(27),e.\u0275\u0275pipe(28,"number"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(29,"div",5),e.\u0275\u0275element(30,"p"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(31,"div",1)(32,"div",6)(33,"div",9)(34,"span"),e.\u0275\u0275text(35,"Novos"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(36,"div",4),e.\u0275\u0275text(37),e.\u0275\u0275pipe(38,"number"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(39,"div",5),e.\u0275\u0275element(40,"p"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275template(41,U,7,1,"div",10),e.\u0275\u0275elementStart(42,"div",11)(43,"div",0)(44,"div",12)(45,"kendo-formfield")(46,"kendo-textbox",13),e.\u0275\u0275listener("ngModelChange",function(r){return n.objFiltro.q=r})("valueChange",function(r){return n.onFilter(r)}),e.\u0275\u0275template(47,Z,1,2,"ng-template",14),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275element(48,"div",12),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(49,"div",15)(50,"app-grid-pedidos",16,17)(52,"div",18)),2&t&&(e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(8,11,n.resumoPedidos.total)," "),e.\u0275\u0275advance(10),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(18,13,n.resumoPedidos.emPreparacao)," "),e.\u0275\u0275advance(10),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(28,15,n.resumoPedidos.emEntrega)," "),e.\u0275\u0275advance(10),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(38,17,n.resumoPedidos.novos)," "),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngIf",n.mensagemSucesso),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngModel",n.objFiltro.q)("clearButton",!0),e.\u0275\u0275advance(4),e.\u0275\u0275property("pedidos",n.pedidos)("carregando",n.carregando)("mesa",n.mesa)("modoGrupoLojas",!0))},dependencies:[p.NgIf,s.JJ,s.On,l.PL,l.d6,l.hg,f.zx,v.U,P.f,p.DecimalPipe],styles:[".card-box[_ngcontent-%COMP%]{text-align:center;padding-top:15px;padding-bottom:15px}.card-box[_ngcontent-%COMP%]   .total[_ngcontent-%COMP%]{font-size:32px;height:32px;line-height:32px;color:#000!important}.card-box.com-borda[_ngcontent-%COMP%]{border-bottom:6px solid #808495;padding-bottom:9px}@media (max-width: 648px){.ribbon-box[_ngcontent-%COMP%]   .ribbon.float-left[_ngcontent-%COMP%]{min-width:120%}.btn-novo[_ngcontent-%COMP%]{right:0;position:relative;font-size:15px;margin-right:0!important}}"]}),o})();var m=d(45807);const H=["gridPedidos"];function W(o,a){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",21),e.\u0275\u0275listener("click",function(i){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.onFilter(i))}),e.\u0275\u0275elementEnd()}2&o&&e.\u0275\u0275property("look","clear")("icon","search")}let X=(()=>{class o{constructor(t){this.grupolojasService=t,this.paginacao={total:1e3},this.carregando=!1,this.filtro={inicio:null,fim:null},this.resumoPedidos={qtde:0,total:0,totalTaxas:0},this.mesa=!1,this.objFiltro={q:""},this.usuario=null}ngOnInit(){this.filtro.inicio=moment().add(-7,"d").toDate(),this.filtro.fim=new Date,this.carreguePedidos()}carreguePedidos(){if(this.carregando)return;let t=moment(this.filtro.inicio).format("YYYYMMDD"),n=moment(this.filtro.fim).format("YYYYMMDD");this.carregando=!0;let i=u.d.pedidos;this.mesa&&(i=u.d.comanda),this.grupolojasService.listePedidos(0,this.paginacao.total,{apenasEncerrados:!0,dtInicio:t,dtFim:n,q:this.objFiltro.q},i).then(r=>{this.setPedidos(r.pedidos||[]),this.gridPedidos.atualizeGridPedidos(this.pedidos,"pagos"),this.carregando=!1}).catch(()=>{this.carregando=!1})}setPedidos(t){this.pedidos=t||[],this.resumoPedidos.qtde=this.pedidos.length,this.resumoPedidos.totalTaxas=this.pedidos.reduce((n,i)=>n+i.taxaEntrega,0),this.resumoPedidos.total=this.pedidos.reduce((n,i)=>n+i.total,0),this.resumoPedidos.total-=this.resumoPedidos.totalTaxas}onFilter(t){this.carreguePedidos()}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275directiveInject(_.R))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["app-pedidos-pago-grupo"]],viewQuery:function(t,n){if(1&t&&e.\u0275\u0275viewQuery(H,5),2&t){let i;e.\u0275\u0275queryRefresh(i=e.\u0275\u0275loadQuery())&&(n.gridPedidos=i.first)}},inputs:{mesa:"mesa"},decls:51,vars:19,consts:[[1,"row"],[1,"col-6","col-lg-3"],[1,"card-box","ribbon-box","com-borda"],[1,"ribbon","ribbon-dark","float-left"],[1,"text-info","float-right","mt-0","total"],[1,"ribbon-content"],[1,"card-box","ribbon-box"],[1,"ribbon","ribbon-success","float-left"],[1,"ribbon","ribbon-warning","float-left"],[1,"mb-2"],[1,"col-auto"],["placeholder","Busque por nome ou telefone do cliente ou por c\xf3digo","name","txtFiltro","appAutoFocus","",2,"width","500px",3,"ngModel","clearButton","ngModelChange","valueChange"],["kendoTextBoxSuffixTemplate",""],[1,"col"],[1,"mr-3"],[1,"label"],["kendoDateRangeStartInput","",3,"ngModel","ngModelChange"],[1,"label","ml-2","mr-2"],["kendoDateRangeEndInput","",3,"ngModel","ngModelChange"],[3,"pedidos","carregando","mesa","modoGrupoLojas"],["gridPedidos",""],["kendoButton","",3,"look","icon","click"]],template:function(t,n){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"span"),e.\u0275\u0275text(5,"Total"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(6,"h5",4),e.\u0275\u0275text(7),e.\u0275\u0275pipe(8,"number"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div",5),e.\u0275\u0275element(10,"p"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(11,"div",1)(12,"div",6)(13,"div",7)(14,"span"),e.\u0275\u0275text(15,"Valor "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"div",4),e.\u0275\u0275text(17),e.\u0275\u0275pipe(18,"currency"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(19,"div",5),e.\u0275\u0275element(20,"p"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(21,"div",1)(22,"div",6)(23,"div",8)(24,"span"),e.\u0275\u0275text(25,"Taxas "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(26,"div",4),e.\u0275\u0275text(27),e.\u0275\u0275pipe(28,"currency"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(29,"div",5),e.\u0275\u0275element(30,"p"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(31,"div",0)(32,"div",9)(33,"div",0)(34,"div",10)(35,"kendo-formfield")(36,"kendo-textbox",11),e.\u0275\u0275listener("ngModelChange",function(r){return n.objFiltro.q=r})("valueChange",function(r){return n.onFilter(r)}),e.\u0275\u0275template(37,W,1,2,"ng-template",12),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(38,"div",13)(39,"label",14),e.\u0275\u0275text(40,"Per\xedodo: "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(41,"kendo-daterange")(42,"label"),e.\u0275\u0275element(43,"span",15),e.\u0275\u0275elementStart(44,"kendo-dateinput",16),e.\u0275\u0275listener("ngModelChange",function(r){return n.filtro.inicio=r})("ngModelChange",function(){return n.carreguePedidos()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(45,"label")(46,"span",17),e.\u0275\u0275text(47,"At\xe9 "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(48,"kendo-dateinput",18),e.\u0275\u0275listener("ngModelChange",function(r){return n.filtro.fim=r})("ngModelChange",function(){return n.carreguePedidos()}),e.\u0275\u0275elementEnd()()()()()()(),e.\u0275\u0275element(49,"app-grid-pedidos",19,20)),2&t&&(e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(8,11,n.resumoPedidos.qtde)," "),e.\u0275\u0275advance(10),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind2(18,13,n.resumoPedidos.total,"BRL")," "),e.\u0275\u0275advance(10),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind2(28,16,n.resumoPedidos.totalTaxas,"BRL")," "),e.\u0275\u0275advance(9),e.\u0275\u0275property("ngModel",n.objFiltro.q)("clearButton",!0),e.\u0275\u0275advance(8),e.\u0275\u0275property("ngModel",n.filtro.inicio),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngModel",n.filtro.fim),e.\u0275\u0275advance(1),e.\u0275\u0275property("pedidos",n.pedidos)("carregando",n.carregando)("mesa",n.mesa)("modoGrupoLojas",!0))},dependencies:[s.JJ,s.On,l.PL,l.d6,l.hg,f.zx,m.Y8,m.SQ,m.oK,m.Bd,v.U,P.f,p.DecimalPipe,p.CurrencyPipe],styles:[".card-box[_ngcontent-%COMP%]{text-align:center;padding-top:15px;padding-bottom:15px}.card-box[_ngcontent-%COMP%]   .total[_ngcontent-%COMP%]{font-size:32px;height:32px;line-height:32px;color:#000!important}.card-box.com-borda[_ngcontent-%COMP%]{border-bottom:6px solid #808495;padding-bottom:9px}.btn-novo[_ngcontent-%COMP%]{top:-10px}@media (max-width: 648px){.ribbon-box[_ngcontent-%COMP%]   .ribbon.float-left[_ngcontent-%COMP%]{min-width:120%}}"]}),o})();var ee=d(56908),x=d.n(ee);const te=["gridPedidos"];function oe(o,a){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",17),e.\u0275\u0275listener("click",function(i){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.onFilter(i))}),e.\u0275\u0275elementEnd()}2&o&&e.\u0275\u0275property("look","clear")("icon","search")}let ne=(()=>{class o{constructor(t){this.grupolojasService=t,this.paginacao={total:1e3},this.carregando=!1,this.filtro={inicio:null,fim:null},this.resumoPedidos={qtde:0,total:0,totalTaxas:0},this.mesa=!1,this.objFiltro={q:""}}ngOnInit(){this.filtro.inicio=x()().add(-7,"d").toDate(),this.filtro.fim=new Date,this.carreguePedidos()}carreguePedidos(){if(this.carregando)return;let t=x()(this.filtro.inicio).format("YYYYMMDD"),n=x()(this.filtro.fim).format("YYYYMMDD");this.carregando=!0;let i=u.d.pedidos;this.mesa&&(i=u.d.comanda),this.grupolojasService.listePedidosCancelados(0,this.paginacao.total,null,t,n,i,this.objFiltro.q).then(r=>{this.setPedidos(r.pedidos||[]),this.gridPedidos.atualizeGridPedidos(this.pedidos,"cancelados"),this.carregando=!1}).catch(()=>{this.carregando=!1})}setPedidos(t){this.pedidos=t||[],this.resumoPedidos.qtde=this.pedidos.length}onFilter(t){this.carreguePedidos()}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275directiveInject(_.R))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["app-pedidos-cancelados-grupo"]],viewQuery:function(t,n){if(1&t&&e.\u0275\u0275viewQuery(te,7),2&t){let i;e.\u0275\u0275queryRefresh(i=e.\u0275\u0275loadQuery())&&(n.gridPedidos=i.first)}},inputs:{mesa:"mesa"},decls:29,vars:11,consts:[[1,"row"],[1,"col-6","col-lg-3"],[1,"card-box","ribbon-box","com-borda"],[1,"ribbon","ribbon-dark","float-left"],[1,"text-info","float-right","mt-0","total"],[1,"ribbon-content"],[1,"col-auto"],["placeholder","Busque por nome ou telefone do cliente ou por c\xf3digo","name","txtFiltro","appAutoFocus","",2,"width","500px",3,"ngModel","clearButton","ngModelChange","valueChange"],["kendoTextBoxSuffixTemplate",""],[1,"col"],[1,"mr-3"],[1,"label"],["kendoDateRangeStartInput","",3,"ngModel","ngModelChange"],[1,"label","ml-2","mr-2"],["kendoDateRangeEndInput","",3,"ngModel","ngModelChange"],[3,"pedidos","carregando","mesa","modoGrupoLojas"],["gridPedidos",""],["kendoButton","",3,"look","icon","click"]],template:function(t,n){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"span"),e.\u0275\u0275text(5,"Total"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(6,"h5",4),e.\u0275\u0275text(7),e.\u0275\u0275pipe(8,"number"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div",5),e.\u0275\u0275element(10,"p"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(11,"div",0)(12,"div",6)(13,"kendo-formfield")(14,"kendo-textbox",7),e.\u0275\u0275listener("ngModelChange",function(r){return n.objFiltro.q=r})("valueChange",function(r){return n.onFilter(r)}),e.\u0275\u0275template(15,oe,1,2,"ng-template",8),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(16,"div",9)(17,"label",10),e.\u0275\u0275text(18,"Per\xedodo: "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(19,"kendo-daterange")(20,"label"),e.\u0275\u0275element(21,"span",11),e.\u0275\u0275elementStart(22,"kendo-dateinput",12),e.\u0275\u0275listener("ngModelChange",function(r){return n.filtro.inicio=r})("ngModelChange",function(){return n.carreguePedidos()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(23,"label")(24,"span",13),e.\u0275\u0275text(25,"At\xe9 "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(26,"kendo-dateinput",14),e.\u0275\u0275listener("ngModelChange",function(r){return n.filtro.fim=r})("ngModelChange",function(){return n.carreguePedidos()}),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275element(27,"app-grid-pedidos",15,16)),2&t&&(e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(8,9,n.resumoPedidos.qtde)," "),e.\u0275\u0275advance(7),e.\u0275\u0275property("ngModel",n.objFiltro.q)("clearButton",!0),e.\u0275\u0275advance(8),e.\u0275\u0275property("ngModel",n.filtro.inicio),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngModel",n.filtro.fim),e.\u0275\u0275advance(1),e.\u0275\u0275property("pedidos",n.pedidos)("carregando",n.carregando)("mesa",n.mesa)("modoGrupoLojas",!0))},dependencies:[s.JJ,s.On,l.PL,l.d6,l.hg,f.zx,m.Y8,m.SQ,m.oK,m.Bd,v.U,P.f,p.DecimalPipe],styles:[".card-box[_ngcontent-%COMP%]{text-align:center;padding-top:15px;padding-bottom:15px}.card-box[_ngcontent-%COMP%]   .total[_ngcontent-%COMP%]{font-size:32px;height:32px;line-height:32px;color:#000!important}.card-box.com-borda[_ngcontent-%COMP%]{border-bottom:6px solid #808495;padding-bottom:9px}.btn-novo[_ngcontent-%COMP%]{top:-10px}@media (max-width: 648px){.ribbon-box[_ngcontent-%COMP%]   .ribbon.float-left[_ngcontent-%COMP%]{min-width:120%}}"]}),o})();function ie(o,a){1&o&&e.\u0275\u0275element(0,"app-acompanhar-pedidos-grupo")}function ae(o,a){1&o&&e.\u0275\u0275element(0,"app-pedidos-pago-grupo")}function re(o,a){1&o&&e.\u0275\u0275element(0,"app-pedidos-cancelados-grupo")}let de=(()=>{class o{constructor(){}ngOnInit(){}}return o.\u0275fac=function(t){return new(t||o)},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["app-pedidos-grupo"]],decls:12,vars:4,consts:[[1,"page-title"],[1,"fe-shopping-cart"],[1,"card"],[1,"card-body"],[1,"nav-bordered","mt-3"],[3,"title","selected"],["kendoTabContent",""],[3,"title"]],template:function(t,n){1&t&&(e.\u0275\u0275elementStart(0,"h4",0),e.\u0275\u0275element(1,"i",1),e.\u0275\u0275text(2," Pedidos"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",2)(4,"div",3)(5,"kendo-tabstrip",4)(6,"kendo-tabstrip-tab",5),e.\u0275\u0275template(7,ie,1,0,"ng-template",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"kendo-tabstrip",7),e.\u0275\u0275template(9,ae,1,0,"ng-template",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"kendo-tabstrip",7),e.\u0275\u0275template(11,re,1,0,"ng-template",6),e.\u0275\u0275elementEnd()()()()),2&t&&(e.\u0275\u0275advance(6),e.\u0275\u0275property("title","Em aberto")("selected",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("title","Finalizados"),e.\u0275\u0275advance(2),e.\u0275\u0275property("title","Cancelados"))},dependencies:[h.tA,h.Xj,h.ZH,K,X,ne]}),o})();var le=d(62002);let se=(()=>{class o{constructor(t){this.activatedRoute=t,this.idEmpresa=this.activatedRoute.snapshot.params.idEmpresa}ngOnInit(){}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275directiveInject(c.gz))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["app-pedido-grupo-detalhes"]],decls:2,vars:2,consts:[[3,"modoGrupoLojas","idEmpresa"],["pedidoDetalhes",""]],template:function(t,n){1&t&&e.\u0275\u0275element(0,"app-pedido-detalhes",0,1),2&t&&e.\u0275\u0275property("modoGrupoLojas",!0)("idEmpresa",n.idEmpresa)},dependencies:[le.n]}),o})();const me=[{path:"",component:q,resolve:{user:d(90562).O},children:[{path:"index",pathMatch:"full",component:de},{path:"pedidos/detalhes/:idEmpresa/:guid",pathMatch:"full",component:se}]}];let ce=(()=>{class o{}return o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=e.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=e.\u0275\u0275defineInjector({imports:[c.Bz.forChild(me),c.Bz]}),o})();var ue=d(62609),ge=d(84197),fe=d(3128),_e=d(70774);let he=(()=>{class o{}return o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=e.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=e.\u0275\u0275defineInjector({imports:[p.CommonModule,s.u5,fe.o0,l.rw,l.I1,f.Fq,l.ow,m.nf,l.oc,l.qm,ge.zh,l.cc,f.Q,ue.x,ce,h.xu,_e.G]}),o})()}}]);
//# sourceMappingURL=555.e4de9e6acdab0dc6.js.map