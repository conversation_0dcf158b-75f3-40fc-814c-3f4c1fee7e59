<a routerLink="{{cartao.link}}"><h4>{{cartao.plano?.tituloCartao}}</h4></a>
<div  *ngFor="let brinde of cartao.plano?.brindes; let indice = index">
  <div class="slide">
    <div class="pontuacao "  >
      <div class="row" [ngClass]="{
          'mb-1': i < obtenhaQuantidadeDeLinhas ( brinde) - 1,
          'mb-2': i == obtenhaQuantidadeDeLinhas ( brinde) -1
      }" *ngFor="let selos of listaMatrizSelos[indice]; let i = index">

        <div class="col" *ngFor="let selo of selos" style="padding-left: 0px;padding-right: 0px;">
          <div class="container_selo" [ngClass]="{
                'on': selo.status == 1,
                'off': selo.status == 0,
                'sem': selo.status == -1
              }">
            <div class="icone selo" [inlineSVG]="'/assets/fidelidade/icones/icon-selo.svg'" [removeSVGAttributes]="['fill']"></div>
            <div class="valor"><span>{{selo.valor}}</span></div>
          </div>
        </div>
      </div>
    </div>
    <div class="brinde linha"  >
      <span class="preco_troca">Atinja {{brinde.valorEmPontos}}  selos e troque por {{brinde.artigo}}</span>
      <div>
        <div class="nome_brinde">{{brinde.nome}}</div>
        <img class="foto_brinde" src="/images/empresa/{{brinde.linkImagem}}">
        <div *ngIf="brinde.valorEmPontos > cartao.pontos">
          <div class="faltam_selos">Junte mais {{brinde.valorEmPontos - cartao.pontos  == 1 ? '1 selo' : brinde.valorEmPontos - cartao.pontos + ' selos'}}</div>
        </div>
        <div *ngIf="brinde.valorEmPontos <= cartao.pontos">
          <div class="faltam_selos">Parabéns! Você já pode fazer a troca!</div>
        </div>
      </div>
    </div>
  </div>

</div>

<kendo-tabstrip class="nav-bordered mt-2"  >
  <kendo-tabstrip-tab [title]=" 'Extrato' "  [selected]="true">
    <ng-template kendoTabContent>

      <app-extrato-pontos [cartao]="cartao"  ></app-extrato-pontos>

    </ng-template>
  </kendo-tabstrip-tab>

  <kendo-tabstrip-tab [title]=" 'Expirando' " *ngIf="cartao.plano.validade || cartao.plano.vencimento" >
    <ng-template kendoTabContent>

      <app-cartao-proximos-vencimentos [cartao]="cartao"  ></app-cartao-proximos-vencimentos>

    </ng-template>
  </kendo-tabstrip-tab>

</kendo-tabstrip>
