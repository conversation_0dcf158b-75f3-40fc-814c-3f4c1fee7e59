<div class="dados_cliente">
  <div class="caixa_titulo">
    <span class="titulo"    *ngIf ="!cliente.id || cliente.cartoes?.length==1 || cartaoLink"><PERSON><PERSON></span>
    <span class="titulo"    *ngIf ="cliente.cartoes && cliente.cartoes?.length > 1 && !cartaoLink">Me<PERSON></span>
    <span class="subtitulo" *ngIf ="carregou"><div class="icone tam1" [inlineSVG]="'/assets/fidelidade/icones/user.svg'"
                                                  [removeSVGAttributes]="['fill']"></div> {{ cliente.nome }} | {{ cliente.telefone | telefone}}</span>
  </div>
</div>

<div class="cartao conteudo">
  <div class="dados_empresa linha" *ngIf="empresa">
      <a routerLink="/">
        <img class="imagem_empresa" src="/images/empresa/{{empresa.logo}}"/>
      </a>
      <div class="detalhes_empresa">
        <a routerLink="/">
          <span class="nome_empresa">
            {{empresa.nome}}
          </span>
        </a>
        <span class="endereco">
            {{empresa.endereco}}
          </span>
        <a class="whatsapp" target="_blank" [href]="'http://wa.me/55' + empresa.numeroWhatsapp?.whatsapp">
          <img class="icone tam1" src='/assets/fidelidade/icones/icon-whatsapp-mini.png'>
          <span>
            {{empresa?.numeroWhatsapp?.whatsapp | mask: '(99) 9-9999-9999'}}
          </span>
        </a>
      </div>
    </div>

  <div class="carregando" *ngIf="carregando" >
    <div class="spinner-border" role="status">
    </div><bR><br>
    <span>Carregando...</span>
  </div>

  <div class="carregando" *ngIf="!carregando && !carregou" >
    <span style="color: red">{{mensagemDeErro}}</span>
  </div>

  <div class="alert alert-success mt-1" role="alert" *ngIf="codigo === 'criado'">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
      <span aria-hidden="true">×</span>
    </button>
    <span >
      Seu cartao foi criado com sucesso!

      <span  *ngIf="cliente.cartoes?.length  && !cliente.cartoes[0].plano.cartaoConsumo">
         Agora comece a juntar  {{cliente.cartoes[0].plano.tipoDeAcumulo}} para conseguir brindes, descontos.
      </span>
    </span>

  </div>

  <div *ngFor="let cartao of cliente.cartoes" class="mt-3">
    <div *ngIf="cartao.plano.tipoDeAcumulo === tipoSelo && !cartao.plano.cartaoConsumo">
      <div   *ngIf="!cartaoLink || cartaoLink==cartao.id">
        <app-cartao-selo  [cartao]="cartao"  > </app-cartao-selo>
      </div>
    </div>

    <div *ngIf="cartao.plano.tipoDeAcumulo === tipoSelo && cartao.plano.cartaoConsumo">
      <div   *ngIf="!cartaoLink || cartaoLink==cartao.id">
        <app-cartao-consumo-selo  [cartao]="cartao"  > </app-cartao-consumo-selo>
      </div>
    </div>

    <div *ngIf="cartao.plano.tipoDeAcumulo === tipoPontos && !cartao.plano.cartaoConsumo">
      <div  [hidden]="(cartao.plano.tipoDeAcumulo == tipoSelo)" *ngIf="!cartaoLink || cartaoLink==cartao.id">
        <app-cartao-pontos  [cartao]="cartao" [empresa]="empresa" > </app-cartao-pontos>
      </div>
    </div>

    <div *ngIf="cartao.plano.tipoDeAcumulo === tipoReais">
      <div    *ngIf="!cartaoLink || cartaoLink==cartao.id">
        <app-cartao-cashback  [cartao]="cartao"  > </app-cartao-cashback>
      </div>
    </div>
  </div>

  <div *ngIf="carregou && !cliente.id">
    <div [hidden]="tela !== 'INFORMAR_TELEFONE'">
      <h2>Informe seu telefone</h2>
      Informe o seu número de telefone
      <form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"  novalidate #frm="ngForm" (ngSubmit)="onSubmit()">
        <div class="form-group mb-2" >
          <input [disabled]="enviado && mensagemAguarde" [(ngModel)]="dadosContato.telefone" #telefone="ngModel" #txtTelefone
                 type="tel" appAutoFocus class="form-control ng-tns-c12-1 ui-inputtext ui-widget ui-state-default ui-corner-all"
                  placeholder="(__) _-____-____" required  campoTelefone  name="telefone"/>
          <div class="invalid-feedback">
            <p *ngIf="telefone.errors?.required">Celular é obrigatório</p>
            <p *ngIf="telefone.errors?.campoTelefone">Informe um número de celular válido</p>
          </div>
        </div>
        <div class="form-group mb-2 text-center">
          <button class="btn btn-block" type="submit" [disabled]="enviando || (enviado && mensagemAguarde)" [ngClass]="{disabled: enviando || (enviado && mensagemAguarde)}"> Abrir meu Cartão </button>
          <div class="alert alert-danger mt-2" role="alert" *ngIf="frm.submitted && mensagemErroEnvio && !frm.valid" [innerHTML]="mensagemErroEnvio">
            {{mensagemErroEnvio}}
          </div>
          <div class="alert alert-danger mt-2" role="alert" *ngIf="frm.submitted && mensagemFalhaEnvio" [innerHTML]="mensagemFalhaEnvio">
            {{mensagemErroEnvio}}
          </div>
        </div>
      </form>
    </div>
    <div [hidden]="tela !== 'INFORMAR_NOME'">
      <div class="card">
        <div class="card-body" style="padding: 0.8rem;">
          <label>Seu telefone</label>

          <div id="cardCollpase1" class="collapse show">
            <div>
              <div class="row mt-1">
                <div class="col">
                  <h4 class="mt-0"><span style="font-weight: bold;">{{dadosContato.telefone | telefone}} </span></h4>
                </div>
              </div> <!-- end row -->
            </div>
          </div> <!-- collapsed end -->
        </div> <!-- end card-body -->
      </div>
      <h3>Agora informe seus dados para completar o cadastro </h3>


      <form [ngClass]="{'needs-validation': !frmCriarNome.submitted, 'was-validated': frmCriarNome.submitted}"  novalidate #frmCriarNome="ngForm" (ngSubmit)="onSubmitCriarCartao()">
        <div class="form-group mb-2" >
          <label for="nome">
            Informe seu nome
          </label>
          <input id="nome" [disabled]="enviado && mensagemAguarde" [(ngModel)]="dadosContato.nome" #txtNome="ngModel"
                 type="text" appAutoFocus class="form-control ng-tns-c12-1 ui-inputtext ui-widget ui-state-default ui-corner-all"
                 placeholder="Seu nome" required  name="nome"/>
          <div class="invalid-feedback">
            <p *ngIf="txtNome.errors?.required">Nome é obrigatório</p>
          </div>
        </div>
        <div class="form-group mb-2" >
          <label for="dataNascimento">
            Informe sua data de nascimento
          </label>
          <kendo-datepicker #txtDataNascimento="ngModel" [(ngModel)]="dadosContato.dataNascimento" class="form-control" id="dataNascimento"
                            name="dataNascimento">
          </kendo-datepicker>
        </div>
        <div class="form-group mb-2" >
          <label >
            Informe seu gênero
          </label>
          <div class="mt-1 mb-1" style="width:250px;">
            <input type="radio" name="sexo" id="sexohi" class="k-radio right" value="Homem" [(ngModel)]="dadosContato.genero" required kendoRadioButton/>
            <label class="k-radio-label mr-1" for="sexohi">Masculino</label>
            <input type="radio" name="sexo" id="sexomi" class="k-radio right" value="Mulher" [(ngModel)]="dadosContato.genero" required kendoRadioButton/>
            <label class="k-radio-label" for="sexomi">Feminino</label>
            <div class="invalid-feedback"  >
              Sexo é obrigatório
            </div>
          </div>
        </div>
        <div class="form-group mb-2 text-center">
          <button class="btn btn-block btn-success" type="submit" [disabled]="enviando || (enviado && mensagemAguarde)" [ngClass]="{disabled: enviando || (enviado && mensagemAguarde)}"> Criar Cartão </button>
          <div class="alert alert-danger mt-2" role="alert" *ngIf="frmCriarNome.submitted && mensagemErroCriarCartao && !frmCriarNome.valid" [innerHTML]="mensagemErroCriarCartao">
            {{mensagemErroCriarCartao}}
          </div>
          <div class="alert alert-danger mt-2" role="alert" *ngIf="frmCriarNome.submitted && mensagemErroCriarCartao" [innerHTML]="mensagemErroCriarCartao">
            {{mensagemErroCriarCartao}}
          </div>
        </div>
      </form>
    </div>
    <div [hidden]="tela !== 'INFORMAR_CODIGO'" class="mt-2">
    <div class="form-group mb-2 text-center" [hidden]="!enviado || !mensagemAguarde">
      {{mensagemAguarde}}
    </div>
    <app-frm-valide-codigo [parent]="this"></app-frm-valide-codigo>

    <div style="text-align: center" [hidden]="validouCodigo">
      <countdown class="countdown-grande" [config]="{leftTime: 60, template: '$!m!:$!s!'}" (finished)="onFinished()"></countdown>
      <br>
      <a href="#" (click)="reenviarCodigo();" *ngIf="habiliteReenviarCodigo"> Renviar Código </a>
    </div>
  </div>
  </div>
</div>
<div class="cartao semborda" >
  <div class="botao azul" (click)="verRegras() " *ngIf="carregou">
    Confira as regras
  </div>
  <div class="botao cinza"  *ngIf="carregando">
    Confira as regras
  </div>
</div>
<div class="rodape">
  <div  class="logo" [inlineSVG]="'/assets/fidelidade/logo-promokit-vertical.svg'" ></div>
</div>
