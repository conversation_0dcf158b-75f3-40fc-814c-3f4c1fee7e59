<a routerLink="{{cartao.link}}"><h4>{{cartao.plano?.tituloCartao}}</h4></a>
<div class="linha">
  <div class="row">
    <div class="col">
      <div class="pontos">

        <div class="pontos-interno">
          <div class="icone estrela" [inlineSVG]="'/assets/fidelidade/icones/star-icone.svg'" [removeSVGAttributes]="['fill']"></div>
          {{cartao.pontos | number}}
        </div>
      </div>


    </div>
  </div>

</div>

<kendo-tabstrip class="nav-bordered"  #tabs id="tabs">

  <kendo-tabstrip-tab [title]="'Brindes'"  [id]="'brindes'" [selected]="true">
    <ng-template kendoTabContent>

      <div class="row margem-fim">
        <div class="col">
          <div class="minimo-troca">
            Troque por brindes a partir de {{cartao.plano.brindes[0].valorEmPontos}} pontos.
          </div>
        </div>

      </div>

      <div class="container-scroll">
        <div class="row flex-row flex-nowrap">
          <div class="col-sm-8 col-10" *ngFor="let brinde of cartao.plano.brindes">
            <div class="brinde linha" >

              <div class="caixa_brinde">
                <div class="titulo">
                  <p class="nome_brinde_pontos"  [ngClass]="{'font11':brinde.nome.length > 50}" >{{brinde.nome}}</p>
                  <p class="preco_troca" [ngClass]="{  'nao_atingiu': brinde.valorEmPontos > cartao.pontos,
                                                 'atingiu': brinde.valorEmPontos <= cartao.pontos }">
                    {{brinde.valorEmPontos}} pontos
                  </p>
                </div>
                <div class="container_imagem">
                  <div class="">
                    <img class="foto_brinde" src="https://fibo.promokit.com.br/images/empresa/{{brinde.linkImagem}}">
                  </div>

                </div>

              </div>
              <div *ngIf="brinde.valorEmPontos > cartao.pontos">
                <div class="botao_troca   faltam_selos">Faltam {{getPontosRestantes(brinde,cartao)}} pontos</div>
              </div>
              <div *ngIf="brinde.valorEmPontos <= cartao.pontos">

                <div class="botao_troca pode_trocar cpointer" (click)="solicitarBrinde(brinde, cartao )">
                  <div class="icone estrela" [inlineSVG]="'/assets/fidelidade/icones/star-icone.svg'" [removeSVGAttributes]="['fill', 'heigth', 'width']"></div>
                  Solicitar brinde
                </div>
              </div>

            </div>


          </div>
        </div>
      </div>
    </ng-template>
  </kendo-tabstrip-tab>

  <kendo-tabstrip-tab [title]="'Extrato'"  [id]="'extrato'">
    <ng-template kendoTabContent>

      <app-extrato-pontos [cartao]="cartao"  ></app-extrato-pontos>

    </ng-template>
  </kendo-tabstrip-tab>

  <kendo-tabstrip-tab [title]=" 'Expirando'"  [id]="'expirando'" *ngIf="cartao.plano.validade || cartao.plano.vencimento" id="3" >
    <ng-template kendoTabContent>

      <app-cartao-proximos-vencimentos [cartao]="cartao"  ></app-cartao-proximos-vencimentos>

    </ng-template>
  </kendo-tabstrip-tab>

</kendo-tabstrip>

