<h4 class="page-title">
  <i class="fab fa-whatsapp fa-lg mr-1" ></i> Configuração ChatBot Whatsapp
</h4>

<ng-container *ngIf="carregandoTela">
  <div class="k-i-loading ml-1 mr-1 mt-4" style="font-size: 40px;"></div>
</ng-container>

<ng-container *ngIf="!carregandoTela">
  <div class="alert alert-success mt-1 col-xl-8 col-12" *ngIf="empresa.cardapio.botAtivo && mensagemBotAtivo">
    {{mensagemBotAtivo}}
  </div>

  <div *ngIf="!empresa.cardapio.botAtivo" class="mb-2">
    <div class="alert alert-success mt-1 col-xl-8 col-12">
      <p>
      Ao clicar no botão <b>"Ativar ChatBot"</b>, você estará habilitando o uso do nosso assistente virtual para responder
      <b>perguntas e solicitações dos seus clientes</b> através do <i class="fab fa-whatsapp fa-lg mr-1" ></i> <b>WhatsApp</b>.
      </p>
      <p>
        Nossa ferramenta é projetada para fornecer <b>respostas rápidas e precisas</b>, permitindo que você e sua equipe
        possam se concentrar em outras tarefas importantes.
      </p>
      <p>
        Se você tiver alguma dúvida ou precisar de ajuda, não hesite em entrar em contato conosco.
      </p>
    </div>

    <button class="btn btn-primary mr-2" (click)="ativarBot()">&nbsp;
      Ativar Bot
    </button>
  </div>

  <div *ngIf="empresa.cardapio.botAtivo" class="mb-2">
    <button class="btn btn-danger mr-2" (click)="desativarBot()">&nbsp;
      Desativar Bot
    </button>
  </div>

  <ng-container>
    <h4 class="mt-3">Mensagens do Bot</h4>
    <div style="position: relative">
      <kendo-grid [pageSize]="paginacao.size"
                  [skip]="paginacao.skip"
                  [style]=""
                  [kendoGridBinding]="listaMensagens"
                  [loading]="loading" style="min-height: 300px;"
                  footerStyle="font-size: 11px;"
                  [pageable]="{
                    buttonCount: paginacao.buttonCount,
                    info: paginacao.info,
                    type: paginacao.type,
                    pageSizes: paginacao.pageSizes,
                    previousNext: paginacao.previousNext
                  }"
                  (edit)="editarTextoMensagem($event)"
                  (pageChange)="mudouPaginacao($event)">
        <kendo-grid-messages
          pagerPage="Página"
          pagerOf="de"
          pagerItems="itens"
          noRecords="nenhum usuário cadastrado"
          loading="Carregando"
          pagerItemsPerPage="itens por página"
        >
        </kendo-grid-messages>

        <kendo-grid-column title="Nome" [width]="180">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <span><b>{{dataItem.nome}}</b></span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column title="Descrição"  >
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <span>{{dataItem.descricao}}</span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column title="Mensagem" >
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <span>{{dataItem.mensagem || '-'}}</span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column title="Ativo" [width]="120">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <i class="fa fa-check text-success" *ngIf="dataItem.ativo"></i>
            <i class="fa fa-times text-danger" *ngIf="!dataItem.ativo"></i>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-command-column title="" width="100">
          <ng-template kendoGridCellTemplate let-isNew="isNew">
            <button kendoGridEditCommand [primary]="true" (selectedChange)="editarTextoMensagem($event)">Editar</button>
          </ng-template>
        </kendo-grid-command-column>
      </kendo-grid>
      <div *ngIf="!empresa.cardapio.botAtivo" style="position:absolute;top: 0px;left: 0px;width: 100%;background: rgba(0, 0, 0, .1);height: 100%;" (click)="mensagemDerro()">
      </div>
    </div>
  </ng-container>
</ng-container>
