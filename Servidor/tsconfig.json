{"compileOnSave": false, "compilerOptions": {"downlevelIteration": true, "importHelpers": true, "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "moduleResolution": "node", "experimentalDecorators": true, "module": "es2020", "target": "es2020", "resolveJsonModule": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "types": ["node", "jasmine", "facebook-js-sdk"], "typeRoots": ["./node_modules/@types"], "lib": ["es2017", "dom"], "baseUrl": "./", "skipLibCheck": true}, "angularCompilerOptions": {"strictTemplates": true}}