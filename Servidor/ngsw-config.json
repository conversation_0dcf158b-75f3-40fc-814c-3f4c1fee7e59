{"configVersion": 1, "index": "/admin/index", "dataGroups": [{"name": "api-performance", "urls": ["/empresas", "/brindes", "/produtos", "/fotos", "/contatos", "/atividades", "/mensagem", "/planos", "/tiposDePontuacao", "/cartoes2", "/cartoes", "/campanha", "/api", "/l", "/notificacoes", "/auth", "/usuario", "/bots", "/upload", "/iugu", "/pagamentos", "/contratos", "/exportar", "socket.io/"], "cacheConfig": {"maxSize": 0, "maxAge": "0", "strategy": "freshness"}}], "assetGroups": [{"name": "app", "installMode": "prefetch", "resources": {"files": ["/favicon.ico", "/admin/index", "/manifest.webmanifest", "/*.css", "/*.js"]}}, {"name": "assets", "installMode": "lazy", "updateMode": "prefetch", "resources": {"files": ["/assets/**", "/*.(eot|svg|cur|jpg|png|webp|gif|otf|ttf|woff|woff2|ani)"]}}]}